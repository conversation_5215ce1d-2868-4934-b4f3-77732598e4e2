name: Playwright UI Test (Reusable)

on:
  workflow_call:
    inputs:
      project:
        required: true
        type: string
        description: "Project name (crm, studio, table)"
      test-script:
        required: true
        type: string
        description: "Test script to run"
      test-env:
        required: true
        type: string
        description: "Environment (dev/prod)"
      org:
        required: true
        type: string
        description: "Organization (resola/qa)"
    secrets:
      USERNAME:
        required: true
      PASSWORD:
        required: true

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Cache node modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            tests/e2e/node_modules
          key: ${{ runner.os }}-e2e-node-${{ hashFiles('tests/e2e/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-e2e-node-

      - name: Install dev dependencies
        run: |
          cd tests/e2e
          npm ci

      - name: Create env file
        run: |
          mkdir -p tests/e2e/config/env
          echo "USERNAME=${{ secrets.USERNAME }}" >> tests/e2e/config/env/.env.${{ inputs.test-env }}.${{ inputs.org }}
          echo "PASSWORD=${{ secrets.PASSWORD }}" >> tests/e2e/config/env/.env.${{ inputs.test-env }}.${{ inputs.org }}

      - name: Cache Playwright browsers
        uses: actions/cache@v4
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-browsers-${{ hashFiles('tests/e2e/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-playwright-browsers-

      - name: Install Playwright browsers
        run: |
          cd tests/e2e
          npx playwright install --with-deps

      - name: Run Playwright tests
        run: |
          cd tests/e2e/apps/${{ inputs.project }}
          npm run ${{ inputs.test-script }} 