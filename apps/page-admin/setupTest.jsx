import * as matchers from '@testing-library/jest-dom/matchers';
/**setupTest.js */
import { expect, vi } from 'vitest';
import '@testing-library/jest-dom';

const originalError = console.error;
const originalWarn = console.warn;

// Mock ResizeObserver
beforeAll(() => {
  console.warn = (...args) => {
    return;
  };
  console.error = (...args) => {
    return;
  };
});

expect.extend(matchers);

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  }),
});

// Mock ContentEditable component
vi.mock('react-contenteditable', () => ({
  __esModule: true,
  default: ({ html, onChange }) => {
    const handleChange = (event) => {
      const mockEvent = {
        currentTarget: { innerHTML: event.target.value },
        target: { value: event.target.value },
      };
      onChange && onChange(mockEvent);
    };
    return (
      <div
        contentEditable={true}
        dangerouslySetInnerHTML={{ __html: html }}
        onChange={handleChange}
      />
    );
  },
}));

// Add this mock for ResizeObserver
class ResizeObserver {
  constructor(callback) {
    this.callback = callback;
  }
  observe() {
    // Mock implementation
  }
  unobserve() {
    // Mock implementation
  }
  disconnect() {
    // Mock implementation
  }
}

global.ResizeObserver = ResizeObserver;

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  TolgeeProvider: ({ children }) => children,
  useTranslate: () => ({
    t: vi.fn((key) => key),
  }),
  useTolgee: () => ({
    tolgee: {
      getLanguage: vi.fn(),
      changeLanguage: vi.fn(),
    },
  }),
}));

// Mock dynamic imports
vi.mock('./src/locales/index.ts', () => ({
  default: {
    common_en: {},
    common_ja: {},
    home_en: {},
    home_ja: {},
    builder_en: {},
    builder_ja: {},
    site_settings_en: {},
    site_settings_ja: {},
    workspace_en: {},
    workspace_ja: {},
  },
}));

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});
