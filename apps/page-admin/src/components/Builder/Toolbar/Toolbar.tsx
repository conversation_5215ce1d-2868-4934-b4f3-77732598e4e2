import { MENU_QUERY_PARAM, SETTING_QUERY_PARAM, TAB_SETTINGS_QUERY_PARAM } from '@/constants';
import { useClickOutside, useUserInfor } from '@/hooks';
import usePages from '@/hooks/usePages';
import { ToolbarMenuKey } from '@/types/enum';
import { Flex } from '@mantine/core';
import { IconBrandNuxt, IconBrush, IconPlus, IconSitemap, IconStack2 } from '@tabler/icons-react';
import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import AddElementMenu from './AddElementMenu/AddElementMenu';
import LayerMenu from './LayerMenu/LayerMenu';
import PagesMenu from './PagesMenu/PagesMenu';
import StudioThemeMenu from './StudioThemeMenu';
import StylingMenu from './StylingMenu';
import classes from './Toolbar.module.css';
import ToolbarMenu from './ToolbarMenu';

const BuilderToolbar = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const { isStudioUser } = useUserInfor();
  const { siteId } = useParams();
  const { data: pages, mutate: mutatePages } = usePages({
    siteId: siteId as string,
  });

  useEffect(() => {
    const menuType = searchParams.get(MENU_QUERY_PARAM);
    setActiveMenu(menuType);
  }, [searchParams]);

  const setRef = useClickOutside(() => {
    if (activeMenu) {
      setActiveMenu(null);
      searchParams.delete(MENU_QUERY_PARAM);
      searchParams.delete(SETTING_QUERY_PARAM);
      searchParams.delete(TAB_SETTINGS_QUERY_PARAM);
      setSearchParams(searchParams);
    }
  });

  return (
    <Flex
      w={56}
      h={'100%'}
      bg='white'
      direction='column'
      ref={setRef}
      align={'center'}
      gap={'md'}
      py={12}
      className={classes.toolbarContainer}
    >
      <ToolbarMenu
        trigger={<IconPlus size={16} />}
        menu={<AddElementMenu />}
        externalOpened={activeMenu === ToolbarMenuKey.AddElementMenu}
      />
      <ToolbarMenu
        trigger={<IconStack2 size={16} />}
        menu={<LayerMenu />}
        externalOpened={activeMenu === ToolbarMenuKey.LayerMenu}
      />
      <ToolbarMenu
        trigger={<IconSitemap size={16} onClick={() => mutatePages()} />}
        menu={<PagesMenu pages={pages} fetchPages={mutatePages} />}
        externalOpened={activeMenu === ToolbarMenuKey.PagesMenu}
      />
      <ToolbarMenu
        trigger={<IconBrush size={16} />}
        menu={<StylingMenu />}
        externalOpened={activeMenu === ToolbarMenuKey.StylingMenu}
      />
      {isStudioUser && (
        <ToolbarMenu
          trigger={<IconBrandNuxt size={16} />}
          menu={<StudioThemeMenu />}
          externalOpened={activeMenu === ToolbarMenuKey.StudioThemeMenu}
        />
      )}
      {/* TODO: Temporary hide integration menu
      <ToolbarMenu
        trigger={<IconPlugConnected size={16} />}
        menu={<IntegrationMenu />}
        externalOpened={activeMenu === ToolbarMenuKey.IntegrationMenu}
      /> */}
    </Flex>
  );
};

export default BuilderToolbar;
