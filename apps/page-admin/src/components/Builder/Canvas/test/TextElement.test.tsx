import { useResponsiveNode } from '@/hooks';
import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { mockElementSetting } from '@/utils/testUtils/mockHelper';
import { render } from '@/utils/testUtils/renderTestUi';
import { fireEvent } from '@testing-library/react';
import { type Mock, describe, expect, it, vi } from 'vitest';
import TextElement from '../TextElement';

// Mock only `useResponsiveNode` and `useCurrentTheme`
vi.mock('@/hooks', async (originalModule) => ({
  ...((await originalModule) as object),
  useResponsiveNode: vi.fn(),
}));

vi.mock('@/hooks/theme/useCurrentTheme', async (originalModule) => ({
  ...((await originalModule) as object),
  useCurrentTheme: vi.fn(),
}));

describe('TextElement', () => {
  const mockSetProp = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();

    (useCurrentTheme as Mock).mockReturnValue({
      theme: {
        typography: {
          heading: { font_family: 'Arial', font_weight: 'bold' },
          caption: { font_family: 'Courier', font_weight: 'regular' },
        },
      },
      getThemeColor: vi.fn().mockImplementation((color: string) => color || 'black'),
    });

    (useResponsiveNode as Mock).mockImplementation((callback: any) => ({
      ...callback({
        data: {
          props: {
            width: 200,
            height: 50,
            align: 'center',
            color: 'blue',
            text: 'Sample Text',
            type: 'heading',
            link: null,
          },
        },

        builderPreviewMode: 'default',
        events: {
          selected: true,
        },
      }),
      actions: {
        setProp: mockSetProp,
      },
      connectors: {
        connect: vi.fn(),
        drag: vi.fn(),
      },
    }));
  });

  it('renders with default properties', () => {
    const { getByText } = render(<TextElement type='heading' />);

    const element = getByText('Sample Text');

    expect(element).toBeInTheDocument();
    expect(element).toHaveStyle({
      color: 'rgb(0, 0, 255)',
    });
  });

  it('shows editable text when clicked', () => {
    const { getByText } = render(<TextElement type='heading' />);

    const element = getByText('Sample Text');
    fireEvent.click(element);

    expect(element).toHaveAttribute('contenteditable', 'true');
  });

  it('calls setProp on text change', () => {
    const { getByText } = render(<TextElement type='heading' />);

    const element = getByText('Sample Text');
    fireEvent.click(element);
    fireEvent.change(element, { target: { innerHTML: 'Updated Text' } });
  });
  it('render settings', () => {
    mockElementSetting(TextElement, {});
  });
});
