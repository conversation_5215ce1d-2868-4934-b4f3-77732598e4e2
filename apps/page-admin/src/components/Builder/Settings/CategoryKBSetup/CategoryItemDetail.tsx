import { useFormContext } from '@/contexts/FormContext';
import { ArticleType } from '@/types/enum';
import {
  Flex,
  Group,
  Paper,
  Radio,
  Stack,
  Text,
  TextInput,
  Textarea,
  Transition,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { useDisclosure, useValidatedState } from '@mantine/hooks';
import { IconArrowLeft, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import clsx from 'clsx';
import { isEmpty } from 'lodash';
import React, { useCallback, useEffect, useRef } from 'react';
import Articles from './Articles';
import classes from './CategoryKBSetup.module.css';
import DangerActionModal from './DangerActionModal';
import SubCategories from './SubCategories';

const scaleX = {
  in: { opacity: 1, transform: 'scaleX(1)' },
  out: { opacity: 0, transform: 'scaleX(0)' },
  common: { transformOrigin: 'right' },
  transitionProperty: 'transform, opacity',
};

interface CategoryItemDetailProps {
  index: number;
  opened: boolean;
  onClose: () => void;
}

const CategoryItemDetail = React.memo(({ index, opened, onClose }: CategoryItemDetailProps) => {
  const theme = useMantineTheme();
  const { t } = useTranslate(['builder', 'common']);
  const form = useFormContext();

  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] =
    useDisclosure(false);
  const [switchModalOpened, { open: openSwitchModal, close: closeSwitchModal }] =
    useDisclosure(false);
  const [{ value, valid }, setName] = useValidatedState('', (state) => state !== '');
  const subType = form.getValues().categories[index].subType;
  const selectedSubTypeRef = useRef(subType);

  const onRemoveCategory = () => {
    form.removeListItem('categories', index);
    onClose();
  };

  const onSwitchCategory = useCallback(() => {
    form.setFieldValue(`categories.${index}.subType`, selectedSubTypeRef.current);
    form.setFieldValue(`categories.${index}.data`, []);
    closeSwitchModal();
  }, [index]);

  // Only set the value to the form when the value is valid
  useEffect(() => {
    if (value) {
      form.setFieldValue(`categories.${index}.name`, value);
    }
  }, [value]);

  // set the value of the name input when the index changes
  useEffect(() => {
    setName(form.getValues().categories[index].name || '');
  }, [index]);

  return (
    <>
      <Transition
        mounted={opened}
        transition={scaleX}
        duration={100}
        timingFunction='ease'
        keepMounted
      >
        {(transitionStyle) => (
          <Paper
            pos='absolute'
            top={0}
            left={0}
            right={0}
            p='md'
            style={{ ...transitionStyle, zIndex: 1 }}
          >
            <Group justify='space-between'>
              <Flex
                align='center'
                justify='center'
                gap={rem(4)}
                onClick={onClose}
                className={clsx('cursor-pointer', {
                  disabled: !isEmpty(form.errors) || !valid,
                })}
              >
                <IconArrowLeft size={20} color={theme.colors.decaBlue[5]} />
                <Text fw={500} c='decaBlue.5'>
                  {t('back')}
                </Text>
              </Flex>

              <IconTrash
                size={20}
                color={theme.colors.decaRed[5]}
                className='cursor-pointer'
                onClick={openDeleteModal}
              />
            </Group>

            <Stack gap={rem(16)} mt={rem(16)} h={`calc(100% - ${rem(20)})`}>
              <TextInput
                size='md'
                placeholder={t('categoryName')}
                label={t('categoryName')}
                withAsterisk
                error={!valid && t('requiredError', { ns: 'common' })}
                maxLength={20}
                classNames={{
                  label: classes.inputLabel,
                }}
                value={value}
                onChange={(event) => setName(event.currentTarget.value)}
              />
              <Textarea
                placeholder={t('categoryDescription')}
                label={t('categoryDescription')}
                minRows={4}
                size='md'
                maxLength={100}
                classNames={{
                  label: classes.inputLabel,
                }}
                autosize
                resize='vertical'
                key={form.key(`categories.${index}.description`)}
                {...form.getInputProps(`categories.${index}.description`)}
              />

              <Group gap={rem(12)}>
                <Text fw={500}>{t('inThisCategoryShow')}</Text>
                <Radio.Group
                  key={form.key(`categories.${index}.subType`)}
                  onChange={(value) => {
                    selectedSubTypeRef.current = value;
                    openSwitchModal();
                  }}
                  value={subType}
                >
                  <Group>
                    <Radio value={ArticleType.Article} fz={rem(14)} label={t('articles')} />
                    <Radio value={ArticleType.Category} fz={rem(14)} label={t('subCategory')} />
                  </Group>
                </Radio.Group>
              </Group>

              {subType === ArticleType.Article ? (
                <Articles index={index} />
              ) : (
                <SubCategories index={index} />
              )}
            </Stack>
          </Paper>
        )}
      </Transition>

      <DangerActionModal
        title={t('deleteCategory')}
        description={t('deleteCategoryDescription')}
        opened={deleteModalOpened}
        onClose={closeDeleteModal}
        onDelete={onRemoveCategory}
      />

      <DangerActionModal
        title={
          selectedSubTypeRef.current === ArticleType.Article
            ? t('switchToArticles')
            : t('switchToSubcategory')
        }
        description={
          selectedSubTypeRef.current === ArticleType.Article
            ? t('switchToArticlesDescription')
            : t('switchToSubcategoryDescription')
        }
        opened={switchModalOpened}
        onClose={closeSwitchModal}
        onDelete={onSwitchCategory}
        deleteText={
          selectedSubTypeRef.current === ArticleType.Article
            ? t('showArticles')
            : t('showSubcategory')
        }
      />
    </>
  );
});

CategoryItemDetail.displayName = 'CategoryItemDetail';

export default CategoryItemDetail;
