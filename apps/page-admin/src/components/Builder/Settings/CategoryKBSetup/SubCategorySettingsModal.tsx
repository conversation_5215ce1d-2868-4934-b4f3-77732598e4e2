import { DraggableItem } from '@/components/Common/DraggableItem';
import { useFormContext } from '@/contexts';
import type { KBTreeNode } from '@/utils/KBtreeHelper';
import {
  Divider,
  Flex,
  Group,
  Modal,
  ScrollArea,
  Stack,
  Text,
  TextInput,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { isNotEmpty, useField } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import { DecaButton } from '@resola-ai/ui';
import { IconSettings } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import ArticleItem from './ArticleItem';
import classes from './CategoryKBSetup.module.css';
import DangerActionModal from './DangerActionModal';

interface SubCategorySettingsModalProps {
  categoryIndex: number;
  subCategoryIndex: number;
  opened: boolean;
  onClose: () => void;
  articles: KBTreeNode[];
  onOpenKBModal: () => void;
  onRemoveArticle: (index: number) => void;
  onMoveItem: (dragIndex: number, hoverIndex: number) => void;
}

const SubCategorySettingsModal = ({
  categoryIndex,
  subCategoryIndex,
  opened,
  onClose,
  articles,
  onOpenKBModal,
  onRemoveArticle,
  onMoveItem,
}: SubCategorySettingsModalProps) => {
  const { t } = useTranslate(['builder', 'common']);
  const theme = useMantineTheme();
  const form = useFormContext();
  const field = useField({
    validateOnChange: true,
    validate: isNotEmpty(t('requiredError', { ns: 'common' })),
    initialValue:
      (form.getValues().categories[categoryIndex]?.data[subCategoryIndex]?.name as string) || '',
  });

  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] =
    useDisclosure(false);

  const handleSave = useCallback(() => {
    // Get current form values and create a deep clone to avoid mutating read-only objects
    const currentValues = form.getValues();
    const updatedCategories = structuredClone(currentValues.categories);

    // Update the subcategory name and articles data in the cloned structure
    updatedCategories[categoryIndex].data[subCategoryIndex].name = field.getValue();
    updatedCategories[categoryIndex].data[subCategoryIndex].data = articles;

    // Set the entire categories array back to the form
    form.setFieldValue('categories', updatedCategories);

    onClose();
  }, [categoryIndex, subCategoryIndex, articles, field, form, onClose]);

  const onRemoveSubcategory = useCallback(() => {
    form.removeListItem(`categories.${categoryIndex}.data`, subCategoryIndex);
    onClose();
  }, [categoryIndex, subCategoryIndex]);

  return (
    <>
      <Modal
        opened={opened}
        size='lg'
        onClose={onClose}
        title={t('subCategorySettingsTitle')}
        styles={{
          header: {
            borderBottom: `1px solid ${theme.colors.decaLight[2]}`,
          },
        }}
        centered
      >
        <ScrollArea h={rem(500)} type='auto'>
          <Stack gap={rem(20)} mt={rem(16)}>
            <TextInput
              label={t('subCategoryName')}
              classNames={{
                label: classes.inputLabel,
              }}
              withAsterisk
              {...field.getInputProps()}
            />

            <Group justify='space-between'>
              <Text c={'decaGrey.9'}>{t('articlesCount', { count: articles.length })}</Text>
              <DecaButton variant='neutral' size='md' onClick={onOpenKBModal}>
                <IconSettings size={20} color={theme.colors.decaGrey[6]} />
                {t('manageArticles')}
              </DecaButton>
            </Group>

            <Stack>
              {articles.map((article, index) => (
                <DraggableItem
                  key={index}
                  id={article.value}
                  index={index}
                  type='ArticleItem'
                  onMove={onMoveItem}
                >
                  <ArticleItem article={article} onRemove={() => onRemoveArticle(index)} />
                </DraggableItem>
              ))}
            </Stack>
          </Stack>
        </ScrollArea>

        <Divider sx={{ borderColor: theme.colors.decaLight[2] }} mx={rem(-16)} mt={rem(20)} />

        <Flex justify='space-between' px={rem(8)} py={rem(16)} pb={0} gap={rem(16)}>
          <DecaButton variant='custom' color={'decaRed.5'} size='md' onClick={openDeleteModal}>
            {t('delete')}
          </DecaButton>
          <Group>
            <DecaButton variant='neutral' size='md' onClick={onClose}>
              {t('cancel')}
            </DecaButton>
            <DecaButton
              variant='primary'
              size='md'
              onClick={handleSave}
              disabled={field.getValue().trim() === ''}
            >
              {t('save')}
            </DecaButton>
          </Group>
        </Flex>
      </Modal>

      <DangerActionModal
        title={t('deleteSubcategory')}
        description={t('deleteSubcategoryDescription')}
        opened={deleteModalOpened}
        onClose={closeDeleteModal}
        onDelete={onRemoveSubcategory}
      />
    </>
  );
};

export default SubCategorySettingsModal;
