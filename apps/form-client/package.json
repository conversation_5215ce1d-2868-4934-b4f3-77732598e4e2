{"name": "form-client", "private": true, "version": "1.16.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "biome check .", "prepare:web:pr": "node ../../packages/scripts/src/preparePreviewEnv.js", "format": "biome format --write .", "lint-staged-check": "lint-staged", "lint:fix": "biome check --write .", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "release": "standard-version -t form-admin@", "release:minor": "standard-version -t form-admin@ --release-as minor", "release:patch": "standard-version -t form-admin@ --release-as patch", "release:major": "standard-version -t form-admin@ --release-as major", "coverage": "vitest --run --coverage"}, "dependencies": {"@emotion/cache": "11.13.1", "@emotion/react": "^11.13.0", "@emotion/serialize": "^1.3.2", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@emotion/utils": "^1.4.1", "@line/liff": "^2.24.0", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/dropzone": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/notifications": "7.17.7", "@resola-ai/models": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@tabler/icons-react": "3.17.0", "@tolgee/format-icu": "^5.32.0", "@tolgee/react": "^5.30.0", "axios": "^1.8.2", "dayjs": "^1.11.10", "dotenv": "16.3.1", "i18next": "23.10.0", "isomorphic-dompurify": "2.21.0", "lodash": "^4.17.21", "next": "^14.2.26", "next-i18next": "^15.3.0", "prettier": "^3.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "14.0.1", "standard-version": "^9.5.0", "swr": "^2.2.5", "type-fest": "3.7.0", "ulid": "^2.3.0", "zod": "^3.24.1"}, "devDependencies": {"@biomejs/biome": "^1.5.3", "@resola-ai/biome-config": "workspace:*", "@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@sentry/react": "^7.81.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/lodash": "^4.14.199", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "^2.1.9", "husky": "^8.0.3", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "lint-staged": "^15.5.0", "postcss-preset-mantine": "^1.12.3", "typescript": "5.6.3", "vitest": "2.1.9"}, "lint-staged": {"*.{js,ts,tsx,jsx}": ["biome format --write", "biome check --write"]}}