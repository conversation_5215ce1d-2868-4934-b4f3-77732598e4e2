import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import NoInternetConnection from './NoInternetConnection';

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, width, height, onLoad }: any) => (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      onLoad={onLoad}
      data-testid='no-connection-image'
    />
  ),
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      const translations = {
        noInternetConnectionTitle: 'No Internet Connection',
        noInternetConnectionDescription: 'Please check your connection and try again.',
      };
      return translations[key] || key;
    },
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
}));

describe('NoInternetConnection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders no internet connection message', () => {
    renderWithProviders(<NoInternetConnection />);

    expect(screen.getByText('No Internet Connection')).toBeInTheDocument();
    expect(screen.getByText('Please check your connection and try again.')).toBeInTheDocument();
  });

  it('renders no connection image', () => {
    renderWithProviders(<NoInternetConnection />);

    const image = screen.getByTestId('no-connection-image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', '/images/no_connection.svg');
    expect(image).toHaveAttribute('alt', '404');
    expect(image).toHaveAttribute('width', '100');
    expect(image).toHaveAttribute('height', '100');
  });

  it('shows content after image loads', () => {
    renderWithProviders(<NoInternetConnection />);

    // Initially hidden
    const wrapper =
      screen.getByTestId('no-connection-image').parentElement?.parentElement?.parentElement
        ?.parentElement;
    expect(wrapper).toHaveStyle({ visibility: 'hidden' });

    // Simulate image load
    const image = screen.getByTestId('no-connection-image');
    fireEvent.load(image);

    // Should be visible after load
    expect(wrapper).toHaveStyle({ visibility: 'visible' });
  });

  it('renders with proper styling classes', () => {
    renderWithProviders(<NoInternetConnection />);

    // Trigger image load to make content visible
    const image = screen.getByTestId('no-connection-image');
    fireEvent.load(image);

    // Check that the component renders without errors
    expect(screen.getByText('No Internet Connection')).toBeInTheDocument();
  });

  it('renders content in a container', () => {
    renderWithProviders(<NoInternetConnection />);

    const container = screen.getByTestId('no-connection-image').closest('.mantine-Container-root');
    expect(container).toBeInTheDocument();
  });

  it('renders content in a flex layout', () => {
    renderWithProviders(<NoInternetConnection />);

    const flexContainer = screen.getByTestId('no-connection-image').closest('.mantine-Flex-root');
    expect(flexContainer).toBeInTheDocument();
  });

  it('renders title with proper heading level', () => {
    renderWithProviders(<NoInternetConnection />);

    // Trigger image load to make content visible
    const image = screen.getByTestId('no-connection-image');
    fireEvent.load(image);

    const title = screen.getByRole('heading', { level: 4 });
    expect(title).toBeInTheDocument();
    expect(title).toHaveTextContent('No Internet Connection');
  });

  it('renders description text', () => {
    renderWithProviders(<NoInternetConnection />);

    const description = screen.getByText('Please check your connection and try again.');
    expect(description).toBeInTheDocument();
  });

  it('handles image load event', () => {
    renderWithProviders(<NoInternetConnection />);

    const image = screen.getByTestId('no-connection-image');

    // Initially the wrapper should be hidden
    const wrapper = image.parentElement?.parentElement?.parentElement?.parentElement;
    expect(wrapper).toHaveStyle({ visibility: 'hidden' });

    // Trigger image load
    fireEvent.load(image);

    // After load, wrapper should be visible
    expect(wrapper).toHaveStyle({ visibility: 'visible' });
  });

  it('renders with proper content structure', () => {
    renderWithProviders(<NoInternetConnection />);

    // Trigger image load to make content visible
    const image = screen.getByTestId('no-connection-image');
    fireEvent.load(image);

    // Check that all main elements are present
    expect(screen.getByTestId('no-connection-image')).toBeInTheDocument();
    expect(screen.getByRole('heading', { level: 4 })).toBeInTheDocument();
    expect(screen.getByText('Please check your connection and try again.')).toBeInTheDocument();
  });

  it('renders with proper accessibility attributes', () => {
    renderWithProviders(<NoInternetConnection />);

    const image = screen.getByTestId('no-connection-image');
    expect(image).toHaveAttribute('alt', '404');
  });

  it('renders with proper responsive design', () => {
    renderWithProviders(<NoInternetConnection />);

    const content = screen.getByTestId('no-connection-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });

  it('renders with proper theme colors', () => {
    renderWithProviders(<NoInternetConnection />);

    // The component should render without errors
    expect(screen.getByText('No Internet Connection')).toBeInTheDocument();
  });

  it('renders with proper spacing and layout', () => {
    renderWithProviders(<NoInternetConnection />);

    const content = screen.getByTestId('no-connection-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });

  it('renders with proper border and border radius', () => {
    renderWithProviders(<NoInternetConnection />);

    const content = screen.getByTestId('no-connection-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });

  it('renders with proper background colors', () => {
    renderWithProviders(<NoInternetConnection />);

    // The component should render without errors
    expect(screen.getByText('No Internet Connection')).toBeInTheDocument();
  });

  it('renders with proper text styling', () => {
    renderWithProviders(<NoInternetConnection />);

    // Trigger image load to make content visible
    const image = screen.getByTestId('no-connection-image');
    fireEvent.load(image);

    const title = screen.getByRole('heading', { level: 4 });
    const description = screen.getByText('Please check your connection and try again.');

    expect(title).toBeInTheDocument();
    expect(description).toBeInTheDocument();
  });

  it('renders with proper image dimensions', () => {
    renderWithProviders(<NoInternetConnection />);

    const image = screen.getByTestId('no-connection-image');
    expect(image).toHaveAttribute('width', '100');
    expect(image).toHaveAttribute('height', '100');
  });

  it('renders with proper content alignment', () => {
    renderWithProviders(<NoInternetConnection />);

    const content = screen.getByTestId('no-connection-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });

  it('renders with proper content sizing', () => {
    renderWithProviders(<NoInternetConnection />);

    const content = screen.getByTestId('no-connection-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });
});
