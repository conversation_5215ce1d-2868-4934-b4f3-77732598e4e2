import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import SuccessMessage from './SuccessMessage';

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, width, height }: any) => (
    <img src={src} alt={alt} width={width} height={height} />
  ),
}));

// Default form settings for testing
const defaultFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test form description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: 'default',
    },
    formFieldStyle: {},
    headingStyle: {},
    paragraphStyle: {},
    headerStyle: {},
    footerStyle: {},
  },
  setting: {
    enableProgressBar: true,
    enablePageNavigation: true,
    submission: {
      mode: 'message',
      message: 'Thank you for your submission!',
      caption: 'Your form has been submitted successfully.',
      redirectUrl: '',
      button: 'Continue',
      enableBranding: true,
    },
  },
  screenshot: {
    preview: 'https://example.com/preview.jpg',
    thumbnail: 'https://example.com/thumbnail.jpg',
  },
  metadata: {},
  status: 'active',
  permissions: {},
  urls: {},
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  createdBy: 'test-user',
  updatedBy: 'test-user',
  version: 1,
  isTemplate: false,
  templateId: null,
  tags: [],
  category: 'general',
  language: 'en',
  timezone: 'UTC',
  expiredAt: null,
  isFavorited: false,
  isPinned: false,
  responses: [],
  startAt: null,
} as any;

describe('SuccessMessage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders success message with default content', () => {
    renderWithProviders(<SuccessMessage formSettings={defaultFormSettings} />);

    expect(screen.getByText('Thank you for your submission!')).toBeInTheDocument();
    expect(screen.getByText('Your form has been submitted successfully.')).toBeInTheDocument();
  });

  it('renders success message with custom title', () => {
    const customSettings = {
      ...defaultFormSettings,
      setting: {
        ...defaultFormSettings.setting,
        submission: {
          ...defaultFormSettings.setting.submission,
          message: 'Custom Success Title',
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    expect(screen.getByText('Custom Success Title')).toBeInTheDocument();
  });

  it('renders success message with custom description', () => {
    const customSettings = {
      ...defaultFormSettings,
      setting: {
        ...defaultFormSettings.setting,
        submission: {
          ...defaultFormSettings.setting.submission,
          caption: 'Custom success description',
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    expect(screen.getByText('Custom success description')).toBeInTheDocument();
  });

  it('renders redirect button when mode is message_redirect and redirectUrl is provided', () => {
    const customSettings = {
      ...defaultFormSettings,
      setting: {
        ...defaultFormSettings.setting,
        submission: {
          ...defaultFormSettings.setting.submission,
          mode: 'message_redirect',
          redirectUrl: 'https://example.com',
          button: 'Go to Website',
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    const button = screen.getByRole('link', { name: 'Go to Website' });
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('href', 'https://example.com');
  });

  it('does not render redirect button when mode is not message_redirect', () => {
    const customSettings = {
      ...defaultFormSettings,
      setting: {
        ...defaultFormSettings.setting,
        submission: {
          ...defaultFormSettings.setting.submission,
          mode: 'message',
          redirectUrl: 'https://example.com',
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    expect(screen.queryByRole('link')).not.toBeInTheDocument();
  });

  it('does not render redirect button when redirectUrl is empty', () => {
    const customSettings = {
      ...defaultFormSettings,
      setting: {
        ...defaultFormSettings.setting,
        submission: {
          ...defaultFormSettings.setting.submission,
          mode: 'message_redirect',
          redirectUrl: '',
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    expect(screen.queryByRole('link')).not.toBeInTheDocument();
  });

  it('renders branding when enableBranding is true', () => {
    const customSettings = {
      ...defaultFormSettings,
      setting: {
        ...defaultFormSettings.setting,
        submission: {
          ...defaultFormSettings.setting.submission,
          enableBranding: true,
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    const brandingImages = screen.getAllByAltText('success');
    const brandingImage = brandingImages.find(
      (img) => img.getAttribute('src') === '/images/DECA_trademark.svg'
    );
    expect(brandingImage).toBeInTheDocument();
    expect(brandingImage).toHaveAttribute('src', '/images/DECA_trademark.svg');
  });

  it('does not render branding when enableBranding is false', () => {
    const customSettings = {
      ...defaultFormSettings,
      setting: {
        ...defaultFormSettings.setting,
        submission: {
          ...defaultFormSettings.setting.submission,
          enableBranding: false,
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    const brandingImages = screen.getAllByAltText('success');
    expect(brandingImages).toHaveLength(1); // Only the success image, not the branding
  });

  it('renders success image', () => {
    renderWithProviders(<SuccessMessage formSettings={defaultFormSettings} />);

    const successImages = screen.getAllByAltText('success');
    const successImage = successImages.find(
      (img) => img.getAttribute('src') === '/images/success.svg'
    );
    expect(successImage).toBeInTheDocument();
    expect(successImage).toHaveAttribute('src', '/images/success.svg');
  });

  it('renders header when headerStyle is provided', () => {
    const customSettings = {
      ...defaultFormSettings,
      appearance: {
        ...defaultFormSettings.appearance,
        headerStyle: {
          position: 'top',
          logoImage: 'https://example.com/logo.png',
          logoSize: 'medium',
          logoAlign: 'left',
          isUsingText: false,
          text: '',
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    // Header should be rendered (we can't easily test the Header component directly)
    expect(screen.getByText('Thank you for your submission!')).toBeInTheDocument();
  });

  it('renders footer when footerStyle is provided', () => {
    const customSettings = {
      ...defaultFormSettings,
      appearance: {
        ...defaultFormSettings.appearance,
        footerStyle: {
          position: 'bottom',
          logoImage: 'https://example.com/logo.png',
          logoSize: 'medium',
          logoAlign: 'left',
          isUsingText: false,
          text: '',
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    // Footer should be rendered (we can't easily test the Footer component directly)
    expect(screen.getByText('Thank you for your submission!')).toBeInTheDocument();
  });

  it('renders with custom form name and description', () => {
    const customSettings = {
      ...defaultFormSettings,
      name: 'Custom Form Name',
      description: 'Custom form description',
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    expect(screen.getByText('Thank you for your submission!')).toBeInTheDocument();
  });

  it('renders with custom screenshot', () => {
    const customSettings = {
      ...defaultFormSettings,
      screenshot: {
        preview: 'https://example.com/custom-preview.jpg',
        thumbnail: 'https://example.com/custom-thumbnail.jpg',
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    expect(screen.getByText('Thank you for your submission!')).toBeInTheDocument();
  });

  it('renders with different submission modes', () => {
    const modes = ['message', 'message_redirect', 'redirect'];

    modes.forEach((mode) => {
      const customSettings = {
        ...defaultFormSettings,
        setting: {
          ...defaultFormSettings.setting,
          submission: {
            ...defaultFormSettings.setting.submission,
            mode,
          },
        },
      };

      const { unmount } = renderWithProviders(<SuccessMessage formSettings={customSettings} />);

      expect(screen.getByText('Thank you for your submission!')).toBeInTheDocument();

      unmount();
    });
  });

  it('renders with custom button text', () => {
    const customSettings = {
      ...defaultFormSettings,
      setting: {
        ...defaultFormSettings.setting,
        submission: {
          ...defaultFormSettings.setting.submission,
          mode: 'message_redirect',
          redirectUrl: 'https://example.com',
          button: 'Custom Button Text',
        },
      },
    };

    renderWithProviders(<SuccessMessage formSettings={customSettings} />);

    const button = screen.getByRole('link', { name: 'Custom Button Text' });
    expect(button).toBeInTheDocument();
  });
});
