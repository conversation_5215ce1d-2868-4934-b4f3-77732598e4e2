import { MantineProvider } from '@mantine/core';
import { render } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Meta from './Meta';

// Mock Next.js Head component
vi.mock('next/head', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='head'>{children}</div>
  ),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<MantineProvider>{component}</MantineProvider>);
};

describe('Meta', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders meta component with required props', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
      />
    );

    expect(container.querySelector('[data-testid="head"]')).toBeInTheDocument();
  });

  it('renders favicon link', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
      />
    );

    const favicon = container.querySelector('link[rel="icon"]');
    expect(favicon).toHaveAttribute('href', '/images/favicon.ico');
  });

  it('renders title tag', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
      />
    );

    const title = container.querySelector('title');
    expect(title).toHaveTextContent('Test Form Title');
  });

  it('renders description meta tag', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
      />
    );

    const description = container.querySelector('meta[name="description"]');
    expect(description).toHaveAttribute('content', 'Test form description');
  });

  it('renders viewport meta tag', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
      />
    );

    const viewport = container.querySelector('meta[name="viewport"]');
    expect(viewport).toHaveAttribute(
      'content',
      'width=device-width, initial-scale=1.0, user-scalable=no'
    );
  });

  it('renders Open Graph meta tags', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
        url='https://example.com/form'
      />
    );

    const ogType = container.querySelector('meta[property="og:type"]');
    const ogUrl = container.querySelector('meta[property="og:url"]');
    const ogTitle = container.querySelector('meta[property="og:title"]');
    const ogDescription = container.querySelector('meta[property="og:description"]');
    const ogImage = container.querySelector('meta[property="og:image"]');

    expect(ogType).toHaveAttribute('content', 'website');
    expect(ogUrl).toHaveAttribute('content', 'https://example.com/form');
    expect(ogTitle).toHaveAttribute('content', 'Test Form Title');
    expect(ogDescription).toHaveAttribute('content', 'Test form description');
    expect(ogImage).toHaveAttribute('content', 'https://example.com/image.jpg');
  });

  it('renders Twitter meta tags', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
        url='https://example.com/form'
      />
    );

    const twitterCard = container.querySelector('meta[name="twitter:card"]');
    const twitterUrl = container.querySelector('meta[name="twitter:url"]');
    const twitterTitle = container.querySelector('meta[name="twitter:title"]');
    const twitterDescription = container.querySelector('meta[name="twitter:description"]');
    const twitterImage = container.querySelector('meta[name="twitter:image"]');

    expect(twitterCard).toHaveAttribute('content', 'https://example.com/image.jpg');
    expect(twitterUrl).toHaveAttribute('content', 'https://example.com/form');
    expect(twitterTitle).toHaveAttribute('content', 'Test Form Title');
    expect(twitterDescription).toHaveAttribute('content', 'Test form description');
    expect(twitterImage).toHaveAttribute('content', 'https://example.com/image.jpg');
  });

  it('uses default url when not provided', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
      />
    );

    const ogUrl = container.querySelector('meta[property="og:url"]');
    const twitterUrl = container.querySelector('meta[name="twitter:url"]');

    expect(ogUrl).toHaveAttribute('content', '');
    expect(twitterUrl).toHaveAttribute('content', '');
  });

  it('renders with custom url', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
        url='https://custom-example.com/form'
      />
    );

    const ogUrl = container.querySelector('meta[property="og:url"]');
    const twitterUrl = container.querySelector('meta[name="twitter:url"]');

    expect(ogUrl).toHaveAttribute('content', 'https://custom-example.com/form');
    expect(twitterUrl).toHaveAttribute('content', 'https://custom-example.com/form');
  });

  it('renders with custom image', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://custom-example.com/custom-image.jpg'
      />
    );

    const ogImage = container.querySelector('meta[property="og:image"]');
    const twitterImage = container.querySelector('meta[name="twitter:image"]');
    const twitterCard = container.querySelector('meta[name="twitter:card"]');

    expect(ogImage).toHaveAttribute('content', 'https://custom-example.com/custom-image.jpg');
    expect(twitterImage).toHaveAttribute('content', 'https://custom-example.com/custom-image.jpg');
    expect(twitterCard).toHaveAttribute('content', 'https://custom-example.com/custom-image.jpg');
  });

  it('renders with custom title', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Custom Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
      />
    );

    const title = container.querySelector('title');
    const ogTitle = container.querySelector('meta[property="og:title"]');
    const twitterTitle = container.querySelector('meta[name="twitter:title"]');

    expect(title).toHaveTextContent('Custom Form Title');
    expect(ogTitle).toHaveAttribute('content', 'Custom Form Title');
    expect(twitterTitle).toHaveAttribute('content', 'Custom Form Title');
  });

  it('renders with custom description', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Custom form description with more details'
        image='https://example.com/image.jpg'
      />
    );

    const description = container.querySelector('meta[name="description"]');
    const ogDescription = container.querySelector('meta[property="og:description"]');
    const twitterDescription = container.querySelector('meta[name="twitter:description"]');

    expect(description).toHaveAttribute('content', 'Custom form description with more details');
    expect(ogDescription).toHaveAttribute('content', 'Custom form description with more details');
    expect(twitterDescription).toHaveAttribute(
      'content',
      'Custom form description with more details'
    );
  });

  it('renders all required meta tags', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
        url='https://example.com/form'
      />
    );

    // Check that all expected meta tags are present
    const expectedMetaTags = [
      'link[rel="icon"]',
      'title',
      'meta[name="description"]',
      'meta[name="viewport"]',
      'meta[property="og:type"]',
      'meta[property="og:url"]',
      'meta[property="og:title"]',
      'meta[property="og:description"]',
      'meta[property="og:image"]',
      'meta[name="twitter:card"]',
      'meta[name="twitter:url"]',
      'meta[name="twitter:title"]',
      'meta[name="twitter:description"]',
      'meta[name="twitter:image"]',
    ];

    expectedMetaTags.forEach((selector) => {
      expect(container.querySelector(selector)).toBeInTheDocument();
    });
  });

  it('renders with empty url', () => {
    const { container } = renderWithProvider(
      <Meta
        title='Test Form Title'
        description='Test form description'
        image='https://example.com/image.jpg'
        url=''
      />
    );

    const ogUrl = container.querySelector('meta[property="og:url"]');
    const twitterUrl = container.querySelector('meta[name="twitter:url"]');

    expect(ogUrl).toHaveAttribute('content', '');
    expect(twitterUrl).toHaveAttribute('content', '');
  });

  it('renders with long title and description', () => {
    const longTitle =
      'This is a very long form title that might exceed normal length limits and should still render properly';
    const longDescription =
      'This is a very long form description that contains a lot of details about the form and what it does and how it works and all the features it provides';

    const { container } = renderWithProvider(
      <Meta title={longTitle} description={longDescription} image='https://example.com/image.jpg' />
    );

    const title = container.querySelector('title');
    const description = container.querySelector('meta[name="description"]');

    expect(title).toHaveTextContent(longTitle);
    expect(description).toHaveAttribute('content', longDescription);
  });
});
