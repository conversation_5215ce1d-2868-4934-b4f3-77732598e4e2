import { fireEvent, render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { DecaButton } from './index';

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <MantineEmotionProvider>
      <MantineProvider
        theme={{
          colors: {
            decaLight: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaNavy: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaDark: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
          },
          breakpoints: {
            sm: '768px',
          },
        }}
      >
        {component}
      </MantineProvider>
    </MantineEmotionProvider>
  );
};

describe('DecaButton', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders button with default props', () => {
    renderWithProvider(<DecaButton>Click me</DecaButton>);

    const button = screen.getByRole('button', { name: 'Click me' });
    expect(button).toBeInTheDocument();
  });

  it('renders button with primary variant', () => {
    renderWithProvider(<DecaButton variant='primary'>Primary Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Primary Button' });
    expect(button).toBeInTheDocument();
  });

  it('renders button with secondary variant', () => {
    renderWithProvider(<DecaButton variant='secondary'>Secondary Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Secondary Button' });
    expect(button).toBeInTheDocument();
  });

  it('renders button with neutral variant', () => {
    renderWithProvider(<DecaButton variant='neutral'>Neutral Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Neutral Button' });
    expect(button).toBeInTheDocument();
  });

  it('renders button with negative variant', () => {
    renderWithProvider(<DecaButton variant='negative'>Negative Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Negative Button' });
    expect(button).toBeInTheDocument();
  });

  it('renders button with different sizes', () => {
    const { unmount } = renderWithProvider(<DecaButton size='sm'>Small Button</DecaButton>);
    expect(screen.getByRole('button', { name: 'Small Button' })).toBeInTheDocument();
    unmount();

    renderWithProvider(<DecaButton size='md'>Medium Button</DecaButton>);
    expect(screen.getByRole('button', { name: 'Medium Button' })).toBeInTheDocument();
    unmount();

    renderWithProvider(<DecaButton size='lg'>Large Button</DecaButton>);
    expect(screen.getByRole('button', { name: 'Large Button' })).toBeInTheDocument();
  });

  it('renders disabled button', () => {
    renderWithProvider(<DecaButton disabled>Disabled Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Disabled Button' });
    expect(button).toBeDisabled();
  });

  it('renders loading button', () => {
    renderWithProvider(<DecaButton loading>Loading Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Loading Button' });
    expect(button).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    renderWithProvider(<DecaButton onClick={handleClick}>Clickable Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Clickable Button' });
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('does not handle click events when disabled', () => {
    const handleClick = vi.fn();
    renderWithProvider(
      <DecaButton disabled onClick={handleClick}>
        Disabled Button
      </DecaButton>
    );

    const button = screen.getByRole('button', { name: 'Disabled Button' });
    fireEvent.click(button);

    expect(handleClick).not.toHaveBeenCalled();
  });

  it('renders submit button', () => {
    renderWithProvider(<DecaButton type='submit'>Submit Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Submit Button' });
    expect(button).toHaveAttribute('type', 'submit');
  });

  it('renders reset button', () => {
    renderWithProvider(<DecaButton type='reset'>Reset Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Reset Button' });
    expect(button).toHaveAttribute('type', 'reset');
  });

  it('applies custom className', () => {
    renderWithProvider(<DecaButton className='custom-class'>Custom Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Custom Button' });
    expect(button).toHaveClass('custom-class');
  });

  it('renders button with icon', () => {
    const Icon = () => <span data-testid='icon'>🚀</span>;
    renderWithProvider(<DecaButton leftSection={<Icon />}>Button with Icon</DecaButton>);

    expect(screen.getByTestId('icon')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '🚀 Button with Icon' })).toBeInTheDocument();
  });

  it('renders button with right section', () => {
    const RightSection = () => <span data-testid='right-section'>→</span>;
    renderWithProvider(
      <DecaButton rightSection={<RightSection />}>Button with Right Section</DecaButton>
    );

    expect(screen.getByTestId('right-section')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Button with Right Section →' })).toBeInTheDocument();
  });

  it('renders full width button', () => {
    renderWithProvider(<DecaButton fullWidth>Full Width Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Full Width Button' });
    expect(button).toBeInTheDocument();
  });

  it('renders button with custom color', () => {
    renderWithProvider(<DecaButton color='red'>Red Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Red Button' });
    expect(button).toBeInTheDocument();
  });

  it('renders button with radius', () => {
    renderWithProvider(<DecaButton radius='xl'>Rounded Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Rounded Button' });
    expect(button).toBeInTheDocument();
  });

  it('renders button with custom styles', () => {
    const customStyles = { backgroundColor: 'purple', color: 'white' };
    renderWithProvider(<DecaButton style={customStyles}>Styled Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Styled Button' });
    expect(button).toHaveStyle({ color: 'rgb(255, 255, 255)' });
  });

  it('forwards ref correctly', () => {
    const ref = vi.fn();
    renderWithProvider(<DecaButton ref={ref}>Ref Button</DecaButton>);

    expect(ref).toHaveBeenCalled();
  });

  it('renders button with aria-label', () => {
    renderWithProvider(<DecaButton aria-label='Accessible button'>Button</DecaButton>);

    const button = screen.getByRole('button', { name: 'Accessible button' });
    expect(button).toHaveAttribute('aria-label', 'Accessible button');
  });

  it('renders button with data attributes', () => {
    renderWithProvider(<DecaButton data-testid='test-button'>Test Button</DecaButton>);

    const button = screen.getByTestId('test-button');
    expect(button).toBeInTheDocument();
  });
});
