import { AppearanceSettingsLogoSize } from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import NotOpenMessage from './NotOpenMessage';

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, width, height, className, onLoad }: any) => (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      onLoad={onLoad}
      data-testid='edge-case-image'
    />
  ),
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTolgee: () => ({
    getLanguage: vi.fn(() => 'en'),
  }),
}));

// Mock Meta component
vi.mock('../Meta/Meta', () => ({
  default: ({ title, description, image }: any) => (
    <div data-testid='meta-component'>
      <span data-testid='meta-title'>{title}</span>
      <span data-testid='meta-description'>{description}</span>
      <span data-testid='meta-image'>{image}</span>
    </div>
  ),
}));

// Mock dayjs
vi.mock('dayjs', () => {
  const mockDayjs = vi.fn(() => ({
    local: () => ({
      format: vi.fn(() => '14:30 15/12/2024'),
    }),
  }));
  (mockDayjs as any).extend = vi.fn();
  return { default: mockDayjs };
});

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <MantineEmotionProvider>
      <MantineProvider
        theme={{
          colors: {
            decaLight: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaNavy: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaDark: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
          },
          breakpoints: {
            sm: '768px',
          },
        }}
      >
        {component}
      </MantineProvider>
    </MantineEmotionProvider>
  );
};

describe('NotOpenMessage', () => {
  const mockFormSettings = {
    id: 'test-form-id',
    name: 'Test Form',
    description: 'Test form description',
    startAt: '2024-12-15T14:30:00Z',
    expiredAt: '2024-12-31T23:59:59Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    status: 'active',
    permissions: ['read', 'write'],
    isFavorited: false,
    isPinned: false,
    responses: 0,
    tags: [],
    urls: {
      public: '/public-url',
      embed: '/embed-url',
      private: '/private-url',
    },
    metadata: {
      organizationId: 'org-id',
      workspaceId: 'workspace-id',
    },
    appearance: {
      headingStyle: {
        fontFamily: 'Arial',
        fontSize: 16,
        color: '#000000',
      },
      paragraphStyle: {
        fontFamily: 'Arial',
        fontSize: 14,
        color: '#000000',
      },
      buttonStyle: {
        type: 'primary',
        fullWidth: false,
        backgroundColor: '#000000',
        textColor: '#ffffff',
        fontFamily: 'Arial',
        fontSize: 14,
      },
      formFieldStyle: {
        color: {
          placeholder: '#000000',
          question: '#000000',
          answer: '#000000',
          icon: '#000000',
          description: '#000000',
          fieldStroke: '#000000',
          fieldBackGround: '#ffffff',
        },
        fontFamily: {
          placeholder: 'Arial',
          question: 'Arial',
          text: 'Arial',
          answer: 'Arial',
        },
        fontSize: {
          placeholder: '14',
          question: 16,
          text: 14,
          answer: 14,
        },
      },
      defaultSettings: {
        color: '#000000',
        font: 'Arial',
        inputStyle: InputStyle.Classic,
      },
      customize: false,
      headerStyle: {
        position: 'top',
        logoImage: '',
        logoSize: AppearanceSettingsLogoSize.Medium,
        logoAlign: 'left',
        isUsingText: false,
        text: '',
      },
      footerStyle: {
        logoImage: '',
        logoSize: AppearanceSettingsLogoSize.Medium,
        logoAlign: 'left',
        isUsingText: false,
        text: '',
      },
    },
    screenshot: {
      preview: '/preview.jpg',
      thumbnail: '/thumbnail.jpg',
      original: '/original.jpg',
    },
    setting: {
      submission: {
        mode: 'message',
        message: 'Thank you for your submission',
        caption: '',
        button: '',
        redirectUrl: '',
        enableBranding: true,
        limitResponse: false,
        limitNumber: 0,
      },
      notification: {
        isAutoresponse: false,
      },
      behavior: {
        isMultipleResponse: false,
      },
      systemMessage: [
        {
          type: 'not_open',
          heading: {
            en: 'Form opens at @[start_at](start_at)',
            es: 'El formulario se abre en @[start_at](start_at)',
          },
          body: {
            en: 'This form is not yet open for submissions.',
            es: 'Este formulario aún no está abierto para envíos.',
          },
        },
      ],
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders NotOpenMessage with correct props', () => {
    renderWithProvider(<NotOpenMessage formSettings={mockFormSettings as any} />);

    expect(screen.getByTestId('meta-component')).toBeInTheDocument();
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
  });

  it('renders Meta component with correct form settings', () => {
    renderWithProvider(<NotOpenMessage formSettings={mockFormSettings as any} />);

    expect(screen.getByTestId('meta-title')).toHaveTextContent('Test Form');
    expect(screen.getByTestId('meta-description')).toHaveTextContent('Test form description');
    expect(screen.getByTestId('meta-image')).toHaveTextContent('/preview.jpg');
  });

  it('renders EdgeCaseMessage with correct image URL', () => {
    renderWithProvider(<NotOpenMessage formSettings={mockFormSettings as any} />);

    const image = screen.getByTestId('edge-case-image');
    expect(image).toHaveAttribute('src', '/images/not_open.svg');
  });

  it('renders title with formatted start date', () => {
    renderWithProvider(<NotOpenMessage formSettings={mockFormSettings as any} />);

    expect(screen.getByText('Form opens at 14:30 15/12/2024')).toBeInTheDocument();
  });

  it('renders description from form settings', () => {
    renderWithProvider(<NotOpenMessage formSettings={mockFormSettings as any} />);

    expect(screen.getByText('This form is not yet open for submissions.')).toBeInTheDocument();
  });

  it('handles missing system message gracefully', () => {
    const formSettingsWithoutMessage = {
      ...mockFormSettings,
      setting: {
        ...mockFormSettings.setting,
        systemMessage: [],
      },
    };

    renderWithProvider(<NotOpenMessage formSettings={formSettingsWithoutMessage as any} />);

    // Should still render the component without crashing
    expect(screen.getByTestId('meta-component')).toBeInTheDocument();
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
  });

  it('handles missing not_open message type', () => {
    const formSettingsWithWrongType = {
      ...mockFormSettings,
      setting: {
        ...mockFormSettings.setting,
        systemMessage: [
          {
            type: 'expired',
            heading: { en: 'Wrong message' },
            body: { en: 'Wrong description' },
          },
        ],
      },
    };

    renderWithProvider(<NotOpenMessage formSettings={formSettingsWithWrongType as any} />);

    // Should still render the component without crashing
    expect(screen.getByTestId('meta-component')).toBeInTheDocument();
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
  });

  it('handles missing language translations', () => {
    const formSettingsWithMissingTranslations = {
      ...mockFormSettings,
      setting: {
        ...mockFormSettings.setting,
        systemMessage: [
          {
            type: 'not_open',
            heading: { en: 'Form opens at @[start_at](start_at)' },
            body: { en: 'This form is not yet open for submissions.' },
          },
        ],
      },
    };

    renderWithProvider(
      <NotOpenMessage formSettings={formSettingsWithMissingTranslations as any} />
    );

    // Should still render the component without crashing
    expect(screen.getByTestId('meta-component')).toBeInTheDocument();
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
  });

  it('handles missing screenshot properties', () => {
    const formSettingsWithoutScreenshot = {
      ...mockFormSettings,
      screenshot: {},
    };

    renderWithProvider(<NotOpenMessage formSettings={formSettingsWithoutScreenshot as any} />);

    expect(screen.getByTestId('meta-component')).toBeInTheDocument();
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
  });

  it('uses thumbnail when preview is not available', () => {
    const formSettingsWithThumbnailOnly = {
      ...mockFormSettings,
      screenshot: {
        thumbnail: '/thumbnail.jpg',
      },
    };

    renderWithProvider(<NotOpenMessage formSettings={formSettingsWithThumbnailOnly as any} />);

    expect(screen.getByTestId('meta-image')).toHaveTextContent('/thumbnail.jpg');
  });

  it('renders with proper accessibility attributes', () => {
    renderWithProvider(<NotOpenMessage formSettings={mockFormSettings as any} />);

    const image = screen.getByTestId('edge-case-image');
    expect(image).toHaveAttribute('alt', 'Edge case image');
  });

  it('renders with proper content structure', () => {
    renderWithProvider(<NotOpenMessage formSettings={mockFormSettings as any} />);

    // Check that all main elements are present
    expect(screen.getByTestId('meta-component')).toBeInTheDocument();
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
    expect(screen.getByText('Form opens at 14:30 15/12/2024')).toBeInTheDocument();
    expect(screen.getByText('This form is not yet open for submissions.')).toBeInTheDocument();
  });
});
