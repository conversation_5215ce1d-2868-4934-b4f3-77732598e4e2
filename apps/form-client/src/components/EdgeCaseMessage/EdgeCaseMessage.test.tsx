import { <PERSON><PERSON>Provider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { fireEvent, render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import EdgeCaseMessage from './EdgeCaseMessage';

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, width, height, className, onLoad }: any) => (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      onLoad={onLoad}
      data-testid='edge-case-image'
    />
  ),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <MantineEmotionProvider>
      <MantineProvider
        theme={{
          colors: {
            decaLight: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaNavy: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaDark: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
          },
          breakpoints: {
            sm: '768px',
          },
        }}
      >
        {component}
      </MantineProvider>
    </MantineEmotionProvider>
  );
};

describe('EdgeCaseMessage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders edge case message with provided props', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired and is no longer available.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    expect(screen.getByText('Form Expired')).toBeInTheDocument();
    expect(
      screen.getByText('This form has expired and is no longer available.')
    ).toBeInTheDocument();
  });

  it('renders image with correct attributes', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const image = screen.getByTestId('edge-case-image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', '/images/expired.svg');
    expect(image).toHaveAttribute('alt', 'Edge case image');
    expect(image).toHaveAttribute('width', '200');
    expect(image).toHaveAttribute('height', '200');
  });

  it('shows content after image loads', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    // Find the wrapper by looking for the element with visibility style
    const wrapper = screen.getByTestId('edge-case-image').closest('[style*="visibility"]');
    expect(wrapper).not.toBeNull();

    // Initially hidden
    expect(wrapper).toHaveStyle({ visibility: 'hidden' });

    // Simulate image load
    const image = screen.getByTestId('edge-case-image');
    fireEvent.load(image);

    // Should be visible after load
    expect(wrapper).toHaveStyle({ visibility: 'visible' });
  });

  it('renders with custom title and description', () => {
    const props = {
      imageUrl: '/images/not-open.svg',
      title: 'Form Not Open',
      description: 'This form is not currently open for submissions.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    expect(screen.getByText('Form Not Open')).toBeInTheDocument();
    expect(
      screen.getByText('This form is not currently open for submissions.')
    ).toBeInTheDocument();
  });

  it('renders with different image URLs', () => {
    const imageUrls = [
      '/images/expired.svg',
      '/images/not-open.svg',
      '/images/closed.svg',
      '/images/error.svg',
    ];

    imageUrls.forEach((imageUrl) => {
      const props = {
        imageUrl,
        title: 'Test Title',
        description: 'Test description',
      };

      const { unmount } = renderWithProvider(<EdgeCaseMessage {...props} />);

      const image = screen.getByTestId('edge-case-image');
      expect(image).toHaveAttribute('src', imageUrl);

      unmount();
    });
  });

  it('renders title with proper heading level', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    // Simulate image load to make content visible
    const image = screen.getByTestId('edge-case-image');
    fireEvent.load(image);

    const title = screen.getByRole('heading', { level: 4 });
    expect(title).toBeInTheDocument();
    expect(title).toHaveTextContent('Form Expired');
  });

  it('renders description text', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired and is no longer available for submissions.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const description = screen.getByText(
      'This form has expired and is no longer available for submissions.'
    );
    expect(description).toBeInTheDocument();
  });

  it('handles image load event', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const image = screen.getByTestId('edge-case-image');

    // Initially the wrapper should be hidden
    const wrapper = image.closest('[style*="visibility"]');
    expect(wrapper).not.toBeNull();
    expect(wrapper).toHaveStyle({ visibility: 'hidden' });

    // Trigger image load
    fireEvent.load(image);

    // After load, wrapper should be visible
    expect(wrapper).toHaveStyle({ visibility: 'visible' });
  });

  it('renders with proper content structure', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    // Simulate image load to make content visible
    const image = screen.getByTestId('edge-case-image');
    fireEvent.load(image);

    // Check that all main elements are present
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
    expect(screen.getByRole('heading', { level: 4 })).toBeInTheDocument();
    expect(screen.getByText('This form has expired.')).toBeInTheDocument();
  });

  it('renders with proper accessibility attributes', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const image = screen.getByTestId('edge-case-image');
    expect(image).toHaveAttribute('alt', 'Edge case image');
  });

  it('renders with proper responsive design', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const content = screen.getByTestId('edge-case-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });

  it('renders with proper theme colors', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    // The component should render without errors
    expect(screen.getByText('Form Expired')).toBeInTheDocument();
  });

  it('renders with proper spacing and layout', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const content = screen.getByTestId('edge-case-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });

  it('renders with proper border and border radius', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const content = screen.getByTestId('edge-case-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });

  it('renders with proper background colors', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    // The component should render without errors
    expect(screen.getByText('Form Expired')).toBeInTheDocument();
  });

  it('renders with proper text styling', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    // Simulate image load to make content visible
    const image = screen.getByTestId('edge-case-image');
    fireEvent.load(image);

    const title = screen.getByRole('heading', { level: 4 });
    const description = screen.getByText('This form has expired.');

    expect(title).toBeInTheDocument();
    expect(description).toBeInTheDocument();
  });

  it('renders with proper image dimensions', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const image = screen.getByTestId('edge-case-image');
    expect(image).toHaveAttribute('width', '200');
    expect(image).toHaveAttribute('height', '200');
  });

  it('renders with proper content alignment', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const content = screen.getByTestId('edge-case-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });

  it('renders with proper content sizing', () => {
    const props = {
      imageUrl: '/images/expired.svg',
      title: 'Form Expired',
      description: 'This form has expired.',
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    const content = screen.getByTestId('edge-case-image').closest('.mantine-Flex-root');
    expect(content).toBeInTheDocument();
  });

  it('renders with long title and description', () => {
    const longTitle =
      'This is a very long edge case title that might exceed normal length limits and should still render properly';
    const longDescription =
      'This is a very long edge case description that contains a lot of details about the error and what went wrong and how to fix it';

    const props = {
      imageUrl: '/images/expired.svg',
      title: longTitle,
      description: longDescription,
    };

    renderWithProvider(<EdgeCaseMessage {...props} />);

    expect(screen.getByText(longTitle)).toBeInTheDocument();
    expect(screen.getByText(longDescription)).toBeInTheDocument();
  });
});
