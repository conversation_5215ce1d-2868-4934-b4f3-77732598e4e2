import { InputStyle } from '@/types/enum/inputStyle';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ExpiredMessage from './ExpiredMessage';

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, width, height, className, onLoad }: any) => (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      onLoad={onLoad}
      data-testid='edge-case-image'
    />
  ),
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTolgee: () => ({
    getLanguage: vi.fn(() => 'en'),
  }),
  useTranslate: () => ({
    t: vi.fn((key: string) => {
      const translations = {
        expiredFormTitle: 'Form Expired',
        expiredFormDescription: 'This form has expired and is no longer available.',
      };
      return translations[key as keyof typeof translations] || key;
    }),
  }),
}));

// Mock Meta component
vi.mock('../Meta/Meta', () => ({
  default: ({ title, description, image }: any) => (
    <div data-testid='meta-component'>
      <span data-testid='meta-title'>{title}</span>
      <span data-testid='meta-description'>{description}</span>
      <span data-testid='meta-image'>{image}</span>
    </div>
  ),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <MantineEmotionProvider>
      <MantineProvider
        theme={{
          colors: {
            decaLight: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaNavy: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaDark: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
          },
          breakpoints: {
            sm: '768px',
          },
        }}
      >
        {component}
      </MantineProvider>
    </MantineEmotionProvider>
  );
};

describe('ExpiredMessage', () => {
  const mockFormSettings = {
    id: 'test-form-id',
    name: 'Test Form',
    description: 'Test form description',
    startAt: '2024-01-01T00:00:00Z',
    expiredAt: '2024-12-31T23:59:59Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    status: 'active',
    permissions: ['read', 'write'],
    isFavorited: false,
    isPinned: false,
    responses: 0,
    tags: [],
    urls: {
      public: '/public-url',
      embed: '/embed-url',
      private: '/private-url',
    },
    metadata: {
      organizationId: 'org-id',
      workspaceId: 'workspace-id',
    },
    appearance: {
      headingStyle: {
        fontFamily: 'Arial',
        fontSize: 16,
        color: '#000000',
      },
      paragraphStyle: {
        fontFamily: 'Arial',
        fontSize: 14,
        color: '#000000',
      },
      buttonStyle: {
        type: 'primary',
        fullWidth: false,
        backgroundColor: '#000000',
        textColor: '#ffffff',
        fontFamily: 'Arial',
        fontSize: 14,
      },
      formFieldStyle: {
        color: {
          placeholder: '#000000',
          question: '#000000',
          answer: '#000000',
          icon: '#000000',
          description: '#000000',
          fieldStroke: '#000000',
          fieldBackGround: '#ffffff',
        },
        fontFamily: {
          placeholder: 'Arial',
          question: 'Arial',
          text: 'Arial',
          answer: 'Arial',
        },
        fontSize: {
          placeholder: '14',
          question: 16,
          text: 14,
          answer: 14,
        },
      },
      defaultSettings: {
        color: '#000000',
        font: 'Arial',
        inputStyle: InputStyle.Classic,
      },
      customize: false,
      headerStyle: {
        position: 'top',
        logoImage: '',
        logoSize: 'medium' as any,
        logoAlign: 'left',
        isUsingText: false,
        text: '',
      },
      footerStyle: {
        logoImage: '',
        logoSize: 'medium' as any,
        logoAlign: 'left',
        isUsingText: false,
        text: '',
      },
    },
    screenshot: {
      preview: '/preview.jpg',
      thumbnail: '/thumbnail.jpg',
      original: '/original.jpg',
    },
    setting: {
      submission: {
        mode: 'message',
        message: 'Thank you for your submission',
        caption: '',
        button: '',
        redirectUrl: '',
        enableBranding: true,
        limitResponse: false,
        limitNumber: 0,
      },
      notification: {
        isAutoresponse: false,
      },
      behavior: {
        isMultipleResponse: false,
      },
      systemMessage: [
        {
          type: 'expired',
          heading: {
            en: 'Form has expired',
            es: 'El formulario ha expirado',
          },
          body: {
            en: 'This form has expired and is no longer available for submissions.',
            es: 'Este formulario ha expirado y ya no está disponible para envíos.',
          },
        },
      ],
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders ExpiredMessage with correct props', () => {
    renderWithProvider(
      <ExpiredMessage formSettings={mockFormSettings as any} messageType='expired' />
    );

    expect(screen.getByTestId('meta-component')).toBeInTheDocument();
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
  });

  it('renders Meta component with correct form settings', () => {
    renderWithProvider(
      <ExpiredMessage formSettings={mockFormSettings as any} messageType='expired' />
    );

    expect(screen.getByTestId('meta-title')).toHaveTextContent('Test Form');
    expect(screen.getByTestId('meta-description')).toHaveTextContent('Test form description');
    expect(screen.getByTestId('meta-image')).toHaveTextContent('/preview.jpg');
  });

  it('renders EdgeCaseMessage with correct image URL', () => {
    renderWithProvider(
      <ExpiredMessage formSettings={mockFormSettings as any} messageType='expired' />
    );

    const image = screen.getByTestId('edge-case-image');
    expect(image).toHaveAttribute('src', '/images/expired_link.svg');
  });

  it('renders title from form settings when available', () => {
    renderWithProvider(
      <ExpiredMessage formSettings={mockFormSettings as any} messageType='expired' />
    );

    expect(screen.getByText('Form has expired')).toBeInTheDocument();
  });

  it('renders description from form settings when available', () => {
    renderWithProvider(
      <ExpiredMessage formSettings={mockFormSettings as any} messageType='expired' />
    );

    expect(
      screen.getByText('This form has expired and is no longer available for submissions.')
    ).toBeInTheDocument();
  });

  it('renders fallback title when message settings not found', () => {
    const formSettingsWithoutMessage = {
      ...mockFormSettings,
      setting: {
        ...mockFormSettings.setting,
        systemMessage: [],
      },
    };

    renderWithProvider(
      <ExpiredMessage formSettings={formSettingsWithoutMessage as any} messageType='expired' />
    );

    expect(screen.getByText('Form Expired')).toBeInTheDocument();
  });

  it('renders fallback description when message settings not found', () => {
    const formSettingsWithoutMessage = {
      ...mockFormSettings,
      setting: {
        ...mockFormSettings.setting,
        systemMessage: [],
      },
    };

    renderWithProvider(
      <ExpiredMessage formSettings={formSettingsWithoutMessage as any} messageType='expired' />
    );

    expect(
      screen.getByText('This form has expired and is no longer available.')
    ).toBeInTheDocument();
  });

  it('handles different message types', () => {
    const formSettingsWithDifferentType = {
      ...mockFormSettings,
      setting: {
        ...mockFormSettings.setting,
        systemMessage: [
          {
            type: 'closed',
            heading: { en: 'Form is closed' },
            body: { en: 'This form is closed for submissions.' },
          },
        ],
      },
    };

    renderWithProvider(
      <ExpiredMessage formSettings={formSettingsWithDifferentType as any} messageType='closed' />
    );

    expect(screen.getByText('Form is closed')).toBeInTheDocument();
    expect(screen.getByText('This form is closed for submissions.')).toBeInTheDocument();
  });

  it('handles missing language translations', () => {
    const formSettingsWithMissingTranslations = {
      ...mockFormSettings,
      setting: {
        ...mockFormSettings.setting,
        systemMessage: [
          {
            type: 'expired',
            heading: {},
            body: {},
          },
        ],
      },
    };

    renderWithProvider(
      <ExpiredMessage
        formSettings={formSettingsWithMissingTranslations as any}
        messageType='expired'
      />
    );

    // Should use fallback translations
    expect(screen.getByText('Form Expired')).toBeInTheDocument();
    expect(
      screen.getByText('This form has expired and is no longer available.')
    ).toBeInTheDocument();
  });

  it('handles missing screenshot properties', () => {
    const formSettingsWithoutScreenshot = {
      ...mockFormSettings,
      screenshot: {},
    };

    renderWithProvider(
      <ExpiredMessage formSettings={formSettingsWithoutScreenshot as any} messageType='expired' />
    );

    expect(screen.getByTestId('meta-component')).toBeInTheDocument();
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
  });

  it('uses thumbnail when preview is not available', () => {
    const formSettingsWithThumbnailOnly = {
      ...mockFormSettings,
      screenshot: {
        thumbnail: '/thumbnail.jpg',
      },
    };

    renderWithProvider(
      <ExpiredMessage formSettings={formSettingsWithThumbnailOnly as any} messageType='expired' />
    );

    expect(screen.getByTestId('meta-image')).toHaveTextContent('/thumbnail.jpg');
  });

  it('renders with proper accessibility attributes', () => {
    renderWithProvider(
      <ExpiredMessage formSettings={mockFormSettings as any} messageType='expired' />
    );

    const image = screen.getByTestId('edge-case-image');
    expect(image).toHaveAttribute('alt', 'Edge case image');
  });

  it('renders with proper content structure', () => {
    renderWithProvider(
      <ExpiredMessage formSettings={mockFormSettings as any} messageType='expired' />
    );

    // Check that all main elements are present
    expect(screen.getByTestId('meta-component')).toBeInTheDocument();
    expect(screen.getByTestId('edge-case-image')).toBeInTheDocument();
    expect(screen.getByText('Form has expired')).toBeInTheDocument();
    expect(
      screen.getByText('This form has expired and is no longer available for submissions.')
    ).toBeInTheDocument();
  });

  it('handles different message types with custom translations', () => {
    const formSettingsWithCustomType = {
      ...mockFormSettings,
      setting: {
        ...mockFormSettings.setting,
        systemMessage: [
          {
            type: 'maintenance',
            heading: { en: 'Form under maintenance' },
            body: { en: 'This form is temporarily unavailable due to maintenance.' },
          },
        ],
      },
    };

    renderWithProvider(
      <ExpiredMessage formSettings={formSettingsWithCustomType as any} messageType='maintenance' />
    );

    expect(screen.getByText('Form under maintenance')).toBeInTheDocument();
    expect(
      screen.getByText('This form is temporarily unavailable due to maintenance.')
    ).toBeInTheDocument();
  });
});
