import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import Header from './Header';

// Mock HeaderContent component
vi.mock('./HeaderContent', () => ({
  default: function MockHeaderContent(props: any) {
    return (
      <div
        data-testid='header-content'
        logoImage={props.logoImage}
        logoSize={props.logoSize}
        isUsingText={props.isUsingText}
        text={props.text}
        logoAlign={props.logoAlign}
        {...props}
      />
    );
  },
}));

describe('Header', () => {
  const defaultProps = {
    position: 'sticky',
    logoImage: 'https://example.com/logo.png',
    logoSize: AppearanceSettingsLogoSize.Medium,
    isUsingText: false,
    text: 'Test Logo',
    logoAlign: AppearanceSettingsLogoAlign.Left,
  };

  it('renders header with correct structure', () => {
    renderWithProviders(<Header {...defaultProps} />);

    const header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();
    expect(header).toHaveAttribute('id', 'header');
  });

  it('renders HeaderContent with passed props', () => {
    renderWithProviders(<Header {...defaultProps} />);

    const headerContent = screen.getByTestId('header-content');
    expect(headerContent).toBeInTheDocument();
  });

  it('applies sticky positioning styles when position is sticky', () => {
    renderWithProviders(<Header {...defaultProps} />);

    const header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();
  });

  it('applies fixed positioning styles when position is inside', () => {
    const insideProps = {
      ...defaultProps,
      position: 'inside',
    };

    renderWithProviders(<Header {...insideProps} />);

    const header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();
  });

  it('renders with all props provided', () => {
    const allProps = {
      position: 'sticky',
      logoImage: 'https://example.com/logo.png',
      logoSize: AppearanceSettingsLogoSize.Large,
      isUsingText: true,
      text: 'Company Name',
      logoAlign: AppearanceSettingsLogoAlign.Center,
    };

    renderWithProviders(<Header {...allProps} />);

    const headerContent = screen.getByTestId('header-content');
    expect(headerContent).toBeInTheDocument();
  });

  it('handles different position values correctly', () => {
    const { rerender } = renderWithProviders(<Header {...defaultProps} />);

    // Test sticky position
    let header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();

    // Test inside position
    rerender(<Header {...defaultProps} position='inside' />);
    header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();
  });
});
