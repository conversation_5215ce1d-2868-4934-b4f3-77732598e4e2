import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import HeaderContent from './HeaderContent';

// Mock Logo component
vi.mock('./Logo', () => ({
  default: function MockLogo(props: any) {
    return (
      <div
        data-testid='logo'
        logoImage={props.logoImage}
        logoSize={props.logoSize}
        isUsingText={props.isUsingText}
        text={props.text}
        {...props}
      />
    );
  },
}));

describe('HeaderContent', () => {
  const defaultProps = {
    logoImage: 'https://example.com/logo.png',
    logoSize: AppearanceSettingsLogoSize.Medium,
    isUsingText: false,
    text: 'Test Logo',
    logoAlign: AppearanceSettingsLogoAlign.Left,
  };

  it('renders header content with correct structure', () => {
    renderWithProviders(<HeaderContent {...defaultProps} />);

    const logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();
  });

  it('renders Logo component with passed props', () => {
    renderWithProviders(<HeaderContent {...defaultProps} />);

    const logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();
  });

  it('applies left alignment styles when logoAlign is left', () => {
    renderWithProviders(
      <HeaderContent {...defaultProps} logoAlign={AppearanceSettingsLogoAlign.Left} />
    );

    const logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();
  });

  it('applies center alignment styles when logoAlign is center', () => {
    renderWithProviders(
      <HeaderContent {...defaultProps} logoAlign={AppearanceSettingsLogoAlign.Center} />
    );

    const logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();
  });

  it('applies right alignment styles when logoAlign is right', () => {
    renderWithProviders(
      <HeaderContent {...defaultProps} logoAlign={AppearanceSettingsLogoAlign.Right} />
    );

    const logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();
  });

  it('renders with all props provided', () => {
    const allProps = {
      logoImage: 'https://example.com/logo.png',
      logoSize: AppearanceSettingsLogoSize.Large,
      isUsingText: true,
      text: 'Company Name',
      logoAlign: AppearanceSettingsLogoAlign.Center,
    };

    renderWithProviders(<HeaderContent {...allProps} />);

    const logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();
  });

  it('handles different logo alignment values correctly', () => {
    const { rerender } = renderWithProviders(<HeaderContent {...defaultProps} />);

    // Test left alignment
    let logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();

    // Test center alignment
    rerender(<HeaderContent {...defaultProps} logoAlign={AppearanceSettingsLogoAlign.Center} />);
    logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();

    // Test right alignment
    rerender(<HeaderContent {...defaultProps} logoAlign={AppearanceSettingsLogoAlign.Right} />);
    logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();
  });

  it('renders with text-only logo', () => {
    const textOnlyProps = {
      ...defaultProps,
      isUsingText: true,
      logoImage: '',
    };

    renderWithProviders(<HeaderContent {...textOnlyProps} />);

    const logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();
  });

  it('renders with image-only logo', () => {
    const imageOnlyProps = {
      ...defaultProps,
      isUsingText: false,
      logoImage: 'https://example.com/logo.png',
    };

    renderWithProviders(<HeaderContent {...imageOnlyProps} />);

    const logo = screen.getByTestId('logo');
    expect(logo).toBeInTheDocument();
  });
});
