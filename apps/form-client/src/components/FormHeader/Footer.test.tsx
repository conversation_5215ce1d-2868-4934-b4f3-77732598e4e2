import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import Footer from './Footer';

// Mock HeaderContent component
vi.mock('./HeaderContent', () => ({
  default: function MockHeaderContent(props: any) {
    return (
      <div
        data-testid='header-content'
        logoImage={props.logoImage}
        logoSize={props.logoSize}
        isUsingText={props.isUsingText}
        text={props.text}
        logoAlign={props.logoAlign}
        {...props}
      />
    );
  },
}));

describe('Footer', () => {
  const defaultProps = {
    logoImage: 'https://example.com/logo.png',
    logoSize: AppearanceSettingsLogoSize.Medium,
    isUsingText: false,
    text: 'Test Logo',
    logoAlign: AppearanceSettingsLogoAlign.Left,
  };

  it('renders footer with correct structure', () => {
    renderWithProviders(<Footer {...defaultProps} />);

    const footer = screen.getByRole('contentinfo');
    expect(footer).toBeInTheDocument();
    expect(footer).toHaveAttribute('id', 'footer');
  });

  it('renders HeaderContent with passed props', () => {
    renderWithProviders(<Footer {...defaultProps} />);

    const headerContent = screen.getByTestId('header-content');
    expect(headerContent).toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    renderWithProviders(<Footer {...defaultProps} />);

    const footer = screen.getByRole('contentinfo');
    expect(footer).toBeInTheDocument();
  });

  it('renders with all props provided', () => {
    const allProps = {
      logoImage: 'https://example.com/logo.png',
      logoSize: AppearanceSettingsLogoSize.Large,
      isUsingText: true,
      text: 'Company Name',
      logoAlign: AppearanceSettingsLogoAlign.Center,
    };

    renderWithProviders(<Footer {...allProps} />);

    const headerContent = screen.getByTestId('header-content');
    expect(headerContent).toBeInTheDocument();
  });
});
