import { AppearanceSettingsLogoSize } from '@/types/enum/appearanceSettings';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import Logo from './Logo';

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, width, height, className, onLoad }: any) => (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      onLoad={onLoad}
      data-testid='logo-image'
    />
  ),
}));

describe('Logo', () => {
  const defaultProps = {
    logoImage: 'https://example.com/logo.png',
    logoSize: AppearanceSettingsLogoSize.Medium,
    isUsingText: false,
    text: 'Test Logo',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders logo image when logoImage is provided', () => {
    renderWithProviders(<Logo {...defaultProps} />);

    const logoImage = screen.getByTestId('logo-image');
    expect(logoImage).toBeInTheDocument();
    expect(logoImage).toHaveAttribute('src', defaultProps.logoImage);
    expect(logoImage).toHaveAttribute('alt', 'Logo');
  });

  it('renders text when isUsingText is true', () => {
    const textProps = {
      ...defaultProps,
      isUsingText: true,
    };

    renderWithProviders(<Logo {...textProps} />);

    const logoText = screen.getByText(defaultProps.text);
    expect(logoText).toBeInTheDocument();
  });

  it('renders both image and text when both are provided', () => {
    const bothProps = {
      ...defaultProps,
      isUsingText: true,
    };

    renderWithProviders(<Logo {...bothProps} />);

    const logoImage = screen.getByTestId('logo-image');
    const logoText = screen.getByText(defaultProps.text);

    expect(logoImage).toBeInTheDocument();
    expect(logoText).toBeInTheDocument();
  });

  it('does not render image when logoImage is not provided', () => {
    const noImageProps = {
      ...defaultProps,
      logoImage: '',
    };

    renderWithProviders(<Logo {...noImageProps} />);

    const logoImage = screen.queryByTestId('logo-image');
    expect(logoImage).not.toBeInTheDocument();
  });

  it('does not render text when isUsingText is false', () => {
    renderWithProviders(<Logo {...defaultProps} />);

    const logoText = screen.queryByText(defaultProps.text);
    expect(logoText).not.toBeInTheDocument();
  });

  it('applies correct height for small logo size', () => {
    const smallProps = {
      ...defaultProps,
      logoSize: AppearanceSettingsLogoSize.Small,
    };

    renderWithProviders(<Logo {...smallProps} />);

    const logoImage = screen.getByTestId('logo-image');
    expect(logoImage).toHaveAttribute('height', '28');
  });

  it('applies correct height for medium logo size', () => {
    renderWithProviders(<Logo {...defaultProps} />);

    const logoImage = screen.getByTestId('logo-image');
    expect(logoImage).toHaveAttribute('height', '40');
  });

  it('applies correct height for large logo size', () => {
    const largeProps = {
      ...defaultProps,
      logoSize: AppearanceSettingsLogoSize.Large,
    };

    renderWithProviders(<Logo {...largeProps} />);

    const logoImage = screen.getByTestId('logo-image');
    expect(logoImage).toHaveAttribute('height', '48');
  });

  it('calculates width based on image dimensions when image loads', () => {
    renderWithProviders(<Logo {...defaultProps} />);

    const logoImage = screen.getByTestId('logo-image');
    expect(logoImage).toBeInTheDocument();
  });

  it('handles case-insensitive logo size', () => {
    const uppercaseProps = {
      ...defaultProps,
      logoSize: 'L' as any, // Test case insensitivity
    };

    renderWithProviders(<Logo {...uppercaseProps} />);

    const logoImage = screen.getByTestId('logo-image');
    expect(logoImage).toHaveAttribute('height', '48');
  });

  it('renders with zero width when image has not loaded', () => {
    renderWithProviders(<Logo {...defaultProps} />);

    const logoImage = screen.getByTestId('logo-image');
    expect(logoImage).toHaveAttribute('width', '0');
  });

  it('handles image load event correctly', () => {
    renderWithProviders(<Logo {...defaultProps} />);

    const logoImage = screen.getByTestId('logo-image');
    expect(logoImage).toBeInTheDocument();
  });

  it('renders text-only logo correctly', () => {
    const textOnlyProps = {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      isUsingText: true,
      text: 'Company Name',
    };

    renderWithProviders(<Logo {...textOnlyProps} />);

    const logoText = screen.getByText('Company Name');
    expect(logoText).toBeInTheDocument();

    const logoImage = screen.queryByTestId('logo-image');
    expect(logoImage).not.toBeInTheDocument();
  });

  it('renders image-only logo correctly', () => {
    const imageOnlyProps = {
      ...defaultProps,
      isUsingText: false,
    };

    renderWithProviders(<Logo {...imageOnlyProps} />);

    const logoImage = screen.getByTestId('logo-image');
    expect(logoImage).toBeInTheDocument();

    const logoText = screen.queryByText(defaultProps.text);
    expect(logoText).not.toBeInTheDocument();
  });
});
