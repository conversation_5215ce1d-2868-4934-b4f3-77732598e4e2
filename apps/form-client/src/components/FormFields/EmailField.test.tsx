import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import EmailField from './EmailField';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock Mantine TextInput to avoid complex input interactions
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    TextInput: ({
      label,
      placeholder,
      required,
      description,
      error,
      leftSection,
      onChange,
      disabled,
      ...props
    }: any) => (
      <div data-testid='text-input'>
        {label && (
          <label htmlFor='' data-testid='field-label'>
            {label}
          </label>
        )}
        <input
          type='email'
          placeholder={placeholder}
          data-testid='email-input'
          required={required}
          disabled={disabled}
          onChange={(e) => onChange?.(e)}
          {...props}
        />
        {leftSection && <div data-testid='email-icon'>{leftSection}</div>}
        {description && <div data-testid='field-description'>{description}</div>}
        {error && <div data-testid='field-error'>{error}</div>}
      </div>
    ),
  };
});

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        emailQuestionId: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  metadata: {
    organizationId: 'test-org',
    workspaceId: 'test-workspace',
  },
  status: 'active',
  permissions: [],
  urls: {
    public: 'https://test.com',
    embed: 'https://test.com/embed',
    private: 'https://test.com/private',
  },
  tags: [],
  expiredAt: '',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('EmailField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders email field correctly', () => {
    renderWithProviders(<EmailField label='Email Address' placeholder='Enter your email' />);

    expect(screen.getByTestId('text-input')).toBeInTheDocument();
    expect(screen.getByTestId('field-label')).toHaveTextContent('Email Address');
    expect(screen.getByTestId('text-input-field')).toHaveAttribute(
      'placeholder',
      'Enter your email'
    );
  });

  it('shows required indicator for required fields', () => {
    renderWithProviders(<EmailField label='Email Address' required={true} />);

    const input = screen.getByTestId('text-input-field');
    expect(input).toBeRequired();
  });

  it('renders description when provided', () => {
    renderWithProviders(
      <EmailField label='Email Address' description='Please enter a valid email address' />
    );

    expect(screen.getByTestId('field-description')).toHaveTextContent(
      'Please enter a valid email address'
    );
  });

  it('handles input change correctly', () => {
    const onChange = vi.fn();

    renderWithProviders(<EmailField label='Email Address' onChange={onChange} />);

    const input = screen.getByTestId('text-input-field');
    fireEvent.change(input, { target: { value: '<EMAIL>' } });

    expect(onChange).toHaveBeenCalled();
  });

  it('displays error state correctly', () => {
    renderWithProviders(<EmailField label='Email Address' error='Invalid email format' />);

    expect(screen.getByTestId('field-error')).toHaveTextContent('Invalid email format');
  });

  it('renders with email icon', () => {
    renderWithProviders(<EmailField label='Email Address' />);

    expect(screen.getByTestId('text-input-icon')).toBeInTheDocument();
  });

  it('handles disabled state', () => {
    renderWithProviders(<EmailField label='Email Address' disabled={true} />);

    const input = screen.getByTestId('text-input-field');
    expect(input).toBeDisabled();
  });

  it('passes through all props to TextInput', () => {
    renderWithProviders(
      <EmailField
        label='Email Address'
        placeholder='Enter email'
        className='custom-class'
        data-testid='custom-email'
      />
    );

    const input = screen.getByTestId('custom-email');
    expect(input).toHaveClass('custom-class');
    expect(input).toHaveAttribute('data-testid', 'custom-email');
  });
});
