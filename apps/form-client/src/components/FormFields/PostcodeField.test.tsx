import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import PostcodeField from './PostcodeField';

// Mock dependencies
vi.mock('@/hooks/useDebounce', () => ({
  default: () => vi.fn(),
}));

vi.mock('@/utils', () => ({
  getAddressFromPostalCode: vi.fn(),
}));

const mockFormSettings = {
  appearanceSettings: {
    logo: {
      size: AppearanceSettingsLogoSize.Medium,
      align: AppearanceSettingsLogoAlign.Left,
    },
    inputStyle: InputStyle.Classic,
  },
  hiddenFieldsMap: {},
};

describe('PostcodeField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders postcode input with placeholder', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByPlaceholderText('Enter postcode')).toBeInTheDocument();
  });

  it('renders with label', () => {
    renderWithProviders(<PostcodeField label='Postcode' placeholder='Enter postcode' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Postcode')).toBeInTheDocument();
  });

  it('handles input changes', () => {
    const onChange = vi.fn();

    renderWithProviders(<PostcodeField placeholder='Enter postcode' onChange={onChange} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    fireEvent.change(input, { target: { value: '12345' } });

    expect(onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: '12345' }),
      })
    );
  });

  it('handles empty input', () => {
    const onChange = vi.fn();

    renderWithProviders(<PostcodeField placeholder='Enter postcode' onChange={onChange} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');

    // First set a value to ensure onChange is working
    fireEvent.change(input, { target: { value: '12345' } });
    expect(onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: '12345' }),
      })
    );

    // Clear the mock calls
    onChange.mockClear();

    // Now try to clear the input
    fireEvent.change(input, { target: { value: '' } });

    expect(onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: '' }),
      })
    );
  });

  it('applies custom class names', () => {
    const customClassNames = {
      input: 'custom-input-class',
      label: 'custom-label-class',
    };

    renderWithProviders(
      <PostcodeField placeholder='Enter postcode' classNames={customClassNames} />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toBeInTheDocument();
  });

  it('handles disabled state', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' disabled />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toBeDisabled();
  });

  it('handles required state', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' required />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toBeRequired();
  });

  it('handles error state', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' error='Invalid postcode' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Invalid postcode')).toBeInTheDocument();
  });

  it('handles description', () => {
    renderWithProviders(
      <PostcodeField placeholder='Enter postcode' description='Please enter your postcode' />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Please enter your postcode')).toBeInTheDocument();
  });

  it('handles withAsterisk prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' withAsterisk />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toBeInTheDocument();
  });

  it('handles size prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' size='lg' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toBeInTheDocument();
  });

  it('handles variant prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' variant='filled' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toBeInTheDocument();
  });

  it('handles radius prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' radius='xl' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toBeInTheDocument();
  });

  it('handles rightSection prop', () => {
    renderWithProviders(
      <PostcodeField
        placeholder='Enter postcode'
        rightSection={<div data-testid='right-section'>Right</div>}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getAllByTestId('right-section')).toHaveLength(2);
  });

  it('handles leftSection prop', () => {
    renderWithProviders(
      <PostcodeField
        placeholder='Enter postcode'
        leftSection={<div data-testid='left-section'>Left</div>}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByTestId('left-section')).toBeInTheDocument();
  });

  it('handles maxLength prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' maxLength={10} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toHaveAttribute('maxLength', '10');
  });

  it('handles minLength prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' minLength={5} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toHaveAttribute('minLength', '5');
  });

  it('handles pattern prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' pattern='[0-9]{5}' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toHaveAttribute('pattern', '[0-9]{5}');
  });

  it('handles autoComplete prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' autoComplete='postal-code' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toHaveAttribute('autocomplete', 'postal-code');
  });

  it('handles spellCheck prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' spellCheck={false} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toHaveAttribute('spellcheck', 'false');
  });

  it('handles readOnly prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' readOnly />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toHaveAttribute('readonly');
  });

  it('handles autoFocus prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' autoFocus />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(document.activeElement).toBe(input);
  });

  it('handles tabIndex prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' tabIndex={0} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toHaveAttribute('tabindex', '0');
  });

  it('handles name prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' name='postcode' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toHaveAttribute('name', 'postcode');
  });

  it('handles id prop', () => {
    renderWithProviders(<PostcodeField placeholder='Enter postcode' id='postcode-field' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Enter postcode');
    expect(input).toHaveAttribute('id', 'postcode-field');
  });
});
