import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType, ValidatorType } from '@/types/form-builder';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import FormFieldBase from '../FormFields/FormFieldBase';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock Mantine form to avoid form context issues
vi.mock('@mantine/form', () => ({
  useFormContext: () => ({
    getInputProps: () => ({
      value: '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
      error: undefined,
    }),
    setFieldValue: vi.fn(),
    getValues: () => ({}),
    validate: vi.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  createFormContext: () => [
    ({ children }: { children: React.ReactNode }) => <>{children}</>,
    () => ({
      getInputProps: () => ({
        value: '',
        onChange: vi.fn(),
        onBlur: vi.fn(),
        error: undefined,
      }),
      setFieldValue: vi.fn(),
      getValues: () => ({}),
      validate: vi.fn(),
      values: {},
      errors: {},
      clearFieldError: vi.fn(),
    }),
    () => ({}),
  ],
}));

const mockFormSettings = {
  appearanceSettings: {
    logo: {
      size: AppearanceSettingsLogoSize.Medium,
      align: AppearanceSettingsLogoAlign.Left,
    },
    inputStyle: InputStyle.Classic,
  },
  hiddenFieldsMap: {},
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        button: 'Submit another response',
      },
    },
  },
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('FormFieldBase', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders short answer field correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.ShortQA,
      label: 'Test Label',
      placeholder: 'Test placeholder',
      required: true,
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('text-input-field')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Test placeholder')).toBeInTheDocument();
  });

  it('renders long answer field correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.LongQA,
      label: 'Test Label',
      placeholder: 'Test placeholder',
      required: true,
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByPlaceholderText('Test placeholder')).toBeInTheDocument();
  });

  it('renders number field correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.Number,
      label: 'Test Number',
      placeholder: 'Enter number',
      required: true,
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByPlaceholderText('Enter number')).toBeInTheDocument();
  });

  it('renders checkbox field correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [{ label: 'Option 1', value: 'option1' }],
      required: true,
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByLabelText('Option 1')).toBeInTheDocument();
  });

  it('renders heading field correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.Heading,
      label: 'Test Heading',
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Heading')).toBeInTheDocument();
  });

  it('renders paragraph field correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.Paragraph,
      label: 'Test Paragraph',
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Paragraph')).toBeInTheDocument();
  });

  it('calls onChange callback when field value changes', () => {
    const onChange = vi.fn();
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.ShortQA,
      label: 'Test Label',
      placeholder: 'Test placeholder',
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} onChange={onChange} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByTestId('text-input-field');
    fireEvent.change(input, { target: { value: 'test value' } });

    expect(onChange).toHaveBeenCalledWith(field, 'test value');
  });

  it('shows required indicator for required fields', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.ShortQA,
      label: 'Test Label',
      required: true,
      validators: [
        {
          type: ValidatorType.Required,
          value: true,
        },
      ],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByRole('textbox');
    expect(input).toBeRequired();
  });

  it('renders description when enabled', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.ShortQA,
      label: 'Test Label',
      description: 'Test description',
      descriptionEnabled: true,
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test description')).toBeInTheDocument();
  });

  it('does not render description when disabled', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.ShortQA,
      label: 'Test Label',
      description: 'Test description',
      descriptionEnabled: false,
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.queryByText('Test description')).not.toBeInTheDocument();
  });

  it('renders email field correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.Email,
      label: 'Email',
      placeholder: 'Enter email',
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByPlaceholderText('Enter email')).toBeInTheDocument();
  });

  it('renders website field correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test_field',
      type: FieldType.Website,
      label: 'Website',
      placeholder: 'Enter website',
      validators: [],
    };

    renderWithProviders(<FormFieldBase field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByPlaceholderText('Enter website')).toBeInTheDocument();
  });
});
