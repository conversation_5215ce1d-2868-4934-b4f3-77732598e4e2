import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import ParagraphField from './ParagraphField';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

const mockFormSettings = {
  appearanceSettings: {
    logo: {
      size: AppearanceSettingsLogoSize.Medium,
      align: AppearanceSettingsLogoAlign.Left,
    },
    inputStyle: InputStyle.Classic,
  },
  hiddenFieldsMap: {},
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        button: 'Submit another response',
      },
    },
  },
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('ParagraphField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders paragraph text correctly', () => {
    const field = {
      id: 'test-paragraph',
      type: FieldType.Paragraph,
      name: 'test_paragraph',
      label: 'This is a paragraph of text that provides additional information to the user.',
      validators: [],
    };

    const style = {
      fontFamily: 'Arial',
      fontSize: 14,
      color: '#000000',
    };

    renderWithProviders(<ParagraphField field={field} style={style} />, {
      formSettings: mockFormSettings as any,
    });

    expect(
      screen.getByText(
        'This is a paragraph of text that provides additional information to the user.'
      )
    ).toBeInTheDocument();
  });

  it('renders empty paragraph when no label provided', () => {
    const field = {
      id: 'test-paragraph',
      type: FieldType.Paragraph,
      name: 'test_paragraph',
      label: '',
      validators: [],
    };

    const style = {
      fontFamily: 'Arial',
      fontSize: 14,
      color: '#000000',
    };

    renderWithProviders(<ParagraphField field={field} style={style} />, {
      formSettings: mockFormSettings as any,
    });

    // For empty label, we should check that the component renders without errors
    // rather than looking for empty text
    expect(() => {
      renderWithProviders(<ParagraphField field={field} style={style} />, {
        formSettings: mockFormSettings as any,
      });
    }).not.toThrow();
  });

  it('applies custom styles when customize is enabled', () => {
    const field = {
      id: 'test-paragraph',
      type: FieldType.Paragraph,
      name: 'test_paragraph',
      label: 'Styled paragraph',
      validators: [],
    };

    const style = {
      fontFamily: 'Arial',
      fontSize: 18,
      color: '#ff0000',
    };

    // Create a mock with customize enabled
    const customMockFormSettings = {
      ...mockFormSettings,
      appearance: {
        ...mockFormSettings.appearance,
        customize: true,
      },
    };

    renderWithProviders(<ParagraphField field={field} style={style} />, {
      formSettings: customMockFormSettings as any,
    });

    expect(screen.getByText('Styled paragraph')).toBeInTheDocument();
  });
});
