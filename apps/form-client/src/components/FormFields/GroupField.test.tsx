import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType, GroupFieldType } from '@/types/form-builder';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import GroupField from './GroupField';

// Mock ResizeObserver for Mantine ScrollArea
if (typeof globalThis.ResizeObserver === 'undefined') {
  globalThis.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
}

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock Mantine form
vi.mock('@mantine/form', () => ({
  useFormContext: () => ({
    getInputProps: () => ({
      value: '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
      error: undefined,
    }),
    setFieldValue: vi.fn(),
    getValues: () => ({}),
    validate: vi.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  createFormContext: () => [
    ({ children }: { children: React.ReactNode }) => <>{children}</>,
    () => ({
      getInputProps: () => ({
        value: '',
        onChange: vi.fn(),
        onBlur: vi.fn(),
        error: undefined,
      }),
      setFieldValue: vi.fn(),
      getValues: () => ({}),
      validate: vi.fn(),
      values: {},
      errors: {},
      clearFieldError: vi.fn(),
    }),
    () => ({}),
  ],
}));

// Mock Tolgee useTranslate hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
}));

// Mock problematic components to prevent errors
vi.mock('./NameField', () => ({
  default: ({ group }: { group: any }) => {
    if (!group || !group.label) return <div>Name Field</div>;
    return <div>{group.label}</div>;
  },
}));

vi.mock('./AddressFields', () => ({
  default: ({ group }: { group: any }) => {
    if (!group || !group.label) return <div>Address Field</div>;
    return <div>{group.label}</div>;
  },
}));

vi.mock('./GroupFormFieldBase', () => ({
  default: ({ group }: { group: any }) => {
    if (!group || !group.label) return <div>Group Field</div>;
    return <div>{group.label}</div>;
  },
}));

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

vi.mock('@/utils', () => ({
  getGridColsByWidth: vi.fn((width) => {
    if (width === 'full') return 12;
    if (width === 'half') return 6;
    return 12;
  }),
  hasRequiredRule: vi.fn((field) => {
    return field.validators?.some(
      (validator) => validator.type === 'required' && validator.value === true
    );
  }),
  getPrefecturesOptions: vi.fn(() => [
    { label: 'Tokyo', value: 'tokyo' },
    { label: 'Osaka', value: 'osaka' },
  ]),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        buttonText: 'Submit another response',
        buttonUrl: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  status: 'active',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  createdBy: 'test-user',
  updatedBy: 'test-user',
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
  metadata: {
    version: '1.0.0',
    lastModified: '2023-01-01T00:00:00Z',
  },
  permissions: {
    canEdit: true,
    canDelete: true,
    canShare: true,
  },
  urls: {
    edit: '/edit',
    view: '/view',
    share: '/share',
  },
  tags: [],
  category: 'general',
  isPublic: true,
  isArchived: false,
  expiredAt: null,
  isFavorited: false,
  isPinned: false,
  responses: {
    total: 0,
    lastResponse: null,
  },
};

describe('GroupField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders address group field correctly', () => {
    const group = {
      id: 'address-group',
      name: 'address',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
        {
          id: 'prefecture',
          name: 'prefecture',
          type: FieldType.Dropdown,
          label: 'Prefecture',
          validators: [],
        },
        {
          id: 'city',
          name: 'city',
          type: FieldType.ShortQA,
          label: 'City',
          validators: [],
        },
        {
          id: 'street',
          name: 'street',
          type: FieldType.ShortQA,
          label: 'Street',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Address')).toBeInTheDocument();
  });

  it('renders name group field correctly', () => {
    const group = {
      id: 'name-group',
      name: 'name',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Name',
      fields: [
        {
          id: 'first-name',
          name: 'firstName',
          type: FieldType.ShortQA,
          label: 'First Name',
          validators: [],
        },
        {
          id: 'last-name',
          name: 'lastName',
          type: FieldType.ShortQA,
          label: 'Last Name',
          validators: [],
        },
        {
          id: 'middle-name',
          name: 'middleName',
          type: FieldType.ShortQA,
          label: 'Middle Name',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Name')).toBeInTheDocument();
  });

  it('renders default group field for unknown type', () => {
    const group = {
      id: 'unknown-group',
      name: 'unknown',
      type: 'UnknownType' as any,
      groupType: 'UnknownType' as any,
      label: 'Unknown Group',
      fields: [
        {
          id: 'field-1',
          name: 'field1',
          type: FieldType.ShortQA,
          label: 'Field 1',
          validators: [],
        },
        {
          id: 'field-2',
          name: 'field2',
          type: FieldType.Email,
          label: 'Field 2',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Unknown Group')).toBeInTheDocument();
  });

  it('handles group with empty fields array', () => {
    const group = {
      id: 'empty-group',
      name: 'empty',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Empty Group',
      fields: [],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Empty Group')).toBeInTheDocument();
  });

  it('handles group without label', () => {
    const group = {
      id: 'no-label-group',
      name: 'noLabel',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: '',
      fields: [
        {
          id: 'field-1',
          name: 'field1',
          type: FieldType.ShortQA,
          label: 'Field 1',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    // Mock only renders the group label, not individual fields
    expect(document.body).toBeInTheDocument();
  });

  it('handles group with description', () => {
    const group = {
      id: 'description-group',
      name: 'description',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Group with Description',
      description: 'This is a description',
      descriptionEnabled: true,
      fields: [
        {
          id: 'field-1',
          name: 'field1',
          type: FieldType.ShortQA,
          label: 'Field 1',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Group with Description')).toBeInTheDocument();
  });

  it('handles group with disabled description', () => {
    const group = {
      id: 'disabled-description-group',
      name: 'disabledDescription',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Group with Disabled Description',
      description: 'This description should not show',
      descriptionEnabled: false,
      fields: [
        {
          id: 'field-1',
          name: 'field1',
          type: FieldType.ShortQA,
          label: 'Field 1',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Group with Disabled Description')).toBeInTheDocument();
    expect(screen.queryByText('This description should not show')).not.toBeInTheDocument();
  });

  it('handles group with hidden fields', () => {
    const group = {
      id: 'hidden-fields-group',
      name: 'hiddenFields',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Group with Hidden Fields',
      fields: [
        {
          id: 'visible-field',
          name: 'visibleField',
          type: FieldType.ShortQA,
          label: 'Visible Field',
          validators: [],
          isHide: false,
        },
        {
          id: 'hidden-field',
          name: 'hiddenField',
          type: FieldType.ShortQA,
          label: 'Hidden Field',
          validators: [],
          isHide: true,
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Group with Hidden Fields')).toBeInTheDocument();
  });

  it('handles group with fields having different widths', () => {
    const group = {
      id: 'different-widths-group',
      name: 'differentWidths',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Group with Different Widths',
      fields: [
        {
          id: 'full-width-field',
          name: 'fullWidthField',
          type: FieldType.ShortQA,
          label: 'Full Width Field',
          validators: [],
          style: {
            width: 'full',
          },
        },
        {
          id: 'half-width-field',
          name: 'halfWidthField',
          type: FieldType.ShortQA,
          label: 'Half Width Field',
          validators: [],
          style: {
            width: 'half',
          },
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Group with Different Widths')).toBeInTheDocument();
  });

  it('handles group with fields having no style property', () => {
    const group = {
      id: 'no-style-group',
      name: 'noStyle',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Group with No Style Fields',
      fields: [
        {
          id: 'no-style-field',
          name: 'noStyleField',
          type: FieldType.ShortQA,
          label: 'No Style Field',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Group with No Style Fields')).toBeInTheDocument();
  });

  it('handles group with complex field types', () => {
    const group = {
      id: 'complex-types-group',
      name: 'complexTypes',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Group with Complex Types',
      fields: [
        {
          id: 'text-field',
          name: 'textField',
          type: FieldType.ShortQA,
          label: 'Text Field',
          validators: [],
        },
        {
          id: 'email-field',
          name: 'emailField',
          type: FieldType.Email,
          label: 'Email Field',
          validators: [],
        },
        {
          id: 'dropdown-field',
          name: 'dropdownField',
          type: FieldType.Dropdown,
          label: 'Dropdown Field',
          validators: [],
          options: [
            { label: 'Option 1', value: 'option1' },
            { label: 'Option 2', value: 'option2' },
          ],
        },
        {
          id: 'checkbox-field',
          name: 'checkboxField',
          type: FieldType.Checkbox,
          label: 'Checkbox Field',
          validators: [],
          options: [
            { label: 'Option 1', value: 'option1' },
            { label: 'Option 2', value: 'option2' },
          ],
        },
      ],
    };

    renderWithProviders(<GroupField group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Group with Complex Types')).toBeInTheDocument();
  });
});
