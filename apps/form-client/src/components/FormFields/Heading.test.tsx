import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import Heading from './Heading';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        emailQuestionId: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  metadata: {
    organizationId: 'test-org',
    workspaceId: 'test-workspace',
  },
  status: 'active',
  permissions: [],
  urls: {
    public: 'https://test.com',
    embed: 'https://test.com/embed',
    private: 'https://test.com/private',
  },
  tags: [],
  expiredAt: '',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('Heading', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders heading text correctly', () => {
    const field = {
      id: 'test-heading',
      type: FieldType.Heading,
      name: 'test_heading',
      label: 'This is a heading',
      validators: [],
    };

    const style = {
      fontFamily: 'Arial',
      fontSize: 20,
      color: '#000000',
    };

    renderWithProviders(<Heading field={field} style={style} />);

    expect(screen.getByText('This is a heading')).toBeInTheDocument();
  });

  it('renders empty heading when no label provided', () => {
    const field = {
      id: 'test-heading',
      type: FieldType.Heading,
      name: 'test_heading',
      label: '',
      validators: [],
    };

    const style = {
      fontFamily: 'Arial',
      fontSize: 20,
      color: '#000000',
    };

    renderWithProviders(<Heading field={field} style={style} />);

    const headingElements = screen.getAllByText('');
    expect(headingElements.length).toBeGreaterThan(0);
  });

  it('applies custom styles when customize is enabled', () => {
    const field = {
      id: 'test-heading',
      type: FieldType.Heading,
      name: 'test_heading',
      label: 'Styled Heading',
      validators: [],
    };

    const style = {
      fontFamily: 'Arial',
      fontSize: 24,
      color: '#ff0000',
    };

    renderWithProviders(<Heading field={field} style={style} />);

    expect(screen.getByText('Styled Heading')).toBeInTheDocument();
  });
});
