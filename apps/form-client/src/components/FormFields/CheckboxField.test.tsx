import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import CheckboxField from './CheckboxField';

// Mock ResizeObserver for Mantine ScrollArea
if (typeof globalThis.ResizeObserver === 'undefined') {
  globalThis.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
}

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock form context
vi.mock('@/contexts/FormContext', () => ({
  useFormContext: () => ({
    setFieldValue: vi.fn(),
    values: {},
  }),
}));

// Mock form settings context
vi.mock('@/contexts/FormSettingsContext', async (importOriginal) => {
  const actual: any = await importOriginal();
  return {
    ...actual,
    useFormSettings: () => ({
      appearance: {
        defaultSettings: {
          inputStyle: InputStyle.Classic,
          color: '#000000',
        },
      },
    }),
  };
});

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

vi.mock('@/utils', () => ({
  getGridColsByLayout: vi.fn((layout) => {
    if (layout === '2-columns') return 6;
    if (layout === '3-columns') return 4;
    return 12;
  }),
  getOtherAnswerValue: vi.fn((value) => `Other: ${value}`),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      const translations = {
        canSelectMultipleOptions: 'You can select multiple options',
        otherOptionLabel: 'Other',
      };
      return translations[key] || key;
    },
  }),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        buttonText: 'Submit another response',
        buttonUrl: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  status: 'active',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  createdBy: 'test-user',
  updatedBy: 'test-user',
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
  metadata: {
    version: '1.0.0',
    lastModified: '2023-01-01T00:00:00Z',
  },
  permissions: {
    canEdit: true,
    canDelete: true,
    canShare: true,
  },
  urls: {
    edit: '/edit',
    view: '/view',
    share: '/share',
  },
  tags: [],
  category: 'general',
  isPublic: true,
  isArchived: false,
  expiredAt: null,
  isFavorited: false,
  isPinned: false,
  responses: {
    total: 0,
    lastResponse: null,
  },
};

describe('CheckboxField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders checkbox options correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
        { label: 'Option 3', value: 'option3' },
      ],
      required: true,
      validators: [],
    };

    renderWithProviders(<CheckboxField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
    expect(screen.getByText('Option 3')).toBeInTheDocument();
  });

  it('renders helper text when showHelperText is true', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [{ label: 'Option 1', value: 'option1' }],
      validators: [],
    };

    renderWithProviders(<CheckboxField field={field} showHelperText={true} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('You can select multiple options')).toBeInTheDocument();
  });

  it('does not render helper text when showHelperText is false', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [{ label: 'Option 1', value: 'option1' }],
      validators: [],
    };

    renderWithProviders(<CheckboxField field={field} showHelperText={false} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.queryByText('You can select multiple options')).not.toBeInTheDocument();
  });

  it('renders "other" option when isOther is true', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [{ label: 'Option 1', value: 'option1' }],
      isOther: true,
      validators: [],
    };

    renderWithProviders(<CheckboxField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Other')).toBeInTheDocument();
  });

  it('does not render "other" option when isOther is false', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [{ label: 'Option 1', value: 'option1' }],
      isOther: false,
      validators: [],
    };

    renderWithProviders(<CheckboxField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.queryByText('Other')).not.toBeInTheDocument();
  });

  it('handles checkbox selection correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ],
      validators: [],
    };

    renderWithProviders(<CheckboxField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes).toHaveLength(2);

    // Verify that checkboxes are clickable
    const option1Checkbox = checkboxes[0];
    const option2Checkbox = checkboxes[1];

    expect(option1Checkbox).not.toBeChecked();
    expect(option2Checkbox).not.toBeChecked();

    // Test that checkboxes can be clicked (UI interaction)
    fireEvent.click(option1Checkbox);
    fireEvent.click(option2Checkbox);

    // Verify the checkboxes are rendered with correct labels
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
  });

  it('handles "other" option selection and input', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [{ label: 'Option 1', value: 'option1' }],
      isOther: true,
      validators: [],
    };

    renderWithProviders(<CheckboxField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    const checkboxes = screen.getAllByRole('checkbox');
    const otherCheckbox = checkboxes[1]; // Second checkbox is the "Other" option
    const otherInput = screen.getByRole('textbox');

    expect(otherCheckbox).not.toBeChecked();
    expect(otherInput).toBeInTheDocument();

    // Test that the other checkbox can be clicked
    fireEvent.click(otherCheckbox);

    // Test that the other input can receive text
    fireEvent.change(otherInput, { target: { value: 'Custom answer' } });
    expect(otherInput).toHaveValue('Custom answer');

    // Verify the "Other" option is rendered
    expect(screen.getByText('Other')).toBeInTheDocument();
  });

  it('applies correct grid layout based on field layout', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ],
      layout: '2-columns',
      validators: [],
    };

    renderWithProviders(<CheckboxField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // The grid should be rendered with the correct layout
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
    expect(screen.getAllByRole('checkbox')).toHaveLength(2);
  });

  it('renders with custom class names', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [{ label: 'Option 1', value: 'option1' }],
      validators: [],
    };

    const customClassNames = {
      input: 'custom-input-class',
      label: 'custom-label-class',
    };

    renderWithProviders(<CheckboxField field={field} classNames={customClassNames} />, {
      formSettings: mockFormSettings as any,
    });

    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).toHaveClass('custom-input-class');
  });

  it('handles empty options array', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Checkbox,
      label: 'Test Checkbox',
      options: [],
      validators: [],
    };

    renderWithProviders(<CheckboxField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // Should render without errors even with no options
    expect(screen.getByText('You can select multiple options')).toBeInTheDocument();
    expect(screen.queryAllByRole('checkbox')).toHaveLength(0);
  });
});
