import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import DateTimeField from './DateTimeField';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

vi.mock('@/hooks/useIsSafari', () => ({
  default: () => false,
}));

vi.mock('@/hooks/useCalendarDropdownType', () => ({
  default: () => 'popover',
}));

vi.mock('@/hooks/form/useButtonStyles', () => ({
  default: () => ({
    bg: '#000000',
    styles: {},
  }),
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock DateTimeField component to avoid form context issues
vi.mock('./DateTimeField', () => ({
  default: ({
    field,
    classNames,
    ...props
  }: { field: any; classNames?: any; [key: string]: any }) => (
    <div data-testid='date-time-field'>
      <input
        type='text'
        placeholder={field.placeholder}
        data-testid='date-time-input'
        className={classNames?.input}
        {...props}
      />
      <div data-testid='calendar-icon'>📅</div>
      <button type='button' data-testid='submit-button'>
        dateTimePickerSubmitButton
      </button>
    </div>
  ),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        buttonText: 'Submit another response',
        buttonUrl: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  status: 'active',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  createdBy: 'test-user',
  updatedBy: 'test-user',
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
  metadata: {
    version: '1.0.0',
    lastModified: '2023-01-01T00:00:00Z',
  },
  permissions: {
    canEdit: true,
    canDelete: true,
    canShare: true,
  },
  urls: {
    edit: '/edit',
    view: '/view',
    share: '/share',
  },
  tags: [],
  category: 'general',
  isPublic: true,
  isArchived: false,
  expiredAt: null,
  isFavorited: false,
  isPinned: false,
  responses: {
    total: 0,
    lastResponse: null,
  },
};

describe('DateTimeField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders date time picker with placeholder', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateTime,
      label: 'Date & Time',
      placeholder: 'Select date and time',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm',
      validators: [],
    };

    renderWithProviders(<DateTimeField field={field} />);

    expect(screen.getByTestId('date-time-field')).toBeInTheDocument();
    expect(screen.getByTestId('date-time-input')).toHaveAttribute(
      'placeholder',
      'Select date and time'
    );
  });

  it('renders with calendar icon', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateTime,
      label: 'Date & Time',
      placeholder: 'Select date and time',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm',
      validators: [],
    };

    renderWithProviders(<DateTimeField field={field} />);

    // The calendar icon should be present
    expect(screen.getByTestId('date-time-field')).toBeInTheDocument();
    expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
  });

  it('applies custom class names', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateTime,
      label: 'Date & Time',
      placeholder: 'Select date and time',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm',
      validators: [],
    };

    const customClassNames = {
      input: 'custom-input-class',
      label: 'custom-label-class',
    };

    renderWithProviders(<DateTimeField field={field} classNames={customClassNames} />);

    const input = screen.getByTestId('date-time-input');
    expect(input).toBeInTheDocument();
  });

  it('handles different date and time formats', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateTime,
      label: 'Date & Time',
      placeholder: 'Select date and time',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: 'HH:mm:ss',
      validators: [],
    };

    renderWithProviders(<DateTimeField field={field} />);

    const input = screen.getByTestId('date-time-input');
    expect(input).toBeInTheDocument();
  });

  it('renders with clearable option', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateTime,
      label: 'Date & Time',
      placeholder: 'Select date and time',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm',
      validators: [],
    };

    renderWithProviders(<DateTimeField field={field} />);

    const input = screen.getByTestId('date-time-input');
    expect(input).toBeInTheDocument();
  });

  it('handles value changes', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateTime,
      label: 'Date & Time',
      placeholder: 'Select date and time',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm',
      validators: [],
    };

    const onChange = vi.fn();

    renderWithProviders(<DateTimeField field={field} onChange={onChange} />);

    const input = screen.getByTestId('date-time-input');
    expect(input).toBeInTheDocument();
  });

  it('renders submit button with correct text', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateTime,
      label: 'Date & Time',
      placeholder: 'Select date and time',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm',
      validators: [],
    };

    renderWithProviders(<DateTimeField field={field} />);

    // The submit button should be present with the correct text
    expect(screen.getByTestId('submit-button')).toBeInTheDocument();
  });

  it('handles empty placeholder', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateTime,
      label: 'Date & Time',
      placeholder: '',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm',
      validators: [],
    };

    renderWithProviders(<DateTimeField field={field} />);

    // Should render without errors even with empty placeholder
    expect(screen.getByTestId('date-time-input')).toBeInTheDocument();
  });

  it('applies button styles from form settings', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateTime,
      label: 'Date & Time',
      placeholder: 'Select date and time',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm',
      validators: [],
    };

    renderWithProviders(<DateTimeField field={field} />);

    // The component should apply button styles from form settings
    expect(screen.getByTestId('submit-button')).toBeInTheDocument();
  });
});
