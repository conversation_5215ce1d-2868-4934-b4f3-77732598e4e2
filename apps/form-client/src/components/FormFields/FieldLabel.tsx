import { useFormSettings } from '@/contexts/FormSettingsContext';
import { Text, type TextProps } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import DOMPurify from 'isomorphic-dompurify';
import { useRouter } from 'next/router';

interface FieldLabelProps extends TextProps {
  label: string;
}
const useStyles = createStyles(() => ({
  label: {
    '& p': {
      margin: 0,
    },
  },
}));

const FieldLabel = ({ label, ...props }: FieldLabelProps) => {
  const router = useRouter();
  const { classes } = useStyles();
  const { hiddenFieldsMap } = useFormSettings();

  const getLabel = (label: string) => {
    if (!label) return '';
    return label.replace(/<span[^>]*data-id="([^"]*)"[^>]*>.*?<\/span>/g, (_, id) => {
      const urlParam = hiddenFieldsMap?.[id];
      if (router.query[urlParam]) {
        return DOMPurify.sanitize(router.query[urlParam] as string);
      }
      return '';
    });
  };

  return (
    <Text
      {...props}
      className={classes.label}
      dangerouslySetInnerHTML={{ __html: getLabel(label) }}
    />
  );
};

export default FieldLabel;
