import { FieldType } from '@/types/form-builder';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import DateRangeField from './DateRangeField';

// Mock dependencies
vi.mock('@/hooks/useCalendarDropdownType', () => ({
  default: () => 'popover',
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock Mantine DatePickerInput to avoid complex date picker interactions
vi.mock('@mantine/dates', () => ({
  DatePickerInput: ({
    placeholder,
    value,
    onChange,
    disabled,
    required,
    error,
    leftSection,
    clearable,
    valueFormat,
    dropdownType,
    type,
    ...props
  }: any) => (
    <div data-testid='date-range-picker-input'>
      <input
        type='text'
        placeholder={placeholder}
        value={value || ''}
        onChange={(e) => onChange?.(e.target.value)}
        disabled={disabled}
        required={required}
        data-testid='date-range-input'
        data-type={type}
        {...props}
      />
      {leftSection && <div data-testid='calendar-icon'>{leftSection}</div>}
      {clearable && (
        <button type='button' data-testid='clear-button'>
          Clear
        </button>
      )}
      {error && <div data-testid='error-message'>{error}</div>}
    </div>
  ),
}));

describe('DateRangeField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders date range picker with placeholder', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} placeholder='Choose date range' />);

    expect(screen.getByTestId('date-range-picker-input')).toBeInTheDocument();
    expect(screen.getByTestId('date-range-input')).toHaveAttribute(
      'placeholder',
      'Choose date range'
    );
  });

  it('renders with calendar icon', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} />);

    // The calendar icon should be present
    expect(screen.getByTestId('date-range-picker-input')).toBeInTheDocument();
    expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
  });

  it('applies custom class names', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} className='custom-input-class' />);

    const input = screen.getByTestId('date-range-input');
    expect(input).toHaveClass('custom-input-class');
  });

  it('handles different date formats', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'DD/MM/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} />);

    const input = screen.getByTestId('date-range-input');
    expect(input).toBeInTheDocument();
  });

  it('renders with clearable option', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} />);

    // DateRangeField should be clearable by default
    expect(screen.getByTestId('clear-button')).toBeInTheDocument();
  });

  it('handles value changes', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    const onChange = vi.fn();

    renderWithProviders(<DateRangeField field={field} onChange={onChange} />);

    const input = screen.getByTestId('date-range-input');
    fireEvent.change(input, { target: { value: '2023-12-25 - 2023-12-31' } });

    expect(onChange).toHaveBeenCalledWith('2023-12-25 - 2023-12-31');
  });

  it('handles empty placeholder', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: '',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} />);

    // Should render without errors even with empty placeholder
    expect(screen.getByTestId('date-range-input')).toBeInTheDocument();
  });

  it('handles disabled state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} disabled />);

    const input = screen.getByTestId('date-range-input');
    expect(input).toBeDisabled();
  });

  it('handles required state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} required />);

    const input = screen.getByTestId('date-range-input');
    expect(input).toBeRequired();
  });

  it('handles error state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} error='Invalid date range' />);

    expect(screen.getByTestId('error-message')).toHaveTextContent('Invalid date range');
  });

  it('passes field dateFormat to DatePickerInput', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'DD/MM/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} />);

    expect(screen.getByTestId('date-range-picker-input')).toBeInTheDocument();
  });

  it('uses popover dropdown type from hook', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} />);

    expect(screen.getByTestId('date-range-picker-input')).toBeInTheDocument();
  });

  it('renders with range type', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.DateRange,
      label: 'Select Date Range',
      placeholder: 'Choose date range',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateRangeField field={field} />);

    const input = screen.getByTestId('date-range-input');
    expect(input).toHaveAttribute('data-type', 'range');
  });
});
