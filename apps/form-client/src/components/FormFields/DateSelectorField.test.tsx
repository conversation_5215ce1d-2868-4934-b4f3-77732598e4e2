import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import DateSelectorField from './DateSelectorField';

// Mock ResizeObserver for Mantine ScrollArea
if (typeof globalThis.ResizeObserver === 'undefined') {
  globalThis.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
}

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock form context
vi.mock('@/contexts/FormContext', () => ({
  useFormContext: () => ({
    setFieldValue: vi.fn(),
    getInputProps: () => ({
      value: '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
      error: undefined,
    }),
    errors: {},
  }),
}));

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations = {
        year: 'Year',
        month: 'Month',
        day: 'Day',
      };
      return translations[key] || key;
    },
  }),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        buttonText: 'Submit another response',
        buttonUrl: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  status: 'active',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  createdBy: 'test-user',
  updatedBy: 'test-user',
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
  metadata: {
    version: '1.0.0',
    lastModified: '2023-01-01T00:00:00Z',
  },
  permissions: {
    canEdit: true,
    canDelete: true,
    canShare: true,
  },
  urls: {
    edit: '/edit',
    view: '/view',
    share: '/share',
  },
  tags: [],
  category: 'general',
  isPublic: true,
  isArchived: false,
  expiredAt: null,
  isFavorited: false,
  isPinned: false,
  responses: {
    total: 0,
    lastResponse: null,
  },
};

describe('DateSelectorField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders date selector with dropdown mode correctly', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Select Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: true,
      inputDirectlyEnabled: false,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Select Date'
        required={true}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Select Date')).toBeInTheDocument();
    expect(screen.getByText('Year')).toBeInTheDocument();
    expect(screen.getByText('Month')).toBeInTheDocument();
    expect(screen.getByText('Day')).toBeInTheDocument();
    expect(screen.getAllByText('*')).toHaveLength(4); // Main label + Year + Month + Day
  });

  it('renders date selector with input mode correctly', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Enter Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: false,
      inputDirectlyEnabled: true,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Enter Date'
        required={false}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Enter Date')).toBeInTheDocument();
    expect(screen.queryByText('*')).not.toBeInTheDocument();
    expect(screen.getByPlaceholderText('YYYY')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('MM')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('DD')).toBeInTheDocument();
  });

  it('handles year selection in dropdown mode', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Select Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: true,
      inputDirectlyEnabled: false,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Select Date'
        required={true}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Year')).toBeInTheDocument();
    expect(screen.getByText('Month')).toBeInTheDocument();
    expect(screen.getByText('Day')).toBeInTheDocument();

    // Verify that the year dropdown has the current year selected
    const yearInputs = screen.getAllByDisplayValue('2025');
    expect(yearInputs.length).toBeGreaterThan(0);

    // Verify that the visible year input has the correct placeholder
    const visibleYearInput = screen.getByPlaceholderText('YYYY');
    expect(visibleYearInput).toHaveValue('2025');
  });

  it('handles month selection in dropdown mode', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Select Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: true,
      inputDirectlyEnabled: false,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Select Date'
        required={true}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Month')).toBeInTheDocument();
  });

  it('handles day selection in dropdown mode', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Select Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: true,
      inputDirectlyEnabled: false,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Select Date'
        required={true}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Day')).toBeInTheDocument();
  });

  it('handles year input in direct input mode', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Enter Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: false,
      inputDirectlyEnabled: true,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Enter Date'
        required={false}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    const yearInput = screen.getByPlaceholderText('YYYY');
    fireEvent.change(yearInput, { target: { value: '2023' } });

    expect(yearInput).toHaveValue(2023);
  });

  it('handles month input in direct input mode', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Enter Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: false,
      inputDirectlyEnabled: true,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Enter Date'
        required={false}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    const monthInput = screen.getByPlaceholderText('MM');
    fireEvent.change(monthInput, { target: { value: '12' } });

    expect(monthInput).toHaveValue(12);
  });

  it('handles day input in direct input mode', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Enter Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: false,
      inputDirectlyEnabled: true,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Enter Date'
        required={false}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    const dayInput = screen.getByPlaceholderText('DD');
    fireEvent.change(dayInput, { target: { value: '25' } });

    expect(dayInput).toHaveValue(25);
  });

  it('handles different date formats', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Select Date',
      dateFormat: 'MM/DD/YYYY',
      fixedDateTitleEnabled: true,
      inputDirectlyEnabled: false,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Select Date'
        required={true}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Month')).toBeInTheDocument();
    expect(screen.getByText('Day')).toBeInTheDocument();
    expect(screen.getByText('Year')).toBeInTheDocument();
  });

  it('handles date of birth mode', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Date of Birth',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: true,
      inputDirectlyEnabled: false,
      dobEnabled: true,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Date of Birth'
        required={true}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Date of Birth')).toBeInTheDocument();
  });

  it('handles disabled fixed date title', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Select Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: false,
      inputDirectlyEnabled: false,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Select Date'
        required={true}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Select Date')).toBeInTheDocument();
    expect(screen.getAllByText('*')).toHaveLength(1); // Only main label
  });

  it('handles empty date format', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Select Date',
      dateFormat: '',
      fixedDateTitleEnabled: true,
      inputDirectlyEnabled: false,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Select Date'
        required={true}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Select Date')).toBeInTheDocument();
  });

  it('handles form errors', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Select Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: true,
      inputDirectlyEnabled: false,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Select Date'
        required={true}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Select Date')).toBeInTheDocument();
  });

  it('handles input blur with padding for month', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Enter Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: false,
      inputDirectlyEnabled: true,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Enter Date'
        required={false}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    const monthInput = screen.getByPlaceholderText('MM');
    fireEvent.change(monthInput, { target: { value: '5' } });
    fireEvent.blur(monthInput);

    expect(monthInput).toHaveValue(5);
  });

  it('handles input blur with padding for day', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Enter Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: false,
      inputDirectlyEnabled: true,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Enter Date'
        required={false}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    const dayInput = screen.getByPlaceholderText('DD');
    fireEvent.change(dayInput, { target: { value: '8' } });
    fireEvent.blur(dayInput);

    expect(dayInput).toHaveValue(8);
  });

  it('handles year validation on blur', () => {
    const field = {
      id: 'date-field',
      name: 'date',
      type: FieldType.Date,
      label: 'Enter Date',
      dateFormat: 'YYYY/MM/DD',
      fixedDateTitleEnabled: false,
      inputDirectlyEnabled: true,
      dobEnabled: false,
      validators: [],
    };

    renderWithProviders(
      <DateSelectorField
        field={field}
        label='Enter Date'
        required={false}
        classNames={{
          error: 'error-class',
          label: 'label-class',
        }}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    const yearInput = screen.getByPlaceholderText('YYYY');
    fireEvent.change(yearInput, { target: { value: '1700' } });
    fireEvent.blur(yearInput);

    // The component has a minimum year of 1800, so 1700 should be corrected to 1800
    expect(yearInput).toHaveValue(1800);
  });
});
