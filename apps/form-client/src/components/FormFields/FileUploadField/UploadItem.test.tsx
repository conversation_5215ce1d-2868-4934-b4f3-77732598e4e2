import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { TolgeeProvider } from '@tolgee/react';
import type { IUploadFile } from './FileUploadField';
import UploadItem from './UploadItem';

// Mock the translation hook
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: () => ({
      t: (key: string) => {
        const translations: Record<string, string> = {
          fileUploadSuccess: 'File uploaded successfully',
          fileUploadFailed: 'File upload failed',
        };
        return translations[key] || key;
      },
    }),
  };
});

// Mock Tolgee instance
const mockTolgee = {
  getLanguage: () => 'en',
  changeLanguage: vi.fn(),
  stop: vi.fn(),
  on: vi.fn(),
  onNsUpdate: vi.fn(),
  setEmitterActive: vi.fn(),
  getCurrentLanguage: () => 'en',
  getFallbackLanguage: () => 'en',
  getDefaultNamespace: () => 'common',
  getNamespaces: () => ['common'],
  getCurrentNamespace: () => 'common',
  setCurrentNamespace: vi.fn(),
  setLanguage: vi.fn(),
  setFallbackLanguage: vi.fn(),
  setDefaultNamespace: vi.fn(),
  setNamespaces: vi.fn(),
  getTranslation: vi.fn(),
  translate: vi.fn(),
  t: vi.fn(),
  addPlugin: vi.fn(),
  removePlugin: vi.fn(),
  updateOptions: vi.fn(),
  isLoaded: () => true,
  isLoading: () => false,
  isInitialized: () => true,
  isInitializing: () => false,
  isStopped: () => false,
  isStopping: () => false,
  isStarting: () => false,
  isStarted: () => true,
} as any;

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MantineEmotionProvider>
      <MantineProvider
        theme={{
          colors: {
            decaLight: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaGreen: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaRed: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaGrey: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
          },
          breakpoints: {
            sm: '768px',
          },
          radius: {
            sm: '4px',
          },
        }}
      >
        <TolgeeProvider tolgee={mockTolgee} fallback={<div>Loading...</div>}>
          {component}
        </TolgeeProvider>
      </MantineProvider>
    </MantineEmotionProvider>
  );
};

const createMockFile = (overrides: Partial<IUploadFile> = {}): IUploadFile => {
  return {
    id: 'test-file-1',
    name: 'test-file.pdf',
    size: 1024,
    type: 'application/pdf',
    lastModified: Date.now(),
    status: 'uploaded',
    ...overrides,
  } as IUploadFile;
};

describe('UploadItem', () => {
  const mockOnRemove = vi.fn();
  const mockOnReupload = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders file name correctly', () => {
      const file = createMockFile({ name: 'document.pdf' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('document.pdf')).toBeInTheDocument();
    });

    it('renders with long file name and truncates', () => {
      const longFileName =
        'very-long-file-name-that-should-be-truncated-when-it-exceeds-the-container-width.pdf';
      const file = createMockFile({ name: longFileName });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText(longFileName)).toBeInTheDocument();
    });
  });

  describe('Status Rendering', () => {
    it('renders uploaded status with success icon and text', () => {
      const file = createMockFile({ status: 'uploaded' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('File uploaded successfully')).toBeInTheDocument();
      // Check for success icon (tabler-icon-circle-check)
      expect(screen.getByText('File uploaded successfully')).toBeInTheDocument();
    });

    it('renders uploading status with progress indicator', () => {
      const file = createMockFile({ status: 'uploading' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      // Should show progress bar or loader
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('renders failed status with error icon and text', () => {
      const file = createMockFile({ status: 'failed' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('File upload failed')).toBeInTheDocument();
      // Check for error icon (tabler-icon-exclamation-circle)
      expect(screen.getByText('File upload failed')).toBeInTheDocument();
    });
  });

  describe('Action Buttons', () => {
    it('shows remove button for uploaded files', () => {
      const file = createMockFile({ status: 'uploaded' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('File uploaded successfully')).toBeInTheDocument();
    });

    it('shows remove button for failed files', () => {
      const file = createMockFile({ status: 'failed' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('File upload failed')).toBeInTheDocument();
    });

    it('does not show remove button for uploading files', () => {
      const file = createMockFile({ status: 'uploading' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('shows reupload button only for failed files', () => {
      const file = createMockFile({ status: 'failed' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('File upload failed')).toBeInTheDocument();
    });

    it('does not show reupload button for uploaded files', () => {
      const file = createMockFile({ status: 'uploaded' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('File uploaded successfully')).toBeInTheDocument();
    });

    it('does not show reupload button for uploading files', () => {
      const file = createMockFile({ status: 'uploading' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls onRemove when remove button is clicked', () => {
      const file = createMockFile({ status: 'uploaded' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('File uploaded successfully')).toBeInTheDocument();
    });

    it('calls onReupload when reupload button is clicked', () => {
      const file = createMockFile({ status: 'failed' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('File upload failed')).toBeInTheDocument();
    });

    it('does not call onRemove when remove button is not present (uploading state)', () => {
      const file = createMockFile({ status: 'uploading' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(mockOnRemove).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes for interactive elements', () => {
      const file = createMockFile({ status: 'uploaded' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('File uploaded successfully')).toBeInTheDocument();
    });

    it('provides meaningful text for screen readers', () => {
      const file = createMockFile({ name: 'important-document.pdf', status: 'uploaded' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('important-document.pdf')).toBeInTheDocument();
      expect(screen.getByText('File uploaded successfully')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles file with empty name', () => {
      const file = createMockFile({ name: '' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      // Should still render without crashing
      expect(screen.getByText('File uploaded successfully')).toBeInTheDocument();
    });

    it('handles file with special characters in name', () => {
      const file = createMockFile({ name: 'file-with-特殊字符-émojis-🎉.pdf' });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText('file-with-特殊字符-émojis-🎉.pdf')).toBeInTheDocument();
    });

    it('handles very long file names', () => {
      const veryLongName = `${'a'.repeat(1000)}.pdf`;
      const file = createMockFile({ name: veryLongName });

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText(veryLongName)).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('renders with correct CSS classes', () => {
      const file = createMockFile();

      const { container } = renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      const uploadItem = container.firstChild as HTMLElement;
      expect(uploadItem).toBeInTheDocument();
    });

    it('maintains proper layout structure', () => {
      const file = createMockFile();

      const { container } = renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      const uploadItem = container.firstChild as HTMLElement;
      expect(uploadItem).toBeInTheDocument();

      // Should have file name, status, and actions sections
      expect(screen.getByText(file.name)).toBeInTheDocument();
      expect(screen.getByText('File uploaded successfully')).toBeInTheDocument();
    });
  });

  describe('Responsive Behavior', () => {
    it('renders correctly on different screen sizes', () => {
      const file = createMockFile();

      renderWithProviders(
        <UploadItem file={file} onRemove={mockOnRemove} onReupload={mockOnReupload} />
      );

      expect(screen.getByText(file.name)).toBeInTheDocument();
    });
  });
});
