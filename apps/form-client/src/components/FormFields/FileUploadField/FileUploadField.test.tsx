import { AppContextProvider } from '@/contexts/AppContext';
import { FormProvider } from '@/contexts/FormContext';
import { FormSettingsProvider } from '@/contexts/FormSettingsContext';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import FileUploadField from './FileUploadField';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

// Mock Tolgee useTranslate hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => children,
}));

const mockFormSettings = {
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: 'default',
    },
    formFieldStyle: {},
  },
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MantineEmotionProvider>
      <MantineProvider
        theme={{
          colors: {
            decaLight: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaNavy: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaDark: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaGrey: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
          },
          breakpoints: {
            sm: '768px',
          },
        }}
      >
        <AppContextProvider>
          <FormSettingsProvider formSettings={mockFormSettings as any} hiddenFields={[]}>
            <FormProvider form={{} as any}>{component}</FormProvider>
          </FormSettingsProvider>
        </AppContextProvider>
      </MantineProvider>
    </MantineEmotionProvider>
  );
};

describe('FileUploadField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders file upload field correctly', () => {
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      description: 'Please upload your files',
      required: true,
      maxFiles: 5,
      maxSize: 10 * 1024 * 1024, // 10MB
      acceptedTypes: ['image/*', 'application/pdf'],
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} />);

    expect(screen.getByText('fileUploadButton')).toBeInTheDocument();
    expect(screen.getByText('fileUploadSizeHelpText')).toBeInTheDocument();
  });

  it('shows required indicator for required fields', () => {
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      required: true,
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} />);

    expect(screen.getByText('fileUploadButton')).toBeInTheDocument();
  });

  it('displays file size limit information', () => {
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      maxSize: 5 * 1024 * 1024, // 5MB
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} />);

    expect(screen.getByText('fileUploadSizeHelpText')).toBeInTheDocument();
  });

  it('displays file count limit information', () => {
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      maxFiles: 3,
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} />);

    expect(screen.getByText('fileUploadSizeHelpText')).toBeInTheDocument();
  });

  it('renders upload button', () => {
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      maxFiles: 1,
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} />);

    expect(screen.getByText('fileUploadButton')).toBeInTheDocument();
  });

  it('renders with different field configurations', () => {
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      maxSize: 1024, // 1KB
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} />);

    expect(screen.getByText('fileUploadButton')).toBeInTheDocument();
  });

  it('renders with file count limit', () => {
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      maxFiles: 1,
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} />);

    expect(screen.getByText('fileUploadButton')).toBeInTheDocument();
  });

  it('renders with accepted file types', () => {
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      acceptedTypes: ['image/*'],
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} />);

    expect(screen.getByText('fileUploadButton')).toBeInTheDocument();
  });

  it('renders with onChange callback', () => {
    const onChange = vi.fn();
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      maxFiles: 1,
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} onChange={onChange} />);

    expect(screen.getByText('fileUploadButton')).toBeInTheDocument();
  });

  it('renders with large file support', () => {
    const field = {
      id: 'file-upload',
      name: 'file-upload',
      type: 'file_upload',
      label: 'Upload Files',
      maxFiles: 1,
      validators: [],
    } as any;

    renderWithProviders(<FileUploadField field={field} />);

    expect(screen.getByText('fileUploadButton')).toBeInTheDocument();
  });
});
