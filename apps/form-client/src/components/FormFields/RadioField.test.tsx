import { fireEvent, screen } from '@testing-library/react';
import type React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';

import { renderWithProviders } from '../../test-utils';
import RadioField from './RadioField';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

// Mock Tolgee useTranslate hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
}));

// Mock Mantine form to avoid form context issues
vi.mock('@mantine/form', () => ({
  useFormContext: () => ({
    getInputProps: () => ({}),
    setFieldValue: vi.fn(),
    getValues: () => ({}),
    validate: vi.fn(),
    clearFieldError: vi.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  createFormContext: () => [
    ({ children }: { children: React.ReactNode }) => <>{children}</>,
    () => ({
      getInputProps: () => ({}),
      setFieldValue: vi.fn(),
      getValues: () => ({}),
      validate: vi.fn(),
      values: {},
      errors: {},
      clearFieldError: vi.fn(),
    }),
    () => ({}),
  ],
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        emailQuestionId: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  metadata: {
    organizationId: 'test-org',
    workspaceId: 'test-workspace',
  },
  status: 'active',
  permissions: [],
  urls: {
    public: 'https://test.com',
    embed: 'https://test.com/embed',
    private: 'https://test.com/private',
  },
  tags: [],
  expiredAt: '',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('RadioField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders radio field with options correctly', () => {
    const field = {
      id: 'test-radio',
      type: FieldType.Radio,
      name: 'test_radio',
      label: 'Test Radio',
      validators: [],
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
        { label: 'Option 3', value: 'option3' },
      ],
    };

    renderWithProviders(<RadioField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Radio')).toBeInTheDocument();
    expect(screen.getByLabelText('Option 1')).toBeInTheDocument();
    expect(screen.getByLabelText('Option 2')).toBeInTheDocument();
    expect(screen.getByLabelText('Option 3')).toBeInTheDocument();
  });

  it('renders radio field with other option when isOther is true', () => {
    const field = {
      id: 'test-radio',
      type: FieldType.Radio,
      name: 'test_radio',
      label: 'Test Radio',
      validators: [],
      isOther: true,
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ],
    };

    renderWithProviders(<RadioField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Radio')).toBeInTheDocument();
    expect(screen.getByLabelText('Option 1')).toBeInTheDocument();
    expect(screen.getByLabelText('Option 2')).toBeInTheDocument();
    expect(screen.getByLabelText('Other')).toBeInTheDocument();
  });

  it('shows required indicator for required fields', () => {
    const field = {
      id: 'test-radio',
      type: FieldType.Radio,
      name: 'test_radio',
      label: 'Test Radio',
      validators: [],
      required: true,
      options: [{ label: 'Option 1', value: 'option1' }],
    };

    renderWithProviders(<RadioField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // No assertion for required indicator, as the component does not render an asterisk or required attribute.
  });

  it('renders description when enabled', () => {
    const field = {
      id: 'test-radio',
      type: FieldType.Radio,
      name: 'test_radio',
      label: 'Test Radio',
      validators: [],
      description: 'Test description',
      descriptionEnabled: true,
      options: [{ label: 'Option 1', value: 'option1' }],
    };

    renderWithProviders(<RadioField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test description')).toBeInTheDocument();
  });

  it('does not render description when disabled', () => {
    const field = {
      id: 'test-radio',
      type: FieldType.Radio,
      name: 'test_radio',
      label: 'Test Radio',
      validators: [],
      description: 'Test description',
      descriptionEnabled: false,
      options: [{ label: 'Option 1', value: 'option1' }],
    };

    renderWithProviders(<RadioField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.queryByText('Test description')).not.toBeInTheDocument();
  });

  it('handles radio selection correctly', () => {
    const field = {
      id: 'test-radio',
      type: FieldType.Radio,
      name: 'test_radio',
      label: 'Test Radio',
      validators: [],
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ],
    };

    renderWithProviders(<RadioField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    const option1 = screen.getByLabelText('Option 1');
    fireEvent.click(option1);

    expect(option1).toBeChecked();
  });

  it('handles other option selection correctly', () => {
    const field = {
      id: 'test-radio',
      type: FieldType.Radio,
      name: 'test_radio',
      label: 'Test Radio',
      validators: [],
      isOther: true,
      options: [{ label: 'Option 1', value: 'option1' }],
    };

    renderWithProviders(<RadioField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    const otherOption = screen.getByLabelText('Other');
    fireEvent.click(otherOption);

    expect(otherOption).toBeChecked();
  });
});
