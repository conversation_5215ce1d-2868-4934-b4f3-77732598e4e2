import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import RatingField from './RatingField';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

// Mock FormContext to avoid form context issues
vi.mock('@/contexts/FormContext', () => ({
  useFormContext: () => ({
    setFieldValue: vi.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        emailQuestionId: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  metadata: {
    organizationId: 'test-org',
    workspaceId: 'test-workspace',
  },
  status: 'active',
  permissions: [],
  urls: {
    public: 'https://test.com',
    embed: 'https://test.com/embed',
    private: 'https://test.com/private',
  },
  tags: [],
  expiredAt: '',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('RatingField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders star rating by default', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // Should render 5 star rating icons
    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: '5' })).toBeInTheDocument();
  });

  it('renders heart rating when shape is heart', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'heart',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });

  it('renders like rating when shape is like', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'like',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });

  it('renders crown rating when shape is crown', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'crown',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });

  it('renders smiley rating when shape is smiley', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'smiley',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });

  it('shows numbers when showNumber is true', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: true,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // Should show numbers 1-5
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('does not show numbers when showNumber is false', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // Should not show numbers
    expect(screen.queryByText('1')).not.toBeInTheDocument();
    expect(screen.queryByText('2')).not.toBeInTheDocument();
    expect(screen.queryByText('3')).not.toBeInTheDocument();
    expect(screen.queryByText('4')).not.toBeInTheDocument();
    expect(screen.queryByText('5')).not.toBeInTheDocument();
  });

  it('handles different max scale values', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 10,
      shape: 'star',
      showNumber: true,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // Should show numbers 1-10
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
  });

  it('applies custom class names', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: false,
      validators: [],
    };

    const customClassNames = {
      input: 'custom-input-class',
      label: 'custom-label-class',
    };

    renderWithProviders(<RatingField field={field} classNames={customClassNames} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });

  it('handles error state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} error='Please provide a rating' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Please provide a rating')).toBeInTheDocument();
  });

  it('handles description', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} description='Please rate from 1 to 5' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Please rate from 1 to 5')).toBeInTheDocument();
  });

  it('handles withAsterisk prop', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} withAsterisk />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });

  it('handles required state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} required />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });

  it('handles disabled state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });

  it('handles label', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: 'star',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} label='Custom Label' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Custom Label')).toBeInTheDocument();
  });

  it('handles empty shape value', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      shape: '',
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // Should default to star rating
    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });

  it('handles undefined shape value', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Rating,
      label: 'Rate this',
      maxScale: 5,
      showNumber: false,
      validators: [],
    };

    renderWithProviders(<RatingField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // Should default to star rating
    expect(screen.getByRole('radio', { name: '1' })).toBeInTheDocument();
  });
});
