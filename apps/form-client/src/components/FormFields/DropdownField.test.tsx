import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import DropdownField from './DropdownField';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock Tolgee properly
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        selectAnOption: 'Select an option',
        selectPrefecture: 'Select prefecture',
        other: 'Other',
      };
      return translations[key] || key;
    },
  }),
}));

// Remove the local mock to use the global mock from test-utils.tsx

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        emailQuestionId: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  metadata: {
    organizationId: 'test-org',
    workspaceId: 'test-workspace',
  },
  status: 'active',
  permissions: [],
  urls: {
    public: 'https://test.com',
    embed: 'https://test.com/embed',
    private: 'https://test.com/private',
  },
  tags: [],
  expiredAt: '',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('DropdownField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders dropdown field with options correctly', () => {
    const field = {
      id: 'test-dropdown',
      type: FieldType.Dropdown,
      name: 'test_dropdown',
      label: 'Test Dropdown',
      validators: [],
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
        { label: 'Option 3', value: 'option3' },
      ],
    };

    renderWithProviders(<DropdownField field={field} />);

    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('selectAnOption')).toBeInTheDocument();
  });

  it('renders dropdown with other option when isOther is true', () => {
    const field = {
      id: 'test-dropdown',
      type: FieldType.Dropdown,
      name: 'test_dropdown',
      label: 'Test Dropdown',
      validators: [],
      isOther: true,
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ],
    };

    renderWithProviders(<DropdownField field={field} />);

    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
    expect(screen.getByText('other')).toBeInTheDocument();
  });

  it('renders searchable dropdown when searchable is true', () => {
    const field = {
      id: 'test-dropdown',
      type: FieldType.Dropdown,
      name: 'test_dropdown',
      label: 'Test Dropdown',
      validators: [],
      searchable: true,
      options: [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ],
    };

    renderWithProviders(<DropdownField field={field} />);

    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('selectAnOption')).toBeInTheDocument();
  });

  it('renders prefecture dropdown with correct placeholder', () => {
    const field = {
      id: 'test-dropdown',
      type: FieldType.Dropdown,
      name: 'prefecture',
      label: 'Prefecture',
      validators: [],
      options: [
        { label: 'Tokyo', value: 'tokyo' },
        { label: 'Osaka', value: 'osaka' },
      ],
    };

    renderWithProviders(<DropdownField field={field} />);

    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('selectPrefecture')).toBeInTheDocument();
  });

  it('handles empty options array', () => {
    const field = {
      id: 'test-dropdown',
      type: FieldType.Dropdown,
      name: 'test_dropdown',
      label: 'Test Dropdown',
      validators: [],
      options: [],
    };

    renderWithProviders(<DropdownField field={field} />);

    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('selectAnOption')).toBeInTheDocument();
  });

  it('passes correct props to Select component', () => {
    const field = {
      id: 'test-dropdown',
      type: FieldType.Dropdown,
      name: 'test_dropdown',
      label: 'Test Dropdown',
      validators: [],
      searchable: true,
      options: [{ label: 'Option 1', value: 'option1' }],
    };

    renderWithProviders(<DropdownField field={field} />);

    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('selectAnOption')).toBeInTheDocument();
  });
});
