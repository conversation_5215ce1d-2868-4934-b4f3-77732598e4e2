import { FieldType } from '@/types/form-builder';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import DateField from './DateField';

// Mock dependencies
vi.mock('@/hooks/useCalendarDropdownType', () => ({
  default: () => 'popover',
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock <PERSON>tine DatePickerInput to avoid complex date picker interactions
vi.mock('@mantine/dates', () => ({
  DatePickerInput: ({
    placeholder,
    value,
    onChange,
    disabled,
    required,
    error,
    leftSection,
    clearable,
    valueFormat,
    dropdownType,
    ...props
  }: any) => (
    <div data-testid='date-picker-input'>
      <input
        type='text'
        placeholder={placeholder}
        value={value || ''}
        onChange={(e) => onChange?.(e.target.value)}
        disabled={disabled}
        required={required}
        data-testid='date-input'
        {...props}
      />
      {leftSection && <div data-testid='calendar-icon'>{leftSection}</div>}
      {clearable && (
        <button type='button' data-testid='clear-button'>
          Clear
        </button>
      )}
      {error && <div data-testid='error-message'>{error}</div>}
    </div>
  ),
}));

describe('DateField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders date picker with placeholder', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} placeholder='Choose a date' />);

    expect(screen.getByTestId('date-picker-input')).toBeInTheDocument();
    expect(screen.getByTestId('date-input')).toHaveAttribute('placeholder', 'Choose a date');
  });

  it('renders with calendar icon', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} />);

    // The calendar icon should be present
    expect(screen.getByTestId('date-picker-input')).toBeInTheDocument();
    expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
  });

  it('applies custom class names', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} className='custom-input-class' />);

    const input = screen.getByTestId('date-input');
    expect(input).toHaveClass('custom-input-class');
  });

  it('handles different date formats', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'DD/MM/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} />);

    const input = screen.getByTestId('date-input');
    expect(input).toBeInTheDocument();
  });

  it('renders with clearable option', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} />);

    // DateField should be clearable by default
    expect(screen.getByTestId('clear-button')).toBeInTheDocument();
  });

  it('handles value changes', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    const onChange = vi.fn();

    renderWithProviders(<DateField field={field} onChange={onChange} />);

    const input = screen.getByTestId('date-input');
    fireEvent.change(input, { target: { value: '2023-12-25' } });

    expect(onChange).toHaveBeenCalledWith('2023-12-25');
  });

  it('handles empty placeholder', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: '',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} />);

    // Should render without errors even with empty placeholder
    expect(screen.getByTestId('date-input')).toBeInTheDocument();
  });

  it('handles disabled state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} disabled />);

    const input = screen.getByTestId('date-input');
    expect(input).toBeDisabled();
  });

  it('handles required state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} required />);

    const input = screen.getByTestId('date-input');
    expect(input).toBeRequired();
  });

  it('handles error state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} error='Invalid date' />);

    expect(screen.getByTestId('error-message')).toHaveTextContent('Invalid date');
  });

  it('passes field dateFormat to DatePickerInput', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'DD/MM/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} />);

    expect(screen.getByTestId('date-picker-input')).toBeInTheDocument();
  });

  it('uses popover dropdown type from hook', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Date,
      label: 'Select Date',
      placeholder: 'Choose a date',
      dateFormat: 'MM/DD/YYYY',
      validators: [],
    };

    renderWithProviders(<DateField field={field} />);

    expect(screen.getByTestId('date-picker-input')).toBeInTheDocument();
  });
});
