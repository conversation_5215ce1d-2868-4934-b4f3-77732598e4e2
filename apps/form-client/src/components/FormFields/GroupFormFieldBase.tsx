import { useFormSettings } from '@/contexts/FormSettingsContext';
import useFieldStyles from '@/hooks/form/useFieldStyles';
import type { FormField, GroupFormField } from '@/types/form-builder';
import { getGridColsByWidth } from '@/utils';
import { Box, Grid } from '@mantine/core';
import FieldLabel from './FieldLabel';
import FormFieldBase from './FormFieldBase';

interface GroupFormFieldProps {
  group: GroupFormField;
  // eslint-disable-next-line no-unused-vars
  onChange?: (field: FormField, value: any) => void;
}

const GroupFormFieldBase = ({ group, onChange }: GroupFormFieldProps) => {
  const { appearance } = useFormSettings();
  const { classNames } = useFieldStyles({
    customize: appearance.customize,
    inputStyle: appearance?.defaultSettings?.inputStyle,
    formFieldStyle: appearance?.formFieldStyle,
    defaultSettings: appearance?.defaultSettings,
  });

  return (
    <Box w='100%'>
      {!!group.label && <FieldLabel className={classNames.label} label={group.label} />}
      {group.descriptionEnabled && <FieldLabel mb='xs' label={group.description || ''} />}
      <Grid gutter={'xl'}>
        {(group.fields ?? [])
          .filter((field) => !field.isHide)
          .map((field, index) => {
            const gridCols = getGridColsByWidth(field.style?.width);
            return (
              <Grid.Col key={index} span={{ base: 12, sm: gridCols }}>
                <FormFieldBase field={field} onChange={onChange} />
              </Grid.Col>
            );
          })}
      </Grid>
    </Box>
  );
};

export default GroupFormFieldBase;
