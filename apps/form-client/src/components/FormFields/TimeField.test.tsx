import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import TimeField from './TimeField';

// Mock dependencies
vi.mock('@/hooks/useIsSafari', () => ({
  default: () => false,
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        emailQuestionId: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  metadata: {
    organizationId: 'test-org',
    workspaceId: 'test-workspace',
  },
  status: 'active',
  permissions: [],
  urls: {
    public: 'https://test.com',
    embed: 'https://test.com/embed',
    private: 'https://test.com/private',
  },
  tags: [],
  expiredAt: '',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('TimeField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders time input with placeholder', () => {
    renderWithProviders(<TimeField placeholder='Select time' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByPlaceholderText('Select time')).toBeInTheDocument();
  });

  it('renders with clock icon', () => {
    renderWithProviders(<TimeField placeholder='Select time' />, {
      formSettings: mockFormSettings as any,
    });

    // The clock icon should be present
    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('applies custom class names', () => {
    const customClassNames = {
      input: 'custom-input-class',
      label: 'custom-label-class',
    };

    renderWithProviders(<TimeField placeholder='Select time' classNames={customClassNames} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles value changes', () => {
    const onChange = vi.fn();

    renderWithProviders(<TimeField placeholder='Select time' onChange={onChange} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    fireEvent.change(input, { target: { value: '14:30' } });

    expect(input).toHaveValue('14:30');
  });

  it('handles empty placeholder', () => {
    renderWithProviders(<TimeField />, {
      formSettings: mockFormSettings as any,
    });

    // Should render without errors even with empty placeholder
    // TimeField sets defaultValue='00:00' for Safari, but our mock returns false for isSafari
    // so it should render without a default value
    const input = document.querySelector('input[type="time"]');
    expect(input).toBeInTheDocument();
  });

  it('applies custom styles', () => {
    const customStyles = {
      input: {
        backgroundColor: '#f0f0f0',
      },
    };

    renderWithProviders(<TimeField placeholder='Select time' styles={customStyles} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles disabled state', () => {
    renderWithProviders(<TimeField placeholder='Select time' disabled />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeDisabled();
  });

  it('handles required state', () => {
    renderWithProviders(<TimeField placeholder='Select time' required />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeRequired();
  });

  it('handles error state', () => {
    renderWithProviders(<TimeField placeholder='Select time' error='Invalid time' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Invalid time')).toBeInTheDocument();
  });

  it('handles label', () => {
    renderWithProviders(<TimeField placeholder='Select time' label='Time' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Time')).toBeInTheDocument();
  });

  it('handles description', () => {
    renderWithProviders(
      <TimeField placeholder='Select time' description='Please select a time' />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Please select a time')).toBeInTheDocument();
  });

  it('handles different time formats', () => {
    renderWithProviders(<TimeField placeholder='Select time' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles step prop', () => {
    renderWithProviders(<TimeField placeholder='Select time' step={900} />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles min and max time', () => {
    renderWithProviders(<TimeField placeholder='Select time' minTime='09:00' maxTime='17:00' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles clearable prop', () => {
    renderWithProviders(<TimeField placeholder='Select time' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles size prop', () => {
    renderWithProviders(<TimeField placeholder='Select time' size='lg' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles variant prop', () => {
    renderWithProviders(<TimeField placeholder='Select time' variant='filled' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles radius prop', () => {
    renderWithProviders(<TimeField placeholder='Select time' radius='xl' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles withAsterisk prop', () => {
    renderWithProviders(<TimeField placeholder='Select time' withAsterisk />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByPlaceholderText('Select time');
    expect(input).toBeInTheDocument();
  });

  it('handles rightSection prop', () => {
    renderWithProviders(
      <TimeField
        placeholder='Select time'
        rightSection={<div data-testid='right-section'>Right</div>}
      />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByTestId('right-section')).toBeInTheDocument();
  });

  // Note: leftSection prop test removed because TimeField always renders its own clock icon
  // and doesn't allow overriding the leftSection
});
