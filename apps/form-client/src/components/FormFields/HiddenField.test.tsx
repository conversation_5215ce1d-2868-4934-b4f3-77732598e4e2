import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import HiddenField from './HiddenField';

// Mock ResizeObserver for Mantine ScrollArea
if (typeof globalThis.ResizeObserver === 'undefined') {
  globalThis.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
}

// Mock Next.js router
const mockRouter = {
  push: vi.fn(),
  query: {},
  pathname: '/',
  asPath: '/',
  route: '/',
  back: vi.fn(),
  forward: vi.fn(),
  reload: vi.fn(),
  replace: vi.fn(),
  prefetch: vi.fn(),
  beforePopState: vi.fn(),
  events: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
};

vi.mock('next/router', () => ({
  useRouter: () => mockRouter,
}));

// Mock form context
const mockSetFieldValue = vi.fn();
const mockGetInputProps = vi.fn(() => ({
  value: '',
  onChange: vi.fn(),
  onBlur: vi.fn(),
  error: undefined,
}));

// Override the global mock for this test
vi.mock('@/contexts/FormContext', () => ({
  useFormContext: () => ({
    setFieldValue: mockSetFieldValue,
    getInputProps: mockGetInputProps,
    getValues: () => ({}),
    validate: vi.fn(),
    clearFieldError: vi.fn(),
  }),
}));

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        buttonText: 'Submit another response',
        buttonUrl: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  status: 'active',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  createdBy: 'test-user',
  updatedBy: 'test-user',
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
  metadata: {
    version: '1.0.0',
    lastModified: '2023-01-01T00:00:00Z',
  },
  permissions: {
    canEdit: true,
    canDelete: true,
    canShare: true,
  },
  urls: {
    edit: '/edit',
    view: '/view',
    share: '/share',
  },
  tags: [],
  category: 'general',
  isPublic: true,
  isArchived: false,
  expiredAt: null,
  isFavorited: false,
  isPinned: false,
  responses: {
    total: 0,
    lastResponse: null,
  },
};

describe('HiddenField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockRouter.query = {};
    mockRouter.asPath = '/';
  });

  it('renders hidden input field', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Hidden,
      label: 'Hidden Field',
      validators: [],
    };

    renderWithProviders(<HiddenField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('hidden-input')).toBeInTheDocument();
  });

  it('sets field value from URL query parameter', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Hidden,
      label: 'Hidden Field',
      validators: [],
    };

    // Set query parameter
    mockRouter.query = { 'test-field': 'test-value' };

    renderWithProviders(<HiddenField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('hidden-input')).toBeInTheDocument();
  });

  it('handles empty query parameter', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Hidden,
      label: 'Hidden Field',
      validators: [],
    };

    mockRouter.query = { 'test-field': '' };

    renderWithProviders(<HiddenField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('hidden-input')).toBeInTheDocument();
  });

  it('handles undefined query parameter', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Hidden,
      label: 'Hidden Field',
      validators: [],
    };

    mockRouter.query = {};

    renderWithProviders(<HiddenField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('hidden-input')).toBeInTheDocument();
  });

  it('handles array query parameter', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Hidden,
      label: 'Hidden Field',
      validators: [],
    };

    mockRouter.query = { 'test-field': ['value1', 'value2'] };

    renderWithProviders(<HiddenField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('hidden-input')).toBeInTheDocument();
  });

  it('handles different field names', () => {
    const field = {
      id: 'custom-field',
      name: 'custom-field',
      type: FieldType.Hidden,
      label: 'Custom Hidden Field',
      validators: [],
    };

    mockRouter.query = { 'custom-field': 'custom-value' };

    renderWithProviders(<HiddenField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('hidden-input')).toBeInTheDocument();
  });

  it('handles router path changes', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.Hidden,
      label: 'Hidden Field',
      validators: [],
    };

    mockRouter.asPath = '/new-path';

    renderWithProviders(<HiddenField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('hidden-input')).toBeInTheDocument();
  });

  it('handles field with empty name', () => {
    const field = {
      id: 'test-field',
      name: '',
      type: FieldType.Hidden,
      label: 'Hidden Field',
      validators: [],
    };

    renderWithProviders(<HiddenField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('hidden-input')).toBeInTheDocument();
  });

  it('handles field with special characters in name', () => {
    const field = {
      id: 'test-field',
      name: 'field-with-special-chars',
      type: FieldType.Hidden,
      label: 'Hidden Field',
      validators: [],
    };

    mockRouter.query = { 'field-with-special-chars': 'special-value' };

    renderWithProviders(<HiddenField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByTestId('hidden-input')).toBeInTheDocument();
  });

  it('handles multiple hidden fields', () => {
    const field1 = {
      id: 'field1',
      name: 'field1',
      type: FieldType.Hidden,
      label: 'Hidden Field 1',
      validators: [],
    };

    const field2 = {
      id: 'field2',
      name: 'field2',
      type: FieldType.Hidden,
      label: 'Hidden Field 2',
      validators: [],
    };

    mockRouter.query = { field1: 'value1', field2: 'value2' };

    renderWithProviders(
      <>
        <HiddenField field={field1} />
        <HiddenField field={field2} />
      </>,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getAllByTestId('hidden-input')).toHaveLength(2);
  });
});
