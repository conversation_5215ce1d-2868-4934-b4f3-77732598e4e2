import { fireEvent, screen } from '@testing-library/react';
import type React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType } from '@/types/form-builder';

import { renderWithProviders } from '../../test-utils';
import OpinionScaleField from './OpinionScaleField';

// Mock ResizeObserver for Mantine ScrollArea
if (typeof globalThis.ResizeObserver === 'undefined') {
  globalThis.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
}

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock Mantine form
vi.mock('@mantine/form', () => ({
  useFormContext: () => ({
    getInputProps: () => ({
      value: '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
      error: undefined,
    }),
    setFieldValue: vi.fn(),
    getValues: () => ({}),
    validate: vi.fn(),
    clearFieldError: vi.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  createFormContext: () => [
    ({ children }: { children: React.ReactNode }) => <>{children}</>,
    () => ({
      getInputProps: () => ({
        value: '',
        onChange: vi.fn(),
        onBlur: vi.fn(),
        error: undefined,
      }),
      setFieldValue: vi.fn(),
      getValues: () => ({}),
      validate: vi.fn(),
      values: {},
      errors: {},
      clearFieldError: vi.fn(),
    }),
    () => ({}),
  ],
}));

// Mock Tolgee useTranslate hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
}));

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

vi.mock('@/utils', () => ({
  getGridColsByWidth: vi.fn((width) => {
    if (width === 'full') return 12;
    if (width === 'half') return 6;
    return 12;
  }),
  hasRequiredRule: vi.fn((field) => {
    return field.validators?.some(
      (validator) => validator.type === 'required' && validator.value === true
    );
  }),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        buttonText: 'Submit another response',
        buttonUrl: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  status: 'active',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  createdBy: 'test-user',
  updatedBy: 'test-user',
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
  metadata: {
    version: '1.0.0',
    lastModified: '2023-01-01T00:00:00Z',
  },
  permissions: {
    canEdit: true,
    canDelete: true,
    canShare: true,
  },
  urls: {
    edit: '/edit',
    view: '/view',
    share: '/share',
  },
  tags: [],
  category: 'general',
  isPublic: true,
  isArchived: false,
  expiredAt: null,
  isFavorited: false,
  isPinned: false,
  responses: {
    total: 0,
    lastResponse: null,
  },
};

describe('OpinionScaleField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders opinion scale chips correctly', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        middle: 'Neutral',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // Should render 5 chips numbered 1-5
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('renders footer label when footerHide is false', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        middle: 'Neutral',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Strongly Disagree')).toBeInTheDocument();
  });

  it('does not render footer label when footerHide is true', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: true,
      footerLabel: {
        left: 'Strongly Disagree',
        middle: 'Neutral',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field} />);

    expect(screen.queryByText('Strongly Disagree')).not.toBeInTheDocument();
  });

  it('handles different max scale values', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 10,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        middle: 'Neutral',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field} />);

    // Should render 10 chips numbered 1-10
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
  });

  it('handles chip selection', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        middle: 'Neutral',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field} />);

    const chip3 = screen.getByText('3');
    fireEvent.click(chip3);

    // The chip should be selected
    expect(chip3).toBeInTheDocument();
  });

  it('handles HTML content in footer label', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: '<strong>Strongly</strong> Disagree',
        middle: 'Neutral',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field} />, {
      formSettings: mockFormSettings as any,
    });

    // Should render HTML content
    expect(screen.getByText('Strongly')).toBeInTheDocument();
    expect(screen.getByText('Disagree')).toBeInTheDocument();
  });

  it('applies custom class names', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    const customClassNames = {
      input: 'custom-input-class',
      label: 'custom-label-class',
    };

    renderWithProviders(<OpinionScaleField field={field as any} classNames={customClassNames} />);

    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('handles error state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field as any} error='Please select an option' />);

    expect(screen.getByTestId('input-error')).toHaveTextContent('Please select an option');
  });

  it('handles description', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(
      <OpinionScaleField field={field as any} description='Please rate from 1 to 5' />
    );

    expect(screen.getByTestId('input-description')).toHaveTextContent('Please rate from 1 to 5');
  });

  it('handles withAsterisk prop', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field as any} withAsterisk />);

    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('handles required state', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field as any} required />);

    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('handles label', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: 'Strongly Disagree',
        right: 'Strongly Agree',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field as any} label='Custom Label' />);

    expect(screen.getByTestId('input-label')).toHaveTextContent('Custom Label');
  });

  it('handles empty footer label', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      footerLabel: {
        left: '',
        right: '',
      },
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field as any} />);

    // Should render without errors even with empty footer label
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('handles undefined footer label', () => {
    const field = {
      id: 'test-field',
      name: 'test-field',
      type: FieldType.OpinionScale,
      label: 'Rate your opinion',
      maxScale: 5,
      footerHide: false,
      validators: [],
    };

    renderWithProviders(<OpinionScaleField field={field} />);

    // Should render without errors even with undefined footer label
    expect(screen.getByText('1')).toBeInTheDocument();
  });
});
