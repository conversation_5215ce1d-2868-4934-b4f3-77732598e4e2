import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType, GroupFieldType } from '@/types/form-builder';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import AddressField from './AddressFields';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

vi.mock('@/hooks/useDebounce', () => ({
  default: () => vi.fn(),
}));

vi.mock('@/utils', () => ({
  getAddressFromPostalCode: vi.fn(),
  getPrefecturesOptions: vi.fn(() => [
    { label: 'Tokyo', value: 'tokyo' },
    { label: 'Osaka', value: 'osaka' },
    { label: 'Kyoto', value: 'kyoto' },
  ]),
  getGridColsByWidth: vi.fn(() => 6),
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock AddressField component to avoid form context issues
vi.mock('./AddressFields', () => ({
  default: ({ group }: { group: any }) => (
    <div data-testid='address-field-group'>
      {group.label && <div data-testid='group-label'>{group.label}</div>}
      {group.description && group.descriptionEnabled && (
        <div data-testid='group-description'>{group.description}</div>
      )}
      {group.fields.map((field: any) => (
        <div key={field.id} data-testid={`field-${field.id}`}>
          <label htmlFor={field.id}>{field.label}</label>
          <input id={field.id} data-testid={`input-${field.id}`} />
        </div>
      ))}
    </div>
  ),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        buttonText: 'Submit another response',
        buttonUrl: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  status: 'active',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  createdBy: 'test-user',
  updatedBy: 'test-user',
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
  metadata: {
    version: '1.0.0',
    lastModified: '2023-01-01T00:00:00Z',
  },
  permissions: {
    canEdit: true,
    canDelete: true,
    canShare: true,
  },
  urls: {
    edit: '/edit',
    view: '/view',
    share: '/share',
  },
  tags: [],
  category: 'general',
  isPublic: true,
  isArchived: false,
  expiredAt: null,
  isFavorited: false,
  isPinned: false,
  responses: {
    total: 0,
    lastResponse: null,
  },
};

describe('AddressField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders address field group correctly', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
        {
          id: 'prefecture',
          name: 'prefecture',
          type: FieldType.Dropdown,
          label: 'Prefecture',
          validators: [],
        },
        {
          id: 'city',
          name: 'city',
          type: FieldType.ShortQA,
          label: 'City',
          validators: [],
        },
        {
          id: 'street',
          name: 'street',
          type: FieldType.ShortQA,
          label: 'Street',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    expect(screen.getByTestId('address-field-group')).toBeInTheDocument();
    expect(screen.getByTestId('group-label')).toHaveTextContent('Address');
    expect(screen.getByTestId('field-postcode')).toHaveTextContent('Postcode');
    expect(screen.getByTestId('field-prefecture')).toHaveTextContent('Prefecture');
    expect(screen.getByTestId('field-city')).toHaveTextContent('City');
    expect(screen.getByTestId('field-street')).toHaveTextContent('Street');
  });

  it('sets prefecture field as searchable', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'prefecture',
          name: 'prefecture',
          type: FieldType.Dropdown,
          label: 'Prefecture',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    // The prefecture field should be set as searchable
    expect(screen.getByText('Prefecture')).toBeInTheDocument();
  });

  it('loads prefecture options', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'prefecture',
          name: 'prefecture',
          type: FieldType.Dropdown,
          label: 'Prefecture',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    // The prefecture field should have options loaded
    expect(screen.getByText('Prefecture')).toBeInTheDocument();
  });

  it('handles postcode field change', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
        {
          id: 'prefecture',
          name: 'prefecture',
          type: FieldType.Dropdown,
          label: 'Prefecture',
          validators: [],
        },
        {
          id: 'city',
          name: 'city',
          type: FieldType.ShortQA,
          label: 'City',
          validators: [],
        },
        {
          id: 'street',
          name: 'street',
          type: FieldType.ShortQA,
          label: 'Street',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    const postcodeInput = screen.getByTestId('input-postcode');
    fireEvent.change(postcodeInput, { target: { value: '12345' } });

    expect(postcodeInput).toHaveValue('12345');
  });

  it('handles missing prefecture field', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
        {
          id: 'city',
          name: 'city',
          type: FieldType.ShortQA,
          label: 'City',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    // Should render without errors even with missing prefecture field
    expect(screen.getByText('Address')).toBeInTheDocument();
    expect(screen.getByText('Postcode')).toBeInTheDocument();
    expect(screen.getByText('City')).toBeInTheDocument();
  });

  it('handles missing city field', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
        {
          id: 'prefecture',
          name: 'prefecture',
          type: FieldType.Dropdown,
          label: 'Prefecture',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    // Should render without errors even with missing city field
    expect(screen.getByText('Address')).toBeInTheDocument();
    expect(screen.getByText('Postcode')).toBeInTheDocument();
    expect(screen.getByText('Prefecture')).toBeInTheDocument();
  });

  it('handles missing street field', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
        {
          id: 'prefecture',
          name: 'prefecture',
          type: FieldType.Dropdown,
          label: 'Prefecture',
          validators: [],
        },
        {
          id: 'city',
          name: 'city',
          type: FieldType.ShortQA,
          label: 'City',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    // Should render without errors even with missing street field
    expect(screen.getByText('Address')).toBeInTheDocument();
    expect(screen.getByText('Postcode')).toBeInTheDocument();
    expect(screen.getByText('Prefecture')).toBeInTheDocument();
    expect(screen.getByText('City')).toBeInTheDocument();
  });

  it('handles empty postcode input', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
        {
          id: 'prefecture',
          name: 'prefecture',
          type: FieldType.Dropdown,
          label: 'Prefecture',
          validators: [],
        },
        {
          id: 'city',
          name: 'city',
          type: FieldType.ShortQA,
          label: 'City',
          validators: [],
        },
        {
          id: 'street',
          name: 'street',
          type: FieldType.ShortQA,
          label: 'Street',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    const postcodeInput = screen.getByTestId('input-postcode');
    fireEvent.change(postcodeInput, { target: { value: '' } });

    expect(postcodeInput).toHaveValue('');
  });

  it('handles non-postcode field changes', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
        {
          id: 'city',
          name: 'city',
          type: FieldType.ShortQA,
          label: 'City',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    const cityInput = screen.getByTestId('input-city');
    fireEvent.change(cityInput, { target: { value: 'Tokyo' } });

    expect(cityInput).toHaveValue('Tokyo');
  });

  it('handles group without label', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: '',
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    // Should render without errors even without group label
    expect(screen.getByText('Postcode')).toBeInTheDocument();
  });

  it('handles group with description', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      description: 'Please enter your address',
      descriptionEnabled: true,
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    expect(screen.getByTestId('group-description')).toHaveTextContent('Please enter your address');
  });

  it('handles group with disabled description', () => {
    const group = {
      id: 'address-group',
      type: GroupFieldType.Address,
      groupType: GroupFieldType.Address,
      label: 'Address',
      description: 'Please enter your address',
      descriptionEnabled: false,
      fields: [
        {
          id: 'postcode',
          name: 'postcode',
          type: FieldType.ShortQA,
          label: 'Postcode',
          validators: [],
        },
      ],
    };

    renderWithProviders(<AddressField group={group} />);

    expect(screen.queryByText('Please enter your address')).not.toBeInTheDocument();
  });
});
