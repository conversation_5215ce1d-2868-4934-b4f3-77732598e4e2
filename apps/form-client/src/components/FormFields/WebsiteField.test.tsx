import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import WebsiteField from './WebsiteField';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    styles: {},
  }),
}));

const mockFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        emailQuestionId: '',
      },
    },
    notification: {
      isAutoresponse: false,
    },
    behavior: {
      isMultipleResponse: false,
    },
    systemMessage: [],
  },
  metadata: {
    organizationId: 'test-org',
    workspaceId: 'test-workspace',
  },
  status: 'active',
  permissions: [],
  urls: {
    public: 'https://test.com',
    embed: 'https://test.com/embed',
    private: 'https://test.com/private',
  },
  tags: [],
  expiredAt: '',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('WebsiteField', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders website field correctly', () => {
    renderWithProviders(<WebsiteField label='Website URL' placeholder='Enter your website URL' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Website URL')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your website URL')).toBeInTheDocument();
  });

  it('shows required indicator for required fields', () => {
    renderWithProviders(<WebsiteField label='Website URL' required={true} />, {
      formSettings: mockFormSettings as any,
    });

    // Check that the required indicator (*) is present
    expect(screen.getByText('*')).toBeInTheDocument();

    // Check that the input has the required attribute
    const input = screen.getByRole('textbox');
    expect(input).toBeRequired();
  });

  it('renders description when provided', () => {
    renderWithProviders(
      <WebsiteField label='Website URL' description='Please enter a valid website URL' />,
      {
        formSettings: mockFormSettings as any,
      }
    );

    expect(screen.getByText('Please enter a valid website URL')).toBeInTheDocument();
  });

  it('handles input change correctly', () => {
    renderWithProviders(<WebsiteField label='Website URL' />, {
      formSettings: mockFormSettings as any,
    });

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'https://example.com' } });

    expect(input).toHaveValue('https://example.com');
  });

  it('displays error state correctly', () => {
    renderWithProviders(<WebsiteField label='Website URL' error='Invalid URL format' />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Invalid URL format')).toBeInTheDocument();
  });
});
