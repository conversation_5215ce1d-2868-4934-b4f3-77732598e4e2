import {
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
} from '@/types/enum/appearanceSettings';
import { InputStyle } from '@/types/enum/inputStyle';
import { FieldType, GroupFieldType, ValidatorType } from '@/types/form-builder';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import GroupFormFieldBase from './GroupFormFieldBase';

// Mock dependencies
vi.mock('@/hooks/form/useFieldStyles', () => ({
  default: () => ({
    classNames: {
      label: 'label-class',
      description: 'description-class',
      error: 'error-class',
    },
  }),
}));

// Mock utils with hasRequiredRule
vi.mock('@/utils', () => ({
  getGridColsByWidth: vi.fn((width) => {
    if (width === 'full') return 12;
    if (width === 'half') return 6;
    return 12;
  }),
  hasRequiredRule: vi.fn((field) => {
    return field.validators?.some(
      (validator) => validator.type === 'required' && validator.value === true
    );
  }),
}));

// Mock Next.js router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

// Mock Mantine form to avoid form context issues
vi.mock('@mantine/form', () => ({
  useFormContext: () => ({
    getInputProps: () => ({
      value: '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
      error: undefined,
    }),
    setFieldValue: vi.fn(),
    getValues: () => ({}),
    validate: vi.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  createFormContext: () => [
    ({ children }: { children: React.ReactNode }) => <>{children}</>,
    () => ({
      getInputProps: () => ({
        value: '',
        onChange: vi.fn(),
        onBlur: vi.fn(),
        error: undefined,
      }),
      setFieldValue: vi.fn(),
      getValues: () => ({}),
      validate: vi.fn(),
      values: {},
      errors: {},
      clearFieldError: vi.fn(),
    }),
    () => ({}),
  ],
}));

// Mock Tolgee useTranslate hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
}));

// Mock ResizeObserver for Mantine ScrollArea
if (typeof globalThis.ResizeObserver === 'undefined') {
  globalThis.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
}

const mockFormSettings = {
  appearanceSettings: {
    logo: {
      size: AppearanceSettingsLogoSize.Medium,
      align: AppearanceSettingsLogoAlign.Left,
    },
    inputStyle: InputStyle.Classic,
  },
  hiddenFieldsMap: {},
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: InputStyle.Classic,
      color: '#000000',
      font: 'default',
    },
    formFieldStyle: {
      color: {
        placeholder: '#000000',
        question: '#000000',
        answer: '#000000',
        icon: '#000000',
        description: '#000000',
        fieldStroke: '#000000',
        fieldBackGround: '#ffffff',
      },
      fontFamily: {
        placeholder: 'default',
        question: 'default',
        text: 'default',
        answer: 'default',
      },
      fontSize: {
        placeholder: '14px',
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    headingStyle: {
      fontFamily: 'default',
      fontSize: 18,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'default',
      fontSize: 14,
      color: '#000000',
    },
    buttonStyle: {
      type: 'default',
      fullWidth: false,
      backgroundColor: '#000000',
      textColor: '#ffffff',
      fontFamily: 'default',
      fontSize: 14,
    },
    headerStyle: {
      position: 'top',
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
  },
  setting: {
    submission: {
      mode: 'message',
      message: 'Thank you',
      caption: '',
      button: '',
      redirectUrl: '',
      enableBranding: true,
      limitResponse: false,
      limitNumber: 0,
      thankMessage: {
        title: 'Thank you',
        message: 'Your response has been recorded.',
        button: 'Submit another response',
      },
    },
  },
  id: 'test-form',
  name: 'Test Form',
  description: 'Test Description',
  createdAt: '',
  updatedAt: '',
  isFavorited: false,
  isPinned: false,
  responses: 0,
  screenshot: {
    original: '',
    thumbnail: '',
    preview: '',
  },
  startAt: '',
};

describe('GroupFormFieldBase', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders group with label correctly', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: [
        {
          id: 'field-1',
          name: 'field1',
          type: FieldType.ShortQA,
          label: 'Field 1',
          validators: [],
        },
        {
          id: 'field-2',
          name: 'field2',
          type: FieldType.Email,
          label: 'Field 2',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.getByText('Field 1')).toBeInTheDocument();
    expect(screen.getByText('Field 2')).toBeInTheDocument();
  });

  it('renders group without label', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: '',
      fields: [
        {
          id: 'field-1',
          name: 'field1',
          type: FieldType.ShortQA,
          label: 'Field 1',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.queryByText('Test Group')).not.toBeInTheDocument();
    expect(screen.getByText('Field 1')).toBeInTheDocument();
  });

  it('renders group with description', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      description: 'This is a description',
      descriptionEnabled: true,
      fields: [
        {
          id: 'field-1',
          name: 'field1',
          type: FieldType.ShortQA,
          label: 'Field 1',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.getByText('This is a description')).toBeInTheDocument();
    expect(screen.getByText('Field 1')).toBeInTheDocument();
  });

  it('does not render description when disabled', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      description: 'This description should not show',
      descriptionEnabled: false,
      fields: [
        {
          id: 'field-1',
          name: 'field1',
          type: FieldType.ShortQA,
          label: 'Field 1',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.queryByText('This description should not show')).not.toBeInTheDocument();
    expect(screen.getByText('Field 1')).toBeInTheDocument();
  });

  it('filters out hidden fields', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: [
        {
          id: 'visible-field',
          name: 'visibleField',
          type: FieldType.ShortQA,
          label: 'Visible Field',
          validators: [],
          isHide: false,
        },
        {
          id: 'hidden-field',
          name: 'hiddenField',
          type: FieldType.ShortQA,
          label: 'Hidden Field',
          validators: [],
          isHide: true,
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.getByText('Visible Field')).toBeInTheDocument();
    expect(screen.queryByText('Hidden Field')).not.toBeInTheDocument();
  });

  it('handles fields with different widths', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: [
        {
          id: 'full-width-field',
          name: 'fullWidthField',
          type: FieldType.ShortQA,
          label: 'Full Width Field',
          validators: [],
          style: {
            width: 'full',
          },
        },
        {
          id: 'half-width-field',
          name: 'halfWidthField',
          type: FieldType.ShortQA,
          label: 'Half Width Field',
          validators: [],
          style: {
            width: 'half',
          },
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.getByText('Full Width Field')).toBeInTheDocument();
    expect(screen.getByText('Half Width Field')).toBeInTheDocument();
  });

  it('handles fields without style property', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: [
        {
          id: 'no-style-field',
          name: 'noStyleField',
          type: FieldType.ShortQA,
          label: 'No Style Field',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />, {
      formSettings: mockFormSettings as any,
    });

    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.getByText('No Style Field')).toBeInTheDocument();
  });

  it('calls onChange when provided', () => {
    const onChange = vi.fn();
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: [
        {
          id: 'field-1',
          name: 'field1',
          type: FieldType.ShortQA,
          label: 'Field 1',
          validators: [],
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} onChange={onChange} />);

    const input = screen.getByTestId('text-input-field');
    fireEvent.change(input, { target: { value: 'test value' } });

    expect(onChange).toHaveBeenCalled();
  });

  it('handles empty fields array', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: [],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />);

    expect(screen.getByText('Test Group')).toBeInTheDocument();
  });

  it('handles group with complex field types', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: [
        {
          id: 'text-field',
          name: 'textField',
          type: FieldType.ShortQA,
          label: 'Text Field',
          validators: [],
        },
        {
          id: 'email-field',
          name: 'emailField',
          type: FieldType.Email,
          label: 'Email Field',
          validators: [],
        },
        {
          id: 'dropdown-field',
          name: 'dropdownField',
          type: FieldType.Dropdown,
          label: 'Dropdown Field',
          validators: [],
          options: [
            { label: 'Option 1', value: 'option1' },
            { label: 'Option 2', value: 'option2' },
          ],
        },
        {
          id: 'checkbox-field',
          name: 'checkboxField',
          type: FieldType.Checkbox,
          label: 'Checkbox Field',
          validators: [],
          options: [{ label: 'Checkbox Field', value: 'checkbox1' }],
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />);

    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.getByText('Text Field')).toBeInTheDocument();
    expect(screen.getByText('Email Field')).toBeInTheDocument();
    expect(screen.getByText('Dropdown Field')).toBeInTheDocument();
    expect(screen.getByText((content) => content.includes('Checkbox Field'))).toBeInTheDocument();
  });

  it('handles group with fields having validators', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: [
        {
          id: 'required-field',
          name: 'requiredField',
          type: FieldType.ShortQA,
          label: 'Required Field',
          validators: [
            {
              type: ValidatorType.Required,
              message: 'This field is required',
            },
          ],
        },
        {
          id: 'email-field',
          name: 'emailField',
          type: FieldType.Email,
          label: 'Email Field',
          validators: [
            {
              type: ValidatorType.Email,
              message: 'Please enter a valid email',
            },
          ],
        },
      ],
    };

    renderWithProviders(<GroupFormFieldBase group={group} />);

    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.getByText('Required Field')).toBeInTheDocument();
    expect(screen.getByText('Email Field')).toBeInTheDocument();
  });

  it('handles group with missing fields property', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
    } as any;

    renderWithProviders(<GroupFormFieldBase group={group} />);

    expect(screen.getByText('Test Group')).toBeInTheDocument();
  });

  it('handles group with null fields', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: null,
    } as any;

    renderWithProviders(<GroupFormFieldBase group={group} />);

    expect(screen.getByText('Test Group')).toBeInTheDocument();
  });

  it('handles group with undefined fields', () => {
    const group = {
      id: 'test-group',
      name: 'test',
      type: GroupFieldType.Name,
      groupType: GroupFieldType.Name,
      label: 'Test Group',
      fields: undefined,
    } as any;

    renderWithProviders(<GroupFormFieldBase group={group} />);

    expect(screen.getByText('Test Group')).toBeInTheDocument();
  });
});
