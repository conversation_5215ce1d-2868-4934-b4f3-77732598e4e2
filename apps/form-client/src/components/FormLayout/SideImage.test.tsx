import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import SideImage from './SideImage';

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, fill, width, height, className }: any) => {
    if (fill) {
      return <img src={src} alt={alt} className={className} data-testid='fill-image' />;
    }
    return <img src={src} alt={alt} width={width} height={height} data-testid='sized-image' />;
  },
}));

describe('SideImage', () => {
  it('renders image when imageUrl is provided', () => {
    const imageUrl = 'https://example.com/test-image.jpg';
    renderWithProviders(<SideImage imageUrl={imageUrl} />);

    const image = screen.getByTestId('fill-image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', imageUrl);
    expect(image).toHaveAttribute('alt', 'Form cover');
  });

  it('renders placeholder when imageUrl is null', () => {
    renderWithProviders(<SideImage imageUrl={null} />);

    const placeholderImage = screen.getByTestId('sized-image');
    expect(placeholderImage).toBeInTheDocument();
    expect(placeholderImage).toHaveAttribute('src', '/images/no_layout_image.svg');
    expect(placeholderImage).toHaveAttribute('alt', 'No layout image');
    expect(placeholderImage).toHaveAttribute('width', '132');
    expect(placeholderImage).toHaveAttribute('height', '72');
  });

  it('renders placeholder when imageUrl is empty string', () => {
    renderWithProviders(<SideImage imageUrl='' />);

    const placeholderImage = screen.getByTestId('sized-image');
    expect(placeholderImage).toBeInTheDocument();
    expect(placeholderImage).toHaveAttribute('src', '/images/no_layout_image.svg');
    expect(placeholderImage).toHaveAttribute('alt', 'No layout image');
  });

  it('applies correct CSS classes to image container', () => {
    const imageUrl = 'https://example.com/test-image.jpg';
    renderWithProviders(<SideImage imageUrl={imageUrl} />);

    const image = screen.getByTestId('fill-image');
    expect(image).toBeInTheDocument();
  });

  it('applies correct CSS classes to image', () => {
    const imageUrl = 'https://example.com/test-image.jpg';
    renderWithProviders(<SideImage imageUrl={imageUrl} />);

    const image = screen.getByTestId('fill-image');
    expect(image).toBeInTheDocument();
  });

  it('renders placeholder with correct background color', () => {
    renderWithProviders(<SideImage imageUrl={null} />);

    const placeholderImage = screen.getByTestId('sized-image');
    expect(placeholderImage).toBeInTheDocument();
  });
});
