import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import FormLayout from './FormLayout';

// Mock Tolgee for this specific test file
vi.mock('@tolgee/react', () => ({
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useTranslate: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
  useTolgee: () => ({
    isLoaded: () => true,
    getRequiredRecords: () => [],
  }),
}));

// Mock FormFields component
vi.mock('../FormFields/FormFields', () => ({
  default: ({ fields }: { fields: any[] }) => (
    <div data-testid='form-fields'>
      {fields.map((field) => (
        <div key={field.id} data-testid={`field-${field.id}`}>
          {field.label}
        </div>
      ))}
    </div>
  ),
}));

const mockFormSettings = {
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: 'default',
    },
    formFieldStyle: {},
  },
};

describe('FormLayout', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders form layout with default configuration', () => {
    const settings = {
      id: 'section-1',
      type: 'section' as any,
      name: 'Test Section',
      layout: {
        type: 'image-top' as any,
        imageUrl: null,
        fieldWidth: '600px',
      },
      content: [
        {
          id: 'field-1',
          type: 'short_qa' as any,
          label: 'Test Field',
          name: 'test_field',
          validators: [],
        },
      ],
    };

    renderWithProviders(
      <FormLayout
        index={0}
        settings={settings}
        showBackButton={false}
        showNextButton={true}
        showSubmitButton={false}
        showSkipButton={false}
        isSaving={false}
        onBack={vi.fn()}
        onNext={vi.fn()}
        onSubmit={vi.fn()}
      />
    );

    expect(screen.getByText('Test Field')).toBeInTheDocument();
  });

  it('renders form layout with custom background image', () => {
    const pages = [
      {
        id: 'page-1',
        title: 'Page 1',
        fields: [],
      },
    ];

    const settings = {
      id: 'section-1',
      type: 'section' as any,
      name: 'Test Section',
      layout: {
        type: 'image-background' as any,
        imageUrl: 'https://example.com/bg.jpg',
        fieldWidth: '600px',
      },
      content: [
        {
          id: 'field-1',
          type: 'short_qa' as any,
          label: 'Test Field',
          name: 'test_field',
          validators: [],
        },
      ],
    };

    renderWithProviders(
      <FormLayout
        index={0}
        settings={settings}
        showBackButton={false}
        showNextButton={true}
        showSubmitButton={false}
        showSkipButton={false}
        isSaving={false}
        onBack={vi.fn()}
        onNext={vi.fn()}
        onSubmit={vi.fn()}
      />
    );

    // Check if background image is applied
    expect(screen.getByText('next')).toBeInTheDocument();
  });

  it('renders form layout with two-column layout', () => {
    const settings = {
      id: 'section-1',
      type: 'section' as any,
      name: 'Test Section',
      layout: {
        type: 'image-left' as any,
        imageUrl: 'https://example.com/image.jpg',
        fieldWidth: '600px',
      },
      content: [
        {
          id: 'field-1',
          type: 'short_qa' as any,
          label: 'Test Field',
          name: 'test_field',
          validators: [],
        },
      ],
    };

    renderWithProviders(
      <FormLayout
        index={0}
        settings={settings}
        showBackButton={false}
        showNextButton={true}
        showSubmitButton={false}
        showSkipButton={false}
        isSaving={false}
        onBack={vi.fn()}
        onNext={vi.fn()}
        onSubmit={vi.fn()}
      />
    );

    expect(screen.getByText('Test Field')).toBeInTheDocument();
  });

  it('renders form layout with custom width', () => {
    const settings = {
      id: 'section-1',
      type: 'section' as any,
      name: 'Test Section',
      layout: {
        type: 'image-top' as any,
        imageUrl: null,
        fieldWidth: '800px',
      },
      content: [],
    };

    renderWithProviders(
      <FormLayout
        index={0}
        settings={settings}
        showBackButton={false}
        showNextButton={true}
        showSubmitButton={false}
        showSkipButton={false}
        isSaving={false}
        onBack={vi.fn()}
        onNext={vi.fn()}
        onSubmit={vi.fn()}
      />
    );

    expect(screen.getByText('next')).toBeInTheDocument();
  });

  it('renders form layout with multiple pages', () => {
    const settings = {
      id: 'section-1',
      type: 'section' as any,
      name: 'Test Section',
      layout: {
        type: 'image-top' as any,
        imageUrl: null,
        fieldWidth: '600px',
      },
      content: [
        {
          id: 'field-1',
          type: 'short_qa' as any,
          label: 'Field 1',
          name: 'field_1',
          validators: [],
        },
        {
          id: 'field-2',
          type: 'short_qa' as any,
          label: 'Field 2',
          name: 'field_2',
          validators: [],
        },
      ],
    };

    renderWithProviders(
      <FormLayout
        index={1}
        settings={settings}
        showBackButton={true}
        showNextButton={false}
        showSubmitButton={true}
        showSkipButton={false}
        isSaving={false}
        onBack={vi.fn()}
        onNext={vi.fn()}
        onSubmit={vi.fn()}
      />
    );

    expect(screen.getByText('submit')).toBeInTheDocument();
  });

  it('renders form layout with empty pages', () => {
    const settings = {
      id: 'section-1',
      type: 'section' as any,
      name: 'Test Section',
      layout: {
        type: 'image-top' as any,
        imageUrl: null,
        fieldWidth: '600px',
      },
      content: [],
    };

    renderWithProviders(
      <FormLayout
        index={0}
        settings={settings}
        showBackButton={false}
        showNextButton={true}
        showSubmitButton={false}
        showSkipButton={false}
        isSaving={false}
        onBack={vi.fn()}
        onNext={vi.fn()}
        onSubmit={vi.fn()}
      />
    );

    expect(screen.getByText('next')).toBeInTheDocument();
  });

  it('renders form layout with preview mode', () => {
    const settings = {
      id: 'section-1',
      type: 'section' as any,
      name: 'Test Section',
      layout: {
        type: 'image-top' as any,
        imageUrl: null,
        fieldWidth: '600px',
      },
      content: [
        {
          id: 'field-1',
          type: 'short_qa' as any,
          label: 'Test Field',
          name: 'test_field',
          validators: [],
        },
      ],
    };

    renderWithProviders(
      <FormLayout
        index={0}
        settings={settings}
        showBackButton={false}
        showNextButton={true}
        showSubmitButton={false}
        showSkipButton={false}
        isSaving={false}
        onBack={vi.fn()}
        onNext={vi.fn()}
        onSubmit={vi.fn()}
      />
    );

    expect(screen.getByText('next')).toBeInTheDocument();
  });
});
