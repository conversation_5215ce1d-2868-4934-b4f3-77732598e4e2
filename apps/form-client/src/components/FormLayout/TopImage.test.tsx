import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import TopImage from './TopImage';

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, fill, priority }: any) => (
    <img src={src} alt={alt} data-testid='top-image' data-priority={priority} />
  ),
}));

describe('TopImage', () => {
  it('renders image when imageUrl is provided', () => {
    const imageUrl = 'https://example.com/test-image.jpg';
    renderWithProviders(<TopImage imageUrl={imageUrl} />);

    const image = screen.getByTestId('top-image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', imageUrl);
    expect(image).toHaveAttribute('alt', 'Form cover');
    expect(image).toHaveAttribute('data-priority', 'true');
  });

  it('renders nothing when imageUrl is null', () => {
    renderWithProviders(<TopImage imageUrl={null} />);

    expect(screen.queryByTestId('top-image')).not.toBeInTheDocument();
  });

  it('renders nothing when imageUrl is empty string', () => {
    renderWithProviders(<TopImage imageUrl='' />);

    expect(screen.queryByTestId('top-image')).not.toBeInTheDocument();
  });

  it('uses default mobileHeight when not provided', () => {
    const imageUrl = 'https://example.com/test-image.jpg';
    renderWithProviders(<TopImage imageUrl={imageUrl} />);

    const image = screen.getByTestId('top-image');
    expect(image).toBeInTheDocument();
  });

  it('uses custom mobileHeight when provided', () => {
    const imageUrl = 'https://example.com/test-image.jpg';
    const customMobileHeight = 200;
    renderWithProviders(<TopImage imageUrl={imageUrl} mobileHeight={customMobileHeight} />);

    const image = screen.getByTestId('top-image');
    expect(image).toBeInTheDocument();
  });

  it('applies correct CSS classes to header container', () => {
    const imageUrl = 'https://example.com/test-image.jpg';
    renderWithProviders(<TopImage imageUrl={imageUrl} />);

    const image = screen.getByTestId('top-image');
    expect(image).toBeInTheDocument();
  });

  it('renders with priority attribute for performance', () => {
    const imageUrl = 'https://example.com/test-image.jpg';
    renderWithProviders(<TopImage imageUrl={imageUrl} />);

    const image = screen.getByTestId('top-image');
    expect(image).toHaveAttribute('data-priority', 'true');
  });

  it('handles different image URLs correctly', () => {
    const imageUrls = [
      'https://example.com/image1.jpg',
      'https://example.com/image2.png',
      'https://example.com/image3.webp',
    ];

    imageUrls.forEach((url) => {
      const { unmount } = renderWithProviders(<TopImage imageUrl={url} />);

      const image = screen.getByTestId('top-image');
      expect(image).toHaveAttribute('src', url);

      unmount();
    });
  });
});
