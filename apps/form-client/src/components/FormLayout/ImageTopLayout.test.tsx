import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import type { FormLayoutProps } from './FormLayout';
import ImageTopLayout from './ImageTopLayout';

// Mock dependencies
vi.mock('./TopImage', () => ({
  default: ({ imageUrl }: { imageUrl: string | null }) => (
    <div data-testid='top-image' data-image-url={imageUrl || ''}>
      Top Image Component
    </div>
  ),
}));

vi.mock('./FormContent', () => ({
  default: (props: any) => (
    <div data-testid='form-content' data-props={JSON.stringify(props)}>
      Form Content Component
    </div>
  ),
}));

const defaultProps: FormLayoutProps = {
  index: 0,
  settings: {
    id: 'section-1',
    type: 'section' as any,
    name: 'Test Section',
    layout: {
      type: 'image-top' as any,
      imageUrl: 'https://example.com/test-image.jpg',
      fieldWidth: '600px',
    },
    content: [
      {
        id: 'field-1',
        type: 'short_qa' as any,
        label: 'Test Field',
        name: 'test_field',
        validators: [],
      },
    ],
  },
  showBackButton: false,
  showNextButton: true,
  showSubmitButton: false,
  showSkipButton: false,
  isSaving: false,
  onBack: vi.fn(),
  onNext: vi.fn(),
  onSubmit: vi.fn(),
};

describe('ImageTopLayout', () => {
  it('renders TopImage component with correct imageUrl', () => {
    renderWithProviders(<ImageTopLayout {...defaultProps} />);

    const topImage = screen.getByTestId('top-image');
    expect(topImage).toBeInTheDocument();
    expect(topImage).toHaveAttribute('data-image-url', 'https://example.com/test-image.jpg');
  });

  it('renders FormContent component with correct props', () => {
    renderWithProviders(<ImageTopLayout {...defaultProps} />);

    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();

    const propsData = JSON.parse(formContent.getAttribute('data-props') || '{}');
    expect(propsData.index).toBe(0);
    expect(propsData.showNextButton).toBe(true);
  });

  it('renders container with correct ID', () => {
    renderWithProviders(<ImageTopLayout {...defaultProps} />);

    const container = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(container).toHaveAttribute('id', 'content_container_0');
  });

  it('applies correct field width from settings', () => {
    const propsWithCustomWidth = {
      ...defaultProps,
      settings: {
        ...defaultProps.settings,
        layout: {
          ...defaultProps.settings.layout,
          fieldWidth: '800px',
        },
      },
    };

    renderWithProviders(<ImageTopLayout {...propsWithCustomWidth} />);

    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();
  });

  it('handles null imageUrl correctly', () => {
    const propsWithNullImage = {
      ...defaultProps,
      settings: {
        ...defaultProps.settings,
        layout: {
          ...defaultProps.settings.layout,
          imageUrl: null,
        },
      },
    };

    renderWithProviders(<ImageTopLayout {...propsWithNullImage} />);

    const topImage = screen.getByTestId('top-image');
    expect(topImage).toHaveAttribute('data-image-url', '');
  });

  it('renders with correct layout structure', () => {
    renderWithProviders(<ImageTopLayout {...defaultProps} />);

    // Check that both TopImage and FormContent are rendered
    expect(screen.getByTestId('top-image')).toBeInTheDocument();
    expect(screen.getByTestId('form-content')).toBeInTheDocument();
  });

  it('passes all props to FormContent component', () => {
    const customProps = {
      ...defaultProps,
      showBackButton: true,
      showSubmitButton: true,
      showSkipButton: true,
      isSaving: true,
    };

    renderWithProviders(<ImageTopLayout {...customProps} />);

    const formContent = screen.getByTestId('form-content');
    const propsData = JSON.parse(formContent.getAttribute('data-props') || '{}');

    expect(propsData.showBackButton).toBe(true);
    expect(propsData.showSubmitButton).toBe(true);
    expect(propsData.showSkipButton).toBe(true);
    expect(propsData.isSaving).toBe(true);
  });

  it('renders with different index values', () => {
    const propsWithIndex1 = { ...defaultProps, index: 1 };
    const { unmount } = renderWithProviders(<ImageTopLayout {...propsWithIndex1} />);

    const container = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(container).toHaveAttribute('id', 'content_container_1');

    unmount();

    const propsWithIndex2 = { ...defaultProps, index: 2 };
    renderWithProviders(<ImageTopLayout {...propsWithIndex2} />);

    const container2 = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(container2).toHaveAttribute('id', 'content_container_2');
  });
});
