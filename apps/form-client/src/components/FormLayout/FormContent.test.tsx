import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import FormContent from './FormContent';
import type { FormLayoutProps } from './FormLayout';

// Mock TolgeeProvider
vi.mock('@tolgee/react', () => ({
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useTranslate: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
}));

// Mock the hooks and components
vi.mock('@/hooks/form/useButtonStyles', () => ({
  default: vi.fn(() => ({
    backgroundColor: '#007bff',
    color: '#ffffff',
    fontSize: '14px',
  })),
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => ({ buttonsDisabled: false }),
  AppContextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

vi.mock('@/contexts/FormSettingsContext', () => ({
  useFormSettings: () => ({
    appearance: {
      customize: false,
      buttonStyle: 'default',
      defaultSettings: { inputStyle: 'default' },
    },
  }),
  FormSettingsProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

vi.mock('../FormFields/FormFields', () => ({
  default: ({ fields }: { fields: any[] }) => (
    <div data-testid='form-fields'>
      {fields.map((field) => (
        <div key={field.id} data-testid={`field-${field.id}`}>
          {field.label}
        </div>
      ))}
    </div>
  ),
}));

const defaultProps: FormLayoutProps = {
  index: 0,
  settings: {
    id: 'section-1',
    type: 'section' as any,
    name: 'Test Section',
    layout: {
      type: 'image-top' as any,
      imageUrl: null,
      fieldWidth: '600px',
    },
    content: [
      {
        id: 'field-1',
        type: 'short_qa' as any,
        label: 'Test Field',
        name: 'test_field',
        validators: [],
      },
    ],
  },
  showBackButton: false,
  showNextButton: true,
  showSubmitButton: false,
  showSkipButton: false,
  isSaving: false,
  onBack: vi.fn(),
  onNext: vi.fn(),
  onSubmit: vi.fn(),
};

describe('FormContent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders form fields correctly', () => {
    renderWithProviders(<FormContent {...defaultProps} />);

    expect(screen.getByTestId('form-fields')).toBeInTheDocument();
    expect(screen.getByTestId('field-field-1')).toBeInTheDocument();
    expect(screen.getByText('Test Field')).toBeInTheDocument();
  });

  it('renders next button when showNextButton is true', () => {
    renderWithProviders(<FormContent {...defaultProps} showNextButton={true} />);

    expect(screen.getByText('next')).toBeInTheDocument();
  });

  it('does not render next button when showNextButton is false', () => {
    renderWithProviders(<FormContent {...defaultProps} showNextButton={false} />);

    expect(screen.queryByText('next')).not.toBeInTheDocument();
  });

  it('renders back button when showBackButton is true', () => {
    renderWithProviders(<FormContent {...defaultProps} showBackButton={true} />);

    expect(screen.getByText('back')).toBeInTheDocument();
  });

  it('does not render back button when showBackButton is false', () => {
    renderWithProviders(<FormContent {...defaultProps} showBackButton={false} />);

    expect(screen.queryByText('back')).not.toBeInTheDocument();
  });

  it('renders submit button when showSubmitButton is true', () => {
    renderWithProviders(<FormContent {...defaultProps} showSubmitButton={true} />);

    expect(screen.getByText('submit')).toBeInTheDocument();
  });

  it('does not render submit button when showSubmitButton is false', () => {
    renderWithProviders(<FormContent {...defaultProps} showSubmitButton={false} />);

    expect(screen.queryByText('submit')).not.toBeInTheDocument();
  });

  it('renders skip button when showSkipButton is true', () => {
    renderWithProviders(<FormContent {...defaultProps} showSkipButton={true} />);

    expect(screen.getByText('skip')).toBeInTheDocument();
  });

  it('does not render skip button when showSkipButton is false', () => {
    renderWithProviders(<FormContent {...defaultProps} showSkipButton={false} />);

    expect(screen.queryByText('skip')).not.toBeInTheDocument();
  });

  it('calls onNext when next button is clicked', () => {
    const onNext = vi.fn();
    renderWithProviders(<FormContent {...defaultProps} onNext={onNext} />);

    fireEvent.click(screen.getByText('next'));
    expect(onNext).toHaveBeenCalledTimes(1);
  });

  it('calls onBack when back button is clicked', () => {
    const onBack = vi.fn();
    renderWithProviders(<FormContent {...defaultProps} showBackButton={true} onBack={onBack} />);

    fireEvent.click(screen.getByText('back'));
    expect(onBack).toHaveBeenCalledTimes(1);
  });

  it('calls onSubmit when submit button is clicked', () => {
    const onSubmit = vi.fn();
    renderWithProviders(
      <FormContent {...defaultProps} showSubmitButton={true} onSubmit={onSubmit} />
    );

    fireEvent.click(screen.getByText('submit'));
    expect(onSubmit).toHaveBeenCalledTimes(1);
  });

  it('calls onNext when skip button is clicked', () => {
    const onNext = vi.fn();
    renderWithProviders(<FormContent {...defaultProps} showSkipButton={true} onNext={onNext} />);

    fireEvent.click(screen.getByText('skip'));
    expect(onNext).toHaveBeenCalledTimes(1);
  });

  it('shows loading state on submit button when isSaving is true', () => {
    renderWithProviders(<FormContent {...defaultProps} showSubmitButton={true} isSaving={true} />);

    const submitButton = screen.getByText('submit');
    expect(submitButton.closest('button')).toHaveAttribute('data-loading', 'true');
  });

  it('renders all buttons when all show flags are true', () => {
    renderWithProviders(
      <FormContent
        {...defaultProps}
        showNextButton={true}
        showBackButton={true}
        showSubmitButton={true}
        showSkipButton={true}
      />
    );

    expect(screen.getByText('next')).toBeInTheDocument();
    expect(screen.getByText('back')).toBeInTheDocument();
    expect(screen.getByText('submit')).toBeInTheDocument();
    expect(screen.getByText('skip')).toBeInTheDocument();
  });

  it('renders only submit button when only showSubmitButton is true', () => {
    renderWithProviders(
      <FormContent
        {...defaultProps}
        showNextButton={false}
        showBackButton={false}
        showSubmitButton={true}
        showSkipButton={false}
      />
    );

    expect(screen.queryByText('next')).not.toBeInTheDocument();
    expect(screen.queryByText('back')).not.toBeInTheDocument();
    expect(screen.getByText('submit')).toBeInTheDocument();
    expect(screen.queryByText('skip')).not.toBeInTheDocument();
  });

  it('renders empty form when content is empty', () => {
    const propsWithEmptyContent = {
      ...defaultProps,
      settings: {
        ...defaultProps.settings,
        content: [],
      },
    };

    renderWithProviders(<FormContent {...propsWithEmptyContent} />);

    expect(screen.getByTestId('form-fields')).toBeInTheDocument();
    expect(screen.queryByTestId('field-field-1')).not.toBeInTheDocument();
  });

  it('applies button styles from useButtonStyles hook', () => {
    renderWithProviders(<FormContent {...defaultProps} showNextButton={true} />);

    const nextButton = screen.getByText('next').closest('button');
    expect(nextButton).toBeInTheDocument();
  });
});
