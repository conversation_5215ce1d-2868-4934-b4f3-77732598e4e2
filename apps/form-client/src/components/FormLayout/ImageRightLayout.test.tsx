import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import type { FormLayoutProps } from './FormLayout';
import ImageRightLayout from './ImageRightLayout';

// Mock dependencies
vi.mock('./SideImage', () => ({
  default: ({ imageUrl }: { imageUrl: string | null }) => (
    <div data-testid='side-image' data-image-url={imageUrl || ''}>
      Side Image Component
    </div>
  ),
}));

vi.mock('./FormContent', () => ({
  default: (props: any) => (
    <div data-testid='form-content' data-props={JSON.stringify(props)}>
      Form Content Component
    </div>
  ),
}));

const defaultProps: FormLayoutProps = {
  index: 0,
  settings: {
    id: 'section-1',
    type: 'section' as any,
    name: 'Test Section',
    layout: {
      type: 'image-right' as any,
      imageUrl: 'https://example.com/test-image.jpg',
      fieldWidth: '600px',
    },
    content: [
      {
        id: 'field-1',
        type: 'short_qa' as any,
        label: 'Test Field',
        name: 'test_field',
        validators: [],
      },
    ],
  },
  showBackButton: false,
  showNextButton: true,
  showSubmitButton: false,
  showSkipButton: false,
  isSaving: false,
  onBack: vi.fn(),
  onNext: vi.fn(),
  onSubmit: vi.fn(),
};

describe('ImageRightLayout', () => {
  it('renders SideImage component with correct imageUrl', () => {
    renderWithProviders(<ImageRightLayout {...defaultProps} />);

    const sideImage = screen.getByTestId('side-image');
    expect(sideImage).toBeInTheDocument();
    expect(sideImage).toHaveAttribute('data-image-url', 'https://example.com/test-image.jpg');
  });

  it('renders FormContent component with correct props', () => {
    renderWithProviders(<ImageRightLayout {...defaultProps} />);

    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();

    const propsData = JSON.parse(formContent.getAttribute('data-props') || '{}');
    expect(propsData.index).toBe(0);
    expect(propsData.showNextButton).toBe(true);
  });

  it('renders container with correct ID', () => {
    renderWithProviders(<ImageRightLayout {...defaultProps} />);

    const container = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(container).toHaveAttribute('id', 'content_container_0');
  });

  it('applies correct field width from settings', () => {
    const propsWithCustomWidth = {
      ...defaultProps,
      settings: {
        ...defaultProps.settings,
        layout: {
          ...defaultProps.settings.layout,
          fieldWidth: '800px',
        },
      },
    };

    renderWithProviders(<ImageRightLayout {...propsWithCustomWidth} />);

    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();
  });

  it('handles null imageUrl correctly', () => {
    const propsWithNullImage = {
      ...defaultProps,
      settings: {
        ...defaultProps.settings,
        layout: {
          ...defaultProps.settings.layout,
          imageUrl: null,
        },
      },
    };

    renderWithProviders(<ImageRightLayout {...propsWithNullImage} />);

    const sideImage = screen.getByTestId('side-image');
    expect(sideImage).toHaveAttribute('data-image-url', '');
  });

  it('renders with correct layout structure', () => {
    renderWithProviders(<ImageRightLayout {...defaultProps} />);

    // Check that both SideImage and FormContent are rendered
    expect(screen.getByTestId('side-image')).toBeInTheDocument();
    expect(screen.getByTestId('form-content')).toBeInTheDocument();
  });

  it('passes all props to FormContent component', () => {
    const customProps = {
      ...defaultProps,
      showBackButton: true,
      showSubmitButton: true,
      showSkipButton: true,
      isSaving: true,
    };

    renderWithProviders(<ImageRightLayout {...customProps} />);

    const formContent = screen.getByTestId('form-content');
    const propsData = JSON.parse(formContent.getAttribute('data-props') || '{}');

    expect(propsData.showBackButton).toBe(true);
    expect(propsData.showSubmitButton).toBe(true);
    expect(propsData.showSkipButton).toBe(true);
    expect(propsData.isSaving).toBe(true);
  });

  it('renders with different index values', () => {
    const propsWithIndex1 = { ...defaultProps, index: 1 };
    const { unmount } = renderWithProviders(<ImageRightLayout {...propsWithIndex1} />);

    const container = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(container).toHaveAttribute('id', 'content_container_1');

    unmount();

    const propsWithIndex2 = { ...defaultProps, index: 2 };
    renderWithProviders(<ImageRightLayout {...propsWithIndex2} />);

    const container2 = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(container2).toHaveAttribute('id', 'content_container_2');
  });

  it('renders footer when renderFooter is provided', () => {
    const mockFooter = vi.fn(() => <div data-testid='footer'>Footer Content</div>);
    const propsWithFooter = {
      ...defaultProps,
      renderFooter: mockFooter,
    };

    renderWithProviders(<ImageRightLayout {...propsWithFooter} />);

    expect(screen.getByTestId('footer')).toBeInTheDocument();
    expect(mockFooter).toHaveBeenCalledTimes(1);
  });

  it('does not render footer when renderFooter is not provided', () => {
    renderWithProviders(<ImageRightLayout {...defaultProps} />);

    expect(screen.queryByTestId('footer')).not.toBeInTheDocument();
  });

  it('renders with responsive layout structure', () => {
    renderWithProviders(<ImageRightLayout {...defaultProps} />);

    // The layout should have a flex direction column-reverse on mobile, row on desktop
    const layoutContainer = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(layoutContainer).toBeInTheDocument();
  });

  it('renders FormContent before SideImage in DOM order', () => {
    renderWithProviders(<ImageRightLayout {...defaultProps} />);

    const formContent = screen.getByTestId('form-content');
    const sideImage = screen.getByTestId('side-image');

    // In the DOM, FormContent should come before SideImage for this layout
    const container = formContent.closest('[id*="content_container"]');
    const children = Array.from(container?.children || []);

    const formContentIndex = children.findIndex((child) => child.contains(formContent));
    const sideImageIndex = children.findIndex((child) => child.contains(sideImage));

    expect(formContentIndex).toBeLessThan(sideImageIndex);
  });
});
