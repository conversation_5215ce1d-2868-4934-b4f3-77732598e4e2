import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../test-utils';
import type { FormLayoutProps } from './FormLayout';
import ImageBackgroundLayout from './ImageBackgroundLayout';

// Mock dependencies
vi.mock('./FormContent', () => ({
  default: (props: any) => (
    <div data-testid='form-content' data-props={JSON.stringify(props)}>
      Form Content Component
    </div>
  ),
}));

const defaultProps: FormLayoutProps = {
  index: 0,
  settings: {
    id: 'section-1',
    type: 'section' as any,
    name: 'Test Section',
    layout: {
      type: 'image-background' as any,
      imageUrl: 'https://example.com/test-image.jpg',
      fieldWidth: '600px',
    },
    content: [
      {
        id: 'field-1',
        type: 'short_qa' as any,
        label: 'Test Field',
        name: 'test_field',
        validators: [],
      },
    ],
  },
  showBackButton: false,
  showNextButton: true,
  showSubmitButton: false,
  showSkipButton: false,
  isSaving: false,
  onBack: vi.fn(),
  onNext: vi.fn(),
  onSubmit: vi.fn(),
};

describe('ImageBackgroundLayout', () => {
  it('renders FormContent component with correct props', () => {
    renderWithProviders(<ImageBackgroundLayout {...defaultProps} />);

    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();

    const propsData = JSON.parse(formContent.getAttribute('data-props') || '{}');
    expect(propsData.index).toBe(0);
    expect(propsData.showNextButton).toBe(true);
  });

  it('renders container with correct ID', () => {
    renderWithProviders(<ImageBackgroundLayout {...defaultProps} />);

    const container = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(container).toHaveAttribute('id', 'content_container_0');
  });

  it('applies correct field width from settings', () => {
    const propsWithCustomWidth = {
      ...defaultProps,
      settings: {
        ...defaultProps.settings,
        layout: {
          ...defaultProps.settings.layout,
          fieldWidth: '800px',
        },
      },
    };

    renderWithProviders(<ImageBackgroundLayout {...propsWithCustomWidth} />);

    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();
  });

  it('handles null imageUrl correctly', () => {
    const propsWithNullImage = {
      ...defaultProps,
      settings: {
        ...defaultProps.settings,
        layout: {
          ...defaultProps.settings.layout,
          imageUrl: null,
        },
      },
    };

    renderWithProviders(<ImageBackgroundLayout {...propsWithNullImage} />);

    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();
  });

  it('renders with correct layout structure', () => {
    renderWithProviders(<ImageBackgroundLayout {...defaultProps} />);

    // Check that FormContent is rendered
    expect(screen.getByTestId('form-content')).toBeInTheDocument();
  });

  it('passes all props to FormContent component', () => {
    const customProps = {
      ...defaultProps,
      showBackButton: true,
      showSubmitButton: true,
      showSkipButton: true,
      isSaving: true,
    };

    renderWithProviders(<ImageBackgroundLayout {...customProps} />);

    const formContent = screen.getByTestId('form-content');
    const propsData = JSON.parse(formContent.getAttribute('data-props') || '{}');

    expect(propsData.showBackButton).toBe(true);
    expect(propsData.showSubmitButton).toBe(true);
    expect(propsData.showSkipButton).toBe(true);
    expect(propsData.isSaving).toBe(true);
  });

  it('renders with different index values', () => {
    const propsWithIndex1 = { ...defaultProps, index: 1 };
    const { unmount } = renderWithProviders(<ImageBackgroundLayout {...propsWithIndex1} />);

    const container = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(container).toHaveAttribute('id', 'content_container_1');

    unmount();

    const propsWithIndex2 = { ...defaultProps, index: 2 };
    renderWithProviders(<ImageBackgroundLayout {...propsWithIndex2} />);

    const container2 = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(container2).toHaveAttribute('id', 'content_container_2');
  });

  it('renders with background transparency when provided', () => {
    const propsWithTransparency = {
      ...defaultProps,
      backgroundTransparency: '0.5',
    };

    renderWithProviders(<ImageBackgroundLayout {...propsWithTransparency} />);

    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();
  });

  it('renders with default background transparency when not provided', () => {
    renderWithProviders(<ImageBackgroundLayout {...defaultProps} />);

    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();
  });

  it('renders with responsive layout structure', () => {
    renderWithProviders(<ImageBackgroundLayout {...defaultProps} />);

    // The layout should have a container with proper positioning
    const layoutContainer = screen.getByTestId('form-content').closest('[id*="content_container"]');
    expect(layoutContainer).toBeInTheDocument();
  });

  it('applies background image styles correctly', () => {
    renderWithProviders(<ImageBackgroundLayout {...defaultProps} />);

    // The component should render with background image
    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();
  });

  it('renders with proper container structure', () => {
    renderWithProviders(<ImageBackgroundLayout {...defaultProps} />);

    // Should render the form content
    const formContent = screen.getByTestId('form-content');
    expect(formContent).toBeInTheDocument();
  });

  it('handles different image URLs correctly', () => {
    const imageUrls = [
      'https://example.com/image1.jpg',
      'https://example.com/image2.png',
      'https://example.com/image3.webp',
    ];

    imageUrls.forEach((url) => {
      const propsWithImage = {
        ...defaultProps,
        settings: {
          ...defaultProps.settings,
          layout: {
            ...defaultProps.settings.layout,
            imageUrl: url,
          },
        },
      };

      const { unmount } = renderWithProviders(<ImageBackgroundLayout {...propsWithImage} />);

      const formContent = screen.getByTestId('form-content');
      expect(formContent).toBeInTheDocument();

      unmount();
    });
  });
});
