import { AppContextProvider } from '@/contexts/AppContext';
import { FormProvider } from '@/contexts/FormContext';
import { FormSettingsProvider } from '@/contexts/FormSettingsContext';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { type RenderOptions, render } from '@testing-library/react';
import { TolgeeProvider } from '@tolgee/react';
import type React from 'react';

import { vi } from 'vitest';

// Mock dependencies
vi.mock('@/contexts/LiffContext', () => ({
  useLiff: () => ({
    isLoggedIn: false,
    profile: null,
  }),
}));

vi.mock('@/hooks/form/useSubmitForm', () => ({
  useSubmitForm: () => ({
    submitForm: vi.fn(),
    isSubmitting: false,
  }),
}));

vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    query: {},
    pathname: '/',
    asPath: '/',
    route: '/',
    back: vi.fn(),
    forward: vi.fn(),
    reload: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
    isFallback: false,
    isLocaleDomain: false,
    isReady: true,
    defaultLocale: 'en',
    domainLocales: [],
    isPreview: false,
  }),
}));

vi.mock('@/contexts/FormContext', () => ({
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useFormContext: () => ({
    getInputProps: () => ({
      value: '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
      error: undefined,
    }),
    setFieldValue: vi.fn(),
    getValues: () => ({}),
    validate: vi.fn(),
    clearFieldError: vi.fn(),
  }),
}));

vi.mock('@mantine/form', () => ({
  useFormContext: () => ({
    getInputProps: () => ({
      value: '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
      error: undefined,
    }),
    setFieldValue: vi.fn(),
    getValues: () => ({}),
    validate: vi.fn(),
    clearFieldError: vi.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

// Mock Mantine Input component to avoid form context issues
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    Input: Object.assign(
      ({ type, ...props }: any) => {
        if (type === 'hidden') {
          return <input type='hidden' {...props} data-testid='hidden-input' />;
        }
        return <input {...props} data-testid='input' />;
      },
      {
        Wrapper: ({ children, label, error, description, ...props }: any) => (
          <div {...props}>
            {label && <div data-testid='input-label'>{label}</div>}
            {children}
            {error && <div data-testid='input-error'>{error}</div>}
            {description && <div data-testid='input-description'>{description}</div>}
          </div>
        ),
      }
    ),
    TextInput: ({
      label,
      placeholder,
      required,
      description,
      error,
      leftSection,
      rightSection,
      onChange,
      disabled,
      ...props
    }: any) => (
      <div data-testid='text-input'>
        {label && (
          <label htmlFor='text-input-field' data-testid='field-label'>
            {label}
            {required && <span>*</span>}
          </label>
        )}
        <input
          type='text'
          placeholder={placeholder}
          data-testid='text-input-field'
          id='text-input-field'
          required={required}
          disabled={disabled}
          onChange={(e) => onChange?.(e)}
          {...props}
        />
        {leftSection && <div data-testid='text-input-icon'>{leftSection}</div>}
        {rightSection && <div data-testid='right-section'>{rightSection}</div>}
        {description && <div data-testid='field-description'>{description}</div>}
        {error && <div data-testid='field-error'>{error}</div>}
      </div>
    ),
  };
});

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useTranslate: () => ({
    t: (key: string) => {
      const translations = {
        noInternetConnectionTitle: 'No Internet Connection',
        noInternetConnectionDescription: 'Please check your connection and try again.',
        canSelectMultipleOptions: 'You can select multiple options',
        otherOptionLabel: 'Other',
      };
      return translations[key] || key;
    },
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
  useTolgee: () => ({
    isLoaded: () => true,
    getRequiredRecords: () => [],
  }),
}));

// Default form settings for testing
export const defaultFormSettings = {
  id: 'test-form',
  name: 'Test Form',
  description: 'Test form description',
  appearance: {
    customize: false,
    defaultSettings: {
      inputStyle: 'default',
    },
    formFieldStyle: {},
    headingStyle: {},
    paragraphStyle: {},
  },
  setting: {
    enableProgressBar: true,
    enablePageNavigation: true,
  },
};

// Default form pages for testing
export const defaultFormPages = [
  {
    id: 'page-1',
    title: 'Page 1',
    fields: [
      {
        id: 'field-1',
        type: 'short_qa',
        label: 'Test Field',
        placeholder: 'Enter test value',
        required: true,
      },
    ],
  },
];

// Default form field for testing
export const defaultFormField = {
  id: 'test-field',
  type: 'short_qa',
  label: 'Test Field',
  placeholder: 'Enter test value',
  required: true,
};

// Custom render function with all necessary providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  formSettings?: typeof defaultFormSettings;
  formPages?: typeof defaultFormPages;
}

export const renderWithProviders = (ui: React.ReactElement, options: CustomRenderOptions = {}) => {
  const {
    formSettings = defaultFormSettings,
    formPages = defaultFormPages,
    ...renderOptions
  } = options;

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <MantineEmotionProvider>
      <MantineProvider
        theme={{
          colors: {
            decaLight: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaNavy: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaDark: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaGrey: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
            decaRed: [
              '#f8f9fa',
              '#e9ecef',
              '#dee2e6',
              '#ced4da',
              '#adb5bd',
              '#6c757d',
              '#495057',
              '#343a40',
              '#212529',
              '#000000',
            ],
          },
          breakpoints: {
            sm: '768px',
          },
        }}
      >
        <TolgeeProvider tolgee={{} as any}>
          <AppContextProvider>
            <FormSettingsProvider formSettings={formSettings as any} hiddenFields={[]}>
              <FormProvider form={{} as any}>{children}</FormProvider>
            </FormSettingsProvider>
          </AppContextProvider>
        </TolgeeProvider>
      </MantineProvider>
    </MantineEmotionProvider>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock data generators
export const createMockFormField = (overrides = {}) => ({
  id: 'test-field',
  type: 'short_qa',
  label: 'Test Field',
  placeholder: 'Enter test value',
  required: true,
  ...overrides,
});

export const createMockFormPage = (overrides = {}) => ({
  id: 'page-1',
  title: 'Test Page',
  fields: [createMockFormField()],
  ...overrides,
});

export const createMockFormSettings = (overrides = {}) => ({
  ...defaultFormSettings,
  ...overrides,
});

// Test data for different field types
export const fieldTypeTestData = {
  short_qa: {
    type: 'short_qa',
    label: 'Short Answer',
    placeholder: 'Enter short answer',
  },
  long_qa: {
    type: 'long_qa',
    label: 'Long Answer',
    placeholder: 'Enter long answer',
  },
  number: {
    type: 'number',
    label: 'Number',
    placeholder: 'Enter number',
  },
  email: {
    type: 'email',
    label: 'Email',
    placeholder: 'Enter email',
  },
  website: {
    type: 'website',
    label: 'Website',
    placeholder: 'Enter website',
  },
  checkbox: {
    type: 'checkbox',
    label: 'Checkbox',
    options: [{ label: 'Option 1', value: 'option1' }],
  },
  checkboxes: {
    type: 'checkboxes',
    label: 'Multiple Choice',
    options: [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' },
    ],
  },
  radio: {
    type: 'radio',
    label: 'Single Choice',
    options: [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' },
    ],
  },
  dropdown: {
    type: 'dropdown',
    label: 'Dropdown',
    options: [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' },
    ],
  },
  date: {
    type: 'date',
    label: 'Date',
  },
  time: {
    type: 'time',
    label: 'Time',
  },
  datetime: {
    type: 'datetime',
    label: 'Date and Time',
  },
  file_upload: {
    type: 'file_upload',
    label: 'File Upload',
    maxFiles: 5,
    maxSize: 10 * 1024 * 1024, // 10MB
    acceptedTypes: ['image/*', 'application/pdf'],
  },
  rating: {
    type: 'rating',
    label: 'Rating',
    maxRating: 5,
  },
  opinion_scale: {
    type: 'opinion_scale',
    label: 'Opinion Scale',
    minValue: 1,
    maxValue: 10,
  },
  heading: {
    type: 'heading',
    label: 'Heading',
  },
  paragraph: {
    type: 'paragraph',
    label: 'Paragraph text',
  },
};

// Utility functions for testing
export const waitForElementToBeRemoved = (element: Element) => {
  return new Promise((resolve) => {
    const observer = new MutationObserver(() => {
      if (!document.contains(element)) {
        observer.disconnect();
        resolve(true);
      }
    });
    observer.observe(document.body, { childList: true, subtree: true });
  });
};

export const createMockFile = (name: string, size: number, type: string) => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

export const mockFormSubmission = vi.fn();

export const mockFormValidation = vi.fn();

// Export everything from testing library for convenience
export * from '@testing-library/react';
