# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [0.19.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.19.0) (2025-07-21)


### Features

* **tables:** TK-2362 improve clear data ux ([#6189](https://github.com/resola-ai/deca-apps/issues/6189)) ([3803fdb](https://github.com/resola-ai/deca-apps/commit/3803fdb2f51618e5d2f4075c3c48bc22c0653549))
* **tables:** TK-4587 update filter rule group header text ([#6148](https://github.com/resola-ai/deca-apps/issues/6148)) ([173a79a](https://github.com/resola-ai/deca-apps/commit/173a79a7912dfee60591a1f90e7da04f63c57c2b))
* **tables:** TK-6165 fix incorrect restore notification message ([#6124](https://github.com/resola-ai/deca-apps/issues/6124)) ([6e93729](https://github.com/resola-ai/deca-apps/commit/6e93729db1c790f2443f8e55095e2814b330f450))
* **tables:** TK-6167 update view icon ui in table toolbar ([#6141](https://github.com/resola-ai/deca-apps/issues/6141)) ([6320355](https://github.com/resola-ai/deca-apps/commit/6320355ecced115a7959bc457d0120e991d5a9bf))
* **tables:** TK-6352 fix table header style ([#6128](https://github.com/resola-ai/deca-apps/issues/6128)) ([5b2bef3](https://github.com/resola-ai/deca-apps/commit/5b2bef355b3c20184c77d482a82455af3aa6c39e))
* **tables:** TK-6356 update edit field form ui ([#6152](https://github.com/resola-ai/deca-apps/issues/6152)) ([55b672a](https://github.com/resola-ai/deca-apps/commit/55b672a163fe9ebb1dfa3e499c0cfadff3bc5a4b))
* **tables:** TK-8769 disable thousand seperator base on setting ([#6335](https://github.com/resola-ai/deca-apps/issues/6335)) ([a72222f](https://github.com/resola-ai/deca-apps/commit/a72222f692433cc4c2ebc37284583f1fe9214e5e))
* **tables:** TK-8769 update currency field setting form ([#6169](https://github.com/resola-ai/deca-apps/issues/6169)) ([102c2b2](https://github.com/resola-ai/deca-apps/commit/102c2b2a563e0fcb7e9109953f3dbf6f6bb670db))
* **tables:** TK-9106 fix date picker style ([#6142](https://github.com/resola-ai/deca-apps/issues/6142)) ([c8d6e10](https://github.com/resola-ai/deca-apps/commit/c8d6e105e336211f2a10101e4ee1ebefbc3758cc))
* **tables:** TK-9108 update percent field icon ([#6123](https://github.com/resola-ai/deca-apps/issues/6123)) ([80c8399](https://github.com/resola-ai/deca-apps/commit/80c8399372231dff51e1811537208d008f6f9256))
* **tables:** TK-9346 update table remove status code ([#6338](https://github.com/resola-ai/deca-apps/issues/6338)) ([b031786](https://github.com/resola-ai/deca-apps/commit/b03178606f4472b349588aa121a68a3a704d9cdf))

## [0.18.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.18.0) (2025-06-30)


### Features

* **tables:** fix update primary field ([#6015](https://github.com/resola-ai/deca-apps/issues/6015)) ([46d8094](https://github.com/resola-ai/deca-apps/commit/46d8094136e32384575e3d68fdff7639dac4b47b))
* **tables:** TK-3602 implement new ui/ux for add record ([#5975](https://github.com/resola-ai/deca-apps/issues/5975)) ([7dcc1e3](https://github.com/resola-ai/deca-apps/commit/7dcc1e3a8c6da0b498c3dce92d6d3f3aa29ce08e))
* **tables:** TK-3924 apply the button from design system to tables ([#5841](https://github.com/resola-ai/deca-apps/issues/5841)) ([5ba30a1](https://github.com/resola-ai/deca-apps/commit/5ba30a1595a1937b119e0e99b92f680319794a1f))
* **tables:** TK-4149 implement default current date for datetime field ([#5840](https://github.com/resola-ai/deca-apps/issues/5840)) ([89095d9](https://github.com/resola-ai/deca-apps/commit/89095d93ad5c43017e838be964dda5bde9618a4e))
* **tables:** TK-4451 support negative number in number field ([#5821](https://github.com/resola-ai/deca-apps/issues/5821)) ([ca1324b](https://github.com/resola-ai/deca-apps/commit/ca1324bc5e9f1dcac0c4d457c4a2f9220067b172))
* **tables:** TK-6015 sync field type select with design ([#5815](https://github.com/resola-ai/deca-apps/issues/5815)) ([334f683](https://github.com/resola-ai/deca-apps/commit/334f683745fc51bbf4f372ce1cc38dbe9f134ed3))
* **tables:** TK-6019 correct cell background color on hover during edit mode ([#5814](https://github.com/resola-ai/deca-apps/issues/5814)) ([863fd06](https://github.com/resola-ai/deca-apps/commit/863fd06f0b783a0f7a1526a89b3a38bd3f09e2d9))
* **tables:** TK-6129 fix search ui ([#6037](https://github.com/resola-ai/deca-apps/issues/6037)) ([dc27344](https://github.com/resola-ai/deca-apps/commit/dc27344785b670109c6275e29483471aa0eaffb1))
* **tables:** TK-6129 implement table search ([#5871](https://github.com/resola-ai/deca-apps/issues/5871)) ([453cf6a](https://github.com/resola-ai/deca-apps/commit/453cf6a91818dc5fe7f5720c1aeb7b1e6b1bc629))
* **tables:** TK-7762 fix menu not working ([7294274](https://github.com/resola-ai/deca-apps/commit/7294274bfe460e943096fed187e4c212e9dd96d7))
* **tables:** TK-8084 show only supported types in change primary field modal ([cd1c635](https://github.com/resola-ai/deca-apps/commit/cd1c6357c6558df47752b96d8d0e878c18c43834))
* **tables:** TK-8085 disable insert left for primary field ([0a37f94](https://github.com/resola-ai/deca-apps/commit/0a37f944381318871ecdcf71fd3c9455e116e4ff))
* **tables:** TK-8086 fix previous table showing column from newly added table ([6d70cfe](https://github.com/resola-ai/deca-apps/commit/6d70cfec8d0a200421394c1cfb54010bbc04534e))
* **tables:** TK-8184 limit upload image field accept type ([3517222](https://github.com/resola-ai/deca-apps/commit/3517222f74e9b3b894fa676f36ddee41dbc178b3))
* **tables:** TK-8211 add table id to presign url payload ([#5990](https://github.com/resola-ai/deca-apps/issues/5990)) ([c132022](https://github.com/resola-ai/deca-apps/commit/c132022cb4977b7e6c3c69d90dff583097f94104))
* **tables:** TK-8234 fix image download name and popup title ([#6043](https://github.com/resola-ai/deca-apps/issues/6043)) ([19567bf](https://github.com/resola-ai/deca-apps/commit/19567bf9648503f8d90b1491a8a57aee97129089))
* **tables:** TK-8234 update image follow design ([#5930](https://github.com/resola-ai/deca-apps/issues/5930)) ([169b880](https://github.com/resola-ai/deca-apps/commit/169b8807063261b3ff31cd5b9ea59c92758d7a85))
* **tables:** TK-8234 update preview image title ([#6062](https://github.com/resola-ai/deca-apps/issues/6062)) ([df4d3a2](https://github.com/resola-ai/deca-apps/commit/df4d3a2958de39242c6b17b53461a7d6f691dfdb))
* **tables:** TK-8257 update primary field modal translation ([b0c3264](https://github.com/resola-ai/deca-apps/commit/b0c3264ad201296604f7984a24e4d94879a2e86f))

## [0.17.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.17.0) (2025-06-10)


### Features

* **tables:** TK-4101 fix percent cell default value ([4ef9702](https://github.com/resola-ai/deca-apps/commit/4ef9702872ceb2a923882712cb4adef3f5c90d2f))
* **tables:** TK-5106 implement shared component to tables ([964ff7a](https://github.com/resola-ai/deca-apps/commit/964ff7a9fd71c217d94a57c0a27fa424b5ae1c7f))
* **tables:** TK-6126 enable column ordering for table header ([61f7860](https://github.com/resola-ai/deca-apps/commit/61f7860b627c747eae40387854f25000e8dce781))
* **tables:** TK-7586 implement primary field ([6208de2](https://github.com/resola-ai/deca-apps/commit/6208de23cb8ecdd3fbf099f353c6cfd0d9bdde40))
* **tables:** TK-7586 update API integration ([0765c1f](https://github.com/resola-ai/deca-apps/commit/0765c1f01e0caa4f65529dd2192471342ff10fe5))
* **tables:** TK-7762 fix menu not working ([0e58e10](https://github.com/resola-ai/deca-apps/commit/0e58e1039e48eca44b68b2ad91757a022a01cfa8))
* **tables:** TK-7762 handle image field type ([0fc84f1](https://github.com/resola-ai/deca-apps/commit/0fc84f18b4a3ff2b67f6d86fffda3adfc30bfa6c))
* **tables:** TK-7765 implement freeze column ([86dc16a](https://github.com/resola-ai/deca-apps/commit/86dc16aeb24aa74c0f31690a96c5899564da8d09))
* **tables:** TK-8084 show only supported types in change primary field modal ([acad507](https://github.com/resola-ai/deca-apps/commit/acad507c2aae859c067fca39676e4ac05db4fb06))
* **tables:** TK-8085 disable insert left for primary field ([7920ef7](https://github.com/resola-ai/deca-apps/commit/7920ef71b53d146b5e26daa0232169827062c7e9))
* **tables:** TK-8086 fix previous table showing column from newly added table ([e15d4b5](https://github.com/resola-ai/deca-apps/commit/e15d4b5d671ac7d8663b056e0fa9f1404fbd9cb9))
* **tables:** TK-8184 limit upload image field accept type ([4e5f8bf](https://github.com/resola-ai/deca-apps/commit/4e5f8bf98fb0462f376e4332596ddf10369ca767))
* **tables:** TK-8257 update primary field modal translation ([9d1b138](https://github.com/resola-ai/deca-apps/commit/9d1b138a737e34a1b6899b5c6aab27da0e4cb227))

## [0.16.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.16.0) (2025-05-21)


### Features

* **tables:** TK-3624 update mapper for currency field payload ([7149f54](https://github.com/resola-ai/deca-apps/commit/7149f54806ca1ae0fde38ca263a5b866d87fc1a4))
* **tables:** TK-4089 fix table auto sort toggle state ([1705ab6](https://github.com/resola-ai/deca-apps/commit/1705ab634c5de4018078015d920a269b674c47be))
* **tables:** TK-4089 only show tooltip when automatic sorting on ([a7d9316](https://github.com/resola-ai/deca-apps/commit/a7d93169e1fe268c291b830ec8f97ee50dfcd650))
* **tables:** TK-4089 support auto sorting in table ([8f828c7](https://github.com/resola-ai/deca-apps/commit/8f828c7e456b2e2161aacc4b5a91dcbe26d2473d))
* **tables:** TK-4089 update auto sorting tooltip ([be66f7b](https://github.com/resola-ai/deca-apps/commit/be66f7b3ab73991d1eb9ddb35d824bd73fedfaa9))
* **tables:** TK-6092 allow copy and paste cell value ([8f2e65f](https://github.com/resola-ai/deca-apps/commit/8f2e65fefa8cd3c332e0e8789bd7252f0371c1e4))
* **tables:** TK-6095 allow press del or backspace to delete cell value ([eb9084f](https://github.com/resola-ai/deca-apps/commit/eb9084f02d5c6bec4f12f8b056083e990f6d9337))
* **tables:** TK-6164 update title of rename workspace modal ([28f9b25](https://github.com/resola-ai/deca-apps/commit/28f9b2575b6329d2ea1befae7a267b299db8505d))
* **tables:** TK-6234 update confirm button label in delete field form ([c087953](https://github.com/resola-ai/deca-apps/commit/c087953ac8a5739894a5788099d50a71bd7fff64))
* **tables:** TK-6234 update for header context modal confirm button text ([0086a91](https://github.com/resola-ai/deca-apps/commit/0086a9141825526b55f3a94d57d6d21d943420cd))
* **tables:** TK-6249 add placeholder in value textbox of filter form ([de82779](https://github.com/resola-ai/deca-apps/commit/de82779a668b9e6ff207b935949493a296901c8a))
* **tables:** TK-6714 fix mapping existing table ([acfe09a](https://github.com/resola-ai/deca-apps/commit/acfe09ad9e03dcc3aed8839e273e85aeb65f0a92))
* **tables:** TK-6714 preserve existing table fields when importing CSV with partial column mapping ([e195e7b](https://github.com/resola-ai/deca-apps/commit/e195e7b583f0e548c072c6a1734b0eef553949dc))
* **tables:** TK-6714 update logic merge fields ([555d1a6](https://github.com/resola-ai/deca-apps/commit/555d1a60cc06aee2c3f88229cc1375d46585c7af))
* **tables:** TK-6714 update logic merge fields ([3653c9d](https://github.com/resola-ai/deca-apps/commit/3653c9d65d7eefcb502b5fcb5421be72111bd38d))
* **tables:** TK-7222 remove search input on table detail toolbar ([70e428c](https://github.com/resola-ai/deca-apps/commit/70e428cab513ddc7dc67d168f7b10b8fe4d6d067))
* **tables:** TK-7261 fix up and down keyboard navigation in table cell ([9f3a1c1](https://github.com/resola-ai/deca-apps/commit/9f3a1c16f83997e001a931cdb79dec237f5de3b8))


### Bug Fixes

* **tables:** TK-6233: update texts ([6385d56](https://github.com/resola-ai/deca-apps/commit/6385d56a0aae1309559a0e6b9d5f0fe80d1751ec))

## [0.15.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.15.0) (2025-04-28)


### Features

* **tables:** handle integration auto number field ([259d024](https://github.com/resola-ai/deca-apps/commit/259d024ac471644dae9cf7eb8c2f35fc3cd09ded))
* **tables:** show error when import csv failed ([bd06f09](https://github.com/resola-ai/deca-apps/commit/bd06f09ea52c21384ed1f454532aad959c9b86a2))
* **tables:** TK-6231 update translation ([a91f097](https://github.com/resola-ai/deca-apps/commit/a91f09731ef4abb1fd800af5e655ee6a9f41f196))
* **tables:** TK-6357 allow show customize fields when tables has only 1 column ([09b763e](https://github.com/resola-ai/deca-apps/commit/09b763e30c9689b0382dfbac41a73224d46e2189))
* **tables:** TK-6504 add combobox and action preview component ([c538dfa](https://github.com/resola-ai/deca-apps/commit/c538dfa80075c5a8609cede6d38c3124ed128c01))
* **tables:** TK-6568 fix percent cell not display correct ([bebbce3](https://github.com/resola-ai/deca-apps/commit/bebbce392e6040b669b13b033d3e34a1a2c1515b))
* **tables:** TK-6570 format decimal number when updating cell value ([9bab4c6](https://github.com/resola-ai/deca-apps/commit/9bab4c61fa4f9569ed16e59732bfed04294be291))


### Bug Fixes

* **tables:** TK-6161: add goToTrash link ([#4979](https://github.com/resola-ai/deca-apps/issues/4979)) ([80191e0](https://github.com/resola-ai/deca-apps/commit/80191e069402e5d2b06ce0ef69fdff9aca7c58f9))
* **tables:** TK-6267: prevent create workspace in trash ([#4980](https://github.com/resola-ai/deca-apps/issues/4980)) ([4015b74](https://github.com/resola-ai/deca-apps/commit/4015b746c34efbf48fc9818a6f56dde81da5934b))
* **tables:** TK-6267: prevent create workspace in trash ([#5057](https://github.com/resola-ai/deca-apps/issues/5057)) ([c2da019](https://github.com/resola-ai/deca-apps/commit/c2da019413e964b0ec86419b614cc3a97a940af2))

## [0.14.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.14.0) (2025-04-08)


### Features

* **tables:** remove duplicated hooks ([58e1294](https://github.com/resola-ai/deca-apps/commit/58e1294f916d3603e5e0a8b4aba8d7915537e333))
* **tables:** TK-2384 add table pagination ([eb779cc](https://github.com/resola-ai/deca-apps/commit/eb779cc868433a2e865ad608cdc03b6745e2fe39))
* **tables:** TK-2384 update UI styling ([b46b8ec](https://github.com/resola-ai/deca-apps/commit/b46b8ec923c84acc8b444654ff94fe4eaa2a1a97))
* **tables:** TK-2398 remove old data when delete all fields ([9ac436f](https://github.com/resola-ai/deca-apps/commit/9ac436fd7b49371bacf1918d9de52fe1fd69e0bc))
* **tables:** TK-2398 show empty state ([ffa8e70](https://github.com/resola-ai/deca-apps/commit/ffa8e70bcf0bd90b09d0a36700a11e688cb27fc6))
* **tables:** TK-2398 show empty state when click cancel on create new field form ([e564258](https://github.com/resola-ai/deca-apps/commit/e56425856d9e583b68a440c16e041f534950341b))
* **tables:** TK-2398 show empty when cancel create field ([fb4d37b](https://github.com/resola-ai/deca-apps/commit/fb4d37b019740af9262f28758d6250ccaf946f57))
* **tables:** TK-2664: show notification in trash page ([57f8309](https://github.com/resola-ai/deca-apps/commit/57f83092809e2c8174ccfa749bd9fa90570b65bd))
* **tables:** TK-2664: update locale ([2cd12cd](https://github.com/resola-ai/deca-apps/commit/2cd12cd67a04e709ae06b6e5725087421a461389))
* **tables:** TK-3621 fix falsy value ([f2c05a2](https://github.com/resola-ai/deca-apps/commit/f2c05a26184ae36d6c0a26b5e2276564a953e133))
* **tables:** TK-3621 update format for currency field ([58721e3](https://github.com/resola-ai/deca-apps/commit/58721e375883f2ef0ce3b17fe3917c6e58b3e3d0))
* **tables:** TK-4037 fix form field schema ([a0d12ae](https://github.com/resola-ai/deca-apps/commit/a0d12ae688c12b24d577eaadff3d7ca9a93e60d5))
* **tables:** TK-4110 remove image field from filters ([7e652e7](https://github.com/resola-ai/deca-apps/commit/7e652e72de066fe8f9ae8115da8d7859eeeea1da))
* **tables:** TK-5746 disable delete column button when column is used in sort or filter ([320440f](https://github.com/resola-ai/deca-apps/commit/320440f57855243eb46ed075bd386e9c8c0538c6))
* **tables:** TK-5746 validate filter and sort ([0dcbb08](https://github.com/resola-ai/deca-apps/commit/0dcbb0821891b799ed3898616256d91188d1d1ba))
* **tables:** TK-6016 remove additional api call, improve table ui state ([f147469](https://github.com/resola-ai/deca-apps/commit/f14746996a0ac3980f41d41b79f01e2641b123ee))
* **tables:** TK-6022 disable auto focus when clear sort ([a5e152d](https://github.com/resola-ai/deca-apps/commit/a5e152dfa63c632dff3efbc4a85470d07ebd77f7))
* **tables:** TK-6383 integrate auto number field ([86d5eef](https://github.com/resola-ai/deca-apps/commit/86d5eef24fa136797f2f4b38e0783135bdd07636))
* **tables:** TK-6624 fix import to existing table ([2a498c0](https://github.com/resola-ai/deca-apps/commit/2a498c0031b690f806bedae1deebbd8bb10ef9df))


### Bug Fixes

* **tables:** TK-6557 fix stuck when load table ([6dde23f](https://github.com/resola-ai/deca-apps/commit/6dde23f824809bc978b54b159ffe19862e39420c))
* **tables:** TK-6580: fix notification in trash ([#4796](https://github.com/resola-ai/deca-apps/issues/4796)) ([0c5d48e](https://github.com/resola-ai/deca-apps/commit/0c5d48e75be63a369b81f3181ce6ed02b13f77af))
* **tables:** TK-6583: fix amount of workspaces ([#4799](https://github.com/resola-ai/deca-apps/issues/4799)) ([c2e1d69](https://github.com/resola-ai/deca-apps/commit/c2e1d6936faedca56a6a75cb3c92aedef69f374e))
* **tables:** TK-6583: update styles for deleted date ([#4797](https://github.com/resola-ai/deca-apps/issues/4797)) ([dcc85da](https://github.com/resola-ai/deca-apps/commit/dcc85da8f04f1fac9e8f394bccaef52d5ac1ea43))
* **tables:** TK-6612: fix display deleted date ([#4800](https://github.com/resola-ai/deca-apps/issues/4800)) ([0f60147](https://github.com/resola-ai/deca-apps/commit/0f601479743390115ab13fac43a66a7ad5a843ba))
* **tables:** TK-6612: update permanently deleted text ([#4795](https://github.com/resola-ai/deca-apps/issues/4795)) ([c4f671b](https://github.com/resola-ai/deca-apps/commit/c4f671bedf32658cb6c0427b4c023bca05508e56))

## [0.13.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.13.0) (2025-03-17)


### Features

* **tables:** TK-2501 update duplicate record position ([4022396](https://github.com/resola-ai/deca-apps/commit/4022396c81e4331c84dbad3fe2ee6592aac69f78))
* **tables:** TK-2939 add loading overlay when import table data ([e9e68ab](https://github.com/resola-ai/deca-apps/commit/e9e68ab54c459e8c39cc64e6356dc75675e2aab8))
* **tables:** TK-3327 enhance bulk actions in status bar ([2fd0e32](https://github.com/resola-ai/deca-apps/commit/2fd0e3289a4637eaad1565307e0ad45a1a20269b))
* **tables:** TK-3627: create a fixed status bar ui with mock data ([#4306](https://github.com/resola-ai/deca-apps/issues/4306)) ([8dcc3fa](https://github.com/resola-ai/deca-apps/commit/8dcc3fa8718f4df9f42f5b46885eea71fc69ccdd))
* **tables:** TK-3637: display total records ([dfb62eb](https://github.com/resola-ai/deca-apps/commit/dfb62ebc0dc7f4ff9d6e9ab6efb6a2e4eb8ab6c2))
* **tables:** TK-3638: reach limit record information ([88c1e41](https://github.com/resola-ai/deca-apps/commit/88c1e41c8b88eaf2a9012062982bd78066e9ecdd))
* **tables:** TK-3639 update navigation button disabled state ([b46619f](https://github.com/resola-ai/deca-apps/commit/b46619fcae18eceb63cd20ba2c01ab32de8d2148))
* **tables:** TK-3639 update navigation disabled state ([a6dd176](https://github.com/resola-ai/deca-apps/commit/a6dd176963aaff5f562dd2e05bebcbedde31a81b))
* **tables:** TK-4085 disable sort for auto number field ([ac44a9e](https://github.com/resola-ai/deca-apps/commit/ac44a9e3cf45ff9b3fadf30eff2ab2e61a9a3f49))
* **tables:** TK-4100 disable hiding field when table has sorting or filters applied ([307887c](https://github.com/resola-ai/deca-apps/commit/307887c0c15fa89e2b5570bd9f7ee494c85adb1d))
* **tables:** TK-4133 fix lint ([a1a739e](https://github.com/resola-ai/deca-apps/commit/a1a739ee1f483c0372b48019991263cbf39ef739))
* **tables:** TK-4133 update sort label in field context menu ([de13c5e](https://github.com/resola-ai/deca-apps/commit/de13c5e8c342236d38deaf70076823c857b37f9e))
* **tables:** TK-4454 handle empty value for number input ([d78a416](https://github.com/resola-ai/deca-apps/commit/d78a41633fd098fb66dea3e5022649a21dbb82f7))
* **tables:** TK-4583 update sort condition ([ffa6f5e](https://github.com/resola-ai/deca-apps/commit/ffa6f5e44ba4fe7d681a3d3383b01502a7b4295f))
* **tables:** TK-4583 update sort condition ([d20dad7](https://github.com/resola-ai/deca-apps/commit/d20dad7111e88872cbe42065170abed850b7732b))
* **tables:** TK-5203: update IconHome icon ([1d7d13a](https://github.com/resola-ai/deca-apps/commit/1d7d13a298be60231b79614e2cfa8390b1dbc54a))
* **tables:** TK-5594 update ws channel name ([07eb04f](https://github.com/resola-ai/deca-apps/commit/07eb04f49b1df64ea7078e82f14c0a48bf18d191))
* **tables:** TK-5623 fix cannot update cell ([866de84](https://github.com/resola-ai/deca-apps/commit/866de84579d88a4e98f50b45e13f682dfc971e19))
* **tables:** TK-5840 refactor use minimongo to get total data count ([5c6d13b](https://github.com/resola-ai/deca-apps/commit/5c6d13bda8064749e7cdb9792bbb122d172f8fef))


### Bug Fixes

* **tables:** TK-4583 hide image field from sorting ([57efd6d](https://github.com/resola-ai/deca-apps/commit/57efd6d8d40f14d233dc32f82e13affbdfac890a))
* **tables:** TK-5664 fix add new sort not updated when mouse click ([6f76073](https://github.com/resola-ai/deca-apps/commit/6f76073189ff8f9221af22a3032200d4e9f2612c))
* **tables:** TK-5699 fix tables export full data ([0d25786](https://github.com/resola-ai/deca-apps/commit/0d257861374c59e4456f33ac6ec76c2b7f7f3a20))
* **tables:** TK-5838: fix text ([#4482](https://github.com/resola-ai/deca-apps/issues/4482)) ([1ac0b6b](https://github.com/resola-ai/deca-apps/commit/1ac0b6b3874e3faef229c34eee9f99a2afd67be1))
* **tables:** TK-5840: add helper function to total records ([#4498](https://github.com/resola-ai/deca-apps/issues/4498)) ([3ef2bbf](https://github.com/resola-ai/deca-apps/commit/3ef2bbf29c5a4332e4a5dc46c913ccc1a19dbe5c))
* **tables:** TK-5840: fix display total records when create manually ([#4507](https://github.com/resola-ai/deca-apps/issues/4507)) ([ba08ffd](https://github.com/resola-ai/deca-apps/commit/ba08ffddf0393f4b953ad6405b3bc4b6b718d358))
* **tables:** TK-5866: fix notification message ([5e16389](https://github.com/resola-ai/deca-apps/commit/5e16389eb32e920e1a29fdfa0bfa583b922aa994))
* **tables:** TK-5866: fix notification message ([adac8e7](https://github.com/resola-ai/deca-apps/commit/adac8e76e8b6b09b9f36589396cefa9f9048b8c1))
* **tables:** TK-5866: update dayleft in notification ([8ba0aab](https://github.com/resola-ai/deca-apps/commit/8ba0aab5e3a0bbdccc3f273d3ae2e4402258ce6b))
* **tables:** TK-5866: update limitedAt ([9e008fe](https://github.com/resola-ai/deca-apps/commit/9e008fe8fcc866fb5c30616485a831b2be169952))
* **tables:** TK-5886 fix scroll to top ([8479153](https://github.com/resola-ai/deca-apps/commit/8479153bd4cf24abf1459d4ab80f4130b142cb2a))

### [0.12.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.2) (2025-03-05)


### Bug Fixes

* **tables:** TK-5664 fix add new sort not updated when mouse click ([458e788](https://github.com/resola-ai/deca-apps/commit/458e7888469bbfaca2b5c1083d8940ad4297b968))
* **tables:** TK-5699 fix tables export full data ([27af622](https://github.com/resola-ai/deca-apps/commit/27af622e7fa27236121dfe17e6658c6ce429b836))

### [0.12.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.1) (2025-03-03)


### Bug Fixes

* **tables:** TK-5623 hotfix cannot update cell ([cf78746](https://github.com/resola-ai/deca-apps/commit/cf78746b48e981ec29d22ac47593313704494d04))

## [0.12.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.0) (2025-02-25)


### Features

* **tables:** handle infinite scroll for table ([b8c3d6b](https://github.com/resola-ai/deca-apps/commit/b8c3d6bb2a458dbcb547384012f39c9168363efc))
* **tables:** TK-2790 fix wrong total count filter ([516389a](https://github.com/resola-ai/deca-apps/commit/516389ae532614730868854b964443c4612c2061))
* **tables:** TK-3618 fix currency form button create disabled ([dea59d0](https://github.com/resola-ai/deca-apps/commit/dea59d01df909b312b990b6974067fd436cf3f14))
* **tables:** TK-4099 improve filter behavior ([2d2d011](https://github.com/resola-ai/deca-apps/commit/2d2d011b0b49f5fb286f6355c25170bb848a899f))
* **tables:** TK-4128 handle insert column ([0714f02](https://github.com/resola-ai/deca-apps/commit/0714f0260037507d156a200e60b8120d879922e4))
* **tables:** TK-4639 update custom fields state to not show deleted fields ([8cd449c](https://github.com/resola-ai/deca-apps/commit/8cd449cc153dda25b3aa2edfb1cb8f20d44353f9))
* **tables:** TK-4640 decompress record data ([10976c6](https://github.com/resola-ai/deca-apps/commit/10976c66fd1e877c4acc50e90ec764ea0d0c17f8))
* **tables:** TK-4768 remove unused code ([15c97cf](https://github.com/resola-ai/deca-apps/commit/15c97cf89bea244fd9bf86bd74362faaec99ee18))
* **tables:** TK-4768 support resize table column ([43b1ffb](https://github.com/resola-ai/deca-apps/commit/43b1ffb3efc696c964d0c692a387b23062930b89))


### Bug Fixes

* **ai-widgets:** TK-4378 trigger validate after restore default prompt ([#4228](https://github.com/resola-ai/deca-apps/issues/4228)) ([9e74103](https://github.com/resola-ai/deca-apps/commit/9e7410391788941a43f010a80e5c8d4a70a064ce))
* **ai-widgets:** TK-4784 fix language search width, refactor DecaSearchBox ([22cc818](https://github.com/resola-ai/deca-apps/commit/22cc818e08f1d1efff8941203701a7e726b4c0ed))
* **ai-widgets:** TK-5057 validate reply assistant prompt setting ([#4209](https://github.com/resola-ai/deca-apps/issues/4209)) ([bef8378](https://github.com/resola-ai/deca-apps/commit/bef83783979008640ac298f393651af5ea2defb7))
* **chatbot:** TK-2457 Enable Code/Script card ([#4057](https://github.com/resola-ai/deca-apps/issues/4057)) ([5a746f8](https://github.com/resola-ai/deca-apps/commit/5a746f8e46c3ec37fcc527d64a278d7ae7f652ec))
* **chatbot:** TK-3988 Set limit relate article ([#4125](https://github.com/resola-ai/deca-apps/issues/4125)) ([9224696](https://github.com/resola-ai/deca-apps/commit/9224696a8600d66fc7754d73f664fd450cba44ab))
* **chatbot:** TK-4469 Improve Select component after updating Mantine V7 ([#4099](https://github.com/resola-ai/deca-apps/issues/4099)) ([be826b2](https://github.com/resola-ai/deca-apps/commit/be826b235a27c2788789112e75dffa3ef984b772))
* **chatbot:** TK-4620 Mapping error code for chatbot ([#4180](https://github.com/resola-ai/deca-apps/issues/4180)) ([e75fbb1](https://github.com/resola-ai/deca-apps/commit/e75fbb1d62aaee8be9a34ea7bd1d129a790d029d))
* **chatbot:** TK-4921 Add dot color to ChatLogs page and update responsive ([#4270](https://github.com/resola-ai/deca-apps/issues/4270)) ([86fa004](https://github.com/resola-ai/deca-apps/commit/86fa00404bd433326945b07b3f74378d1b952f8f))
* **chatbot:** TK-4921 Display Session ID in Log UI with Color Coding ([#4240](https://github.com/resola-ai/deca-apps/issues/4240)) ([e8d23ee](https://github.com/resola-ai/deca-apps/commit/e8d23ee10b6b199bd3b83bfe2f858b0e243d08d7))
* **chatbot:** TK-4949 Update FE to disable intents related feature ([#4140](https://github.com/resola-ai/deca-apps/issues/4140)) ([0af2e59](https://github.com/resola-ai/deca-apps/commit/0af2e599f27e12941f02bc73d2594d7463509c29))
* **chatbot:** TK-5022 Update min max number of reference article ([#4165](https://github.com/resola-ai/deca-apps/issues/4165)) ([1fc2812](https://github.com/resola-ai/deca-apps/commit/1fc281252a90c61531212308c09f205b86dce0f7))
* **chatbox:** TK-3962: avoid email address when replace non-protocol url ([0ef5c09](https://github.com/resola-ai/deca-apps/commit/0ef5c09d8225bd6e849fa65d2976580d3aca2bb9))
* **crm:** check empty receiver before sending ([46ddc47](https://github.com/resola-ai/deca-apps/commit/46ddc47fc9309b1aa3793d7f92240cdf38b13ab4))
* **crm:** Fix wrong id view when hiding field ([#4052](https://github.com/resola-ai/deca-apps/issues/4052)) ([dd41de5](https://github.com/resola-ai/deca-apps/commit/dd41de550e8ec44d8ccbf05729ef04248d80863b))
* **crm:** make cell render correctly on virtual column ([3499fed](https://github.com/resola-ai/deca-apps/commit/3499fed2cad27b3509117bbd97cd639c53b84186))
* **crm:** TK-4459 - update mutate object when deleting colums ([#4243](https://github.com/resola-ai/deca-apps/issues/4243)) ([753053f](https://github.com/resola-ai/deca-apps/commit/753053fdd4e9d7b8ed5382b479c875698b0a1dec))
* **crm:** TK-4459 - update mutate object when deleting colums ([#4243](https://github.com/resola-ai/deca-apps/issues/4243)) ([2213d69](https://github.com/resola-ai/deca-apps/commit/2213d69d456c1096d21e31744a302aa9f616c8b0))
* **crm:** TK-4765 - update non manager do not allow to edit protected field ([#4072](https://github.com/resola-ai/deca-apps/issues/4072)) ([95061f5](https://github.com/resola-ai/deca-apps/commit/95061f58268e71c7a507745bedb50aaf86b935ae))
* **crm:** TK-5169 TK-5171 cannot move the column ([#4233](https://github.com/resola-ai/deca-apps/issues/4233)) ([cf5b016](https://github.com/resola-ai/deca-apps/commit/cf5b016392edd7cae10b314cd0dd822a28be347d))
* **crm:** TK-5178 revalidate when  update view ([0621709](https://github.com/resola-ai/deca-apps/commit/0621709d5760d4b70461d2b0392d051826e4c5a8))
* **crm:** tk-5202 ([#4265](https://github.com/resola-ai/deca-apps/issues/4265)) ([b6867c0](https://github.com/resola-ai/deca-apps/commit/b6867c09d73ac7210dbf9b049c14361e1bebcd20))
* **form-admin:** TK-2875 Fix field chekcbox and radio color picker overlap ([#4107](https://github.com/resola-ai/deca-apps/issues/4107)) ([de028dc](https://github.com/resola-ai/deca-apps/commit/de028dc5100e3ad6c38d4a62ffc3d030a0109981))
* **form-admin:** TK-3146 Back to Home on create template page ([#4158](https://github.com/resola-ai/deca-apps/issues/4158)) ([edb10f2](https://github.com/resola-ai/deca-apps/commit/edb10f25943c8d59f8e4fa7e519f05fc6229f257))
* **form-admin:** TK-3689 Update integration icon ([#4117](https://github.com/resola-ai/deca-apps/issues/4117)) ([237e0f6](https://github.com/resola-ai/deca-apps/commit/237e0f6484fde43d68b6c4682aa1459e485691ca))
* **form-admin:** TK-4321 - The save button is unresponsive on post submission setting page even if they already input the valid value ([#4081](https://github.com/resola-ai/deca-apps/issues/4081)) ([4ad75f1](https://github.com/resola-ai/deca-apps/commit/4ad75f1f6f350184c09dd97ab470645b62435bec))
* **form-admin:** TK-4402 apply button comps ([#4157](https://github.com/resola-ai/deca-apps/issues/4157)) ([c004dbd](https://github.com/resola-ai/deca-apps/commit/c004dbdf7de482ba4aab40a4fb51eab9ce462df0))
* **form-admin:** TK-4481 Add missing icons in List View ([#4190](https://github.com/resola-ai/deca-apps/issues/4190)) ([51de8a1](https://github.com/resola-ai/deca-apps/commit/51de8a11d8f939f6edaffbe100199db5f3e2a47d))
* **form-admin:** TK-4481 Center align the number ([#4212](https://github.com/resola-ai/deca-apps/issues/4212)) ([653633a](https://github.com/resola-ai/deca-apps/commit/653633a12f1a68c6260007787161230f41eefd57))
* **form-admin:** TK-4817 Using closet because in select option could be element, not text ([#4259](https://github.com/resola-ai/deca-apps/issues/4259)) ([82680a8](https://github.com/resola-ai/deca-apps/commit/82680a86c8a2ae353e944512bee324a944413195))
* **form-admin:** TK-4818 Select Mantine changed there class item make deslect field before update ([#4101](https://github.com/resola-ai/deca-apps/issues/4101)) ([7f17b9e](https://github.com/resola-ai/deca-apps/commit/7f17b9e7d9f3ab59081fb8aede27ba9c44c9ae6b))
* **form-admin:** TK-4819 Adjustment for template deletion ([#4118](https://github.com/resola-ai/deca-apps/issues/4118)) ([4636abb](https://github.com/resola-ai/deca-apps/commit/4636abb68a79ddf7b6d84d0e8b357bc0b2db5fa2))
* **form-admin:** TK-4821 - The user can not set start/end date on form setting page ([#4098](https://github.com/resola-ai/deca-apps/issues/4098)) ([58719f3](https://github.com/resola-ai/deca-apps/commit/58719f310036a42c211a0468d2cd597174609c18))
* **form-admin:** TK-4833 - The user cannot add email notification on the integration page ([#4129](https://github.com/resola-ai/deca-apps/issues/4129)) ([b644286](https://github.com/resola-ai/deca-apps/commit/b644286b20852d1bb07e6f5e84505576e9cbfc67))
* **form-admin:** TK-4971 Dont allow deslect datetime and upload file options ([#4264](https://github.com/resola-ai/deca-apps/issues/4264)) ([df45161](https://github.com/resola-ai/deca-apps/commit/df4516171f9475b3e9745894fdd58de697effdeb))
* **form-admin:** TK-4971 Dont allow deslect datetime and upload file options ([#4266](https://github.com/resola-ai/deca-apps/issues/4266)) ([ca0119f](https://github.com/resola-ai/deca-apps/commit/ca0119f996b17d07f30774ae9a2bd1782645eaf5))
* **form-admin:** TK-4972 - Fix the form field couldn't search ([#4163](https://github.com/resola-ai/deca-apps/issues/4163)) ([1178aab](https://github.com/resola-ai/deca-apps/commit/1178aabfe6932dc981ec69b07c58585a7c1b3c33))
* **form-admin:** TK-5009 update height size in result page ([#4194](https://github.com/resola-ai/deca-apps/issues/4194)) ([5d0e67f](https://github.com/resola-ai/deca-apps/commit/5d0e67f79f5b582cc979895de29f2348941f3a36))
* **form-admin:** TK-5167 Add default flag to not adding to history ([#4258](https://github.com/resola-ai/deca-apps/issues/4258)) ([42204fa](https://github.com/resola-ai/deca-apps/commit/42204fa1d89bd758bc3d67f97b3846194d4fb509))
* **form-admin:** TK-5167 Fix history duplicated ([#4256](https://github.com/resola-ai/deca-apps/issues/4256)) ([b10ed12](https://github.com/resola-ai/deca-apps/commit/b10ed12e9f6e2d9703cd49775c7f965dd12d6a14))
* **general:** update amplify yml and vite version ([#4069](https://github.com/resola-ai/deca-apps/issues/4069)) ([eabc3ec](https://github.com/resola-ai/deca-apps/commit/eabc3ec66d0598f054b767774028be21de0a79e3))
* **general:** update amplify yml and vite version ([#4069](https://github.com/resola-ai/deca-apps/issues/4069)) ([0577593](https://github.com/resola-ai/deca-apps/commit/057759385f6ea53782b0053228ee231c8231ca20))
* **kb:** [TK-4621] UI break when filename too long ([#4272](https://github.com/resola-ai/deca-apps/issues/4272)) ([b203ebf](https://github.com/resola-ai/deca-apps/commit/b203ebf35269f294758978bac8260d2b9a450e46))
* **kb:** [TK-4800] dont reload articles after import ([#4247](https://github.com/resola-ai/deca-apps/issues/4247)) ([8f477d9](https://github.com/resola-ai/deca-apps/commit/8f477d9f371a1942fedb8227a8f8cd805c82f67b))
* **kb:** [TK-4800] make component ImportModal can dyanmic with multiple usecase ([#4244](https://github.com/resola-ai/deca-apps/issues/4244)) ([d3f6c7c](https://github.com/resola-ai/deca-apps/commit/d3f6c7c4573e5c64c7d59b35252066b5edc994f5))
* **kb:** [TK-4800] show error when upload success + update position dropdown + update public link for template ([#4237](https://github.com/resola-ai/deca-apps/issues/4237)) ([831f409](https://github.com/resola-ai/deca-apps/commit/831f40992e79bad7d8563b61fdd9e0b50707dd8e))
* **kb:** [TK-4800] update text KB name + lineClamp file name + handle success ([#4241](https://github.com/resola-ai/deca-apps/issues/4241)) ([b9cfa1d](https://github.com/resola-ai/deca-apps/commit/b9cfa1d3f1f3674ecd9a3746879ddd05cf24d36d))
* **kb:** [TK-5147] prevent double click when upload files ([#4213](https://github.com/resola-ai/deca-apps/issues/4213)) ([cc06903](https://github.com/resola-ai/deca-apps/commit/cc0690317e9718707e516d0a42d1e3794cf89352))
* **kb:** [TK-5147] prevent double click when upload files ([#4220](https://github.com/resola-ai/deca-apps/issues/4220)) ([1983519](https://github.com/resola-ai/deca-apps/commit/19835190e0b0e6bd0f30e817e29b969eb17360eb))
* **kb:** TK-4824 Correct cannot move article when has related articles ([#4090](https://github.com/resola-ai/deca-apps/issues/4090)) ([4d16516](https://github.com/resola-ai/deca-apps/commit/4d165168d2acabef1f59b4a59753bb223f3e6901))
* **kb:** TK-4961 Correct break line in template title ([#4250](https://github.com/resola-ai/deca-apps/issues/4250)) ([d63f911](https://github.com/resola-ai/deca-apps/commit/d63f91108f1128f2c4ecb896a92b1b4cec709de5))
* **kb:** TK-4961 Correct error from SVG style when loading in article modal ([#4227](https://github.com/resola-ai/deca-apps/issues/4227)) ([072da9f](https://github.com/resola-ai/deca-apps/commit/072da9fef427f08f9fcb33c21bb4fcaef07ffb4e))
* **kb:** TK-4961 Correct long text break line in Job Article ([#4254](https://github.com/resola-ai/deca-apps/issues/4254)) ([7389850](https://github.com/resola-ai/deca-apps/commit/738985026df8e6cd321f2169abf84eb10237202f))
* **kb:** TK-4961 Correct SVG size rendering error and sorting in KB selection ([#4246](https://github.com/resola-ai/deca-apps/issues/4246)) ([0a3421a](https://github.com/resola-ai/deca-apps/commit/0a3421a02cbe61b2ebdd97cdaf324a6fca8156cb))
* **kb:** TK-5030 Corrected cannot save article with empty text content ([#4181](https://github.com/resola-ai/deca-apps/issues/4181)) ([17cf74e](https://github.com/resola-ai/deca-apps/commit/17cf74e35df8b8d46744b4438cf0c27771b75af0))
* **kb:** TK-5030 Improve article editor validation to fix saving error ([#4197](https://github.com/resola-ai/deca-apps/issues/4197)) ([84ccb5f](https://github.com/resola-ai/deca-apps/commit/84ccb5f34b72501929e2ea0e535c54633232a802))
* **livechat:** fix-could-not-load-more-message-next-page ([b68f948](https://github.com/resola-ai/deca-apps/commit/b68f9484e863fb276556e6a8239186171b3f3d3f))
* **livechat:** TK-4543 bookmark-issue ([#4102](https://github.com/resola-ai/deca-apps/issues/4102)) ([ec4ff1e](https://github.com/resola-ai/deca-apps/commit/ec4ff1e26bf599825362ffb3afc599d036b05520))
* **livechat:** TK-4723 - Fix English text displaying as Japanese ([#4260](https://github.com/resola-ai/deca-apps/issues/4260)) ([7377647](https://github.com/resola-ai/deca-apps/commit/73776474ed95c251b211f22ed8b84fb8d7a5e02c))
* **livechat:** TK-4725 fix-ui-issue ([#4056](https://github.com/resola-ai/deca-apps/issues/4056)) ([6bce0d9](https://github.com/resola-ai/deca-apps/commit/6bce0d924d11be299e168a804b5cfb07ad837bcc))
* **livechat:** TK-4787 fix-prompt ([#4066](https://github.com/resola-ai/deca-apps/issues/4066)) ([3860aea](https://github.com/resola-ai/deca-apps/commit/3860aeacd9f8b5fc8c5e8d18c412e5361f133398))
* **livechat:** TK-4984 TK-4983 hotfix/livechat/could-not-load-more-conversation ([af0daa1](https://github.com/resola-ai/deca-apps/commit/af0daa15ae1a075bd16c60a9fb7a373505868324))
* **livechat:** TK-4984 TK-4983 hotfix/livechat/could-not-load-more-conversation ([e075d85](https://github.com/resola-ai/deca-apps/commit/e075d85bbbd97beb858fc05967b864594f14844f))
* **livechat:** TK-4984 TK-4983 hotfix/livechat/could-not-load-more-conversation ([9b90693](https://github.com/resola-ai/deca-apps/commit/9b906932e92af972597d1e956ed3ebff53f85d8b))
* **livechat:** TK-4984 TK-4983 some-issues ([#4152](https://github.com/resola-ai/deca-apps/issues/4152)) ([c4ab793](https://github.com/resola-ai/deca-apps/commit/c4ab79325e8fb324356a7656e6bf5f674268f863))
* **livechat:** TK-4997 bookmark-issue ([#4267](https://github.com/resola-ai/deca-apps/issues/4267)) ([655d86f](https://github.com/resola-ai/deca-apps/commit/655d86feff6ce1778a326273fdb5951c4d71015b))
* **livechat:** TK-4997 mismatch-bookmark-when-wrapup ([#4161](https://github.com/resola-ai/deca-apps/issues/4161)) ([fa6d3f9](https://github.com/resola-ai/deca-apps/commit/fa6d3f917c4e4f3b4e50c00c208ce7e916030fc5))
* **livechat:** TK-5274 fix-refetch-list-when-wrapping-up ([#4275](https://github.com/resola-ai/deca-apps/issues/4275)) ([21f78fa](https://github.com/resola-ai/deca-apps/commit/21f78fae528179085b9284f2954ca953e326fd0d))
* **livechat:** tk-fix-cannot-load-old-messages  ([#4166](https://github.com/resola-ai/deca-apps/issues/4166)) ([ef74b47](https://github.com/resola-ai/deca-apps/commit/ef74b47841082f68f80cb9962258d4c267959561))
* **page-admin:** TK-4366 Update translation update size settings ([#4106](https://github.com/resola-ai/deca-apps/issues/4106)) ([b6d2a55](https://github.com/resola-ai/deca-apps/commit/b6d2a5575338a36cc9d3c7c9ba5aa7a5c85dbbc6))
* **page-admin:** TK-4367 Update settings for buttons and divider ([#4162](https://github.com/resola-ai/deca-apps/issues/4162)) ([5dd4352](https://github.com/resola-ai/deca-apps/commit/5dd4352153e6798f8ad68a301917021b7be52a43))
* **security:** bump zod version ([#4092](https://github.com/resola-ai/deca-apps/issues/4092)) ([d15a888](https://github.com/resola-ai/deca-apps/commit/d15a888904fed5b7237366e5a9f982b2ea1f1eaf))
* **security:** node-fetch forwards secure headers to untrusted sites ([#4097](https://github.com/resola-ai/deca-apps/issues/4097)) ([4998742](https://github.com/resola-ai/deca-apps/commit/49987427737618dabce2d983189c013046a28724))
* **security:** update dompurify packages ([#4207](https://github.com/resola-ai/deca-apps/issues/4207)) ([9500f08](https://github.com/resola-ai/deca-apps/commit/9500f08457b4933ffb34c146ef8d3c07c7d15bbf))
* **security:** update library version ([ba47139](https://github.com/resola-ai/deca-apps/commit/ba47139c07009c24d565810ceda279a9e237618e))
* **security:** update package to handle ReDOS in cross-spawn ([#4096](https://github.com/resola-ai/deca-apps/issues/4096)) ([72c3088](https://github.com/resola-ai/deca-apps/commit/72c3088fea8daa2366b7ab47915cca4457004f50))
* **security:** update vitest version ([#4088](https://github.com/resola-ai/deca-apps/issues/4088)) ([99ae5c9](https://github.com/resola-ai/deca-apps/commit/99ae5c98edb0c7c3a486065facbcc8b2b70728f2))
* **shared:** fix unit test fail with no tests ([fd4a69a](https://github.com/resola-ai/deca-apps/commit/fd4a69a406936abd109ae6581d0330edc315c9b1))
* **shared:** TK-4722 check icon existence in DecaSearchBox item ([5c8b7c1](https://github.com/resola-ai/deca-apps/commit/5c8b7c1baac4b031ffe8e4ec64d7f1a93a2f3da5))
* **tables:** TK-4383: update label ([#4082](https://github.com/resola-ai/deca-apps/issues/4082)) ([4cac258](https://github.com/resola-ai/deca-apps/commit/4cac258427c1741636a0cecd722f583131629224))
* **tables:** TK-5309 fix filter and sort modal ([f68fd4a](https://github.com/resola-ai/deca-apps/commit/f68fd4a39f16f7e413c943577c2fbd4c8a4649c2))
* **widget-builder:** TK-4602 fix widget list icon link in navigation menu ([0ffda9c](https://github.com/resola-ai/deca-apps/commit/0ffda9c474fae57aaad8a2a864a3284e4322b801))
* **widget-builder:** TK-4646 update widget name on editor page ([#4054](https://github.com/resola-ai/deca-apps/issues/4054)) ([2185b09](https://github.com/resola-ai/deca-apps/commit/2185b09be9214a2b695878f82121661349d535d6))
* **widget-builder:** TK-4715 reload widget list after widget details update ([f25973c](https://github.com/resola-ai/deca-apps/commit/f25973c9454d62e99886a5994acadb7ebcfaed75))
* **widget-builder:** TK-4716 add settings icon to navigation menu ([de7c8d3](https://github.com/resola-ai/deca-apps/commit/de7c8d38f78c70eef96d099aec41160b9c77da1a))
* **widget-builder:** TK-4753 update explorer with sort and indent ([#4055](https://github.com/resola-ai/deca-apps/issues/4055)) ([1cb17c5](https://github.com/resola-ai/deca-apps/commit/1cb17c5ca56c9444e2bbd1ce6b7465af227beae5))
* **widget-builder:** TK-4831 update publish widget flow ([#4245](https://github.com/resola-ai/deca-apps/issues/4245)) ([68cb9c4](https://github.com/resola-ai/deca-apps/commit/68cb9c41d8c6669639ff7b50a30706fc18173c2e))
* **widget-builder:** TK-5023 update editor config and behavior ([#4171](https://github.com/resola-ai/deca-apps/issues/4171)) ([9fc7f07](https://github.com/resola-ai/deca-apps/commit/9fc7f07dbb7ffca3c7568e03f086bab2577eb0e5))
* **widget-builder:** TK-5066 update search and remove old tab editor when switch widgets ([#4188](https://github.com/resola-ai/deca-apps/issues/4188)) ([dc066f3](https://github.com/resola-ai/deca-apps/commit/dc066f304cfa0f54900867c894ab9403569dced0))
* **widget-builder:** TK-5067 update widget details and editor ([#4211](https://github.com/resola-ai/deca-apps/issues/4211)) ([507fabf](https://github.com/resola-ai/deca-apps/commit/507fabf5d917935c71bb4d11a5b5b5bb3b1e612d))
* **widget-builder:** TK-5098 add type and settings field to widget creation payload ([766ce42](https://github.com/resola-ai/deca-apps/commit/766ce423dab50d147c9b441b3a07c027a7482382))
* **workshop:** TK-2290 Remove extra file and folders added by mistake ([#4104](https://github.com/resola-ai/deca-apps/issues/4104)) ([0a9aca2](https://github.com/resola-ai/deca-apps/commit/0a9aca2b3dbd1daba7f7d148bfd645e41c014477))

### [0.11.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.11.1) (2025-02-05)


### Bug Fixes

* **general:** update amplify yml and vite version ([#4069](https://github.com/resola-ai/deca-apps/issues/4069)) ([fc70d59](https://github.com/resola-ai/deca-apps/commit/fc70d59521e4ab060612bf92cdb6d430135bd051))

## [0.11.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.11.0) (2025-02-05)


### Features

* **chatbot:** tk-4559 add exceeded path for livechat node ([#4011](https://github.com/resola-ai/deca-apps/issues/4011)) ([0a61f83](https://github.com/resola-ai/deca-apps/commit/0a61f83e1428d28aaf5d792eb98a086dea27ac26))
* **chatbox:** TK-3357: create disableChatwindow setting in CW admin ([d333940](https://github.com/resola-ai/deca-apps/commit/d3339403d73cb51de97dab4aa11272093b379639))
* **chatbox:** TK-3357: update condition ([4c69d8b](https://github.com/resola-ai/deca-apps/commit/4c69d8baf4bb8b1b4fa796f01adbef18d4750989))
* **chatbox:** TK-3357: update setting enable cw admin ([#3994](https://github.com/resola-ai/deca-apps/issues/3994)) ([94d308a](https://github.com/resola-ai/deca-apps/commit/94d308a9e9803635fe639fb78806ceec3de67109))
* **chatbox:** TK-4497: improve CW UI after migrate Manntine v7 ([#3989](https://github.com/resola-ai/deca-apps/issues/3989)) ([c4f08c0](https://github.com/resola-ai/deca-apps/commit/c4f08c0ffc56f77d1eab565b6f0c7adaa218f1df))
* **chatbox:** TK-4497: improve previewImage, buttons ([#3990](https://github.com/resola-ai/deca-apps/issues/3990)) ([7a2d6e9](https://github.com/resola-ai/deca-apps/commit/7a2d6e9fdfa4981141ea89b5015a9eb32eca53bd))
* **chatbox:** TK-4590: break linebreak in html ([#4016](https://github.com/resola-ai/deca-apps/issues/4016)) ([233d722](https://github.com/resola-ai/deca-apps/commit/233d7221694c2a57c49b707e916182f2351e250a))
* **crm:** CRM - TK-4335 - Update text sort function ([#3917](https://github.com/resola-ai/deca-apps/issues/3917)) ([2a86312](https://github.com/resola-ai/deca-apps/commit/2a86312857d08960f9628eb95131885bcf502d2d))
* **crm:** CRM - update wrong name object ([#3912](https://github.com/resola-ai/deca-apps/issues/3912)) ([736d672](https://github.com/resola-ai/deca-apps/commit/736d672e749354d10fa981d19e777bb7fb381089))
* **crm:** TK-4334 - Add protected field ([#4035](https://github.com/resola-ai/deca-apps/issues/4035)) ([3b551ef](https://github.com/resola-ai/deca-apps/commit/3b551efe9bf4f1dc54f4b09f9f81c7638a710f4d))
* **crm:** TK-4344 Show content of sent mails in CRM' ([11ec179](https://github.com/resola-ai/deca-apps/commit/11ec179e8289a405cf0453c9ec00ecdbb1fbb037))
* **crm:** TK-4519 - Update order custom object fields ([#3984](https://github.com/resola-ai/deca-apps/issues/3984)) ([0ef3957](https://github.com/resola-ai/deca-apps/commit/0ef395780388ecf97c4f59b79b89e8ca90a23c67))
* **crm:** UI issue related to mantine v7 migration ([#3997](https://github.com/resola-ai/deca-apps/issues/3997)) ([21adff5](https://github.com/resola-ai/deca-apps/commit/21adff5664c3467e811066b292052eecb8c4a920))
* **form-client:** TK-4235 page builder initial layout ([#3938](https://github.com/resola-ai/deca-apps/issues/3938)) ([1a7049d](https://github.com/resola-ai/deca-apps/commit/1a7049dc8427e615d706237f29716fc00a939ba2))
* **general:** migrate mantine v7 ([#3423](https://github.com/resola-ai/deca-apps/issues/3423)) ([ccc3551](https://github.com/resola-ai/deca-apps/commit/ccc3551a2a17ee4d848b3dcaf00a561295f71747)), closes [#3622](https://github.com/resola-ai/deca-apps/issues/3622)
* **general:** TK-4358 - can select env from the given list ([ffc6a08](https://github.com/resola-ai/deca-apps/commit/ffc6a0853ba5e3a5a85ad02ddc3c9cb8d697fcfb))
* **general:** TK-4358 - create workflow for manually trigger any amplify build - with update on scripts and constants ([#3982](https://github.com/resola-ai/deca-apps/issues/3982)) ([d78ffb8](https://github.com/resola-ai/deca-apps/commit/d78ffb8941c4017f6b87cbc7d3f78d9855f7bc76))
* **general:** TK-4358 - improve manual-trigger-amplify-build script ([7dcf90e](https://github.com/resola-ai/deca-apps/commit/7dcf90ede479b558d204b82916ab299f0e5f7fc2))
* **general:** TK-4358 - improve manual-trigger-amplify-build script ([ec76197](https://github.com/resola-ai/deca-apps/commit/ec76197adcd790881c086a9a4269008f33c151f1))
* **general:** TK-4358 - improve manual-trigger-amplify-build script ([8df2327](https://github.com/resola-ai/deca-apps/commit/8df2327121a80b7a7da4c6044988fac1ebf479fb))
* **general:** TK-4358 - improve manual-trigger-amplify-build script ([956cc39](https://github.com/resola-ai/deca-apps/commit/956cc396e91a61b98565decc26803cb624911962))
* **general:** Upgrade mantine to latest version v7.16.1, fix storybook issue, upgrade storybook to latest verison, migrate demo app to mantine latest version ([#4013](https://github.com/resola-ai/deca-apps/issues/4013)) ([733e4c1](https://github.com/resola-ai/deca-apps/commit/733e4c14a0414e0c04db09f7c50a41d32ebbbdf3))
* **kb:** [TK-3763][TK-3766] Handle view list + detail document files ([#3948](https://github.com/resola-ai/deca-apps/issues/3948)) ([d95c658](https://github.com/resola-ai/deca-apps/commit/d95c65864e3f65579d307236078ea17db8d0e39f))
* **kb:** [TK-3766] handle download file via dialog ([#4024](https://github.com/resola-ai/deca-apps/issues/4024)) ([342ce00](https://github.com/resola-ai/deca-apps/commit/342ce00f85e16ca134b84bed9f8007ba3b1b0721))
* **kb:** [TK-3766] integrate API download + handle reload + intergrate update access level ([#3991](https://github.com/resola-ai/deca-apps/issues/3991)) ([2270d29](https://github.com/resola-ai/deca-apps/commit/2270d290d13effb2f22c9d964365c1ff4c4ef7a6))
* **kb:** [TK-3767] update search result show files ([#3965](https://github.com/resola-ai/deca-apps/issues/3965)) ([b44df37](https://github.com/resola-ai/deca-apps/commit/b44df3709876e52af37fe957619c70fb7b369a63))
* **kb:** TK-3698 Correct message namespace in article moving ([#3890](https://github.com/resola-ai/deca-apps/issues/3890)) ([2e9ba4c](https://github.com/resola-ai/deca-apps/commit/2e9ba4c4a27c49924e86e027e6807929b7e2f227))
* **kb:** TK-3700 Correct width in Drawer and improve loading state ([#3995](https://github.com/resola-ai/deca-apps/issues/3995)) ([c803374](https://github.com/resola-ai/deca-apps/commit/c803374efbd0194b038c69b5169c1328ba58b086))
* **kb:** TK-3700 Implement job article editing and saving and correct some UI issues ([#3993](https://github.com/resola-ai/deca-apps/issues/3993)) ([0fb6b99](https://github.com/resola-ai/deca-apps/commit/0fb6b99f0f4ae1ef0bf202b5227c6c5691180db5))
* **kb:** TK-3759 Implement select and save Job Articles to KB ([#3946](https://github.com/resola-ai/deca-apps/issues/3946)) ([02864ef](https://github.com/resola-ai/deca-apps/commit/02864ef728e624548a05a2ad4bed906a77f2d1b0))
* **kb:** TK-4282 Enable article generator feature after Sprint 2 released ([#3907](https://github.com/resola-ai/deca-apps/issues/3907)) ([b5a1d79](https://github.com/resola-ai/deca-apps/commit/b5a1d79b9d278309df2e9b8338992b0138e123a1))
* **kb:** TK-4282 Enabled document upload feature ([#3910](https://github.com/resola-ai/deca-apps/issues/3910)) ([e00ac98](https://github.com/resola-ai/deca-apps/commit/e00ac9829d14b6bc4e968b2b4215ecbfaad3745a))
* **kb:** TK-4282 Implement loading state and improve drawer width styles ([#4022](https://github.com/resola-ai/deca-apps/issues/4022)) ([5a5c78e](https://github.com/resola-ai/deca-apps/commit/5a5c78ee513bf80a9ad54a13fe2a8eef13131676))
* **kb:** TK-4282 Integrate Job createdBy and correct some feedback ([#4007](https://github.com/resola-ai/deca-apps/issues/4007)) ([8c68f0a](https://github.com/resola-ai/deca-apps/commit/8c68f0a59f69da39eb12fc79f26a10988c885e7c))
* **kb:** TK-4282 Integrate Job generation error message and handle job deleting ([#3957](https://github.com/resola-ai/deca-apps/issues/3957)) ([5b2d0c6](https://github.com/resola-ai/deca-apps/commit/5b2d0c65e50c38b9240b9e56bbd1d6816eae35f6))
* **kb:** TK-4282 integrate retry API to job generator ([#4002](https://github.com/resola-ai/deca-apps/issues/4002)) ([0c3117e](https://github.com/resola-ai/deca-apps/commit/0c3117eab7fb5a1334163e93b40c38015d5d5577))
* **kb:** TK-4467 Correct some UI issues in KB after synced new Mantine version ([#3928](https://github.com/resola-ai/deca-apps/issues/3928)) ([db9c078](https://github.com/resola-ai/deca-apps/commit/db9c078e59169764f62bda1cb3d26d72019635d8))
* **kb:** TK-4467, TK-4544 Correct date range picker feedback and adjust UI related to Mantine ([#3967](https://github.com/resola-ai/deca-apps/issues/3967)) ([5739818](https://github.com/resola-ai/deca-apps/commit/57398182232a6af13f70e24bbe444c9dd14d2724))
* **kb:** TK-4544 Correct and force filter date in Japan timezone ([#3973](https://github.com/resola-ai/deca-apps/issues/3973)) ([9376b76](https://github.com/resola-ai/deca-apps/commit/9376b7637e759a8bb9898f9060f73a6125066a5c))
* **kb:** TK-4544 Correct end of date time for Japan query time ([#3970](https://github.com/resola-ai/deca-apps/issues/3970)) ([f9175bc](https://github.com/resola-ai/deca-apps/commit/f9175bc97e1b0a51bb4dfe32b941fc02f1d72454))
* **kb:** TK-4647 Update document author name logic ([#4042](https://github.com/resola-ai/deca-apps/issues/4042)) ([98092f3](https://github.com/resola-ai/deca-apps/commit/98092f324d48dc47f5f4d1587ff01f7cb1b7165c))
* **kb:** TK-4647 Update document author name logic ([#4042](https://github.com/resola-ai/deca-apps/issues/4042)) ([7311b47](https://github.com/resola-ai/deca-apps/commit/7311b4709479494c22244ab32219819345a87fff))
* **kb:** TK-4650 Update default prompt in Job Generator ([#4041](https://github.com/resola-ai/deca-apps/issues/4041)) ([cda6f43](https://github.com/resola-ai/deca-apps/commit/cda6f43ae581ab0d9f11440a44133ca22ff4b093))
* **kb:** TK-4650 Update default prompt in Job Generator ([#4041](https://github.com/resola-ai/deca-apps/issues/4041)) ([2e4929d](https://github.com/resola-ai/deca-apps/commit/2e4929da0ff38675a6fc87cba00646a4d2610941))
* **livechat:** TK-3497 fix racing assign ([#4008](https://github.com/resola-ai/deca-apps/issues/4008)) ([6a8c7a4](https://github.com/resola-ai/deca-apps/commit/6a8c7a4744b68fa34ee25ca6d120290d03b92dfb))
* **livechat:** TK-3497 fix-racing-assign ([#3986](https://github.com/resola-ai/deca-apps/issues/3986)) ([929b470](https://github.com/resola-ai/deca-apps/commit/929b470400f3fd4f16dd97faadccbfc31384f640))
* **livechat:** TK-3544 replace-lib-moment-to-dayjs ([#3962](https://github.com/resola-ai/deca-apps/issues/3962)) ([b13b7ec](https://github.com/resola-ai/deca-apps/commit/b13b7ec27964e32e6d1cfb1c41807257bb82f51c))
* **livechat:** tk-4314-implement-change-status ([6ea24b5](https://github.com/resola-ai/deca-apps/commit/6ea24b5aa90af29fc907bada026456d14dc961b1))
* **livechat:** tk-4314-implement-change-status ([c554d05](https://github.com/resola-ai/deca-apps/commit/c554d0562e778ed186ea2cad869141bac8ed62a4))
* **livechat:** tk-4314-implement-change-status ([87e6925](https://github.com/resola-ai/deca-apps/commit/87e6925b2af3c35112a9009bcee9e1302ca411c5))
* **livechat:** tk-4314-implement-change-status ([b66f0a6](https://github.com/resola-ai/deca-apps/commit/b66f0a60c509721c92a76f98ccd87aa5e48bafab))
* **livechat:** tk-4314-implement-change-status ([51000a8](https://github.com/resola-ai/deca-apps/commit/51000a8833e2c8eb4832251bc6e9cdb0f3522681))
* **livechat:** TK-4597 add max conversations settings ([#3996](https://github.com/resola-ai/deca-apps/issues/3996)) ([085e05b](https://github.com/resola-ai/deca-apps/commit/085e05bfab83c3e43503b5bd70eee3a0574c3ca4))
* **page-admin:** TK-10 Update translation ([#4026](https://github.com/resola-ai/deca-apps/issues/4026)) ([66b39f6](https://github.com/resola-ai/deca-apps/commit/66b39f6894928de66bcb93f1e8ceb9c217f62e4f))
* **page-admin:** TK-3812 UI for search and pagination ([#4039](https://github.com/resola-ai/deca-apps/issues/4039)) ([442db49](https://github.com/resola-ai/deca-apps/commit/442db495847f894d746b4558cea1c6ab0d334200))
* **page-admin:** TK-3812 UI for Site List ([#3956](https://github.com/resola-ai/deca-apps/issues/3956)) ([48ec72f](https://github.com/resola-ai/deca-apps/commit/48ec72f970058f732b89c82f9945e5f6e3fe8aa0))
* **page-admin:** TK-3813 create a new site feature ([#4025](https://github.com/resola-ai/deca-apps/issues/4025)) ([26a0f04](https://github.com/resola-ai/deca-apps/commit/26a0f04e62212ec13cc3fa40adf6030c57be349f))
* **page-admin:** TK-3826 - update the translation key ([#4018](https://github.com/resola-ai/deca-apps/issues/4018)) ([0d7279d](https://github.com/resola-ai/deca-apps/commit/0d7279d665685d59e22036cd33f75e8e2d38ac82))
* **page-admin:** TK-3841 - Show the not found page to the end users ([#3975](https://github.com/resola-ai/deca-apps/issues/3975)) ([5dc2af1](https://github.com/resola-ai/deca-apps/commit/5dc2af179ffbf6d20c89981bae4c7bed77f76500))
* **page-admin:** TK-4235 cleanup unused code, add link settings ([#3945](https://github.com/resola-ai/deca-apps/issues/3945)) ([7ee0d46](https://github.com/resola-ai/deca-apps/commit/7ee0d46514448fa29ff82d2db85cf1a4e520782c))
* **page-admin:** TK-4364 implement text element ([#3972](https://github.com/resola-ai/deca-apps/issues/3972)) ([bb640f4](https://github.com/resola-ai/deca-apps/commit/bb640f478ee97657adda4ff91e425c6d92a042f0))
* **page-admin:** TK-4365 Add button element ([#3999](https://github.com/resola-ai/deca-apps/issues/3999)) ([e062325](https://github.com/resola-ai/deca-apps/commit/e0623259483306d55648d69cca44ebbec10ec5fc))
* **page-admin:** TK-4366 Implement UI for Media (Image) element ([#3963](https://github.com/resola-ai/deca-apps/issues/3963)) ([3895964](https://github.com/resola-ai/deca-apps/commit/3895964f3de6a941c56a0ba40e375cddfb626334))
* **page-admin:** TK-4367 Add divider ([#3969](https://github.com/resola-ai/deca-apps/issues/3969)) ([9076265](https://github.com/resola-ai/deca-apps/commit/90762659e115b201009b34d10bf2a5a41d31bf3e))
* **page-admin:** TK-4368 Implement UI for embed google map page builder ([#3952](https://github.com/resola-ai/deca-apps/issues/3952)) ([ff90e0d](https://github.com/resola-ai/deca-apps/commit/ff90e0de48cba327b6b5ce5d67f781002adfe90c))
* **page-admin:** TK-4381 - correct package names for page admin and builder ([a686159](https://github.com/resola-ai/deca-apps/commit/a686159e4da9e13633a7d83800d1259bd5895f9e))
* **page-admin:** TK-4381 - update customHttp.yml to include page-admin ([ebad920](https://github.com/resola-ai/deca-apps/commit/ebad92069b1661759eecd2d923bfe2245bbb0bc6))
* **tables:** TK-2502: hide clearview ([#4040](https://github.com/resola-ai/deca-apps/issues/4040)) ([6488861](https://github.com/resola-ai/deca-apps/commit/6488861065f4c83960ec2767db89fa38925f834f))
* **tables:** TK-3328 right click sidebar items ([016eb84](https://github.com/resola-ai/deca-apps/commit/016eb84a3e32a633c596dea153e6331ad3e900d3))
* **tables:** TK-3625 stream records from sse ([1882386](https://github.com/resola-ai/deca-apps/commit/1882386ef6212387f969f987b2a523a1c9b1c080))
* **tables:** TK-3690: update icon for workspaces ([#4033](https://github.com/resola-ai/deca-apps/issues/4033)) ([d00debd](https://github.com/resola-ai/deca-apps/commit/d00debd49bc5e9c0729b68daa79e6d7f1948ab5c))
* **tables:** TK-4386 fix import button ([0c028d6](https://github.com/resola-ai/deca-apps/commit/0c028d649b7f033c5bc40f0863aff316c0867ff2))
* **widget-builder:** TK-3900 add widget type badge to widget list ([f4e6182](https://github.com/resola-ai/deca-apps/commit/f4e618294373e6db449a54d2e41fe2bf25f5e1f9))
* **widget-builder:** TK-3904 add translation for widget details page ([11d3ec7](https://github.com/resola-ai/deca-apps/commit/11d3ec793c7d3a25cbb6a55471a871844c2f4830))
* **widget-builder:** TK-3910 integrated remove draft widget ([#3977](https://github.com/resola-ai/deca-apps/issues/3977)) ([b4dd971](https://github.com/resola-ai/deca-apps/commit/b4dd971f24a2e632916107106600754abd5cc4aa))
* **widget-builder:** TK-4009 create ui for preview section ([#3942](https://github.com/resola-ai/deca-apps/issues/3942)) ([9d876f3](https://github.com/resola-ai/deca-apps/commit/9d876f3fe835af76fac72fc2c1988f05c4cbe50b))
* **widget-builder:** TK-4012 show files to explorer ([#4043](https://github.com/resola-ai/deca-apps/issues/4043)) ([f35bdc6](https://github.com/resola-ai/deca-apps/commit/f35bdc625baf9e1e301830c5346cd04fb6a744ad))
* **widget-builder:** TK-4558 update search widgets ([#3980](https://github.com/resola-ai/deca-apps/issues/3980)) ([780f143](https://github.com/resola-ai/deca-apps/commit/780f143e4b57b650d5f70ecb1c7846b4535ea044))


### Bug Fixes

* **account:** TK-3691 - Fix recent activity page ([#3949](https://github.com/resola-ai/deca-apps/issues/3949)) ([20880a0](https://github.com/resola-ai/deca-apps/commit/20880a099011c7af4e194134c3f26347a0ad04bd))
* **account:** TK-3691 - Migrate Account app to use API v2 ([#3936](https://github.com/resola-ai/deca-apps/issues/3936)) ([ae24cad](https://github.com/resola-ai/deca-apps/commit/ae24cad0a83f3587cf44841deecffe96f31c8301))
* **account:** TK-4517 - Update Account app to use new /avatar endpoints ([#3976](https://github.com/resola-ai/deca-apps/issues/3976)) ([bfc40e7](https://github.com/resola-ai/deca-apps/commit/bfc40e7536703fa92b55e8d240d3edb6bdd0496d))
* **account:** update-profile-data-fix ([0456b4a](https://github.com/resola-ai/deca-apps/commit/0456b4a8e75b0dcf6f780423040514104b859f7c))
* **ai-widgets:** TK-3754 update proofreading default prompt ([#3944](https://github.com/resola-ai/deca-apps/issues/3944)) ([bb51ead](https://github.com/resola-ai/deca-apps/commit/bb51ead43719e9a702f3b88161b3f450b3779492))
* **ai-widgets:** TK-3755 update cancel text in dictionary mode ([#3943](https://github.com/resola-ai/deca-apps/issues/3943)) ([ca72fe1](https://github.com/resola-ai/deca-apps/commit/ca72fe1b1e0de110995ed6e476363d015af061ba))
* **ai-widgets:** TK-4441 update kb endpoint on production ([#3908](https://github.com/resola-ai/deca-apps/issues/3908)) ([f8ed714](https://github.com/resola-ai/deca-apps/commit/f8ed714f0961dfb4f0ebebbd7ef4ee7a3f57c28a))
* **ai-widgets:** TK-4441 update prod mode check ([#3911](https://github.com/resola-ai/deca-apps/issues/3911)) ([062c1f3](https://github.com/resola-ai/deca-apps/commit/062c1f3f944e253b261bfd86586947ffe9e70030))
* **chatbot:** TK-3356 Add finish chatbot text config ([#3987](https://github.com/resola-ai/deca-apps/issues/3987)) ([7013ae1](https://github.com/resola-ai/deca-apps/commit/7013ae12806b99a7ea961bba77498b9743c4d553))
* **chatbot:** TK-3356 Add option to enable/disable end conversation message ([#4019](https://github.com/resola-ai/deca-apps/issues/4019)) ([8624966](https://github.com/resola-ai/deca-apps/commit/8624966c8320310dbce28912cb20fa3a1677f809))
* **chatbot:** TK-3356 Update text description for finish chatbot text config ([#4031](https://github.com/resola-ai/deca-apps/issues/4031)) ([f54c2ba](https://github.com/resola-ai/deca-apps/commit/f54c2ba3a6a8507c41ddb076df18fd84628a4967))
* **chatbot:** TK-4452 Check empty content and empty value from editor ([#3918](https://github.com/resola-ai/deca-apps/issues/3918)) ([7eff0cd](https://github.com/resola-ai/deca-apps/commit/7eff0cdf917b418d65d62623351670d4ca721508))
* **chatbot:** TK-4452 Check empty content and empty value from editor ([#3918](https://github.com/resola-ai/deca-apps/issues/3918)) ([a993e5c](https://github.com/resola-ai/deca-apps/commit/a993e5ca6b72b3fa67e4ec096e9e0dc802a65499))
* **chatbot:** TK-4452 Trim multi break lines in markdown editor ([#3923](https://github.com/resola-ai/deca-apps/issues/3923)) ([143a49b](https://github.com/resola-ai/deca-apps/commit/143a49b47a0552fee0494d3ddf7bd28eac6437ac))
* **chatbot:** TK-4452 Trim multi break lines in markdown editor ([#3923](https://github.com/resola-ai/deca-apps/issues/3923)) ([86eb126](https://github.com/resola-ai/deca-apps/commit/86eb126a50fa70e92252c67fb59650b6c48d3712))
* **chatbot:** TK-4469 Improve Chatbot UI after updating Mantine V7 ([#3998](https://github.com/resola-ai/deca-apps/issues/3998)) ([1454384](https://github.com/resola-ai/deca-apps/commit/145438449819ac1b24a677e6687bf7e883a54daa))
* **chatbot:** TK-4469 Improve Chatbot UI after updating Mantine V7 ([#4038](https://github.com/resola-ai/deca-apps/issues/4038)) ([494ad82](https://github.com/resola-ai/deca-apps/commit/494ad823dbc4d1218281f61619302a7f61742eb2))
* **chatbot:** TK-4472 Implement pagination for Versions page ([#3974](https://github.com/resola-ai/deca-apps/issues/3974)) ([46cac9a](https://github.com/resola-ai/deca-apps/commit/46cac9aa136279995cbeafb9ff0cabdc9eaa3051))
* **chatbot:** TK-4473 Add confirmation dialog allow input version name when create version ([#3985](https://github.com/resola-ai/deca-apps/issues/3985)) ([824f0eb](https://github.com/resola-ai/deca-apps/commit/824f0ebbd1baa71449788703fee4817e868038e6))
* **chatbox:** TK-4438: create useClickImage hook for  QA and HTML message ([f6b6581](https://github.com/resola-ai/deca-apps/commit/f6b6581c712107a3aceb2463100cfd6629765d47))
* **chatbox:** TK-4452: trim text and check empty string. ([#3922](https://github.com/resola-ai/deca-apps/issues/3922)) ([3c4c068](https://github.com/resola-ai/deca-apps/commit/3c4c068b023f6dd8c8558f4dce9e79613e09e90a))
* **chatbox:** TK-4452: trim text and check empty string. ([#3922](https://github.com/resola-ai/deca-apps/issues/3922)) ([d023edb](https://github.com/resola-ai/deca-apps/commit/d023edbc8674d1bd428f5bdac4f7f36575759bb0))
* **chatbox:** TK-4563: Wrapped the CW client in a shadow DOM ([#4000](https://github.com/resola-ai/deca-apps/issues/4000)) ([8f79558](https://github.com/resola-ai/deca-apps/commit/8f795589c45011d4aefa5a2d116be1dbb5a8984a))
* **crm:** TK - 4459 - Fix edit trigger wrong fields ([#3929](https://github.com/resola-ai/deca-apps/issues/3929)) ([2f1e510](https://github.com/resola-ai/deca-apps/commit/2f1e510d75bc8a914457787f252e7f237a43992b))
* **crm:** TK-4370 reduce html template requests ([95e8a6c](https://github.com/resola-ai/deca-apps/commit/95e8a6ca115cbc03b4b4dba6883fd8ce1a951c61))
* **crm:** tk-4552 Return correct status for Email/SMS when sending failed ([06f6c81](https://github.com/resola-ai/deca-apps/commit/06f6c81c9128462fd3e7cda0d9ebf3ce0b834342))
* **crm:** TK-4577, TK-4582 fix highlight and missing icon ([#4006](https://github.com/resola-ai/deca-apps/issues/4006)) ([22b021f](https://github.com/resola-ai/deca-apps/commit/22b021f20f16cd591650c350f400ddff1ff957e4))
* **crm:** update email template and editor with mantine v7 ([fa60514](https://github.com/resola-ai/deca-apps/commit/fa605142192b376221e1dab07163af443a479fad))
* **crm:** update type ([cc1d4ab](https://github.com/resola-ai/deca-apps/commit/cc1d4ab81a1810f7d57b398dac824c45086d1baa))
* **form-admin:** TK-4510 Fix cannot scroll ([#3968](https://github.com/resola-ai/deca-apps/issues/3968)) ([7e093c3](https://github.com/resola-ai/deca-apps/commit/7e093c3645ff1d80949a062202de203d01484dfa))
* **form-admin:** TK-4513 Fix to remove the white bar bottom of template page ([#4017](https://github.com/resola-ai/deca-apps/issues/4017)) ([28c9cb4](https://github.com/resola-ai/deca-apps/commit/28c9cb44b5a21a003b2f6f596a861c2bce6cf192))
* **form-admin:** TK-4524 fix color ([#3979](https://github.com/resola-ai/deca-apps/issues/3979)) ([da2826b](https://github.com/resola-ai/deca-apps/commit/da2826bf9412206854790f6a4d0bb299b3982410))
* **kb:** [TK-4593] The file was not uploaded to the correct folder selected ([#4003](https://github.com/resola-ai/deca-apps/issues/4003)) ([7b6bff3](https://github.com/resola-ai/deca-apps/commit/7b6bff334dcfc5b74b36081b8147df89a404972a))
* **kb:** [TK-4606][TK-4633] load more articles + kbs into article selector, missing folder in folder page ([#4014](https://github.com/resola-ai/deca-apps/issues/4014)) ([6ddc546](https://github.com/resola-ai/deca-apps/commit/6ddc54617b4cc35e59c62526fdcfd5f50907dd2a))
* **kb:** TK-4282 Fix search layout and resolve conflicts ([#4012](https://github.com/resola-ai/deca-apps/issues/4012)) ([aaf7ca5](https://github.com/resola-ai/deca-apps/commit/aaf7ca5b3ff58f7630e8c7a261ecca1a9fec3ec3))
* **kb:** TK-4467 Adjust some UI issues related to Mantine component ([#4001](https://github.com/resola-ai/deca-apps/issues/4001)) ([c97ebf9](https://github.com/resola-ai/deca-apps/commit/c97ebf960eb4a4993570f9bab271aad5302df23d))
* **kb:** TK-4647 Correct Markdown detect in blocknote editor ([#4047](https://github.com/resola-ai/deca-apps/issues/4047)) ([be9508e](https://github.com/resola-ai/deca-apps/commit/be9508e1f83b18a1117bab7fc5076459c77be433))
* **kb:** TK-4647 Correct Markdown detect in blocknote editor ([#4047](https://github.com/resola-ai/deca-apps/issues/4047)) ([ae72d4e](https://github.com/resola-ai/deca-apps/commit/ae72d4eb93145ad76b436ba3b8df552cc9cfcc71))
* **kb:** TK-4647 Correct retry job success message ([#4037](https://github.com/resola-ai/deca-apps/issues/4037)) ([da4d4e4](https://github.com/resola-ai/deca-apps/commit/da4d4e4ca1c3d10864057018d7466713053b4ee8))
* **kb:** TK-4647 Correct retry job success message ([#4037](https://github.com/resola-ai/deca-apps/issues/4037)) ([37f9e0e](https://github.com/resola-ai/deca-apps/commit/37f9e0e11cff712e39c4279ef79bba2b55dc3540))
* **kb:** TK-4650, TK-4643, TK-4647 Update default prompt and adjust UI for Job Generator ([#4034](https://github.com/resola-ai/deca-apps/issues/4034)) ([429fba2](https://github.com/resola-ai/deca-apps/commit/429fba2644bd69b02e67a490471ab8c5404e02ab))
* **kb:** TK-4650, TK-4643, TK-4647 Update default prompt and adjust UI for Job Generator ([#4034](https://github.com/resola-ai/deca-apps/issues/4034)) ([803b783](https://github.com/resola-ai/deca-apps/commit/803b783a49589d19c02c406300fb4de060e34cc7))
* **livechat:** TK-3533 - Update menu icon in live chat ([#3961](https://github.com/resola-ai/deca-apps/issues/3961)) ([547e38e](https://github.com/resola-ai/deca-apps/commit/547e38e0351777aaf87c10ba95e57195466d0906))
* **livechat:** tk-4363-change-memo-api-function ([#3883](https://github.com/resola-ai/deca-apps/issues/3883)) ([39d8a18](https://github.com/resola-ai/deca-apps/commit/39d8a187ca3a57ff8cab0b6c530c38c0141ede71))
* **livechat:** TK-4446 - Fix missing strings ([#3992](https://github.com/resola-ai/deca-apps/issues/3992)) ([5c8f4cb](https://github.com/resola-ai/deca-apps/commit/5c8f4cb588288117786cc57d43b0f13ba5286d71))
* **livechat:** TK-4482 adjust some UI issues ([#3953](https://github.com/resola-ai/deca-apps/issues/3953)) ([291f83b](https://github.com/resola-ai/deca-apps/commit/291f83b31f3b77a10877b503f2c2554e704d958e))
* **livechat:** TK-4482 adjust some UI issues ([#3971](https://github.com/resola-ai/deca-apps/issues/3971)) ([13c8516](https://github.com/resola-ai/deca-apps/commit/13c85162d52faa456a397aa9294e8b4b129ef7fc))
* **livechat:** tk-4482-adjust-some-ui-issues ([#3950](https://github.com/resola-ai/deca-apps/issues/3950)) ([d050d05](https://github.com/resola-ai/deca-apps/commit/d050d05d54b21a199f26b427eef655e1d1905b87))
* **livechat:** TK-4500 - Update settings icon ([#3988](https://github.com/resola-ai/deca-apps/issues/3988)) ([2dad2b0](https://github.com/resola-ai/deca-apps/commit/2dad2b0ec5295ca70d04ac3cdb185c9fe424accc))
* **livechat:** TK-4527 permission-to-remove-memo ([#3959](https://github.com/resola-ai/deca-apps/issues/3959)) ([bcee522](https://github.com/resola-ai/deca-apps/commit/bcee522fe4d87c03cda5e0f968152435719d0281))
* **livechat:** TK-4604 adjust-some-ui ([#4005](https://github.com/resola-ai/deca-apps/issues/4005)) ([859b560](https://github.com/resola-ai/deca-apps/commit/859b56028358b539bb795ffeb168baf845d83bf7))
* **livechat:** TK-4632 - Fix automation icon line thickness ([#4036](https://github.com/resola-ai/deca-apps/issues/4036)) ([c4f5cfd](https://github.com/resola-ai/deca-apps/commit/c4f5cfdc31d441b20a3935ebd950000033b63d3b))
* **page-admin:** TK-3826 - Migrate the mantine ui to v7 for page settings ([#3978](https://github.com/resola-ai/deca-apps/issues/3978)) ([ba47919](https://github.com/resola-ai/deca-apps/commit/ba479192a0a8b29b0f729fe9e25dde8321026006))
* **tables:** TK-4032 cannot reset filter ([61ddeb5](https://github.com/resola-ai/deca-apps/commit/61ddeb56ed3f0b5c0dcfff29bad65c4b203ab7a2))

## [0.10.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.10.0) (2025-01-14)


### Features

* **tables:** TK-2383: handle open create field popover ([af259da](https://github.com/resola-ai/deca-apps/commit/af259daa74015aa019b18ade4514c6361ca5a9a6))
* **tables:** TK-2461 update visibility toggle ([248651e](https://github.com/resola-ai/deca-apps/commit/248651e393ac9a945a03d8e933e80e41dd13b899))
* **tables:** TK-2461 update visibility toggle ([3793f7f](https://github.com/resola-ai/deca-apps/commit/3793f7f740c58f5e0576c1e6c4550ebd3370521c))
* **tables:** TK-2486 add translation for delete multiple rows ([8b42cd4](https://github.com/resola-ai/deca-apps/commit/8b42cd4a0e74c7e0e6b05bdfc7f55447b4c6eafb))
* **tables:** TK-2486 temporarily hide delete when select multiple records ([9e8f772](https://github.com/resola-ai/deca-apps/commit/9e8f77209a6bc63979d32a16f92a5e64ea038a56))
* **tables:** TK-2486 temporarily hide delete when select multiple records ([0818404](https://github.com/resola-ai/deca-apps/commit/0818404b771edcae4463515ce85e7f01c8318518))
* **tables:** TK-2494 refactor field context menu ([62867d1](https://github.com/resola-ai/deca-apps/commit/62867d10836117cf36a017e7b7ca7ed19f137b4b))
* **tables:** TK-2494 refactor onViewChange decoupling ([057cbf9](https://github.com/resola-ai/deca-apps/commit/057cbf934bc452bea3bb9e41fb2660b79e4b3e87))
* **tables:** TK-2509: update col deletedAt ([c7317b0](https://github.com/resola-ai/deca-apps/commit/c7317b0a63053bc139fd386f7ec69f02ac8e7e3e))
* **tables:** TK-2509: update col deletedAt ([d8cdd55](https://github.com/resola-ai/deca-apps/commit/d8cdd551579379e9df9891787227787b5bade3f7))
* **tables:** TK-2509: update text ([83d2d55](https://github.com/resola-ai/deca-apps/commit/83d2d559c9be59269e915793becc4b31e1c8416c))
* **tables:** TK-2509: update text ([4024813](https://github.com/resola-ai/deca-apps/commit/4024813fc9ccc07a48f3ef4179fe015097e6230e))
* **tables:** TK-2683 tolgee integration ([c619dbc](https://github.com/resola-ai/deca-apps/commit/c619dbcfb9b9a10441c7252ea39c1406894ad8d5))
* **tables:** TK-2683 update language constants ([d20c763](https://github.com/resola-ai/deca-apps/commit/d20c763d9dfe0ce872530949bd239ef44d950e1b))
* **tables:** TK-3054 fix field mapper, add typesafe to render cell ([04c3b93](https://github.com/resola-ai/deca-apps/commit/04c3b93fea171c7cf599523f0f541d1fd1269222))
* **tables:** TK-3054 fix number parse error ([ac58ef4](https://github.com/resola-ai/deca-apps/commit/ac58ef46a484de6caa87a7ed910f2b75d21f3b93))
* **tables:** TK-3055 fix table sort order not updated ([711dc6f](https://github.com/resola-ai/deca-apps/commit/711dc6f87a4625b57051922adfdd6504b20c936a))
* **tables:** TK-3122: update tooltip ([#3839](https://github.com/resola-ai/deca-apps/issues/3839)) ([08a698f](https://github.com/resola-ai/deca-apps/commit/08a698fa67d6d0d2f44b4f09292c9c5268c5126f))
* **tables:** TK-3622: remove paging table/table trash layout ([#3777](https://github.com/resola-ai/deca-apps/issues/3777)) ([6151c4f](https://github.com/resola-ai/deca-apps/commit/6151c4f68a8d2d1a996050f7844eef26d873e984))
* **tables:** update delete multiple records api ([60fd8db](https://github.com/resola-ai/deca-apps/commit/60fd8db6ddc2027afd00c4b1a865ebf9d2cd646f))


### Bug Fixes

* **tables:** TK-2380: fix height of table view ([8ac8d9f](https://github.com/resola-ai/deca-apps/commit/8ac8d9f49cb08aa927a2f404e94fdb28d310666e))
* **tables:** TK-2396: remove select control in context menu ([49c61e0](https://github.com/resola-ai/deca-apps/commit/49c61e073fdfabf48daea2a69523344c6488f947))
* **tables:** TK-2396: remove unused code ([8a56396](https://github.com/resola-ai/deca-apps/commit/8a5639615347f541f2af0c7a66bbfd043d3fe569))
* **tables:** TK-3122: fix table name ([ddb9c41](https://github.com/resola-ai/deca-apps/commit/ddb9c411718b67c5f061341e1606d9da4d4d0969))


## [0.9.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.9.0) (2024-12-17)

### Features

* **tables:** refactor row context menu ([4da7b51](https://github.com/resola-ai/deca-apps/commit/4da7b51221163d176e21ecab1d1b75925c45fa92))
* **tables:** TK-2386: fix locales, remove SearchComponent in package ui ([5bfd9fa](https://github.com/resola-ai/deca-apps/commit/5bfd9fa62ca25567327b1e14749a93eb3e252c56))
* **tables:** TK-2396 update translation and select behavior ([857e954](https://github.com/resola-ai/deca-apps/commit/857e9548f6293a2bdcbfe385b6eae6892e076d83))
* **tables:** TK-2486 delete multiple records ([f0d2a4b](https://github.com/resola-ai/deca-apps/commit/f0d2a4b5ff2dfdc0f61e90353bab9e7fe2efe8b0))
* **tables:** TK-2495 fix merge conflicts ([094e18b](https://github.com/resola-ai/deca-apps/commit/094e18b5c722e690c2d35634ea9abdf5b2becb60))
* **tables:** use sortHeader and project for field sort and visibility ([6853684](https://github.com/resola-ai/deca-apps/commit/6853684e45517dd9afd1c647088e9f3cc7a878dc))

## [0.8.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.8.0) (2024-12-12)


### Features

* **tables:** TK-2298 update toolbar export and trash icon, add export data ([f7f2c3e](https://github.com/resola-ai/deca-apps/commit/f7f2c3e9cc5afad695de1c75d05b568d71dd89e8))
* **tables:** tk-2381: update icon for multiple select ([b121801](https://github.com/resola-ai/deca-apps/commit/b1218016021dabd88b8ff47a43e9dbccf56d8be5))
* **tables:** TK-2385: add AddRow button ([6b98f36](https://github.com/resola-ai/deca-apps/commit/6b98f36c46c4a1b768b5ac9ba289f0c0553ded6b))
* **tables:** TK-2386: update search ui ([f07fb84](https://github.com/resola-ai/deca-apps/commit/f07fb8485e76875d1e68c12186a956203e480a63))
* **tables:** TK-2387: update phone field ([307c872](https://github.com/resola-ai/deca-apps/commit/307c872c89a2aaaa94c6790a5bc5cedf0e938acc))
* **tables:** TK-2390 fix currency field ([144c6cd](https://github.com/resola-ai/deca-apps/commit/144c6cd79fad54fa271e92bc0fcafa85943c67d8))
* **tables:** TK-2396 improvement for rows selection UX ([b59c84d](https://github.com/resola-ai/deca-apps/commit/b59c84d548ad0288b22eb8524a4e0353a72c4961))
* **tables:** TK-2396 remove unnecessary variable ([448dcd4](https://github.com/resola-ai/deca-apps/commit/448dcd4684976c7d77b4bcdb045eeaf8b212b48f))
* **tables:** TK-2402: select value when focus input, prevent input non-numeric ([81bac97](https://github.com/resola-ai/deca-apps/commit/81bac97009c60c1d40d38dc8223afdcfd93be2ff))
* **tables:** tk-2459 fix update and delete custom field ([6d69d77](https://github.com/resola-ai/deca-apps/commit/6d69d7791ea110b158602e747e02540bafcb5ed0))
* **tables:** TK-2459 menu support change field name for tables ([9acb9e6](https://github.com/resola-ai/deca-apps/commit/9acb9e6fca9955a9dd241cf994d3acd207f8d7a5))
* **tables:** TK-2460 support change field order ([6b60842](https://github.com/resola-ai/deca-apps/commit/6b60842f4f2fe819ca1e52de539f4448a81ce80e))
* **tables:** TK-2490 fix delete field for first time does not reload the table ([51adc63](https://github.com/resola-ai/deca-apps/commit/51adc63df88640fcb7fde91a2f99efd0426e92db))
* **tables:** TK-2491 adjust table virtualizer render all columns ([6bab097](https://github.com/resola-ai/deca-apps/commit/6bab097a420f26d2d9e5c70283d07b125e9d31c3))
* **tables:** TK-2493 add virtual for table import and add translation for import validate ([9038818](https://github.com/resola-ai/deca-apps/commit/9038818ea08e8bca0ac3a0981b8d85f8598633e4))
* **tables:** tk-2493 fix preview table not loaded when tab change first time ([0ebabdc](https://github.com/resola-ai/deca-apps/commit/0ebabdc3a3adf7a5e61d5e4f6c4594e885f91e52))
* **tables:** TK-2499: add description to upload button ([0115997](https://github.com/resola-ai/deca-apps/commit/0115997adfe311f17120ec1577a8d9729c1b869b))
* **tables:** TK-2509: add PernamentDeletedAt col ([dbe2472](https://github.com/resola-ai/deca-apps/commit/dbe2472b0461b1ea3c5bb6b1b760f6f01b1d4e25))
* **tables:** TK-2509: refactor TableLayout components ([bf25eac](https://github.com/resola-ai/deca-apps/commit/bf25eac6712e4c10c33158d5c04458f52a19634a))
* **tables:** TK-2509: update deleteAt col ([7c4cced](https://github.com/resola-ai/deca-apps/commit/7c4ccedae7661d7a2d6d14c8aaa7b4147fde83b2))
* **tables:** TK-3043 fix format export ([f8de590](https://github.com/resola-ai/deca-apps/commit/f8de5906da90ac66c4c31e5a74e123322e52b88d))
* **tables:** TK-3171 fix import count and allow enter submit ([3f44f7c](https://github.com/resola-ai/deca-apps/commit/3f44f7c40016083d16ba4460fa05069af9b20fa8))


### Bug Fixes

* **tables:** TK-2401: fix styling transform ([5a240c2](https://github.com/resola-ai/deca-apps/commit/5a240c2721fba873c0dc0b43fe7db5fed66ccb0b))
* **tables:** TK-2401: update transform styling of items ([6c9a467](https://github.com/resola-ai/deca-apps/commit/6c9a467cd88770a423c4e8b045e62d31c89ed7b3))

## [0.7.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.7.0) (2024-11-21)

### Features

* **tables:** add useSWR for field, integrate field event with ws ([a7e1da2](https://github.com/resola-ai/deca-apps/commit/a7e1da2f1b2b942fcf6cef5b0116b35d9447eb2d))
* **tables:** add useSWR for record, support async callback for useMinimongo, handle optimistic ui for record update ([4ad17d7](https://github.com/resola-ai/deca-apps/commit/4ad17d71c79153482a7abbdb6172ef5761e7c3de))
* **tables:** fix bugs select and multiple select ([c1d4147](https://github.com/resola-ai/deca-apps/commit/c1d41477e9b6c4330b22b534e40f965da5dc3003))
* **tables:** TABLES-133 update context menu follow table style ([76430f9](https://github.com/resola-ai/deca-apps/commit/76430f9726e209bc1a824c80eef6a2ca1091d161))
* **tables:** TABLES-157 added a query parameter to filter bases created by me in the get list API ([6a74d15](https://github.com/resola-ai/deca-apps/commit/6a74d1557541913330a106f1e5574270fd1b1eb8))
* **tables:** TABLES-163 add duplication modal ([04d2a1f](https://github.com/resola-ai/deca-apps/commit/04d2a1f6589992f14e928062eb02abbb5542e79e))
* **tables:** TABLES-212 fix lint ([29180ee](https://github.com/resola-ai/deca-apps/commit/29180ee00aa514e478b40140aea8818c14e96b88))
* **tables:** TABLES-212 handle duplicate error for select option item ([1efbcd2](https://github.com/resola-ai/deca-apps/commit/1efbcd251f73fd73df51d2552f73ef34c63ac746))
* **tables:** TABLES-212 update mapper for single, multiple select ([49c6cf1](https://github.com/resola-ai/deca-apps/commit/49c6cf14f80b9ee857a68c635348b8088b172d93))
* **tables:** TABLES-219 update payload ([a48fb73](https://github.com/resola-ai/deca-apps/commit/a48fb732ca8d5257c19c1cb0923438d63adc94b5))
* **tables:** TABLES-219 use table api to create and update mutliple fields when import csv ([8c19a1a](https://github.com/resola-ai/deca-apps/commit/8c19a1a4e2234a980a6a4babbddd048b574a43c8))
* **tables:** TABLES-221 decoupling ToolbarItem ([3c0e77a](https://github.com/resola-ai/deca-apps/commit/3c0e77a5b52d33e7bf32cd229e1a027e2e621031))
* **tables:** TABLES-224 set the default option to exclude when importing csv into an existing table ([5592ade](https://github.com/resola-ai/deca-apps/commit/5592ade699ad54e271ed0d88150c1ce4ea5dfee4))
* **tables:** TABLES-245 update latest import UI ([52876b6](https://github.com/resola-ai/deca-apps/commit/52876b69091f269e3d5016d6b98ad037ce0dfe8e))
* **tables:** TABLES-269 refactor shared form modal, add useSWR for tables, update table sidebar link button ([0b067c0](https://github.com/resola-ai/deca-apps/commit/0b067c0bb17e7651ed1036db72fbb7a8537dc753))
* **tables:** TABLES-270 improve api integration, cleanup code ([5b1382b](https://github.com/resola-ai/deca-apps/commit/5b1382b06427e78b5fcd9ed854a1d75b004293ab))
* **tables:** update translations ([a0687fe](https://github.com/resola-ai/deca-apps/commit/a0687fe30cb24aa3b60f212616201d6ab735d174))

### Bug Fixes

* **tables:** fix table state get override ([4720a89](https://github.com/resola-ai/deca-apps/commit/4720a89d5c999f4c26c5462f5cb2dd9d877100b4))
* **tables:** remove hidden on screen < `md` ([134baf2](https://github.com/resola-ai/deca-apps/commit/134baf2829d0634a57b61482a9055cbe19947a5b))
* **tables:** update typo ([e26a5aa](https://github.com/resola-ai/deca-apps/commit/e26a5aa36c106dc7c314ead1cb57f6dd538c1e79))

## [0.6.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.6.0) (2024-11-05)

### Features

* **tables:** change API import from import-manual to import ([37c65c9](https://github.com/resola-ai/deca-apps/commit/37c65c98dc7b93b3837057bd197e5d7f9f307c8a))
* **tables:** fix default view ([12b23be](https://github.com/resola-ai/deca-apps/commit/12b23be02e7afd48dab9e47d2799378746a2b64f))
* **tables:** improve table context and add duplicate API ([f7e6c8d](https://github.com/resola-ai/deca-apps/commit/f7e6c8d93d4f9d0ce4456ceaabb1b5a1ca8fd86f))
* **tables:** TABLES-181 handle insert and duplicate record ([b3adb97](https://github.com/resola-ai/deca-apps/commit/b3adb97bdececeef43208c0d31dcaa976361bad6))
* **tables:** TABLES-208 sync table design ([e7131d0](https://github.com/resola-ai/deca-apps/commit/e7131d0064e847b472d1d9af75133038c656b657))
* **tables:** TABLES-216 update text + fix create button ([76d9ab0](https://github.com/resola-ai/deca-apps/commit/76d9ab0c21c99c1315b78d222201c114c300d56c))
* **tables:** TABLES-221 seperate toolbar item logic, add toolbar context ([14a99fb](https://github.com/resola-ai/deca-apps/commit/14a99fb7031f81d1bce93f9b7945c62b5eb52454))
* **tables:** TABLES-227 adjust scrollarea height with max limit to resolve modal overflow issues ([73385c5](https://github.com/resola-ai/deca-apps/commit/73385c51c514a6b2cbd0bf658a43a118055e43e8))
* **tables:** TABLES-232 enhance import logic to automatically map and assign fields with matching names for existing tables ([2e83b6f](https://github.com/resola-ai/deca-apps/commit/2e83b6f79d8e8c5ad5555185f623af3566914ca3))

### Bug Fixes

* **tables:** fix table state get override ([4720a89](https://github.com/resola-ai/deca-apps/commit/4720a89d5c999f4c26c5462f5cb2dd9d877100b4))
* **tables:** remove hidden on screen < `md` ([134baf2](https://github.com/resola-ai/deca-apps/commit/134baf2829d0634a57b61482a9055cbe19947a5b))
* **tables:** update typo ([e26a5aa](https://github.com/resola-ai/deca-apps/commit/e26a5aa36c106dc7c314ead1cb57f6dd538c1e79))

## [0.5.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.0) (2024-10-21)

### Features

* **table:** add sorting component ([fb69316](https://github.com/resola-ai/deca-apps/commit/fb69316264b9a100545d4051a80ef251d55f54a0))
* **table:** add translation for sorting UI ([1d654ef](https://github.com/resola-ai/deca-apps/commit/1d654ef1dc502128da9f42899cae4e510e692ea5))
* TABLES-54 add translation ([df27827](https://github.com/resola-ai/deca-apps/commit/df278272e7ae971b39eb3b571d59de0ff31b909e))
* TABLES-54 update MR feedbacks ([b89944e](https://github.com/resola-ai/deca-apps/commit/b89944e3b1bd8e8acdec1256373ba5b9aba4bb75))

### Bug Fixes

* **tables:** fix build error ([adbfa99](https://github.com/resola-ai/deca-apps/commit/adbfa99929b80b07f7ee8a2707e15bfb0999e557))

## 0.4.1 (2024-10-02)

### Features

### Bug Fixes

## 0.4.0 (2024-10-02)

### Features

- **tables:** add grid layout and base trash page ([34150e1](https://github.com/resola-ai/deca-apps/commit/34150e148835852707673c86dd48fa179cbbc047))

- **tables:** add overlay and skeleton loading props to deca table ([526a42c](https://github.com/resola-ai/deca-apps/commit/526a42c37a398d8685306acfd65949a3a85f2960))

- **tables:** clean up code, use papaparse to parse csv instead of xlsx ([0a3e2ea](https://github.com/resola-ai/deca-apps/commit/0a3e2eae05b8865e35d3db44ce7e57317bd80068))

- **tables:** fix header dropdown zindex overlap ([c966d49](https://github.com/resola-ai/deca-apps/commit/c966d495d0cfab5d27dd5e360be0cedfef5d889f))

- **tables:** fix import field mapping overflow ([c384f94](https://github.com/resola-ai/deca-apps/commit/c384f9477db999883940b9f2cee1aca8c73d2c32))

- **tables:** fix select options in add new column form not updated ([7ab8c16](https://github.com/resola-ai/deca-apps/commit/7ab8c16b69a5c1e64c24d2f3bddc7d0383d63748))

- **tables:** hide unsupported field types ([#2860](https://github.com/resola-ai/deca-apps/issues/2860)) ([b6b9e2b](https://github.com/resola-ai/deca-apps/commit/b6b9e2bf04568acb1f25680639c27ce9d102fe50))

- **tables:** Init Tables app ([#1393](https://github.com/resola-ai/deca-apps/issues/1393)) ([7390460](https://github.com/resola-ai/deca-apps/commit/7390460b7ea9f8c7cf19a94d32a6997b4c868724))

- **tables:** init websocket integration ([8e958f3](https://github.com/resola-ai/deca-apps/commit/8e958f3ae91f80a86b041be71b6ee555a5de725c))

- **tables:** TABLES-101 add longtext and phone field ([ab383ba](https://github.com/resola-ai/deca-apps/commit/ab383bac6560f5d8906dc6cda6af52fd4b2875f4))

- **tables:** TABLES-102 add email and url field, fix cell layout shift ([e182a47](https://github.com/resola-ai/deca-apps/commit/e182a478e30f01fde4c2baeb28e31ba6857d4adc))

- **tables:** TABLES-105 convert select field ([f41e909](https://github.com/resola-ai/deca-apps/commit/f41e909775c0482cd91ca492f07f47e50c48d239))

- **tables:** TABLES-107 restructure fields, add datetime ([64b4fd5](https://github.com/resola-ai/deca-apps/commit/64b4fd5b8ce3ad707b24339b420c5d9c699e16ec))

- **tables:** TABLES-108 update createdBy, updatedBy and handle mapper for date time fields ([4ac7bf4](https://github.com/resola-ai/deca-apps/commit/4ac7bf4452fcc20206720165cbf7aa3c87a61594))

- **tables:** TABLES-110 add image field with mock upload fn ([26a6706](https://github.com/resola-ai/deca-apps/commit/26a670602ed9ef6fdb58f6062378f0c56bd6bb6f))

- **tables:** TABLES-111 rename table fields data component field, add number field ([1825d35](https://github.com/resola-ai/deca-apps/commit/1825d35cabde26397bca4a4db163d01fe171aeaa))

- **tables:** TABLES-112 add checkbox field ([36e9e00](https://github.com/resola-ai/deca-apps/commit/36e9e00d7fc4f9489c6bdccf5f773639bdd325f1))

- **tables:** TABLES-113 add currency field ([df1b4a1](https://github.com/resola-ai/deca-apps/commit/df1b4a128cc128c0ad211739ccc67145eacabee9))

- **tables:** TABLES-114 add createdBy updatedBy autoId fields ([d37af74](https://github.com/resola-ai/deca-apps/commit/d37af74d76d86f3b3d5049e77a18099bb80440bb))

- **tables:** TABLES-115 add percent field ([da37988](https://github.com/resola-ai/deca-apps/commit/da3798879549f15143f702c84fabbc6a9fb31931))

- **tables:** TABLES-118 handle filtering and sorting ([c28b90b](https://github.com/resola-ai/deca-apps/commit/c28b90b98d013d0501284cbffcc9b8561ea25daa))

- **tables:** TABLES-119 sync ui to latest design ([e523fd7](https://github.com/resola-ai/deca-apps/commit/e523fd7e2f75e2436c0485d5d0d4c1ceaf12506d))

- **tables:** TABLES-119 update hook dependencies ([0a4dc9b](https://github.com/resola-ai/deca-apps/commit/0a4dc9b31108af462dff070ca79022896c13b335))

- **tables:** TABLES-122 integrate clear table API ([f44db91](https://github.com/resola-ai/deca-apps/commit/f44db91651ba11e414bd84e711e6e918c1254287))

- **tables:** TABLES-129 fix scrollbar in table sidebar and table view, remove view clear data ([949c5ef](https://github.com/resola-ai/deca-apps/commit/949c5ef16d156a518ee0268e6b033b90036f5a99))

- **tables:** TABLES-132 update table title cell hover ([48a5ac1](https://github.com/resola-ai/deca-apps/commit/48a5ac14cfbd20fd8856db92fa988bca45123206))

- **tables:** TABLES-139 import to existing table ([702428f](https://github.com/resola-ai/deca-apps/commit/702428f4ae5625d1d7a4dcfe27477c9ae8b74ca9))

- **tables:** TABLES-142 display api error message on notification ([85f0a11](https://github.com/resola-ai/deca-apps/commit/85f0a11bc37e78548cb54139a24cce302de711f6))

- **tables:** TABLES-149 add csv import upload screen ([f7218ba](https://github.com/resola-ai/deca-apps/commit/f7218bada4cf42f40dad09dae797e284283b958c))

- **tables:** TABLES-149 update table import csv select screen ([3b0dd91](https://github.com/resola-ai/deca-apps/commit/3b0dd9148e65184272113330b28325e1d914a12e))

- **tables:** TABLES-150 update table import csv field mapping screen ([95f0e8d](https://github.com/resola-ai/deca-apps/commit/95f0e8de46d29c05def6449e6a21185e4e18fd8b))

- **tables:** TABLES-151 clean up code ([22066ab](https://github.com/resola-ai/deca-apps/commit/22066ab24bc3237eeede435dad0298100bd04895))

- **tables:** TABLES-151 fix build failed ([748ef9b](https://github.com/resola-ai/deca-apps/commit/748ef9bf6c8772fbee960c78ecfaef478a644f3d))

- **tables:** TABLES-151 import preview ui and read csv integration ([f3521e6](https://github.com/resola-ai/deca-apps/commit/f3521e649c3c46f93e5c9394709998c3d17af113))

- **tables:** TABLES-156 add tooltip to base list ([04b8dfa](https://github.com/resola-ai/deca-apps/commit/04b8dfa5c5503d23a0a2a18bbd74191652e6d281))

- **tables:** TABLES-161 add empty status, alert for base list ([69933c4](https://github.com/resola-ai/deca-apps/commit/69933c4d13b7cfe669317c86585fb64583a3f920))

- **tables:** TABLES-161 fetch data by using useSWR ([f28d723](https://github.com/resola-ai/deca-apps/commit/f28d7233ff62defac91a2b0c6911cf23329c729f))

- **tables:** TABLES-169 update translations ([#2856](https://github.com/resola-ai/deca-apps/issues/2856)) ([e39c9a1](https://github.com/resola-ai/deca-apps/commit/e39c9a181fbcb984c64ebd78e8d9d92965186dfa))

- **tables:** TABLES-33 - Create sidebar menu Home page for tables ([#1417](https://github.com/resola-ai/deca-apps/issues/1417)) ([ed54c5f](https://github.com/resola-ai/deca-apps/commit/ed54c5f116fd688815616d76d564fa84452b5a43))

- **tables:** TABLES-37 handle update record ([635dbc4](https://github.com/resola-ai/deca-apps/commit/635dbc4976c012b78b39b7bc541c97607c7cfabe))

- **tables:** TABLES-37 skip update to wait API fix ([a401757](https://github.com/resola-ai/deca-apps/commit/a4017579f6bb5cf7abcef6cac13a68e25f38e1e0))

- **tables:** TABLES-37 use entities to update minimongo, add ws event listener for crud record ([7ef320c](https://github.com/resola-ai/deca-apps/commit/7ef320cfd58582da021df758babc596938b6107f))

- **tables:** TABLES-54 ui for homepage ([4b35cd8](https://github.com/resola-ai/deca-apps/commit/4b35cd80acdad08bd619fea618e6ce1ea85b5e2d))

- **tables:** TABLES-69 fix build failed ([2e135ad](https://github.com/resola-ai/deca-apps/commit/2e135ada6d5705502d35299e9fb0e0e8233e85e4))

- **tables:** TABLES-69 fix code review ([d02dd6c](https://github.com/resola-ai/deca-apps/commit/d02dd6cc68269d67ee598e7d23209a1f5b75c1a3))

- **tables:** TABLES-69 integrate list bases API ([8c24387](https://github.com/resola-ai/deca-apps/commit/8c24387a6a13cdea47eff1f73ebf46659ad16d61))

- **tables:** TABLES-69 remove redundant code ([c126e83](https://github.com/resola-ai/deca-apps/commit/c126e834683c8af8841a84e4393454227fbb5a37))

- **tables:** TABLES-75 fix build failed ([ad35704](https://github.com/resola-ai/deca-apps/commit/ad35704f1373cbb5457587e9ad51a95fb88568df))

- **tables:** TABLES-75 fix build failed ([d5f0133](https://github.com/resola-ai/deca-apps/commit/d5f013333739712080d0b65acff08509a4005060))

- **tables:** TABLES-75 improving integration API ([4859f2f](https://github.com/resola-ai/deca-apps/commit/4859f2f128dcb99d7490480e845c89fba205070f))

- **tables:** TABLES-77 fix build failed ([baef18f](https://github.com/resola-ai/deca-apps/commit/baef18f009fa7ea2c48fede885af9a7a4ed3f117))

- **tables:** TABLES-77 improve code quality ([699d7a9](https://github.com/resola-ai/deca-apps/commit/699d7a9af6220594a2080614a43893cd95eaeec2))

- **tables:** TABLES-77 ui for sidebar table base ([69c67bf](https://github.com/resola-ai/deca-apps/commit/69c67bf70b723f60ea2c889f3c873575ead3b676))

- **tables:** TABLES-77 ui for table sidebar ([375c3eb](https://github.com/resola-ai/deca-apps/commit/375c3ebc58f98be16af0b6ecf1781bd1b4871b9c))

- **tables:** TABLES-78 improve code quality ([d0276e1](https://github.com/resola-ai/deca-apps/commit/d0276e177282ef1acf67352171ecde9cb2834b10))

- **tables:** TABLES-78 integrating api sidebar ([d8307c6](https://github.com/resola-ai/deca-apps/commit/d8307c66ebf786d6ccf23c2cfb0c44c6c18a586d))

- **tables:** TABLES-78 remove log ([9c0709e](https://github.com/resola-ai/deca-apps/commit/9c0709ed5703477f06ad021f6c64f29077a67400))

- **tables:** TABLES-86 support customizing fields for DecaTable ([8d3d6bd](https://github.com/resola-ai/deca-apps/commit/8d3d6bda5f6fcc9161c19b9365c83fd712eee269))

- **tables:** TABLES-86 update DecaTable to fix backward-compatible issue ([1ecbfc3](https://github.com/resola-ai/deca-apps/commit/1ecbfc33fc0805c93cf29714c0e3e48ac1895efc))

- **tables:** TABLES-87 view API integration ([956f253](https://github.com/resola-ai/deca-apps/commit/956f253c044bc682a333673b4d16116343abe439))

- **tables:** TABLES-87 view integration ([a9dbcb6](https://github.com/resola-ai/deca-apps/commit/a9dbcb6c073337e1523110d5ea3f17a1aa34c81c))

- **tables:** TABLES-88 fields API integration ([41dabc3](https://github.com/resola-ai/deca-apps/commit/41dabc3d925826b727c5ae4d3e5338f921124c1a))

- **tables:** TABLES-88 improve code quality ([1949b1e](https://github.com/resola-ai/deca-apps/commit/1949b1e0da7734b586fe63d48a2a2fddf9be2acb))

- **tables:** TABLES-89 improve code quality ([163041d](https://github.com/resola-ai/deca-apps/commit/163041d247c7eb6443c686692b8072d5222ebc64))

- **tables:** TABLES-89 remove log ([6559c56](https://github.com/resola-ai/deca-apps/commit/6559c563a743addc0e20dff70c8c28d9f904ece5))

- **tables:** TABLES-89 update api integration for filter, sorting, text field record ([f9eed3c](https://github.com/resola-ai/deca-apps/commit/f9eed3c6d2d83d6074dd750075761f87e52d36f3))

- **tables:** TABLES-92 fix typing error ([2b59b58](https://github.com/resola-ai/deca-apps/commit/2b59b586a3d5e42f9db342ba43ddf783b02fb9a4))

- **tables:** TABLES-92 improve code quality ([ffb8603](https://github.com/resola-ai/deca-apps/commit/ffb8603cafb2244fb9fd68708d7dfd00c2a44d77))

- **tables:** TABLES-92 improve code quality ([9b3708a](https://github.com/resola-ai/deca-apps/commit/9b3708a73571d38d9fe9d41147c98bbc5d080e70))

- **tables:** TABLES-92 update ui and integration base list ([c7a5e5f](https://github.com/resola-ai/deca-apps/commit/c7a5e5fa6ab0f9bb78a5b5ad60c4e84b6aa723b2))

- **tables:** TABLES-94 improve code quality ([cbba180](https://github.com/resola-ai/deca-apps/commit/cbba180d674a9f7befe30ef9dbc4eea002e62edb))

- **tables:** TABLES-94 refactor update font, logo, spacing ([47e2716](https://github.com/resola-ai/deca-apps/commit/47e271696d55afde96d20b307be07ad4109b7f7c))

- **tables:** TABLES-94 remove log ([9aa1687](https://github.com/resola-ai/deca-apps/commit/9aa16873c08f9e5196786c19902f270533563bfe))

- **tables:** TABLES-95 improve code quality ([74e7bdf](https://github.com/resola-ai/deca-apps/commit/74e7bdf8e549a7a5fd77fb02be9e6077b4ad82ab))

- **tables:** TABLES-95 update pagination ui, integration for search and trash page ([f0971df](https://github.com/resola-ai/deca-apps/commit/f0971dfabb88e1acc68b203818c89befa6b26d67))

- **tables:** TABLES-96 empty state for DecaTable ([91ca7f4](https://github.com/resola-ai/deca-apps/commit/91ca7f4144bc59f56e78e56657c4c26a54683cc4))

- **tables:** TABLES-97 add new hook to get pagination search params ([08010d7](https://github.com/resola-ai/deca-apps/commit/08010d7e29fcc84fff7c981f704c8f58280499df))

- **tables:** TABLES-97 base pagination api integration ([01b1061](https://github.com/resola-ai/deca-apps/commit/01b1061e9f7b9339dd315b51b613d5f08207d5cf))

- **tables:** TABLES-99 add mapper for table field components ([f7523d8](https://github.com/resola-ai/deca-apps/commit/f7523d88f3a2e3c6d28f870954737af784bbc351))

- **tables:** TABLES-99 create scaffold table fields and restructure table fields folder ([47f6770](https://github.com/resola-ai/deca-apps/commit/47f6770bfba336148aa3e4b70a887d6d20dff196))

- **tables:** TALBES-158 fix base page params not updated when search ([fcd129d](https://github.com/resola-ai/deca-apps/commit/fcd129dc4ab7709e847e0d3018a7297e979f9137))

- **tables:** truncate long text in base list ([2ab00e5](https://github.com/resola-ai/deca-apps/commit/2ab00e587b49c9ee3991efb3e431f099fce0ba91))

- **tables:** update crud tables ui and integration api ([fa90304](https://github.com/resola-ai/deca-apps/commit/fa9030427027064730ee4e736ee985db4fb5bfc9))

- **tables:** update integration for createdBy, updatedBy, phone fields ([480d144](https://github.com/resola-ai/deca-apps/commit/480d144526eaf5650b237f5a2e001a9901483a55))

### Bug Fixes

- **tables:** fix build error ([adbfa99](https://github.com/resola-ai/deca-apps/commit/adbfa99929b80b07f7ee8a2707e15bfb0999e557))

- **tables:** fix build failed ([cf54ef0](https://github.com/resola-ai/deca-apps/commit/cf54ef06193e1e1ace2301846110cdcb63234f97))

- **tables:** fix build failed ([2368471](https://github.com/resola-ai/deca-apps/commit/23684719e63b3b50801cb602b02a5a477bbb9e98))
