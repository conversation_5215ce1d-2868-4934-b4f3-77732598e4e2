import { describe, it, expect, vi } from 'vitest';
import { screen, fireEvent, act } from '@testing-library/react';
import { IconPlus } from '@tabler/icons-react';

import { MantineWrapper, mockLibraries, renderWithMantine } from '@/utils/test';
import AICard, { type AICardActions } from './index';
import { MemoryRouter } from 'react-router-dom';
import type { CardStatus } from '@/types';

// Mock useSearchParams
const mockSetSearchParams = vi.fn();
const mockSearchParams = new URLSearchParams('?lang=en');

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [mockSearchParams, mockSetSearchParams],
  };
});

// Mock ActionsMenu component since we're not testing its functionality
vi.mock('../ActionsMenu', () => ({
  default: ({ onTest }: { onTest?: () => void }) => {
    return (
      <button type='button' data-testid='am-target-button' onClick={() => onTest?.()}>
        Actions Menu
      </button>
    );
  },
}));

vi.mock('../AlertStatus', () => ({
  default: ({ message, status }: { message: string; status: string }) => (
    <div data-testid='status-alert-target-button'>
      {message} - {status}
    </div>
  ),
}));

mockLibraries();

describe('AICard', () => {
  const defaultProps = {
    title: 'Test Card',
    actions: {
      items: [
        { label: 'Edit', onClick: () => {} },
        { label: 'Delete', onClick: () => {} },
      ],
    } as AICardActions,
  };

  it('renders with basic props', () => {
    renderWithMantine(<AICard {...defaultProps} />);
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByTestId('am-target-button')).toBeInTheDocument();
  });

  it('renders with description', () => {
    const description = 'Test description';
    renderWithMantine(<AICard {...defaultProps} description={description} />);
    expect(screen.getByText(description)).toBeInTheDocument();
  });

  it('renders with custom icon', () => {
    const icon = <IconPlus data-testid='aic-custom-icon' />;
    renderWithMantine(<AICard {...defaultProps} icon={icon} />);
    expect(screen.getByTestId('aic-custom-icon')).toBeInTheDocument();
    // Should still render title when icon is present
    expect(screen.getByText('Test Card')).toBeInTheDocument();
  });

  it('renders with AI model information', () => {
    const aiModel = 'GPT-4';
    renderWithMantine(<AICard {...defaultProps} aiModel={aiModel} />);
    expect(screen.getByText(aiModel)).toBeInTheDocument();
    expect(screen.getByTestId('aic-model-icon')).toBeInTheDocument();
  });

  it('applies correct styles', () => {
    renderWithMantine(<AICard {...defaultProps} />);
    const card = screen.getByTestId('aic-card');
    expect(card).toHaveClass('mantine-Card-root');
  });

  it('renders full-width layout correctly', () => {
    renderWithMantine(<AICard {...defaultProps} aiModel='GPT-4' isFullWidth />);

    const card = screen.getByTestId('aic-card-full-width');
    expect(card).toBeInTheDocument();
    expect(card).toHaveStyle({
      'border-radius': 'calc(0.625rem * var(--mantine-scale))',
      'border-color': '#e3e3e8',
    });

    const infoGroup = screen.getByTestId('aic-info-group');
    expect(infoGroup).toBeInTheDocument();
    expect(infoGroup).toHaveStyle('flex-grow: 4');

    const modelGroup = screen.getByTestId('aic-model-updated-group');
    expect(modelGroup).toBeInTheDocument();
    expect(modelGroup).toHaveStyle('flex-grow: 2');
  });

  it('renders with correct locale based on URL parameter', () => {
    // Need to render within a Router context to avoid the useLocation error
    renderWithMantine(
      <MemoryRouter>
        <AICard updatedAt={new Date()} title={''} actions={{}} />
      </MemoryRouter>
    );

    // Reset the mock after test
    vi.resetModules();
  });

  it('handles onClick event', () => {
    const handleClick = vi.fn();
    renderWithMantine(<AICard {...defaultProps} onClick={handleClick} />);

    const card = screen.getByTestId('aic-card');
    fireEvent.click(card);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('renders with modelIcons and switchButton', () => {
    const modelIcons = <div data-testid='test-model-icons'>Model Icons</div>;
    const switchButton = <div data-testid='test-switch-button'>Switch Button</div>;

    renderWithMantine(
      <AICard {...defaultProps} modelIcons={modelIcons} switchButton={switchButton} />
    );

    expect(screen.getByTestId('aic-model-icons-switch')).toBeInTheDocument();
    expect(screen.getByTestId('aic-model-icons')).toBeInTheDocument();
    expect(screen.getByTestId('aic-switch-button')).toBeInTheDocument();
    expect(screen.getByTestId('test-model-icons')).toBeInTheDocument();
    expect(screen.getByTestId('test-switch-button')).toBeInTheDocument();
  });

  describe('Status display', () => {
    it('should display status when set through actions', async () => {
      const props = {
        ...defaultProps,
        actions: {
          ...defaultProps.actions,
          onTest: (callback?: (status: CardStatus) => void) => {
            callback?.({ message: 'Test status message', status: 'success' as const });
          },
        },
      };
      renderWithMantine(<AICard {...props} />);

      // Verify that onSetStatus is passed to ActionsMenu
      expect(screen.getByTestId('am-target-button')).toBeInTheDocument();

      // Trigger the onTest action through the ActionsMenu
      await act(async () => {
        const actionsMenu = screen.getByTestId('am-target-button');
        fireEvent.click(actionsMenu);
      });

      // Wait for status to be set
      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      // Verify that status alert is displayed
      expect(screen.getByTestId('status-alert-target-button')).toBeInTheDocument();
      expect(screen.getByText('Test status message - success')).toBeInTheDocument();
    });

    it('should not display status alert when no status is set', () => {
      const props = {
        title: 'Test Card',
      };
      renderWithMantine(<AICard {...props} />);

      // Verify that status alert is not present
      expect(screen.queryByTestId('status-alert-target-button')).not.toBeInTheDocument();
    });

    it('should display status in both full-width', async () => {
      // Test compact layout
      const props = {
        ...defaultProps,
        actions: {
          ...defaultProps.actions,
          onTest: (callback?: (status: CardStatus) => void) => {
            callback?.({ message: 'Test status message', status: 'success' as const });
          },
        },
      };
      const { rerender } = renderWithMantine(<AICard {...props} />);

      await act(async () => {
        const actionsMenu = screen.getByTestId('am-target-button');
        fireEvent.click(actionsMenu);
      });

      expect(screen.getByTestId('status-alert-target-button')).toBeInTheDocument();
      expect(screen.getByText('Test status message - success')).toBeInTheDocument();

      // Test full-width layout
      rerender(
        <MantineWrapper>
          <AICard {...props} isFullWidth />
        </MantineWrapper>
      );
      expect(screen.getByTestId('status-alert-target-button')).toBeInTheDocument();
      expect(screen.getByText('Test status message - success')).toBeInTheDocument();
    });
  });
});
