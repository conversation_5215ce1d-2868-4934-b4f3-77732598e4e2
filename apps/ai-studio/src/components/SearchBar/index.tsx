import { forwardRef, memo, useCallback, useEffect } from 'react';
import { Box, Input, rem } from '@mantine/core';
import { IconSearch } from '@tabler/icons-react';
import { createStyles } from '@mantine/emotion';
import { useSearchParams } from 'react-router-dom';

interface SearchBarProps {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
  'data-testid'?: string;
  allowUpdateUrl?: boolean;
}

const useStyles = createStyles(() => ({
  input: {
    fontSize: rem(14),
  },
}));

const SearchBar = forwardRef<HTMLInputElement, SearchBarProps>(
  (
    {
      value,
      onChange,
      className,
      placeholder,
      'data-testid': dataTestId,
      allowUpdateUrl = false,
    }: SearchBarProps,
    ref
  ) => {
    const { classes, cx } = useStyles();
    const [searchParams, setSearchParams] = useSearchParams();

    const handleChange = useCallback(
      (newValue: string) => {
        if (allowUpdateUrl) {
          if (newValue) {
            searchParams.set('search', newValue);
            setSearchParams(searchParams);
          } else {
            searchParams.delete('search');
            setSearchParams(searchParams);
          }
        }

        onChange(newValue);
      },
      [allowUpdateUrl, setSearchParams, searchParams, onChange]
    );

    useEffect(() => {
      if (!allowUpdateUrl) {
        return;
      }

      const search = searchParams.get('search') || '';
      if (search && !value) {
        onChange(search);
      }
    }, []);

    return (
      <Box data-testid='search-bar' w={'100%'}>
        <Input
          data-testid={dataTestId || 'search-input'}
          classNames={{
            input: cx(classes.input, className),
          }}
          placeholder={placeholder}
          leftSection={<IconSearch size={16} />}
          value={value}
          onChange={(e) => handleChange(e.target.value)}
          ref={ref}
        />
      </Box>
    );
  }
);

SearchBar.displayName = 'SearchBar';
export default memo(SearchBar);
