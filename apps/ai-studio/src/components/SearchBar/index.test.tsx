import { describe, it, expect, vi } from 'vitest';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { renderWithMantine } from '@/utils/test';
import SearchBar from './index';
import { MemoryRouter } from 'react-router-dom';
import { AppContextProvider } from '@/contexts/AppContext';

const mockSetSearchParams = vi.fn();

const mockSearchParams = new URLSearchParams('?lang=en');

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [mockSearchParams, mockSetSearchParams],
  };
});

const renderWithRouter = (ui: React.ReactElement) => {
  return renderWithMantine(
    <MemoryRouter>
      <AppContextProvider>{ui}</AppContextProvider>
    </MemoryRouter>
  );
};

describe('SearchBar Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset the URLSearchParams to initial state
    mockSearchParams.delete('search');
    mockSearchParams.set('lang', 'en');
  });

  it('renders correctly with placeholder', () => {
    renderWithRouter(<SearchBar placeholder='Search...' value='' onChange={() => {}} />);

    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
  });

  it('displays the provided value', () => {
    renderWithRouter(<SearchBar placeholder='Search...' value='test query' onChange={() => {}} />);

    const inputElement = screen.getByPlaceholderText('Search...') as HTMLInputElement;
    expect(inputElement.value).toBe('test query');
  });

  it('calls onChange when user types and allowUpdateUrl is true', async () => {
    const handleChange = vi.fn();
    renderWithRouter(
      <SearchBar placeholder='Search...' value='' onChange={handleChange} allowUpdateUrl={true} />
    );

    const inputElement = screen.getByPlaceholderText('Search...');
    await userEvent.type(inputElement, 'a');

    expect(handleChange).toHaveBeenCalledWith('a');
    expect(mockSetSearchParams).toHaveBeenCalledWith(expect.any(URLSearchParams));
  });

  it('does not call onChange when allowUpdateUrl is false', async () => {
    const handleChange = vi.fn();
    renderWithRouter(
      <SearchBar placeholder='Search...' value='' onChange={handleChange} allowUpdateUrl={false} />
    );

    const inputElement = screen.getByPlaceholderText('Search...');
    await userEvent.type(inputElement, 'a');

    expect(handleChange).toHaveBeenCalledWith('a');
    expect(mockSetSearchParams).not.toHaveBeenCalled();
  });

  it('calls onChange when input becomes empty', async () => {
    const handleChange = vi.fn();
    renderWithRouter(
      <SearchBar placeholder='Search...' value='test' onChange={handleChange} allowUpdateUrl />
    );

    const inputElement = screen.getByPlaceholderText('Search...');
    await userEvent.clear(inputElement);

    expect(handleChange).toHaveBeenCalledWith('');
  });

  it('calls onChange when input becomes empty even when allowUpdateUrl is false', async () => {
    const handleChange = vi.fn();
    renderWithRouter(
      <SearchBar
        placeholder='Search...'
        value='test'
        onChange={handleChange}
        allowUpdateUrl={false}
      />
    );

    const inputElement = screen.getByPlaceholderText('Search...');
    await userEvent.clear(inputElement);

    expect(handleChange).toHaveBeenCalledWith('');
    expect(mockSetSearchParams).not.toHaveBeenCalled();
  });

  it('renders with search icon', () => {
    renderWithRouter(<SearchBar placeholder='Search...' value='' onChange={() => {}} />);

    // Check for the search icon (this is a bit implementation-specific)
    const iconElement = document.querySelector('svg');
    expect(iconElement).toBeInTheDocument();
  });

  it('applies custom className when provided', () => {
    renderWithRouter(
      <SearchBar placeholder='Search...' value='' onChange={() => {}} className='custom-class' />
    );

    const inputElement = screen.getByPlaceholderText('Search...');
    expect(inputElement).toHaveClass('custom-class');
  });

  it('updates URL when typing and allowUpdateUrl is true', async () => {
    renderWithRouter(
      <SearchBar placeholder='Search...' value='' onChange={() => {}} allowUpdateUrl />
    );
    const inputElement = screen.getByPlaceholderText('Search...');
    await userEvent.type(inputElement, 'a');
    await userEvent.type(inputElement, 'b');

    expect(mockSetSearchParams).toHaveBeenCalledWith(expect.any(URLSearchParams));
  });

  it('initializes from URL search parameter on mount', () => {
    const handleChange = vi.fn();
    // Set up the search parameter in the URLSearchParams
    mockSearchParams.set('search', 'initial-search');

    renderWithRouter(
      <SearchBar placeholder='Search...' value='' onChange={handleChange} allowUpdateUrl={true} />
    );

    expect(handleChange).toHaveBeenCalledWith('initial-search');
  });

  it('does not initialize from URL if value is already set', () => {
    const handleChange = vi.fn();
    // Set up the search parameter in the URLSearchParams
    mockSearchParams.set('search', 'initial-search');

    renderWithRouter(
      <SearchBar placeholder='Search...' value='existing-value' onChange={handleChange} />
    );

    expect(handleChange).not.toHaveBeenCalled();
  });

  it('does not initialize from URL when allowUpdateUrl is false and value is set', () => {
    const handleChange = vi.fn();
    // Set up the search parameter in the URLSearchParams
    mockSearchParams.set('search', 'initial-search');

    renderWithRouter(
      <SearchBar
        placeholder='Search...'
        value='existing-value'
        onChange={handleChange}
        allowUpdateUrl={false}
      />
    );

    expect(handleChange).not.toHaveBeenCalled();
  });

  it('renders with custom data-testid when provided', () => {
    renderWithRouter(
      <SearchBar placeholder='Search...' value='' onChange={() => {}} data-testid='custom-search' />
    );

    expect(screen.getByTestId('custom-search')).toBeInTheDocument();
  });

  it('renders with default data-testid when not provided', () => {
    renderWithRouter(<SearchBar placeholder='Search...' value='' onChange={() => {}} />);

    expect(screen.getByTestId('search-input')).toBeInTheDocument();
  });
});
