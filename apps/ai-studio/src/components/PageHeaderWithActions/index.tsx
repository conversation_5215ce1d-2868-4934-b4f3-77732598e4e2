import { Box, Button, Flex, Group, type MenuProps, rem } from '@mantine/core';
import PageTitle from '../PageTitle';
import SearchBar from '../SearchBar';
import LayoutControl from '../LayoutControl';
import ButtonMenuActions from '../ButtonMenuActions';

import { createStyles } from '@mantine/emotion';
import type { LayoutType } from '@/types';
import { type ReactNode, useEffect, useState } from 'react';

interface PageHeaderWithActionsProps {
  title: string;
  description: string;
  searchPlaceholder: string;
  searchValue: string;
  onSearchChange: (value: string) => void;
  layoutType: LayoutType;
  onLayoutChange: (value: LayoutType) => void;
  buttonActionLabel: string;
  isUsingButtonMenuActions?: boolean;
  handleButtonActionClick?: () => void;
  handleCreateFromScratch?: () => void;
  handleImportFromFile?: () => void;
  handleCreateFromTemplate?: () => void;
  btnMenuOptionPosition?: MenuProps['position'];
  className?: string;
  controlOnTop?: boolean;
  filterComponent?: ReactNode;
  hasData?: boolean;
}

const useStyles = createStyles((theme) => ({
  button: {
    fontSize: rem(16),
    '&:hover': {
      backgroundColor: theme.colors.decaNavy[4],
    },
  },
}));

const PageHeaderWithActions = ({
  title,
  description,
  searchPlaceholder,
  searchValue,
  onSearchChange,
  layoutType,
  onLayoutChange,
  buttonActionLabel,
  isUsingButtonMenuActions = false,
  handleButtonActionClick,
  handleCreateFromScratch,
  handleImportFromFile,
  handleCreateFromTemplate,
  className,
  controlOnTop = false,
  btnMenuOptionPosition,
  filterComponent,
  hasData = true,
}: PageHeaderWithActionsProps) => {
  const { classes } = useStyles();
  const [showControls, setShowControls] = useState(hasData);
  const [prevHasData, setPrevHasData] = useState(hasData);

  useEffect(() => {
    setPrevHasData(hasData);
  }, [hasData]);

  // Check to hide/show controls when server response is ready
  useEffect(() => {
    if (searchValue.length > 0) {
      setShowControls(true);
    } else {
      if (prevHasData !== hasData) {
        setShowControls(hasData);
      }
    }
  }, [searchValue, hasData, prevHasData]);

  return (
    <Box className={className}>
      {controlOnTop ? (
        <Group justify='space-between' mb='md'>
          <PageTitle title={title} description={description} />
          <LayoutControl value={layoutType} onChange={onLayoutChange} />
        </Group>
      ) : (
        <PageTitle title={title} description={description} />
      )}
      <Flex justify='space-between' align='center' w={'100%'}>
        <Flex align='center' gap='md' miw={'300px'} w={'30%'}>
          {showControls && (
            <>
              <SearchBar
                value={searchValue}
                placeholder={searchPlaceholder}
                onChange={onSearchChange}
                allowUpdateUrl
              />
              {filterComponent}
              {controlOnTop ? null : <LayoutControl value={layoutType} onChange={onLayoutChange} />}
            </>
          )}
        </Flex>
        <Flex align='center'>
          {isUsingButtonMenuActions ? (
            <ButtonMenuActions
              buttonLabel={buttonActionLabel}
              menuPosition={btnMenuOptionPosition}
              handleCreateFromScratch={handleCreateFromScratch}
              handleImportFromFile={handleImportFromFile}
              handleCreateFromTemplate={handleCreateFromTemplate}
            />
          ) : (
            <Button className={classes.button} onClick={handleButtonActionClick}>
              {buttonActionLabel}
            </Button>
          )}
        </Flex>
      </Flex>
    </Box>
  );
};

export default PageHeaderWithActions;
