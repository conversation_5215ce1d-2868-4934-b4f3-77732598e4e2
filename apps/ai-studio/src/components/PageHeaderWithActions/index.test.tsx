import { screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import PageHeaderWithActions from './index';
import { renderWithMantine } from '@/utils/test';
import { LayoutType } from '@/types';

describe('PageHeaderWithActions', () => {
  const defaultProps = {
    title: 'Test Title',
    description: 'Test Description',
    searchPlaceholder: 'Search...',
    searchValue: '',
    onSearchChange: vi.fn(),
    layoutType: LayoutType.GRID,
    onLayoutChange: vi.fn(),
    buttonActionLabel: 'Create New',
    isUsingButtonMenuActions: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderWithRouter = (component: React.ReactElement) => {
    return renderWithMantine(<MemoryRouter>{component}</MemoryRouter>);
  };

  it('renders correctly with default props', () => {
    renderWithRouter(<PageHeaderWithActions {...defaultProps} />);

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    expect(screen.getByTestId('layout-control')).toBeInTheDocument();
    expect(screen.getByText('Create New')).toBeInTheDocument();
  });

  it('renders button menu actions when isUsingButtonMenuActions is true', () => {
    renderWithRouter(<PageHeaderWithActions {...defaultProps} isUsingButtonMenuActions={true} />);

    // ButtonMenuActions should be rendered instead of a regular button
    expect(screen.queryByRole('button', { name: 'Create New' })).toBeInTheDocument();
  });

  it('calls onSearchChange when search input changes', () => {
    renderWithRouter(<PageHeaderWithActions {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchInput, { target: { value: 'test search' } });

    expect(defaultProps.onSearchChange).toHaveBeenCalledWith('test search');
  });
  it('calls onLayoutChange when layout is changed', () => {
    renderWithRouter(<PageHeaderWithActions {...defaultProps} />);

    const layoutControl = screen.getByTestId('layout-control');
    const listOption = layoutControl.querySelector('[value="list"]');

    if (listOption) {
      fireEvent.click(listOption);
      expect(defaultProps.onLayoutChange).toHaveBeenCalledWith('list');
    } else {
      throw new Error('List option not found');
    }
  });
});
