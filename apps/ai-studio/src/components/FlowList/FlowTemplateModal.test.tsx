import { SAMPLE_FLOW_TEMPLATE_LIST } from '@/mockdata/flow';
import type { Flow } from '@/models/flow';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import { MantineProvider } from '@mantine/core';
import '@testing-library/jest-dom';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import FlowTemplateModal, { NoTemplateFound } from './FlowTemplateModal';
import { MemoryRouter } from 'react-router-dom';

mockLibraries();

// Mock the useTranslate hook
vi.mock('@tolgee/react', () => ({
  ...vi.importActual('@tolgee/react'),
  useTranslate: () => ({
    t: (key: string) => key, // Simple mock translation function
  }),
}));

// Mock useSearchParams
const mockSetSearchParams = vi.fn();
const mockSearchParams = new URLSearchParams('?lang=en');

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [mockSearchParams, mockSetSearchParams],
  };
});

describe('FlowTemplateModal', () => {
  const user = userEvent.setup();
  const mockOnClose = vi.fn();
  const mockOnSelectTemplate = vi.fn();

  const renderComponent = (opened = true) => {
    return renderWithMantine(
      <MemoryRouter>
        <FlowTemplateModal
          opened={opened}
          onClose={mockOnClose}
          onSelectTemplate={mockOnSelectTemplate}
        />
      </MemoryRouter>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the modal with the correct title', () => {
    renderComponent();
    expect(screen.getByText('templateModelTitle')).toBeInTheDocument();
  });

  it('renders the search bar', () => {
    renderComponent();
    expect(screen.getByPlaceholderText('searchTemplate')).toBeInTheDocument();
  });

  it('renders the NoTemplateFound component when there are no matching templates', async () => {
    renderComponent(true);
    const searchInput = screen.getByTestId('flow-template-search-input');
    await user.type(searchInput, 'non-existent-template');
    await waitFor(() => {
      expect(screen.getByTestId('no-template-found-placeholder')).toBeInTheDocument();
      expect(screen.getByText('noTemplateFound')).toBeInTheDocument();
    });
  });

  it('filters templates based on search value', async () => {
    renderComponent();
    const searchInput = screen.getByTestId('flow-template-search-input');
    await user.type(searchInput, SAMPLE_FLOW_TEMPLATE_LIST[0].name);
    await waitFor(() => {
      const list = screen.getAllByTestId('flow-template-card');
      expect(list.length).toBe(1);
    });
  });

  it('calls onClose when the modal is closed', async () => {
    renderComponent();
    const modal = screen.getByTestId('flow-template-modal');
    const closeButton = modal.getElementsByTagName('button')[0];
    await user.click(closeButton);
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls onSelectTemplate when a template is selected', async () => {
    renderComponent();
    const firstTemplate = screen.getAllByTestId('flow-template-card')[0];
    await user.click(firstTemplate);
    expect(mockOnSelectTemplate).toHaveBeenCalled();
  });

  it('renders all templates when search value is empty', async () => {
    renderComponent();
    const searchInput = screen.getByTestId('flow-template-search-input');
    await user.type(searchInput, 'test');
    await user.clear(searchInput);
    await waitFor(() => {
      const list = screen.getAllByTestId('flow-template-card');
      expect(list.length).toBe(SAMPLE_FLOW_TEMPLATE_LIST.length);
    });
  });

  it('calls onSelectTemplate with the correct template data', async () => {
    renderComponent();
    const firstTemplate = screen.getAllByTestId('flow-template-card')[0];
    const expectedTemplate: Flow = SAMPLE_FLOW_TEMPLATE_LIST[0];
    await user.click(firstTemplate);
    expect(mockOnSelectTemplate).toHaveBeenCalledWith(expectedTemplate);
  });
});

describe('NoTemplateFound', () => {
  it('renders the NoTemplateFound component with the correct text', () => {
    renderWithMantine(
      <MantineProvider>
        <NoTemplateFound />
      </MantineProvider>
    );
    expect(screen.getByTestId('no-template-found-placeholder')).toBeInTheDocument();
    expect(screen.getByText('noTemplateFound')).toBeInTheDocument();
  });
});
