import { ActionIcon, Box, Flex, rem, Text } from '@mantine/core';
import { IconPencil } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useRef, useState } from 'react';

import { showSuccessNotification } from '@/utils/notification';

interface NodeNameProps {
  orderedNumber: number;
  displayName: string;
  nodeId?: string;
  onSave: (displayName: string) => void;
}

function NodeName({
  orderedNumber,
  displayName: initialDisplayName,
  nodeId,
  onSave,
}: NodeNameProps) {
  const { t } = useTranslate('flow');
  const [isEditing, setIsEditing] = useState(false);
  const [displayName, setDisplayName] = useState(initialDisplayName);
  const ref = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setDisplayName(initialDisplayName);
  }, [initialDisplayName]);

  const handleEdit = useCallback(() => {
    setIsEditing(true);
    setTimeout(() => {
      ref.current?.select();
    }, 100);
  }, []);

  const handleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setDisplayName(event.target.value);
    },
    [setDisplayName]
  );

  const handleSave = useCallback(() => {
    setIsEditing(false);
    onSave(displayName);
  }, [displayName, onSave]);

  const handleCopyNodeId = useCallback(async () => {
    if (nodeId) {
      await navigator.clipboard.writeText(nodeId);
      const message = t('nodeIdCopied');
      if (!message.includes('[hide]')) {
        showSuccessNotification(message);
      }
    }
  }, [nodeId, t]);

  return (
    <Box flex={1}>
      <Flex gap={4} align='center'>
        <Text fw={500} size='lg'>
          {orderedNumber ? `${orderedNumber}. ` : ''}
        </Text>
        {isEditing ? (
          <input
            ref={ref}
            value={displayName}
            onChange={handleChange}
            onBlur={handleSave}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSave();
              }
              if (e.key === 'Escape') {
                setDisplayName(initialDisplayName);
                setIsEditing(false);
              }
            }}
            style={{
              fontSize: rem(16),
              fontWeight: 500,
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              width: '100%',
              padding: 0,
              margin: 0,
              color: 'inherit',
            }}
          />
        ) : (
          <Text fw={500} size='lg' onClick={handleEdit} sx={{ cursor: 'pointer' }}>
            {displayName}
          </Text>
        )}

        {!isEditing && (
          <ActionIcon variant='subtle' color='gray' size='sm' ml={4} onClick={handleEdit}>
            <IconPencil size={16} />
          </ActionIcon>
        )}
      </Flex>

      {nodeId && (
        <Text
          size='xs'
          c='dimmed'
          mt={2}
          onClick={handleCopyNodeId}
          sx={{ cursor: 'pointer', '&:hover': { opacity: 0.7 } }}
          title={t('copyNodeIdTooltip')}
        >
          {nodeId}
        </Text>
      )}
    </Box>
  );
}

export default NodeName;
