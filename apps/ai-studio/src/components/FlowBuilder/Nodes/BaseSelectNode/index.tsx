import { ActionIcon, Box, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconArrowsSplit, IconBolt, IconInfinity } from '@tabler/icons-react';
import { Handle, Position } from '@xyflow/react';
import { type ReactNode, useMemo } from 'react';
import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { COMMON_HEIGHT_NODE, COMMON_WIDTH_NODE } from '../../constants';
import type { FlowNodeType } from '@/models/flow';

const useStyles = createStyles(theme => ({
  gradientBackground: {
    outline: 'none',
    position: 'relative',
    borderRadius: '10px',
    backgroundColor: 'transparent',
    border: '3px solid transparent',
    backgroundClip: 'padding-box, border-box',
    backgroundOrigin: 'padding-box, border-box',
    backgroundImage: 'linear-gradient(white, white),linear-gradient(to right, #6fa7f1, #a16ee1)',
    boxShadow: '0 0 4px 2px rgba(111, 167, 241, 0.65)',
    '&:hover': {
      outline: 'none',
      borderColor: theme.colors.decaBlue[5],
    },
    transition: 'border-color 0.2s ease, background-color 0.2s ease, background-image 0.2s ease',
  },
  nodeContainer: {
    width: rem(COMMON_WIDTH_NODE),
    maxHeight: rem(COMMON_HEIGHT_NODE),
    overflow: 'hidden',
    cursor: 'pointer',
    position: 'relative',
    borderRadius: rem(8),
    backgroundColor: theme.white,
    padding: `${rem(8)} ${rem(12)}`,
    transition: 'outline 0.2s ease',
    outline: `1px solid ${theme.colors.gray[2]}`,
    outlineOffset: '-1px',
    border: '3px solid transparent',
    '&:hover': {
      outline: `1px solid ${theme.colors.decaBlue[5]}`,
    },
  },
  handle: {
    width: rem(5),
    height: rem(5),
    background: 'transparent',
    borderRadius: '50%',
    border: 'none',
  },
  icon: {
    color: theme.colors.decaBlue[5],
    fontSize: rem(24),
  },
  title: {
    fontSize: rem(12),
    fontWeight: 500,
    color: theme.colors.decaBlue[5],
  },
  description: {
    fontSize: rem(12),
    lineHeight: rem(16),
    fontWeight: 500,
    color: theme.colors.decaGrey[6],
  },
  customIcon: {
    svg: {
      color: theme.colors.decaBlue[5],
    },
  },
}));

interface BaseSelectNodeProps {
  id: string;
  title: ReactNode;
  type?: FlowNodeType;
  description: string;
  dataTestId?: string;
  menuContent?: ReactNode;
  hideIcon?: boolean;
  icon?: 'bolt' | 'arrow-split' | 'looping';
  baseStyle?: string;
  openModalAction?: () => void;
  customIcon?: ReactNode;
}

export default function BaseSelectNode({
  id,
  title,
  baseStyle,
  description,
  dataTestId,
  menuContent,
  customIcon,
  icon = 'bolt',
  hideIcon = false,
  openModalAction,
}: BaseSelectNodeProps) {
  const { classes, cx } = useStyles();
  const { currentSelectNodeId, isDebugging } = useFlowBuilderContext();

  const Icon = useMemo(() => {
    const icons = {
      bolt: IconBolt,
      looping: IconInfinity,
      'arrow-split': IconArrowsSplit,
    };
    return icons?.[icon] || IconBolt;
  }, [icon]);

  const isSelected = useMemo(() => {
    return currentSelectNodeId === id;
  }, [currentSelectNodeId, id]);

  const nodeDescription = useMemo(() => {
    const [prefix, ...rest] = description.split(' ');
    return {
      prefix,
      text: rest.join(' '),
    };
  }, [description]);

  const renderedIcon = useMemo(() => {
    if (hideIcon) return null;
    return customIcon ? (
      <ActionIcon
        fz='sm'
        size={rem(20)}
        variant='transparent'
        aria-label='ActionIcon the same size as inputs'
        className={classes.customIcon}>
        {customIcon}
      </ActionIcon>
    ) : (
      <Icon size={20} className={classes.icon} data-testid={`icon-${icon}`} />
    );
  }, [hideIcon, customIcon, classes.customIcon, classes.icon, icon]);

  return (
    <Box
      className={cx(
        'nodrag',
        classes.nodeContainer,
        { [classes.gradientBackground]: isSelected },
        baseStyle
      )}>
      <Handle
        id='handle-top-target-top'
        type='target'
        position={Position.Top}
        className={classes.handle}
        data-testid='handle-top-target-top'
      />
      <Handle
        id='handle-bottom-source-bottom'
        type='source'
        position={Position.Bottom}
        className={classes.handle}
        data-testid='handle-bottom-source-bottom'
      />

      <Box onClick={openModalAction}>
        <Flex align='center' justify='space-between'>
          <Flex align='center' gap={rem(4)}>
            {renderedIcon}
            <Text className={classes.title} data-testid={dataTestId}>
              {title}
            </Text>
          </Flex>
          {menuContent}
        </Flex>
        {isDebugging ? <p>{id}</p> : ''}

        <Flex mt={rem(8)} align='center'>
          <Text className={classes.description}>
            <Text component='span' fz='inherit' c='decaGrey.9' mr={rem(4)} fw={500}>
              {nodeDescription.prefix}
            </Text>
            {nodeDescription.text}
          </Text>
        </Flex>
      </Box>
    </Box>
  );
}
