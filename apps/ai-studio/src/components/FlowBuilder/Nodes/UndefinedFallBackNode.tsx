import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { FlowTypeNode } from '@/models/flow';
import { Group, Text } from '@mantine/core';
import { IconAlertCircleFilled } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useMemo } from 'react';
import MenuActionNode from '../MenuActionNode';
import BaseSelectNode from './BaseSelectNode';
import { createStyles } from '@mantine/emotion';

interface UndefinedFallbackNodeProps {
  id: string;
  data?: {
    onEdit?: (id: string) => void;
    nextNodeId?: string;
    parentNodeId?: string;
    orderedNumber?: number;
    actualParentNodeId?: string;
  };
}

const useStyles = createStyles(theme => ({
  errorNode: {
    outline: `1px solid ${theme.colors.red[3]}`,
    ':hover': {
      outline: `2px solid ${theme.colors.red[4]}`,
    },
  },
}));

export default function UndefinedFallBackNode({ id, data }: UndefinedFallbackNodeProps) {
  const { t } = useTranslate('flow');
  const { classes, theme } = useStyles();
  const { flowActionHandlers, handleOpenCatalogForReplaceEmptyNode } = useFlowBuilderContext();
  const onEdit = useCallback(() => {
    handleOpenCatalogForReplaceEmptyNode({
      nodeId: id,
    });
  }, [id, handleOpenCatalogForReplaceEmptyNode]);

  const onDelete = useCallback(() => {
    flowActionHandlers.handleRemoveNode({
      nodeId: data?.nextNodeId ?? '',
      typeRemove: FlowTypeNode.Node,
      parentId: data?.actualParentNodeId ?? '',
    });
  }, [flowActionHandlers, id, data]);

  const orderedNumber = data?.orderedNumber ?? 0;

  const title = useMemo(() => {
    return (
      <Group justify='left' align='flex-start' gap={4}>
        <Text fz={12} fw={500} c={'gray.8'}>
          {t('undefinedNodeLabel')}
        </Text>
        <IconAlertCircleFilled size={18} color={theme.colors.red[7]} />
      </Group>
    );
  }, [t]);

  return (
    <BaseSelectNode
      id={id}
      title={title}
      hideIcon={true}
      openModalAction={onEdit}
      dataTestId='error-node-id'
      baseStyle={classes.errorNode}
      menuContent={<MenuActionNode onDelete={onDelete} />}
      description={`${orderedNumber}. ${t('undefinedNodeDescription')}`}
    />
  );
}
