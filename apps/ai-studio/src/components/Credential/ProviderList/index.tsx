import AICard from '@/components/AICard';
import GridLayout from '@/components/GridLayout';
import { useCredentialContext } from '@/contexts/CredentialContext';
import type { ITool } from '@/models';
import { SearchBar } from '@/components';
import { useEffect, useRef, useState } from 'react';
import { Box, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import CategoryIcon from '@resola-ai/ui/components/Catalog/components/CatalogNodeIcon';
interface ProviderListProps {
  onSelect: (tool: ITool) => void;
  isOpen?: boolean;
}

const useStyles = createStyles((theme) => ({
  categoryContainer: {
    width: '180px',
    height: '100%',
    padding: '10px',
    backgroundColor: theme.colors.decaLight[0],
    marginBottom: rem(16),
  },
  categoryTitle: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.decaGray[4],
    marginBottom: rem(5),
  },
  category: {
    cursor: 'pointer',
    padding: '10px 5px',
    borderRadius: '5px',
    overflow: 'hidden',
    fontSize: theme.fontSizes.md,
    color: theme.colors.decaGray[9],
    '&:hover, &.active': {
      backgroundColor: theme.colors.decaLight[1],
      color: theme.colors.decaNavy[5],
    },
  },
  toolsContainer: {
    flex: 1,
  },
  searchBar: {
    marginBottom: rem(16),
    width: rem(400),
  },
}));

export const ProviderList = ({ onSelect, isOpen }: ProviderListProps) => {
  const { tools } = useCredentialContext();
  const [searchValue, setSearchValue] = useState('');
  const [selectedCategory] = useState('all');
  const [filteredTools, setFilteredTools] = useState<any[]>(tools);
  const { classes } = useStyles();
  const { t } = useTranslate('credential');
  const searchBarRef = useRef<HTMLInputElement>(null);
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  // const handleCategoryChange = (category: string) => {
  //   setSelectedCategory(category);
  // };

  useEffect(() => {
    const filteredTools = tools.filter(
      (tool) =>
        tool.displayName.toLowerCase().includes(searchValue.toLowerCase()) &&
        (selectedCategory === 'all' || (tool.categories || []).includes(selectedCategory))
    );
    setFilteredTools(filteredTools);
  }, [searchValue, selectedCategory, tools]);

  useEffect(() => {
    if (isOpen && searchBarRef.current) {
      setTimeout(() => {
        searchBarRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  return (
    <Box>
      <SearchBar
        placeholder={t('tool.list.search')}
        value={searchValue}
        onChange={handleSearchChange}
        className={classes.searchBar}
        ref={searchBarRef}
      />
      <Flex gap={rem(16)}>
        {/* <Box className={classes.categoryContainer}>
          <Text className={classes.categoryTitle}>{t('tool.list.categories')}</Text>
          <Stack gap={rem(3)}>
            {mockCategories.map(category => (
              <Box
                key={category.id}
                onClick={() => handleCategoryChange(category.id)}
                className={`${classes.category} ${
                  selectedCategory === category.id ? 'active' : ''
                }`}
              >
                <Text lineClamp={1}>{category.name}</Text>
              </Box>
            ))}
          </Stack>
        </Box> */}
        <Box className={classes.toolsContainer}>
          <GridLayout isFullWidth={false} data-testid='provider-list'>
            {filteredTools.map((tool) => (
              <AICard
                key={tool.id}
                title={tool.displayName}
                description={tool.description}
                icon={<CategoryIcon name={tool.name} />}
                onClick={() => onSelect(tool)}
              />
            ))}
          </GridLayout>
        </Box>
      </Flex>
    </Box>
  );
};
