import { screen, waitFor } from '@testing-library/react';
import { ProvidersModal } from './index';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import { vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import userEvent from '@testing-library/user-event';
import { AppContextProvider } from '@/contexts/AppContext';
import { mockTools } from '@/mockdata/credential';

mockLibraries();

// Mock useSearchParams
const mockSetSearchParams = vi.fn();
const mockSearchParams = new URLSearchParams('?lang=en');

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [mockSearchParams, mockSetSearchParams],
  };
});

const mockOnSelect = vi.fn();
const mockOnClose = vi.fn();

// Mock CredentialContext
vi.mock('@/contexts/CredentialContext', () => ({
  useCredentialContext: () => ({
    tools: mockTools,
  }),
  CredentialContextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

const renderWithProviders = (ui: React.ReactElement) => {
  return renderWithMantine(
    <MemoryRouter>
      <AppContextProvider>{ui}</AppContextProvider>
    </MemoryRouter>
  );
};

describe('ProvidersModal', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders modal when opened is true', () => {
    renderWithProviders(
      <ProvidersModal opened={true} onClose={mockOnClose} onSelect={mockOnSelect} />
    );

    expect(screen.getByText('tool.modal.title')).toBeInTheDocument();
    expect(screen.getByText('Slack')).toBeInTheDocument();
    expect(screen.getByText('Google')).toBeInTheDocument();
  });

  it('does not render when opened is false', () => {
    renderWithProviders(
      <ProvidersModal opened={false} onClose={mockOnClose} onSelect={mockOnSelect} />
    );

    expect(screen.queryByText('tool.modal.title')).not.toBeInTheDocument();
    expect(screen.queryByText('Slack')).not.toBeInTheDocument();
    expect(screen.queryByText('Google Calendar')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', async () => {
    renderWithProviders(
      <ProvidersModal opened={true} onClose={mockOnClose} onSelect={mockOnSelect} />
    );

    const user = userEvent.setup();
    const closeButton = await screen.findByTestId('modal-close-button');

    await user.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls onSelect when a tool is selected', async () => {
    renderWithProviders(
      <ProvidersModal opened={true} onClose={mockOnClose} onSelect={mockOnSelect} />
    );

    const user = userEvent.setup();
    const slackCard = screen.getByText('Slack').closest('div[data-testid="aic-card"]');

    if (slackCard) {
      await user.click(slackCard);
    }

    await waitFor(() => {
      expect(mockOnSelect).toHaveBeenCalled();
    });
  });

  it('filters tools by search', async () => {
    renderWithProviders(
      <ProvidersModal opened={true} onClose={mockOnClose} onSelect={mockOnSelect} />
    );

    const searchInput = screen.getByPlaceholderText('tool.list.search');
    const user = userEvent.setup();

    await user.type(searchInput, 'Slack');

    expect(screen.getByText('Slack')).toBeInTheDocument();
    expect(screen.queryByText('Google Calendar')).not.toBeInTheDocument();
  });

  it.skip('filters tools by category', async () => {
    renderWithProviders(
      <ProvidersModal opened={true} onClose={mockOnClose} onSelect={mockOnSelect} />
    );

    const user = userEvent.setup();
    const calendarCategory = screen.getByText('Calendar');

    await user.click(calendarCategory);

    await waitFor(() => {
      expect(screen.queryByText('OpenAI')).toBeInTheDocument();
      expect(screen.getByText('Google')).toBeInTheDocument();
      expect(screen.queryByText('Slack')).not.toBeInTheDocument();
    });
  });
});
