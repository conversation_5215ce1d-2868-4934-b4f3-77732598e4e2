{"actions": {"create": "Create an Agent", "delete": "Delete Agent", "edit": "Edit"}, "builder": {"message": {"placeholder": "Write your message to run your agent"}}, "description": "Executes tasks, leverages AI functions and tools, and automates decision-making to enhance your productivity by Agents.", "generateModal": {"apply": "Apply", "cancel": "Cancel", "description": "The Prompt Generator employs the selected model to enhance prompts, improving their quality and organization. Please provide clear and comprehensive instructions.", "example": "Example", "examples": {"generate": "Please create instructions prompt for {exampleText} purpose", "meetingTakeaways": "Meeting takeaways", "outputGoal": "Output Goal Setter", "professionalAnalyst": "Professional analyst", "responseCustomization": "Response Customization Kit", "translator": "Translater", "travelPlanning": "Travel planning", "webSearch": "Web Search Template"}, "generate": "Generate", "generatedFailed": "Failed to generate prompt, please try again.", "generatedPrompt": "Generated Prompt", "generating": "Generating...", "instructions": "Instructions", "instructionsPlaceholder": "Please provide clear and detailed instructions.", "title": "Prompt Generator"}, "message": {"codeBlock": {"defaultLang": "CODE", "linesCount": "{count} lines"}}, "modal": {"create": {"title": "Create an Agent"}, "delete": {"content": "Your Agent will be permanently deleted with no chance of recovery. Type <strong>{value}</strong> in the input below to confirm.", "reEnter": {"placeholder": "Enter Agent name", "text": "Re-enter Agent name", "validate": "The name you entered does not match the <PERSON> name."}}, "edit": {"title": "Edit Name & Description", "titleLabel": "Agent Name"}}, "search": {"placeholder": "Search an Agents..."}, "title": "Agents"}