{"createFunctionLabel": "Create a Function", "delete": "Delete", "deleteFunction": "Are you sure you want to delete the function <strong>{name}</strong>?</br>This action cannot be undone.", "deleteModalTitle": "Delete Function", "description": "Execute predefined AI-powered operations that process data, interact with tools, and generate outputs based on your input.", "editFunction": "Edit", "editNameDescriptionLabel": "Edit Name & Description", "functionBuilder": {"aiGenerate": "AI Generate", "aiGeneratePlaceholder": "Ask AI to create a function or modify code", "array": "Array", "boolean": "Boolean", "cancel": "Cancel", "createInputParameter": "Create Input Parameter", "defaultValue": "Default Value", "defaultValuePlaceholder": "Enter a default value", "description": "Description", "descriptionPlaceholder": "Enter a description", "editInputParameter": "Edit Input Parameter", "format": "Format", "functionDuplicatedError": "Couldn't duplicate the Function. Please try again.", "functionDuplicatedSuccessfully": "Function duplicated successfully.", "generate": "Generate", "inputParameterTitle": "Input Parameters", "isRequiredField": "This field is required", "name": "Name", "namePlaceholder": "Enter a name", "nameRequired": "Name is required", "no": "No", "number": "Number", "object": "Object", "output": "Output", "outputExample": "Output Example", "outputExampleError": "The output example is invalid format", "outputExamplePlaceholder": "Add an example for the output", "required": "Required", "run": "Run", "save": "Save", "snippets": "Snippets", "string": "String", "type": "Type", "yes": "Yes"}, "functionName": "Function Name", "searchPlaceholder": "Search a Functions...", "title": "Functions"}