{"createFunctionLabel": "関数を作成", "delete": "削除", "deleteFunction": "関数 <strong>{name}</strong> を削除しますか？<br>この操作は元に戻すことができません。", "deleteModalTitle": "関数を削除", "description": "データを処理し、ツールと対話し、入力に基づいて出力を生成する、事前定義されたAIパワード操作を実行します。", "editFunction": "編集", "editNameDescriptionLabel": "名前と説明の編集", "functionBuilder": {"aiGenerate": "AIで生成 ", "aiGeneratePlaceholder": "関数を作成するか、コードを変更するようにAIに依頼してください", "array": "配列", "boolean": "ブール値", "cancel": "キャンセル", "createInputParameter": "入力パラメータを作成", "defaultValue": "デフォルト値", "defaultValuePlaceholder": "デフォルト値を入力してください", "description": "説明", "descriptionPlaceholder": "パラメータの説明を入力してください", "editInputParameter": "入力パラメータを編集", "format": "フォーマット", "functionDuplicatedError": "関数を複製できませんでした。もう一度お試しください。", "functionDuplicatedSuccessfully": "関数の複製に成功しました。", "generate": "生成する", "inputParameterTitle": "入力パラメータ", "isRequiredField": "このフィールドは必須です", "name": "名前", "namePlaceholder": "パラメータ名を入力してください", "nameRequired": "パラメータ名は必須です", "no": "いいえ", "number": "数値", "object": "オブジェクト", "output": "出力", "outputExample": "出力例", "outputExampleError": "出力例の形式が不正です", "outputExamplePlaceholder": "出力例を追加してください", "required": "必須", "run": "実行", "save": "保存", "snippets": "スニペット", "string": "文字列", "type": "型", "yes": "はい"}, "functionName": "関数名", "searchPlaceholder": "関数を検索 ...", "title": "関数"}