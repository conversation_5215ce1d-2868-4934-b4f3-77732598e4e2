import { ActionIcon, Flex, rem, Text } from '@mantine/core';
import { hideNotification, showNotification } from '@mantine/notifications';
import { IconCircleCheck, IconExclamationCircle, IconX } from '@tabler/icons-react';
import { ReactNode } from 'react';
import { ulid } from 'ulid';

export const showSuccessNotification = (message: ReactNode) => {
  const id = `success-notification${ulid()}`;

  showNotification({
    id,
    message: (
      <Flex gap={rem(10)} align='center'>
        <Text c='decaGreen.9' miw={180}>
          {message}
        </Text>
        <ActionIcon onClick={() => hideNotification(id)} variant='transparent'>
          <IconX size={18} />
        </ActionIcon>
      </Flex>
    ),
    color: 'green',
    withCloseButton: false,
    icon: <IconCircleCheck size={24} />,
    autoClose: 3000,
    styles: (theme) => ({
      root: {
        borderRadius: rem(16),
        padding: `${theme.spacing.xs} ${theme.spacing.sm}`,
        top: rem(100),
        float: 'right',
        backgroundColor: `${theme.colors.decaGreen[0]}`,
        '.mantine-Notification-icon': {
          backgroundColor: 'unset',
        },
        svg: {
          stroke: theme.colors.decaGreen[9],
        },
      },
    }),
  });
};

export const showErrorNotification = (message: ReactNode) => {
  const id = `error-notification${ulid()}`;

  showNotification({
    id,
    message: (
      <Flex gap={rem(10)} align='center'>
        <Text c='red.9' miw={180}>
          {message}
        </Text>
        <ActionIcon onClick={() => hideNotification(id)} variant='transparent'>
          <IconX size={18} />
        </ActionIcon>
      </Flex>
    ),
    color: 'red',
    withCloseButton: false,
    icon: <IconExclamationCircle size={24} />,
    autoClose: 4000,
    styles: (theme) => ({
      root: {
        borderRadius: rem(16),
        padding: `${theme.spacing.xs} ${theme.spacing.sm}`,
        top: rem(100),
        float: 'right',
        backgroundColor: `${theme.colors.red[0]}`,
        '.mantine-Notification-icon': {
          backgroundColor: 'unset',
        },
        svg: {
          stroke: theme.colors.red[9],
        },
      },
    }),
  });
};
