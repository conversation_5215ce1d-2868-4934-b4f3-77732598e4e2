import type { ReactNode } from 'react';
import { describe, it, expect, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { FunctionAPI } from '@/services/api/functions';
import { FunctionContextProvider, useFunctionContext } from './index';
import type { IFunction } from '@/models/function';
import { notifications } from '@mantine/notifications';
import { showErrorNotification } from '@/utils/notification';
import { TemplateAPI } from '@/services/api/template';
import { ITemplateCreatePayload } from '@/models/template';
import { useParams } from 'react-router-dom';
import { DEFAULT_PER_PAGE } from '@/constants/common';

// Mocks
vi.mock('swr', () => ({
  default: () => ({ data: { data: [] }, isLoading: false, mutate: vi.fn() }),
}));

vi.mock('react-router-dom', () => ({
  useParams: vi.fn(() => ({ workspaceId: 'test-workspace', functionId: 'test-function' })),
}));

vi.mock('@/utils/notification', () => ({
  showSuccessNotification: vi.fn(),
  showErrorNotification: vi.fn(),
}));

// Test helpers
const createMockFunction = (overrides: Partial<IFunction> = {}): IFunction => ({
  id: 'test-id',
  name: 'Test Function',
  description: 'Test Description',
  code: 'test code',
  settings: {
    input: [],
    output: { type: 'json_object', properties: { processed: { type: 'string' } } },
  },
  createdAt: '2025-07-23T06:17:18.424Z',
  updatedAt: '2025-07-23T06:17:18.424Z',
  ...overrides,
});

const createMockTemplate = (): ITemplateCreatePayload => ({
  name: 'Test Function',
  description: 'Test Description',
  type: 'function',
  resource: 'function',
  settings: {
    input: [],
    output: { type: 'json_object', properties: { processed: { type: 'string' } } },
  },
});

const wrapper = ({ children }: { children: ReactNode }) => (
  <FunctionContextProvider>{children}</FunctionContextProvider>
);

const renderFunctionContext = () => renderHook(() => useFunctionContext(), { wrapper });

// Helper to test API calls with error handling
const testApiCall = async (
  methodName: keyof typeof FunctionAPI,
  contextMethod: string,
  args: any[],
  expectedApiArgs: any[]
) => {
  const { result } = renderFunctionContext();
  const apiSpy = vi.spyOn(FunctionAPI, methodName).mockResolvedValue(createMockFunction());

  await act(async () => {
    await (result.current as any)[contextMethod](...args);
  });

  expect(apiSpy).toHaveBeenCalledWith(...expectedApiArgs);
  apiSpy.mockRestore();
};

// Helper to test error handling
const testErrorHandling = async (
  methodName: keyof typeof FunctionAPI,
  contextMethod: string,
  args: any[],
  expectedApiArgs: any[]
) => {
  const { result } = renderFunctionContext();
  const mockError = new Error('Test error');
  const apiSpy = vi.spyOn(FunctionAPI, methodName).mockRejectedValue(mockError);
  const notificationsSpy = vi.spyOn(notifications, 'show');

  await act(async () => {
    await (result.current as any)[contextMethod](...args);
  });

  expect(apiSpy).toHaveBeenCalledWith(...expectedApiArgs);
  expect(notificationsSpy).toHaveBeenCalledWith({ message: 'Test error', color: 'red' });

  apiSpy.mockRestore();
  notificationsSpy.mockRestore();
};

// Helper to test early returns
const testEarlyReturn = async (
  methodName: keyof typeof FunctionAPI,
  contextMethod: string,
  args: any[]
) => {
  const { result } = renderFunctionContext();
  const apiSpy = vi.spyOn(FunctionAPI, methodName);

  await act(async () => {
    await (result.current as any)[contextMethod](...args);
  });

  expect(apiSpy).not.toHaveBeenCalled();
  apiSpy.mockRestore();
};

describe('FunctionContext', () => {
  describe('Initial state and SWR key logic', () => {
    it('should provide initial state', () => {
      const { result } = renderFunctionContext();
      expect(result.current).toMatchObject({
        functions: { data: [] },
        isLoadingFunctions: false,
        page: 1,
        searchValue: '',
        limit: DEFAULT_PER_PAGE,
        cursor: '',
      });
    });

    it('should update SWR key when search parameters change', async () => {
      const { result } = renderFunctionContext();

      await act(async () => {
        result.current.updateSearchText('new search');
        result.current.setLimit(20);
        result.current.setCursor('new-cursor');
      });

      expect(result.current.searchValue).toBe('new search');
      expect(result.current.limit).toBe(20);
      expect(result.current.cursor).toBe('new-cursor');
    });
  });

  describe('API operations - Success cases', () => {
    it('should handle function creation', async () => {
      const mockFunction = createMockFunction();
      await testApiCall(
        'create',
        'createFunction',
        [mockFunction],
        ['test-workspace', mockFunction]
      );
    });

    it('should handle function update', async () => {
      const mockFunction = createMockFunction();
      await testApiCall(
        'update',
        'updateFunction',
        [mockFunction],
        ['test-workspace', mockFunction.id, mockFunction]
      );
    });

    it('should handle function deletion', async () => {
      await testApiCall('delete', 'deleteFunction', ['test-id'], ['test-workspace', 'test-id']);
    });

    it('should handle function duplication', async () => {
      await testApiCall(
        'duplicate',
        'duplicateFunction',
        ['test-id'],
        ['test-workspace', 'test-id']
      );
    });

    it('should handle function export', async () => {
      const { result } = renderFunctionContext();
      const mockExportData = {
        name: 'exported function',
        description: 'test description',
        code: 'test code',
        settings: {
          input: [],
          output: { type: 'json_object', properties: { processed: { type: 'string' } } },
        },
      };
      const exportSpy = vi.spyOn(FunctionAPI, 'export').mockResolvedValue(mockExportData);
      const createElementSpy = vi
        .spyOn(document, 'createElement')
        .mockReturnValue(document.createElement('a'));

      await act(async () => {
        await result.current.exportFunction('test-id');
      });

      expect(exportSpy).toHaveBeenCalledWith('test-workspace', 'test-id');
      expect(createElementSpy).toHaveBeenCalledWith('a');
    });

    it('should handle function execution', async () => {
      const { result } = renderFunctionContext();
      const mockInput = { test: 'input' };
      const mockCode = 'test code';
      const mockResult = {
        id: 'test-id',
        output: { result: 'test output' },
        logs: [],
        duration: 100,
        error: '',
      };
      const executeSpy = vi.spyOn(FunctionAPI, 'execute').mockResolvedValue(mockResult);

      await act(async () => {
        await result.current.executeFunction(mockInput, mockCode, 'test-function');
      });

      expect(executeSpy).toHaveBeenCalledWith(
        'test-workspace',
        'test-function',
        mockInput,
        mockCode
      );
      expect(result.current.executeResult).toEqual(mockResult);
      expect(result.current.isExecuting).toBe(false);
    });

    it('should handle save function as template', async () => {
      const { result } = renderFunctionContext();
      const mockTemplate = createMockTemplate();
      const saveAsTemplateSpy = vi
        .spyOn(TemplateAPI, 'create')
        .mockResolvedValue({ id: 'template-id', ...mockTemplate } as any);

      await act(async () => {
        await result.current.saveFunctionAsTemplate(mockTemplate);
      });

      expect(saveAsTemplateSpy).toHaveBeenCalledWith('test-workspace', mockTemplate);
    });
  });

  describe('API operations - Error cases', () => {
    const errorTestCases = [
      {
        api: 'create',
        context: 'createFunction',
        args: [createMockFunction()],
        apiArgs: ['test-workspace', createMockFunction()],
      },
      {
        api: 'update',
        context: 'updateFunction',
        args: [createMockFunction()],
        apiArgs: ['test-workspace', 'test-id', createMockFunction()],
      },
      {
        api: 'delete',
        context: 'deleteFunction',
        args: ['test-id'],
        apiArgs: ['test-workspace', 'test-id'],
      },
      {
        api: 'execute',
        context: 'executeFunction',
        args: [{ test: 'input' }, 'test code', 'test-function'],
        apiArgs: ['test-workspace', 'test-function', { test: 'input' }, 'test code'],
      },
    ];

    errorTestCases.forEach(({ api, context, args, apiArgs }) => {
      it(`should handle error during ${context}`, async () => {
        await testErrorHandling(api as keyof typeof FunctionAPI, context, args, apiArgs);
      });
    });

    it('should handle error during save function as template', async () => {
      const { result } = renderFunctionContext();
      const mockTemplate = createMockTemplate();
      const mockError = new Error('Test error');
      const saveAsTemplateSpy = vi.spyOn(TemplateAPI, 'create').mockRejectedValue(mockError);

      await act(async () => {
        await result.current.saveFunctionAsTemplate(mockTemplate);
      });

      expect(saveAsTemplateSpy).toHaveBeenCalledWith('test-workspace', mockTemplate);
      expect(showErrorNotification).toHaveBeenCalled();
    });
  });

  describe('Early return conditions - Parameter validation', () => {
    const earlyReturnTestCases = [
      { api: 'delete', context: 'deleteFunction', testCases: [[''], [null]] },
      { api: 'duplicate', context: 'duplicateFunction', testCases: [[''], [null]] },
      { api: 'export', context: 'exportFunction', testCases: [[''], [null]] },
      {
        api: 'update',
        context: 'updateFunction',
        testCases: [[createMockFunction({ id: '' })], [createMockFunction({ id: null as any })]],
      },
    ];

    earlyReturnTestCases.forEach(({ api, context, testCases }) => {
      testCases.forEach((args, index) => {
        const condition = index === 0 ? 'empty' : 'null';
        it(`should return early from ${context} when parameter is ${condition}`, async () => {
          await testEarlyReturn(api as keyof typeof FunctionAPI, context, args);
        });
      });
    });

    it('should return early from saveFunctionAsTemplate when template is null/undefined', async () => {
      const { result } = renderFunctionContext();
      const saveAsTemplateSpy = vi.spyOn(TemplateAPI, 'create');

      for (const template of [null, undefined]) {
        await act(async () => {
          await result.current.saveFunctionAsTemplate(template as any);
        });
        expect(saveAsTemplateSpy).not.toHaveBeenCalled();
      }
    });
  });
});

describe('FunctionContext - Missing workspaceId scenarios', () => {
  beforeAll(() => {
    vi.mocked(useParams).mockReturnValue({
      workspaceId: undefined,
      functionId: 'test-function',
    });
  });

  afterAll(() => {
    vi.mocked(useParams).mockReturnValue({
      workspaceId: 'test-workspace',
      functionId: 'test-function',
    });
  });

  describe('Early return when workspaceId is missing', () => {
    const workspaceIdTestCases = [
      {
        api: 'create',
        context: 'createFunction',
        args: [createMockFunction()],
        expectUndefined: true,
      },
      { api: 'duplicate', context: 'duplicateFunction', args: ['test-id'], expectUndefined: false },
      {
        api: 'update',
        context: 'updateFunction',
        args: [createMockFunction()],
        expectUndefined: false,
      },
      { api: 'delete', context: 'deleteFunction', args: ['test-id'], expectUndefined: false },
      { api: 'export', context: 'exportFunction', args: ['test-id'], expectUndefined: false },
      {
        api: 'execute',
        context: 'executeFunction',
        args: [{ test: 'input' }, 'test code', 'test-function'],
        expectUndefined: true,
      },
    ];

    workspaceIdTestCases.forEach(({ api, context, args, expectUndefined }) => {
      it(`should return early from ${context} when workspaceId is missing`, async () => {
        const { result } = renderFunctionContext();
        const apiSpy = vi.spyOn(FunctionAPI, api as keyof typeof FunctionAPI);

        let returnValue: any;
        await act(async () => {
          returnValue = await (result.current as any)[context](...args);
        });

        expect(apiSpy).not.toHaveBeenCalled();
        if (expectUndefined) {
          expect(returnValue).toBeUndefined();
        }
        if (context === 'executeFunction') {
          expect(result.current.isExecuting).toBe(false);
        }
      });
    });

    it('should return early from saveFunctionAsTemplate when workspaceId is missing', async () => {
      const { result } = renderFunctionContext();
      const saveAsTemplateSpy = vi.spyOn(TemplateAPI, 'create');

      await act(async () => {
        await result.current.saveFunctionAsTemplate(createMockTemplate());
      });

      expect(saveAsTemplateSpy).not.toHaveBeenCalled();
    });
  });
});
