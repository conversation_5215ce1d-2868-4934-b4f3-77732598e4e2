import * as matchers from '@testing-library/jest-dom/matchers';
/**setupTest.js */
import { expect, vi } from 'vitest';
import '@testing-library/jest-dom';

const originalError = console.error;
const originalWarn = console.warn;

// Mock ResizeObserver
beforeAll(() => {
  console.warn = (...args) => {
    return;
  };
  console.error = (...args) => {
    return;
  };
});

expect.extend(matchers);

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  }),
});

vi.mock('react-contenteditable', () => ({
  __esModule: true,
  default: ({html, onChange}) => {
    const handleChange = (event) => {
      const mockEvent = {
        currentTarget: { innerHTML: event.target.value },
        target: { value: event.target.value },
      };
      onChange && onChange(mockEvent);
    };
    return (
      <div
        contentEditable={true}
        dangerouslySetInnerHTML={{ __html: html }}
        onChange={handleChange}
      />
    );
  },
}));
// Mock dynamic imports
vi.mock('./src/locales/index.ts', () => ({
  default: {
    common_en: {},
    common_ja: {},
    home_en: {},
    home_ja: {},
    form_en: {},
    form_ja: {},
    share_en: {},
    share_ja: {},
    form_builder_en: {},
    form_builder_ja: {},
    workspace_en: {},
    workspace_ja: {},
    results_en: {},
    results_ja: {},
    form_settings_en: {},
    form_settings_ja: {},
    integrations_en: {},
    integrations_ja: {},
    template_en: {},
    template_ja: {},
  },
}));

// Mock SWR
vi.mock('swr', async (importOriginal) => {
  const origin = await importOriginal();
  return ({
    ...origin,
    default: vi.fn().mockImplementation((key, fetcher) => ({
      data: undefined,
      error: undefined,
      isLoading: false,
      mutate: vi.fn(),
      isValidating: false,
    })),
  })
});

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

window.ResizeObserver = ResizeObserver;
