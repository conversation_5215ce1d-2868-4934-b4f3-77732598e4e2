import { screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import FormActionsDropdown from '../FormActionsDropdown';
import { Form } from '@/types';
import { render } from '@/utils/test-util/testUiComponent';

const mockMutate = vi.fn();

const mockForm: Form = {
  id: '12345',
  name: 'Example Form',
  responses: 50,
  created_by: {
    id: 'creator123',
    nickname: 'john_doe',
    picture: 'https://example.com/picture.jpg',
  },
  status: 'active',
  tags: ['survey', 'feedback'],
  start_at: '2023-11-01T08:00:00Z',
  expired_at: '2023-12-01T08:00:00Z',
  updated_at: '2023-11-05T12:00:00Z',
  integrations: [
    {
      id: 'int-123',
      type: 'email',
    },
    {
      id: 'int-456',
      type: 'google',
    },
  ],
  thumbnail: 'https://example.com/thumbnail.jpg',
  avatar: 'https://example.com/avatar.jpg',
  is_pinned: false,
  is_draft: false,
  is_favorited: false,
  screenshot: {
    original: 'https://example.com/screenshot-original.jpg',
    thumbnail: 'https://example.com/screenshot-thumbnail.jpg',
    preview: null,
  },
};

vi.mock('@/hooks/useFormActions', () => ({
  default: vi.fn().mockReturnValue({
    handleTogglePin: vi.fn(),
    handleTogglePublish: vi.fn(),
    handleFavorite: vi.fn(),
    handleCopy: vi.fn(),
    handleDuplicateForm: vi.fn(),
    handleSaveAsTemplate: vi.fn(),
  }),
}));

describe('FormActionsDropdown', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderComponent = () => render(<FormActionsDropdown form={mockForm} mutate={mockMutate} />);

  it('should render the dropdown trigger button', () => {
    renderComponent();
    const trigger = screen.getByRole('button');
    expect(trigger).toBeDefined();
  });

  it('should open and display menu items on click', async () => {
    renderComponent();
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);
  });

  it('should call handleDuplicateForm when duplicate is clicked', async () => {
    renderComponent();
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);

    const duplicate = await screen.findByText(/duplicate/i);
    fireEvent.click(duplicate);
  });

  it('should call handleTogglePublish when publish is clicked', async () => {
    renderComponent();
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);

    const publish = await screen.findByText(/publish/i);
    fireEvent.click(publish);
  });

  it('should call handleFavorite when favorite is clicked', async () => {
    renderComponent();
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);

    const favorite = await screen.findByText(/^favorite/i);
    fireEvent.click(favorite);
  });

  it('should open DeleteFormModal when delete is clicked', async () => {
    renderComponent();
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);

    const deleteButton = await screen.findByText(/delete/i);
    fireEvent.click(deleteButton);

  });
});
