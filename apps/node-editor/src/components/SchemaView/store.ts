import { create } from 'zustand';
import {
  type ComboboxNode,
  nodes,
  type SchemaFormCallback,
  type SchemaFormInput,
  type SchemaType,
} from '@resola-ai/ui';
import AppSelectorJsonSchema from '../SchemaEditor/schemas/appSelector.json';
import httpJsonSchema from '../SchemaEditor/schemas/http.json';
import setByPath from 'lodash/set';

// Utils
export const nameTransformer = (name: string): string => {
  if (name.toLowerCase() === 'loop') return 'looping';
  return name.toLowerCase().replace(/\s+/g, '-');
};

// Store types
interface SchemaEngineState {
  // App selection state
  appSelectorSchema: SchemaType;
  appSelectorJsonValue: string;
  selectedItem: any;
  // Schema data state
  jsonString: string;
  formValueJsonString: string;
  credentialSettingsObject: SchemaType;
  schema: SchemaType;
  activeStep: number;
  completedStep: number;
  form: SchemaFormInput;
  previousNodes: ComboboxNode[];
  // Actions
  setSelectedItem: (item: any) => void;
  setJsonString: (jsonString: string) => void;
  setFormValueJsonString: (formValueJsonString: string) => void;
  setActiveStep: (activeStep: number) => void;
  setSchema: (schema: SchemaType) => void;
  // Complex actions
  handleSelectedItemChange: (item: any) => void;
  handleFormChange: SchemaFormCallback;
  parseJsonData: () => void;
  parseFormJsonData: () => void;
}

const previousNodes: ComboboxNode[] = [
  {
    label: 'Previous Steps',
    options: [1, 2, 3].map((item) => ({
      value: `previous_step_${item}`,
      label: `Previous Step ${item}`,
      description: `Previous Step ${item}`,
      icon: '🌐',
    })),
  },
  {
    label: 'Variables',
    options: [1, 2, 3].map((item) => ({
      value: `variable_${item}`,
      label: `Variable ${item}`,
      description: `Variable ${item}`,
      icon: '🔍',
    })),
  },
];

export const useSchemaEngineStore = create<SchemaEngineState>((set, get) => ({
  // Initial state
  appSelectorSchema: AppSelectorJsonSchema,
  appSelectorJsonValue: JSON.stringify(AppSelectorJsonSchema, null, 2),
  selectedItem: null,
  jsonString: JSON.stringify(httpJsonSchema, null, 2),
  formValueJsonString: JSON.stringify({}, null, 2),
  schema: {},
  credentialSettingsObject: {},
  activeStep: 0,
  completedStep: -1,
  form: {
    credential: undefined,
    action: undefined,
    trigger: undefined,
    settings: {},
  },
  previousNodes,

  // Simple actions
  setSelectedItem: (item) => set({ selectedItem: item }),
  setSchema: (schema) => set({ schema }),
  setJsonString: (jsonString) => {
    set({ jsonString });
    get().parseJsonData();
  },
  setFormValueJsonString: (formValueJsonString) => {
    set({ formValueJsonString });
  },
  setActiveStep: (activeStep) => set({ activeStep }),

  // Complex actions
  handleSelectedItemChange: (item) => {
    const { appSelectorSchema } = get();

    // Update the schema and selected item
    const newSchema = {
      ...appSelectorSchema,
      properties: {
        ...appSelectorSchema.properties,
        app: {
          ...(appSelectorSchema.properties?.app || {}),
          default: item,
        },
      },
    };

    // Find and set the selected node data
    const newJsonData = nodes.find(
      (node: any) => nameTransformer(node.name) === nameTransformer(item.name)
    );

    const newFormValueJsonData = {
      app: item.id,
    };

    set({
      selectedItem: item,
      appSelectorSchema: newSchema,
      appSelectorJsonValue: JSON.stringify(newSchema, null, 2),
      jsonString: JSON.stringify(newJsonData || {}, null, 2),
      form: newFormValueJsonData,
      formValueJsonString: JSON.stringify(newFormValueJsonData || {}, null, 2),
      activeStep: 0,
      completedStep: -1,
    });

    // Parse the new JSON data
    get().parseJsonData();
  },

  parseJsonData: () => {
    const { jsonString } = get();

    try {
      const parsedJson = JSON.parse(jsonString);

      set({ schema: parsedJson });

      // Process specific schema sections
      if (
        parsedJson.settings ||
        parsedJson.credentials ||
        parsedJson.triggers ||
        parsedJson.actions
      ) {
        const newState: Partial<SchemaEngineState> = {};

        // Handle credentials
        if (parsedJson.settings?.properties) {
          const credentialSettings = parsedJson.settings.properties?.credential || {};
          newState.credentialSettingsObject = credentialSettings;
        }

        set(newState);
      }
    } catch (error) {
      // Silently handle JSON parsing errors
    }
  },

  parseFormJsonData: () => {
    const { formValueJsonString } = get();

    try {
      const parsedFormJson = JSON.parse(formValueJsonString);

      set({ form: parsedFormJson });
    } catch (error) {
      // Silently handle JSON parsing errors
    }
  },

  handleFormChange: (formValues, fieldName) => {
    const { form } = get();

    let updatedPath = '';

    switch (fieldName) {
      case 'action': {
        updatedPath = 'action';
        break;
      }
      case 'trigger': {
        updatedPath = 'trigger';
        break;
      }
      case 'credential': {
        updatedPath = 'settings.credential.id';
        break;
      }
      case 'completedFormStep': {
        set({ completedStep: formValues.completedFormStep });
        break;
      }
      default: {
        updatedPath = `settings.${fieldName}`;
      }
    }

    if (updatedPath !== '') {
      const value = formValues[fieldName];

      const updatedForm = { ...form };

      setByPath(updatedForm, updatedPath, value);

      const updatedFormValueJsonString = JSON.stringify(updatedForm, null, 2);

      set({ form: updatedForm, formValueJsonString: updatedFormValueJsonString });
    }
  },
}));
