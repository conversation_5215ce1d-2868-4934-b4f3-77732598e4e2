/**
 * A Unified Schema Engine Component
 * This component can render any schema into UI, based on the schema type in Schema Editor
 */

import {
  Catalog,
  SchemaEngineWithTolgee,
  type ICredentialPayload,
  type NodeApiCallCallback,
} from '@resola-ai/ui';
import { useMemo } from 'react';
import { Box, Container, Flex, rem, Text } from '@mantine/core';
import { AppSelector, SplitPaneWrapper } from '../SchemaEditor/components';
import { useSchemaEngineStore } from './store';
import { mockCredentials } from '@/constants/credential';

// Catalog View component
const CatalogView: React.FC = () => {
  const { appSelectorSchema, handleSelectedItemChange } = useSchemaEngineStore();
  const catalogSchema = useMemo(() => appSelectorSchema.properties?.catalog, [appSelectorSchema]);

  if (!catalogSchema) return null;

  return (
    <Flex direction='column' id='node-editor-catalog-container'>
      <Flex justify='center' mt={'5%'}>
        <Text size='xl' fw={500} mb={10} style={{ fontStyle: 'italic' }}>
          Please select an item from the catalog
        </Text>
      </Flex>
      <Flex align='start' justify='center' h='100vh' w='100vw' sx={{ '&>div': { height: 'auto' } }}>
        <Catalog schema={catalogSchema} onSelect={handleSelectedItemChange} />
      </Flex>
    </Flex>
  );
};

const SchemaView: React.FC = () => {
  const {
    selectedItem,
    setSelectedItem,
    jsonString,
    setJsonString,
    schema,
    formValueJsonString,
    setFormValueJsonString,
    activeStep,
    setActiveStep,
    form,
    handleFormChange,
    previousNodes,
    completedStep,
  } = useSchemaEngineStore();

  // Initialize store data if needed
  useMemo(() => {
    useSchemaEngineStore.getState().parseJsonData();
  }, []);

  const isSelectedItem = selectedItem !== null;

  const createCredential = async (credential: ICredentialPayload) => {
    console.log('create credential', credential);
    return null;
  };

  const handleClose = () => {
    setSelectedItem(null);
    setActiveStep(0);
  };

  const handleOpenAppCatalog = () => {
    setSelectedItem(null);
  };

  const handleNodeApiCall: NodeApiCallCallback = async () => {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    if (schema.name === 'webhook') {
      return {
        data: {
          url: 'https://api.exampleapp.com/webhooks/orders/3ac3d45e-8c29-4a7f-aed2-59c5fe9d1ea1',
          apiKey: '****************************************',
        },
      };
    }
    return { data: {} };
  };

  return (
    <Box>
      {/* App selector header when an item is selected */}
      <Flex
        px={5}
        py={5}
        style={{
          position: 'sticky',
          zIndex: 100,
          right: 0,
          top: 0,
          display: isSelectedItem ? 'flex' : 'none',
        }}
      >
        <AppSelector selectedApp={selectedItem} onChangeClick={() => setSelectedItem(null)} />
      </Flex>

      {/* Catalog view when no item is selected */}
      {!isSelectedItem && <CatalogView />}

      {/* Schema editor view when an item is selected */}
      {isSelectedItem && (
        <Flex id='node-editor-data-container' direction='column'>
          <SplitPaneWrapper
            jsonValue={jsonString}
            jsonValueChange={setJsonString}
            formValueJsonValue={formValueJsonString}
            formValueJsonValueChange={setFormValueJsonString}
          >
            <Container p='xl' maw={rem(450)} h='full'>
              <SchemaEngineWithTolgee
                schema={schema}
                form={form}
                activeStep={activeStep}
                onStepChange={setActiveStep}
                onOpenAppCatalog={handleOpenAppCatalog}
                onClose={handleClose}
                onFormChange={handleFormChange}
                credentials={mockCredentials}
                isCredentialsLoading={false}
                createCredential={createCredential}
                previousNodes={previousNodes}
                completedStep={completedStep}
                onNodeApiCall={handleNodeApiCall}
              />
            </Container>
          </SplitPaneWrapper>
        </Flex>
      )}
    </Box>
  );
};

export default SchemaView;
