# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [0.15.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.15.0) (2025-07-23)


### Features

* **blocknote-editor:** TK-8275 Upgrade blocknote version to 0.32.0 ([#6114](https://github.com/resola-ai/deca-apps/issues/6114)) ([cf1877a](https://github.com/resola-ai/deca-apps/commit/cf1877a530e38b734d6646b491330aff86597687))
* **blocknote-editor:** TK-9673 Remove eslint prettier from BlockNote editor package ([#6377](https://github.com/resola-ai/deca-apps/issues/6377)) ([35a6aca](https://github.com/resola-ai/deca-apps/commit/35a6acac283be898e1cf80a5a96e45e62485e2c8))
* **kb:** TK-8704 Implement and apply tolgee to KB application ([#6163](https://github.com/resola-ai/deca-apps/issues/6163)) ([804213a](https://github.com/resola-ai/deca-apps/commit/804213a7a4ccf5e63dc75806246b10ecc0eb5aab))
* **kb:** TK-8822 Implement Article publishing status component ([#6154](https://github.com/resola-ai/deca-apps/issues/6154)) ([06ac78b](https://github.com/resola-ai/deca-apps/commit/06ac78b594940c1763047aa842b702084680da92))
* **kb:** TK-8956 Migrate eslint and prettier to biome for kb ([#6205](https://github.com/resola-ai/deca-apps/issues/6205)) ([173b048](https://github.com/resola-ai/deca-apps/commit/173b04882debe5ec95b25e595d5def6afd497559))
* **kb:** TK-9157 Enabled retry job action in the long running or queued jobs ([#6181](https://github.com/resola-ai/deca-apps/issues/6181)) ([a1f1ec4](https://github.com/resola-ai/deca-apps/commit/a1f1ec49b9033284768ab82f8874521a28f0e49a))
* **security:** remove eslint-config-prettier due to security issue ([#6375](https://github.com/resola-ai/deca-apps/issues/6375)) ([283f07d](https://github.com/resola-ai/deca-apps/commit/283f07d261063e91ecab2e96161abc6d36b27ef8))


### Bug Fixes

* **kb, blocknote-editor:** TK-9391, TK-9360 Correct missing translation text and link toolbar from new Blocknote ([#6268](https://github.com/resola-ai/deca-apps/issues/6268)) ([ba421a4](https://github.com/resola-ai/deca-apps/commit/ba421a42039cd0726bc5150690cbeda1c5d8aa56))
* **kb:** TK-8822 Add Pages to analytics views counting and adjust some ui issues ([#6331](https://github.com/resola-ai/deca-apps/issues/6331)) ([ee5e9fb](https://github.com/resola-ai/deca-apps/commit/ee5e9fb3f1ed943cef9bcc9e7469d67159d3abae))
* **kb:** TK-9373 Correct unexpected status updating when moving article ([#6323](https://github.com/resola-ai/deca-apps/issues/6323)) ([5e2a33a](https://github.com/resola-ai/deca-apps/commit/5e2a33a9463143bef4765eddbff9fd232d45b8dd))
* **kb:** TK-9374, TK-9360, TK-9417 Implement publish status in shortcut article and rendering in article list ([#6277](https://github.com/resola-ai/deca-apps/issues/6277)) ([0e5fbba](https://github.com/resola-ai/deca-apps/commit/0e5fbba93f59cf7bf4fa52dbcd78b5ee1b916e76))
* **kb:** TK-9419 add article publish status to search result ([#6293](https://github.com/resola-ai/deca-apps/issues/6293)) ([e72963c](https://github.com/resola-ai/deca-apps/commit/e72963cf0a165fb664638df1fceb4c0aab8abd76))
* **kb:** TK-9546 Correct status cannot updated with article that has related data ([#6341](https://github.com/resola-ai/deca-apps/issues/6341)) ([6c55f24](https://github.com/resola-ai/deca-apps/commit/6c55f249e443c5ea5fbce023ad8bddc7d62ef023))
* **shared:** TK-8315 Ignore eslint unused variable from the reuqired attribues dom warning code ([#6117](https://github.com/resola-ai/deca-apps/issues/6117)) ([d533ed9](https://github.com/resola-ai/deca-apps/commit/d533ed9fa40fc7a5aa97e2f25aefe305f89046d8))
* **ui:** TK-8492 Correct build error from message input ([#6175](https://github.com/resola-ai/deca-apps/issues/6175)) ([36cf3d7](https://github.com/resola-ai/deca-apps/commit/36cf3d79c17bd9c3851c202a4be47039aa945f4f))

## [0.14.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.14.0) (2025-07-01)

### Features

- **blocknote-editor:** TK-7890 Implement unit test for KB content format changing unexpected case ([#5906](https://github.com/resola-ai/deca-apps/issues/5906)) ([7c6ddc9](https://github.com/resola-ai/deca-apps/commit/7c6ddc9016ba71100478f9298a0b0ceae94b8dd9))
- **blocknote-editor:** TK-8275 Replace blocknote editor using from workspace to npm package ([#5986](https://github.com/resola-ai/deca-apps/issues/5986)) ([20cd226](https://github.com/resola-ai/deca-apps/commit/20cd226e798526645bbf9a67a0dac34b428572b6))
- **ci:** TK-8889 - Github Runner Fallback ([#5994](https://github.com/resola-ai/deca-apps/issues/5994)) ([8c03f38](https://github.com/resola-ai/deca-apps/commit/8c03f38cd07ae193f7525f1fa0fb935529ff00a5))
- **ci:** TK-8889 - Refactor CI workflow to separate runner strategy jobs ([#6035](https://github.com/resola-ai/deca-apps/issues/6035)) ([63e6491](https://github.com/resola-ai/deca-apps/commit/63e6491e033a5efdfe19e93c92705b0f52756d59))
- **general:** TK-9033 TK-9041 improve test for all apps in deca-apps ([#6025](https://github.com/resola-ai/deca-apps/issues/6025)) ([f0eb062](https://github.com/resola-ai/deca-apps/commit/f0eb062a90bf85eee066f12cd1e36a88ce01f1ad))
- **kb:** TK-7906 Adjust the fodler and kb selector in Articles export job ([#5712](https://github.com/resola-ai/deca-apps/issues/5712)) ([32c67cc](https://github.com/resola-ai/deca-apps/commit/32c67cc973111a721f87d1b4d575f7531e27f7f0))
- **kb:** TK-7906 Implement UI and integrate API to Article Export job feature ([#5687](https://github.com/resola-ai/deca-apps/issues/5687)) ([56cd1c2](https://github.com/resola-ai/deca-apps/commit/56cd1c2b8bfb19cf39276a881c0e355f458c669b))
- **kb:** TK-8201 Implement export button from homepage ([#5851](https://github.com/resola-ai/deca-apps/issues/5851)) ([48b7627](https://github.com/resola-ai/deca-apps/commit/48b7627ed477360a9527b483038121278d59337a))
- **kb:** TK-8201 Implement export menu action in kb card context menu ([#5823](https://github.com/resola-ai/deca-apps/issues/5823)) ([52a40d3](https://github.com/resola-ai/deca-apps/commit/52a40d3d1de9f555ec393da7b83e3cf46bc96726))
- **kb:** TK-8539 Remove kb document type when creating and improve export structure ([#5867](https://github.com/resola-ai/deca-apps/issues/5867)) ([418d44f](https://github.com/resola-ai/deca-apps/commit/418d44f68136bc8d2c10454ee174ad35df664e33))

### Bug Fixes

- **blocknote-editor:** TK-8248 Correct link toolbar rendering from editor ([#5692](https://github.com/resola-ai/deca-apps/issues/5692)) ([4ba2357](https://github.com/resola-ai/deca-apps/commit/4ba23570166f58b14daf7d0e0bcecd158fb47654))
- **blocknote-editor:** TK-8275 Correct JSX error related to blocknote viewer CSS rendering ([#5995](https://github.com/resola-ai/deca-apps/issues/5995)) ([0d51f40](https://github.com/resola-ai/deca-apps/commit/0d51f4058159af47f9e5fd56a85a6d7379276bef))
- **blocknote-editor:** TK-8275 Correct mantine config in blocknote package ([#5988](https://github.com/resola-ai/deca-apps/issues/5988)) ([47ca4b8](https://github.com/resola-ai/deca-apps/commit/47ca4b8cee39a3c08d9c83784fe97e3a50388038))
- **blocknote-editor:** TK-8275 Revert blocknote package from other apps except kb ([#5999](https://github.com/resola-ai/deca-apps/issues/5999)) ([9c49681](https://github.com/resola-ai/deca-apps/commit/9c49681414e37d097cbfaa2a30bf4c940ff113f8))
- **blocknote-editor:** TK-8275 Update new package with latest change and include pusblish script ([#6049](https://github.com/resola-ai/deca-apps/issues/6049)) ([a7256ef](https://github.com/resola-ai/deca-apps/commit/a7256ef6f32d440e58cb5b8a4f4fad456f3a6497))
- **kb, chatbot:** TK-8237 Correct some QA feedbacks in kb and chatbot ([#6029](https://github.com/resola-ai/deca-apps/issues/6029)) ([10737b1](https://github.com/resola-ai/deca-apps/commit/10737b1661709d7279bad3853c60918c7379dcdc))
- **kb:** [TK-8065] missing error message details ([#5627](https://github.com/resola-ai/deca-apps/issues/5627)) ([d782196](https://github.com/resola-ai/deca-apps/commit/d782196436c4441168ec0a448e9b3d9f96bbbe54))
- **kb:** TK-7906 Adjust the checkbox behavior when current folder is ROOT ([#5730](https://github.com/resola-ai/deca-apps/issues/5730)) ([b53df11](https://github.com/resola-ai/deca-apps/commit/b53df112a31dd5902013ff2f851ec282bdcd4599))
- **kb:** TK-8237 Correct KB logout but still access application after clicking back from Browser ([#6048](https://github.com/resola-ai/deca-apps/issues/6048)) ([bd4be73](https://github.com/resola-ai/deca-apps/commit/bd4be7354118aeafb5b11bb8c93230cb3db3453f))
- **kb:** TK-8882 Correct time range issue in Article statistic ([#6036](https://github.com/resola-ai/deca-apps/issues/6036)) ([ae46d4d](https://github.com/resola-ai/deca-apps/commit/ae46d4d6a36fea4fd9ce4f27ac9020a73071f3c9))
- **kb:** TK-9061 Handle em tag from Document search result description ([#6059](https://github.com/resola-ai/deca-apps/issues/6059)) ([d624cd3](https://github.com/resola-ai/deca-apps/commit/d624cd361c1716e40065ca9ffc13a04dbe413540))

## [0.13.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.13.0) (2025-06-10)

### Features

- **blocknote-editor:** TK-7084 Implement Variable suggestion popup and added unit test ([#5489](https://github.com/resola-ai/deca-apps/issues/5489)) ([df95daa](https://github.com/resola-ai/deca-apps/commit/df95daaf0db247377b1c63eab0f2b2f1f10126ab))
- **datadog:** Add organization attribute to user tracking in Datadog (TK-7901 - TK-7856) ([#5492](https://github.com/resola-ai/deca-apps/issues/5492)) ([6fa2706](https://github.com/resola-ai/deca-apps/commit/6fa27068fdbc6cf264d5ee2a439674f633a2b925))
- **datadog:** Improve org support (TK-7901 TK-7856) ([#5495](https://github.com/resola-ai/deca-apps/issues/5495)) ([88256e4](https://github.com/resola-ai/deca-apps/commit/88256e47a1580eb7c88e40292b88fa359ff54e71))
- **datadog:** refactor organization context handling (TK-7901 TK-7856) ([#5506](https://github.com/resola-ai/deca-apps/issues/5506)) ([079b36f](https://github.com/resola-ai/deca-apps/commit/079b36f4d25e3bf329a9d898310ae65354f75027))
- **datadog:** setup datadog for Apex (TK-7926 - TK-7856) ([#5507](https://github.com/resola-ai/deca-apps/issues/5507)) ([20daf3c](https://github.com/resola-ai/deca-apps/commit/20daf3c38dafec0bc37d1db865f497a613d61af0))
- **datadog:** setup datadog for Chat Window Client (TK-7938 - TK-7856) ([#5519](https://github.com/resola-ai/deca-apps/issues/5519)) ([d2dfe6b](https://github.com/resola-ai/deca-apps/commit/d2dfe6b0dbcfeea7073caa1ea9fbc0961d8e3524))
- **datadog:** Setup Datadog For Form Admin - TK-7854 - TK-7856 ([#5480](https://github.com/resola-ai/deca-apps/issues/5480)) ([2af13a2](https://github.com/resola-ai/deca-apps/commit/2af13a2e3b8a4353adf82a99d9a31325175d2dcf))
- **datadog:** setup datadog for form client (TK-7939 TK-7856) ([#5524](https://github.com/resola-ai/deca-apps/issues/5524)) ([5cecf29](https://github.com/resola-ai/deca-apps/commit/5cecf29ba9831c31296443672c99f35dee0eba96))
- **datadog:** Setup Datadog for Livechat - TK-7776 - TK-7856 ([#5482](https://github.com/resola-ai/deca-apps/issues/5482)) ([7f0a60c](https://github.com/resola-ai/deca-apps/commit/7f0a60c4bc6c863df76d0b624f77bfd9a2b2286b))
- **datadog:** Setup datadog for multiple applications in deca-apps (TK-7856) ([9521d52](https://github.com/resola-ai/deca-apps/commit/9521d52ac3f0c9d8afd765f75385f2a6418dc3c7))
- **datadog:** Setup datadog for multiple applications in deca-apps (TK-7856) ([00cbef8](https://github.com/resola-ai/deca-apps/commit/00cbef89fa70585a5bd06e3ff9fe05adef5e436b))
- **datadog:** Setup datadog for multiple applications in deca-apps (TK-7856) ([ff80fbe](https://github.com/resola-ai/deca-apps/commit/ff80fbef1437d8d0d46b9e08a52d4e23bdedaa1b))
- **datadog:** Setup datadog for multiple applications in deca-apps (TK-7856) ([c9e08e3](https://github.com/resola-ai/deca-apps/commit/c9e08e3bfbb17015df77a3605bd02794513974c4))
- **datadog:** Setup datadog for multiple applications in deca-apps (TK-7856) ([aea3616](https://github.com/resola-ai/deca-apps/commit/aea361623d4611e731a8fc2dde337b05ddbc9dc1))
- **datadog:** Setup datadog for multiple applications in deca-apps (TK-7856) ([dbfc863](https://github.com/resola-ai/deca-apps/commit/dbfc8639a0114c7557f0fb937cfe5e65fd2de91a))
- **datadog:** Setup datadog for multiple applications in deca-apps (TK-7856) ([#5536](https://github.com/resola-ai/deca-apps/issues/5536)) (sync all the datadog integration commits into one) ([47c67f7](https://github.com/resola-ai/deca-apps/commit/47c67f77a30eeb19b7a8ee7fc2f171bbcd1f77c4))
- **datadog:** Setup Datadog for Page Admin - TK-7855 - TK-7856 ([#5481](https://github.com/resola-ai/deca-apps/issues/5481)) ([844f444](https://github.com/resola-ai/deca-apps/commit/844f4445d17276d1de1675451756d5c58e17395f))
- **datadog:** setup datadog for page-builder (TK-7940 - TK-7856) ([#5525](https://github.com/resola-ai/deca-apps/issues/5525)) ([e3b0ba7](https://github.com/resola-ai/deca-apps/commit/e3b0ba700cc0141d3fb0b6c4bb3ffaded3306473))
- **datadog:** Setup Datadog for Tables - TK-7778 - TK-7856 ([#5479](https://github.com/resola-ai/deca-apps/issues/5479)) ([185c242](https://github.com/resola-ai/deca-apps/commit/185c242d39c19e1e0a9f620aa9bd8835ad1b39ae))
- **datadog:** Support Version in datadog - TK-7892 TK-7856 ([#5484](https://github.com/resola-ai/deca-apps/issues/5484)) ([f16bcbc](https://github.com/resola-ai/deca-apps/commit/f16bcbc58925814228761cddf2d6c6ff1c74cf8b))
- **datadog:** TK-7775 - setup Datadog for CRM ([#5461](https://github.com/resola-ai/deca-apps/issues/5461)) ([a40e917](https://github.com/resola-ai/deca-apps/commit/a40e91763394a51e14efc1aac62e55e13ea0fff5))
- **datadog:** TK-7856 - Add session management to Datadog service initialization ([#5477](https://github.com/resola-ai/deca-apps/issues/5477)) ([8495066](https://github.com/resola-ai/deca-apps/commit/8495066a217fb236bba565d8720977d116f4118f))
- **datadog:** TK-7856 - TK-7768 - Add Datadog configuration to ai-studio ([#5470](https://github.com/resola-ai/deca-apps/issues/5470)) ([f310b0a](https://github.com/resola-ai/deca-apps/commit/f310b0a6a59bc4188334b1f43abe1d0b329e3f28))
- **datadog:** TK-7856 - TK-7772 - setup datadog for AI Widgets Admin ([#5478](https://github.com/resola-ai/deca-apps/issues/5478)) ([e8795e8](https://github.com/resola-ai/deca-apps/commit/e8795e8d0336c82e59fc0b5c3bd2938d2bb7ad86))
- **datadog:** TK-7856 - TK-7773 - Setup Datadog for DECA Chatbot ([#5476](https://github.com/resola-ai/deca-apps/issues/5476)) ([38307e7](https://github.com/resola-ai/deca-apps/commit/38307e7be37117bb366473cc8246fbbb3415bd83))
- **datadog:** TK-7856 - TK-7774 - Add Datadog configuration to chatwindow (chatbox) ([#5472](https://github.com/resola-ai/deca-apps/issues/5472)) ([fb4357c](https://github.com/resola-ai/deca-apps/commit/fb4357c27475beef12f6b7720bac2f46992ee10b))
- **datadog:** TK-7856 - TK-7777 - Setup Datadog integration in account app ([#5474](https://github.com/resola-ai/deca-apps/issues/5474)) ([ec3cd94](https://github.com/resola-ai/deca-apps/commit/ec3cd94b6bfdec7e73831c48a88ce9b54b49ad13))
- **datadog:** TK-7856 - TK-7779 - Add Datadog service initialization in widget-builder ([#5473](https://github.com/resola-ai/deca-apps/issues/5473)) ([47cad35](https://github.com/resola-ai/deca-apps/commit/47cad35aa275acfa5903a62ab9375520cd0cb4d4))
- **datadog:** TK-7856 TK-7859 TK-7860 make datadog a common service and support user tracking ([#5459](https://github.com/resola-ai/deca-apps/issues/5459)) ([8340bee](https://github.com/resola-ai/deca-apps/commit/8340beeb54b2704f53292835a2949b215a1157a7))
- **kb:** TK-7906 Adjust the fodler and kb selector in Articles export job ([#5712](https://github.com/resola-ai/deca-apps/issues/5712)) ([3781f2b](https://github.com/resola-ai/deca-apps/commit/3781f2b775bf998ac8e94e0c78f6e528d10b6a93))
- **kb:** TK-7906 Implement UI and integrate API to Article Export job feature ([#5687](https://github.com/resola-ai/deca-apps/issues/5687)) ([5123dd2](https://github.com/resola-ai/deca-apps/commit/5123dd21b6bd8b871e611438debbe47aa14d3b2c))

### Bug Fixes

- **blocknote-editor:** TK-3856 Correct missing react emotion from blocknote package ([#5610](https://github.com/resola-ai/deca-apps/issues/5610)) ([d337b14](https://github.com/resola-ai/deca-apps/commit/d337b14d252831b504c78742facd917bfaba8cbb))
- **blocknote-editor:** TK-5836 Correct tsc error from Blocknote editor and enabled type check ([#5588](https://github.com/resola-ai/deca-apps/issues/5588)) ([c4c74a3](https://github.com/resola-ai/deca-apps/commit/c4c74a3172aa0cebe0be6c8aeedeae711eac6e88))
- **blocknote-editor:** TK-5836 Corrected undefined error when open File Panel from BlockNote editor ([#5581](https://github.com/resola-ai/deca-apps/issues/5581)) ([fd9f17e](https://github.com/resola-ai/deca-apps/commit/fd9f17e2f76801133145e3602332e331841b17ad))
- **blocknote-editor:** TK-5836 Remove state image checking from BlockNote image block ([#5460](https://github.com/resola-ai/deca-apps/issues/5460)) ([90adb2c](https://github.com/resola-ai/deca-apps/commit/90adb2c95ffd6016d38ad4c7c8243dff7cba0887))
- **blocknote-editor:** TK-5836 Remove state image checking from BlockNote image block ([#5460](https://github.com/resola-ai/deca-apps/issues/5460)) ([46fee9f](https://github.com/resola-ai/deca-apps/commit/46fee9f98803c1b40f0090e5d32f135d4fa52f47))
- **blocknote-editor:** TK-8248 Correct link toolbar rendering from editor ([#5692](https://github.com/resola-ai/deca-apps/issues/5692)) ([911a352](https://github.com/resola-ai/deca-apps/commit/911a3521f1c7553ded503cc3803027c340b5c448))
- **kb:** [TK-8065] missing error message details ([#5627](https://github.com/resola-ai/deca-apps/issues/5627)) ([6df7145](https://github.com/resola-ai/deca-apps/commit/6df7145541c83c0e2f2096990d1d8abea3fc96a2))
- **kb:** TK-5836 Corrected type in tiptap editor in kb ([#5526](https://github.com/resola-ai/deca-apps/issues/5526)) ([0e9d11b](https://github.com/resola-ai/deca-apps/commit/0e9d11bf46705e7e4b89ee34bcba2de6c3094453))
- **kb:** TK-7906 Adjust the checkbox behavior when current folder is ROOT ([#5730](https://github.com/resola-ai/deca-apps/issues/5730)) ([a778a7c](https://github.com/resola-ai/deca-apps/commit/a778a7c8fbc5b576768416ecd31cc759fb505aac))
- **ui:** use classes instead of inline styles to fix build for [#5606](https://github.com/resola-ai/deca-apps/issues/5606) ([36998f4](https://github.com/resola-ai/deca-apps/commit/36998f48caec3d03bc8892bebdb173e0afb1e922))
- **ui:** use classes instead of inline styles to fix build for [#5606](https://github.com/resola-ai/deca-apps/issues/5606) ([650e1ec](https://github.com/resola-ai/deca-apps/commit/650e1ecea2fa88a903ed97f2e681a6ce7bb968a3))

### [0.12.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.1) (2025-05-22)

### Bug Fixes

- **blocknote-editor:** TK-5836 Remove state image checking from BlockNote image block ([#5460](https://github.com/resola-ai/deca-apps/issues/5460)) ([a52fa3a](https://github.com/resola-ai/deca-apps/commit/a52fa3a01fa62a64146389c5e4727f3e9731ffd6))

## [0.12.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.0) (2025-05-21)

### Features

- **kb:** TK-5836 Setting up Sonar tracking in BlockNote editor package and fixed some bugs ([#5307](https://github.com/resola-ai/deca-apps/issues/5307)) ([8806794](https://github.com/resola-ai/deca-apps/commit/880679498b3c465f9466c67a2bd0f649ec5c3075))
- **kb:** TK-7522 Correct response handler in job articles download ([#5276](https://github.com/resola-ai/deca-apps/issues/5276)) ([81eecfa](https://github.com/resola-ai/deca-apps/commit/81eecfaaeb58af437b9db996c082d24b435f19e2))
- **kb:** TK-7522 Implement export options in job articles ([#5273](https://github.com/resola-ai/deca-apps/issues/5273)) ([31b5bc5](https://github.com/resola-ai/deca-apps/commit/31b5bc52d7cc56b351717bc8f8d783dd4c3920f5))
- **kb:** TK-7732 - integrate Datadog logging and monitoring services ([#5406](https://github.com/resola-ai/deca-apps/issues/5406)) ([b094466](https://github.com/resola-ai/deca-apps/commit/b0944661436d08de016572c54d64f6d2482c46a8))
- **shared:** TK-5836 Implement and moving BlockNote Editor component to new package ([#5210](https://github.com/resola-ai/deca-apps/issues/5210)) ([be8b92a](https://github.com/resola-ai/deca-apps/commit/be8b92adb8fe63f94ea0089bddf57e5c99fd973f))
- **shared:** TK-5836 Replace new BlockNote Editor package and remove old component from UI ([#5222](https://github.com/resola-ai/deca-apps/issues/5222)) ([33c5fba](https://github.com/resola-ai/deca-apps/commit/33c5fba3e0e38c6589a40a027f790cfd3d9d537f))

### Bug Fixes

- **blocknote-editor:** TK-5836 Corrected focusing handling in button node text editor ([#5383](https://github.com/resola-ai/deca-apps/issues/5383)) ([595bbbe](https://github.com/resola-ai/deca-apps/commit/595bbbef2c1274ccd95fe83de5b39fa959281872))
- **kb:** TK-7395 Correct article not ready modal when article deleting ([#5231](https://github.com/resola-ai/deca-apps/issues/5231)) ([bd64fdf](https://github.com/resola-ai/deca-apps/commit/bd64fdf75132f7b5d83f15d585d315ee36a493e4))
- **shared:** TK-5836 Correct util missing from blocknote editor package ([#5257](https://github.com/resola-ai/deca-apps/issues/5257)) ([c3eedb4](https://github.com/resola-ai/deca-apps/commit/c3eedb4e97b05b88633958be3f41e3c0c81db779))

## [0.11.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.11.0) (2025-05-07)

### Features

- **kb:** TK-5869 Update breadcrumbs component from ui package and add the unit test ([#5025](https://github.com/resola-ai/deca-apps/issues/5025)) ([b315fe0](https://github.com/resola-ai/deca-apps/commit/b315fe0d7796c0c11479c95be79123f2b8060857))
- **kb:** TK-6862 Implement media viewer modal and apply to BlockNote content viewer ([#4975](https://github.com/resola-ai/deca-apps/issues/4975)) ([ddff0ea](https://github.com/resola-ai/deca-apps/commit/ddff0ea8144791a95cf5f6a80d86d313e5bb5341))
- **kb:** TK-7120 Disabled unlink related action in shortcut article ([#5100](https://github.com/resola-ai/deca-apps/issues/5100)) ([126241c](https://github.com/resola-ai/deca-apps/commit/126241c101e270d978d2f0b6e45823368681d886))
- **kb:** TK-7120 Enabled moving shortcut article function in KB ([#5080](https://github.com/resola-ai/deca-apps/issues/5080)) ([b7c9918](https://github.com/resola-ai/deca-apps/commit/b7c991818704d2b9c85d64f5eca8ac7e5139d882))

### Bug Fixes

- **kb:** [TK-6969] dont show document image on firefox ([#5012](https://github.com/resola-ai/deca-apps/issues/5012)) ([0c593bd](https://github.com/resola-ai/deca-apps/commit/0c593bd29679ed3f3b0e748a2e78fa0b64347b05))
- **kb:** TK-6412 Control the aria-hidden from BlockTypeSelect ([#4994](https://github.com/resola-ai/deca-apps/issues/4994)) ([9bf19da](https://github.com/resola-ai/deca-apps/commit/9bf19da78cef94dc7050201dc1110f01a5c528d7))
- **kb:** TK-6412 Correct build error related to unused ID ([#4997](https://github.com/resola-ai/deca-apps/issues/4997)) ([d2f53e5](https://github.com/resola-ai/deca-apps/commit/d2f53e58bc8bc5358d69c57fde1a5b9fd04f2051))
- **kb:** TK-6412 Correct dropdown aria hidden when open many times in Editor ([#4893](https://github.com/resola-ai/deca-apps/issues/4893)) ([c2b322e](https://github.com/resola-ai/deca-apps/commit/c2b322e1c9d35dbd94107146fe15b332f206a638))
- **kb:** TK-6412 Custom the select component in BlockTypeSelect of editor toolbar ([#5030](https://github.com/resola-ai/deca-apps/issues/5030)) ([66f6dc9](https://github.com/resola-ai/deca-apps/commit/66f6dc91026d89ce00435f0e5aaee3045a576844))
- **kb:** TK-6412 Override and custom BlockTypeSelect toolbar from BlockNote ([#4989](https://github.com/resola-ai/deca-apps/issues/4989)) ([5007698](https://github.com/resola-ai/deca-apps/commit/5007698c163700363414632d617df51f9c47f242))
- **kb:** TK-6412 Remove logic related to aria control to verify on dev ([#5006](https://github.com/resola-ai/deca-apps/issues/5006)) ([0e355cf](https://github.com/resola-ai/deca-apps/commit/0e355cf3c6ed73633780afa60335515aeb711cff))
- **kb:** TK-6644 Correct image cannot embed in Job Article ([#4955](https://github.com/resola-ai/deca-apps/issues/4955)) ([018396d](https://github.com/resola-ai/deca-apps/commit/018396d900b8e132bd0ceba2f09076f0fd3f69e7))
- **kb:** TK-7031 Correct HTML rendering in KB Description via sanitize util ([#5044](https://github.com/resola-ai/deca-apps/issues/5044)) ([49c291b](https://github.com/resola-ai/deca-apps/commit/49c291b6e25b4d4da619989d419df6f0d43b7d98))
- **shared:** TK-6412 Correct markdown editor toolbar in chatbot ([#5068](https://github.com/resola-ai/deca-apps/issues/5068)) ([87b88b7](https://github.com/resola-ai/deca-apps/commit/87b88b79da214b43dd8fca56e8e3c3d2ed45dcbd))
- **shared:** TK-6857 Corrected focusing handling in button node text editor ([#5109](https://github.com/resola-ai/deca-apps/issues/5109)) ([a19fbbe](https://github.com/resola-ai/deca-apps/commit/a19fbbe64ba4d59c3cec215817ca615a108bedde))

## [0.10.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.10.0) (2025-04-08)

### Features

- **ci:** TK-6380 - add react-library TypeScript config copying step to CI workflow ([2b684d7](https://github.com/resola-ai/deca-apps/commit/2b684d70d7b4aa1cab2a34fffe814ffaf6d54840))
- **ci:** TK-6380 - extend TypeScript config copying step to include additional config directories ([9c19d0b](https://github.com/resola-ai/deca-apps/commit/9c19d0b4dc3f57a76bef03ae0e48df70cf1f4b18))
- **ci:** TK-6380 - remove unnecessary pnpm install step from CI workflow ([20eb18d](https://github.com/resola-ai/deca-apps/commit/20eb18d8cfbe1d0b6973439015a03d4cf2c18204))
- **ci:** TK-6380 - update SonarCloud configuration to include changed apps and enhance coverage exclusions ([2a46170](https://github.com/resola-ai/deca-apps/commit/2a46170add7d1fefa2843edb8dd08e4190089a2d))
- **ci:** TK-6386 - create separate job for sonarCloud Scan in deca-apps; add ci to allowscope in check-commit-message script ([822cbc2](https://github.com/resola-ai/deca-apps/commit/822cbc286caec9486724a74f33b1370ebde47edb))
- **ci:** TK-6399 - decouple the detect change logic in CI script ([189b487](https://github.com/resola-ai/deca-apps/commit/189b4872c6e3e70db183e36ab25416dc3e29d525))
- **ci:** TK-6399 - enhance script to retrieve default branch and SHA if not provided ([cc37a4d](https://github.com/resola-ai/deca-apps/commit/cc37a4d723c7b5a95aafde4fb32f7d784300a6d2))
- **ci:** TK-6399 - TK-6406 - modify detect-changes script to output changed apps in a structured format ([c5b0068](https://github.com/resola-ai/deca-apps/commit/c5b0068a7ae783a7f6c794ac8687bcfff207f1c9))
- **ci:** TK-6399 - update scripts to handle changed apps as a comma-separated list ([f0f1cdc](https://github.com/resola-ai/deca-apps/commit/f0f1cdcc1957b3c2d5f493132bcc7434e02c2a17))
- **ci:** TK-6406 - add detect-changes job to CI workflow and update scripts to utilize changed apps list ([6d23dae](https://github.com/resola-ai/deca-apps/commit/6d23dae7f1f8c05295d75a60ec5cacd56b71224c))
- **ci:** TK-6406 - add logging for changed apps in detect-changes script ([ec4b8c7](https://github.com/resola-ai/deca-apps/commit/ec4b8c78683319c6eff51b5444997d9d3e06841e))
- **ci:** TK-6406 - update regex in detect-changes script to correctly extract changed apps ([5ad2127](https://github.com/resola-ai/deca-apps/commit/5ad21275b6062c0dc110f430fd05f87fb68688dc))
- **kb:** [TK-6216] implement components modal confirm and modal edit ([f78464c](https://github.com/resola-ai/deca-apps/commit/f78464c9b734364b3aa399b3465616c37d0c89ac))
- **kb:** [TK-6230] add icon access level on document file ([#4708](https://github.com/resola-ai/deca-apps/issues/4708)) ([63ea4ef](https://github.com/resola-ai/deca-apps/commit/63ea4ef2eb918d726f08adf47fc36b6abe9adfa9))
- **kb:** [TK-6236] add default image for document image ([#4786](https://github.com/resola-ai/deca-apps/issues/4786)) ([dd96626](https://github.com/resola-ai/deca-apps/commit/dd96626ae71d2c5961af460a5f9e8125a88567c2))
- **kb:** [TK-6236] Allow Upload Image as a Document ([#4754](https://github.com/resola-ai/deca-apps/issues/4754)) ([cf7eb38](https://github.com/resola-ai/deca-apps/commit/cf7eb3826ba02bc2b9cdcf4745173aa84e22d386))
- **kb:** [TK-6236] trigger get link document when image have error ([#4793](https://github.com/resola-ai/deca-apps/issues/4793)) ([1c220a8](https://github.com/resola-ai/deca-apps/commit/1c220a8aabad46dc190932293fad87567df01af1))
- **kb:** TK-2413 Implement view original article action and improve unit test ([#4785](https://github.com/resola-ai/deca-apps/issues/4785)) ([49dcb0d](https://github.com/resola-ai/deca-apps/commit/49dcb0d12db4708954d6a162bade48b5281190bf))
- **kb:** TK-3221 Handle API error and implement 404 page in KB for Detail ([#4679](https://github.com/resola-ai/deca-apps/issues/4679)) ([0d93509](https://github.com/resola-ai/deca-apps/commit/0d935093cdfbdf4437ae1f0e7146b9160836ad6f))
- **kb:** TK-3221 Implement article not found in KB Article dialog ([#4740](https://github.com/resola-ai/deca-apps/issues/4740)) ([ed49ac5](https://github.com/resola-ai/deca-apps/commit/ed49ac5667009e0942934489e28c67f1cb791d57))
- **kb:** TK-4774 Implement Article shortcut feature with creating and viewing ([#4769](https://github.com/resola-ai/deca-apps/issues/4769)) ([16a91bc](https://github.com/resola-ai/deca-apps/commit/16a91bc593bc40b6c63aa5b1d954f4e16cdcf5b0))
- **kb:** TK-6101 Add export button to Job Result with UI only ([#4711](https://github.com/resola-ai/deca-apps/issues/4711)) ([4809804](https://github.com/resola-ai/deca-apps/commit/480980418274089aa904aafb3069acba72e5f082))
- **kb:** TK-6201 Implement max articles validation when saving to KB ([#4687](https://github.com/resola-ai/deca-apps/issues/4687)) ([8295b91](https://github.com/resola-ai/deca-apps/commit/8295b91bc25b2b0f8c5e8bc2dc24d64e6d82abd7))
- **kb:** TK-6238 Correct some feedback about fields disabled and analytics data in Article Shortcut ([#4819](https://github.com/resola-ai/deca-apps/issues/4819)) ([293d46f](https://github.com/resola-ai/deca-apps/commit/293d46f7d4ecd15fc1716640cbfbce6eb594daff))
- **kb:** TK-6238 Enhance confirm dialog and kb information when moving or creating article shortcut ([#4852](https://github.com/resola-ai/deca-apps/issues/4852)) ([7e283b0](https://github.com/resola-ai/deca-apps/commit/7e283b0c02bc9a0c3fd1ea90e8a8beb9974c49c4))
- **kb:** TK-6238 Update logic related to article shortcut deleting and unit test ([#4804](https://github.com/resola-ai/deca-apps/issues/4804)) ([fdd0ac1](https://github.com/resola-ai/deca-apps/commit/fdd0ac1d08777ccc2a22715e482075999c3309e1))
- **kb:** TK-6238 Update original article id when getting analytics ([#4854](https://github.com/resola-ai/deca-apps/issues/4854)) ([d28a5d3](https://github.com/resola-ai/deca-apps/commit/d28a5d379b7a3efebaf620507542116b0630af37))
- **kb:** TK-6372 Integrate Download gen articles api and rename api handler hook ([#4779](https://github.com/resola-ai/deca-apps/issues/4779)) ([6c49e72](https://github.com/resola-ai/deca-apps/commit/6c49e72d72f783a70abd4b310ab6b2717fba06e7))
- **scripts:** TK-6399 - update default SHA retrieval to use the previous commit ([8de05ea](https://github.com/resola-ai/deca-apps/commit/8de05ea472982b855a03dbea1ec939a52e6cfaf9))
- **sonar:** TK-6380 - add TypeScript config copying step to CI workflow ([ab3f1bc](https://github.com/resola-ai/deca-apps/commit/ab3f1bce04a0de96d468a28e5cfc75e9f43fdd83))
- **sonar:** TK-6380 - add TypeScript config copying step to CI workflow ([7281970](https://github.com/resola-ai/deca-apps/commit/7281970f9296af8b6dd15ab81ac91d5b1ad6b732))
- **sonar:** TK-6380 - add TypeScript tsconfig paths to sonar-project.properties ([fe6842b](https://github.com/resola-ai/deca-apps/commit/fe6842b0107854f4e1b9bd2d9a4076535b396ad9))

### Bug Fixes

- **kb:** [TK-6230] hide access level on folder ([#4709](https://github.com/resola-ai/deca-apps/issues/4709)) ([fd18d25](https://github.com/resola-ai/deca-apps/commit/fd18d2500627196125d42311f2c9514f40d59c9e))
- **kb:** [TK-6675] The description "This knowledge base will be prirate." always show even status change to public ([#4824](https://github.com/resola-ai/deca-apps/issues/4824)) ([099dbed](https://github.com/resola-ai/deca-apps/commit/099dbed9cfac084f5ddacd8599318877a94b0d5e))
- **kb:** [TK-6677] update params withOriginalArticle to withOriginArticle ([#4833](https://github.com/resola-ai/deca-apps/issues/4833)) ([6a85ccc](https://github.com/resola-ai/deca-apps/commit/6a85ccc0b98db4e0c89a3607360cc5d1d358fd3c))
- **kb:** TK-6181 Correct target link when exporting from blocknote and remove unused dom purify ([#4641](https://github.com/resola-ai/deca-apps/issues/4641)) ([05a46b3](https://github.com/resola-ai/deca-apps/commit/05a46b3c2cdf8584f6376112e8a9333e58d8fe1a))
- **kb:** TK-6238 Update API and logic when getting article shortcut feedback in articles table ([#4865](https://github.com/resola-ai/deca-apps/issues/4865)) ([e7ef277](https://github.com/resola-ai/deca-apps/commit/e7ef277ec4ac6bff2d57059d8bfe28781e65a8cc))
- **security:** bump next patch version ([#4851](https://github.com/resola-ai/deca-apps/issues/4851)) ([5eaed5f](https://github.com/resola-ai/deca-apps/commit/5eaed5f6bb3bee5c2c344670c8a1e6ce270dce9f))
- **security:** bump vite version ([#4723](https://github.com/resola-ai/deca-apps/issues/4723)) ([d3ddd56](https://github.com/resola-ai/deca-apps/commit/d3ddd56fbace4be06ec09afeb06ece4b39b87db1))
- **security:** bump vite version ([#4789](https://github.com/resola-ai/deca-apps/issues/4789)) ([1a2d4f7](https://github.com/resola-ai/deca-apps/commit/1a2d4f7f71ea0fc72c62375e2c3a60daf0f11e42))
- **security:** bump vite version ([#4850](https://github.com/resola-ai/deca-apps/issues/4850)) ([127c6dd](https://github.com/resola-ai/deca-apps/commit/127c6dd41eaf4a87d818ad97eb7dd10c229de402))
- **security:** update braces and lint-staged version ([#4721](https://github.com/resola-ai/deca-apps/issues/4721)) ([0718e18](https://github.com/resola-ai/deca-apps/commit/0718e18f18218bfd55602102f495ed2e04064357))

### [0.9.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.9.1) (2025-03-19)

### Features

### Bug Fixes

- **kb:** TK-6181 Correct target link when exporting from blocknote and remove unused dom purify ([#4641](https://github.com/resola-ai/deca-apps/issues/4641)) ([b34fda8](https://github.com/resola-ai/deca-apps/commit/b34fda8951b397cd24802868f039cb7f80214fe6))

## [0.9.0](https://github.com-resola/resola-ai/deca-apps/compare/<EMAIL>@0.9.0) (2025-03-18)

### Features

- **general:** TK-5818 - add studio to menu ([#4465](https://github.com-resola/resola-ai/deca-apps/issues/4465)) ([d0215d7](https://github.com-resola/resola-ai/deca-apps/commit/d0215d723b22fcecc92158cda08ee6871effa6d1))
- **github-workflows:** TK-4372 - Optimize CI Workflows for Deca-Apps: Implement Parallel Execution ([#4585](https://github.com-resola/resola-ai/deca-apps/issues/4585)) ([0783653](https://github.com-resola/resola-ai/deca-apps/commit/07836530b3c39b74ae6253b3305d3cdff0f84885))
- **kb:** [TK-4621] allow update access level before upload file ([#4145](https://github.com-resola/resola-ai/deca-apps/issues/4145)) ([f4479df](https://github.com-resola/resola-ai/deca-apps/commit/f4479df47f3cf9e8d4035bc39e5b5d33c57c7a70))
- **kb:** [TK-4800] implement import article via csv ([#4206](https://github.com-resola/resola-ai/deca-apps/issues/4206)) ([207656b](https://github.com-resola/resola-ai/deca-apps/commit/207656bf1dc1b0ed7978aa6da97a40611c498781))
- **kb:** [TK-5307] integrate API custom prompt ([b832b32](https://github.com-resola/resola-ai/deca-apps/commit/b832b32e57b0937b5454f30f9f38a9b77266fb2f))
- **kb:** [TK-5307] update custom prompt ([e10b052](https://github.com-resola/resola-ai/deca-apps/commit/e10b052557f533f9fc8efe4f7e66bcbaa0f59f52))
- **kb:** [TK-5397] Missing deffault prompt + handle apply prompt ([5e2fc3f](https://github.com-resola/resola-ai/deca-apps/commit/5e2fc3f627c625592ff3a218cadb773aba183a44))
- **kb:** [TK-5656] update error message import csv create article ([cdf8343](https://github.com-resola/resola-ai/deca-apps/commit/cdf834318cef4bef9e45342466c4813fe8b95a46))
- **kb:** Correct search with part of modifier was typing ([#4308](https://github.com-resola/resola-ai/deca-apps/issues/4308)) ([a5e695e](https://github.com-resola/resola-ai/deca-apps/commit/a5e695e344fcc51d174924a5a753be5ee203a522))
- **kb:** improve UX for customize prompts ([46e0c14](https://github.com-resola/resola-ai/deca-apps/commit/46e0c14383930e7c83c5d8f2ff0b59a9d0e86397))
- **kb:** TK-2412 Correct reset payload when user change page size in Articles list ([#4153](https://github.com-resola/resola-ai/deca-apps/issues/4153)) ([fe0be83](https://github.com-resola/resola-ai/deca-apps/commit/fe0be83a830b2b5b2d0405a03f9e9bda34014e6a))
- **kb:** TK-2412 Implement page size selection in articles list ([7d1f1cd](https://github.com-resola/resola-ai/deca-apps/commit/7d1f1cdb78f4411f06db3e1b09fccf5fb5ba303c))
- **kb:** TK-2883 Correct validation article content when creating new ([#4325](https://github.com-resola/resola-ai/deca-apps/issues/4325)) ([4f7e815](https://github.com-resola/resola-ai/deca-apps/commit/4f7e8157767efb3398543e982613dd59bd85ce6f))
- **kb:** TK-4728 Correct search entities map and sort in kb selection ([#4239](https://github.com-resola/resola-ai/deca-apps/issues/4239)) ([ba8cd5d](https://github.com-resola/resola-ai/deca-apps/commit/ba8cd5d13310bd2978b413f360999c0c171739d4))
- **kb:** TK-4728 Correct warning in DynamicAutocomplete ([#4314](https://github.com-resola/resola-ai/deca-apps/issues/4314)) ([f4d2527](https://github.com-resola/resola-ai/deca-apps/commit/f4d2527f1e25dbdf6d148ae000d7b897c8f0a7ab))
- **kb:** TK-4728 Implement advanced search component and replace to KB Homepage ([#4179](https://github.com-resola/resola-ai/deca-apps/issues/4179)) ([de9d252](https://github.com-resola/resola-ai/deca-apps/commit/de9d2524b034b57183d6c8ad93291a0c2804608b))
- **kb:** TK-4728 Improve behavior when user types modifier in search ([#4307](https://github.com-resola/resola-ai/deca-apps/issues/4307)) ([ddebe4e](https://github.com-resola/resola-ai/deca-apps/commit/ddebe4e4a18c5dae5f0550071c9d396fc96347c3))
- **kb:** TK-4728 Update search with translation label in search box and suggestion ([#4311](https://github.com-resola/resola-ai/deca-apps/issues/4311)) ([109bb32](https://github.com-resola/resola-ai/deca-apps/commit/109bb32ac8ed403676a8fcc1aa20f0bffc82905f))
- **kb:** TK-4732 Implement article generator activities and saving history ([#4150](https://github.com-resola/resola-ai/deca-apps/issues/4150)) ([c40c6f5](https://github.com-resola/resola-ai/deca-apps/commit/c40c6f5c6c0d5f769e678348652e18d6420fbf24))
- **kb:** TK-4886 Implemented search behavior and integrated search filter API ([#4221](https://github.com-resola/resola-ai/deca-apps/issues/4221)) ([10915c6](https://github.com-resola/resola-ai/deca-apps/commit/10915c6d7d7a48679050094143df2998182ef9eb))
- **kb:** TK-4886 Update keyphrase field in search filter to keyword ([#4319](https://github.com-resola/resola-ai/deca-apps/issues/4319)) ([bdfee02](https://github.com-resola/resola-ai/deca-apps/commit/bdfee02f47e491e1b5cdc5dcd2d734ad5de86760))
- **kb:** TK-5248 Implement Open new tab option in BlockNote Link component ([#4384](https://github.com-resola/resola-ai/deca-apps/issues/4384)) ([abf16a3](https://github.com-resola/resola-ai/deca-apps/commit/abf16a3549ddbd51f90e775504f8dcfa751b405b))
- **kb:** TK-5298 Update Job card with more data actions and disabled state ([#4471](https://github.com-resola/resola-ai/deca-apps/issues/4471)) ([1730339](https://github.com-resola/resola-ai/deca-apps/commit/1730339df8574f45120b841f7a2c96e425281545))
- **kb:** TK-5299 Implement Job Summary and Prompt viewer component ([#4504](https://github.com-resola/resola-ai/deca-apps/issues/4504)) ([d3f57ca](https://github.com-resola/resola-ai/deca-apps/commit/d3f57ca08aebf0f4714423b5103c895d858de068))
- **kb:** TK-5299 Restructure Job Detail components and create new Job Result page ([#4486](https://github.com-resola/resola-ai/deca-apps/issues/4486)) ([e689c70](https://github.com-resola/resola-ai/deca-apps/commit/e689c708eff34136ea8a976f28a27859caf62388))
- **kb:** TK-5901 Correct right section event in search textbox ([#4570](https://github.com-resola/resola-ai/deca-apps/issues/4570)) ([b5b1360](https://github.com-resola/resola-ai/deca-apps/commit/b5b13608916d25cd6a731978a3cabe23e258818c))
- **kb:** TK-5901 Implement Document Source in Job Article detail ([#4526](https://github.com-resola/resola-ai/deca-apps/issues/4526)) ([da3de42](https://github.com-resola/resola-ai/deca-apps/commit/da3de42c4810b2a2dd2edae65bec0ecb9332cc3d))
- **kb:** TK-5901 Integrated API to Job article document and adjust UI when Document 404 ([#4567](https://github.com-resola/resola-ai/deca-apps/issues/4567)) ([062a881](https://github.com-resola/resola-ai/deca-apps/commit/062a8819f246427aff6a36662741d7f143c15c99))
- **kb:** TK-5901 Integrated new update from API Job detail ([#4529](https://github.com-resola/resola-ai/deca-apps/issues/4529)) ([d6dcbab](https://github.com-resola/resola-ai/deca-apps/commit/d6dcbabc35ea92f7f31038f7ecef16ba7e9568e1))
- **shared:** TK-3923 - Remove unused style decabutton ([#4360](https://github.com-resola/resola-ai/deca-apps/issues/4360)) ([02a5f22](https://github.com-resola/resola-ai/deca-apps/commit/02a5f226c7dfbb4f4f1e66cf57468fa6bdc2b206))
- **shared:** TK-5790 - implement configurable hub items via environment variables ([#4461](https://github.com-resola/resola-ai/deca-apps/issues/4461)) ([6e18e02](https://github.com-resola/resola-ai/deca-apps/commit/6e18e02627d45341c2dfa1e0f5835762ec144f87))
- **sonar:** TK-6086 integrate sonarqube ([#4586](https://github.com-resola/resola-ai/deca-apps/issues/4586)) ([7f44da0](https://github.com-resola/resola-ai/deca-apps/commit/7f44da01f2eeb0ad3353db427b4c0bf1b631f941))
- **sonar:** TK-6103 - update coverage path for sonar ([b45d6f7](https://github.com-resola/resola-ai/deca-apps/commit/b45d6f7eee19005b570f3aac017dd2e07815f47d))
- **sonar:** TK-6103 - add coverageMerger script and integrate it into CI workflow ([c95f1c4](https://github.com-resola/resola-ai/deca-apps/commit/c95f1c4d910cda9c388990ab3ef40038353b6576))
- **sonar:** TK-6103 - add glob package and update coverageMerger script to use destructured import ([88f185e](https://github.com-resola/resola-ai/deca-apps/commit/88f185e47f4497c7e731b3f82f775b4562ef9611))
- **sonar:** TK-6103 - add Node.js memory configuration to sonar-project.properties ([882fea1](https://github.com-resola/resola-ai/deca-apps/commit/882fea14d8781e000c54e1300fd7afe1852530b0))
- **sonar:** TK-6103 - add test inclusions for improved coverage analysis in sonar-project.properties ([5895fa2](https://github.com-resola/resola-ai/deca-apps/commit/5895fa201135857f43075caccc64bdfdadc8259a))
- **sonar:** TK-6103 - improve test coverage for sonar in CI workflow ([281e43f](https://github.com-resola/resola-ai/deca-apps/commit/281e43fc0bf2d241f27435c83fd5f544279ec3b9))
- **sonar:** TK-6103 - refine lcov report paths and expand coverage exclusions in sonar-project.properties ([ddcbaa7](https://github.com-resola/resola-ai/deca-apps/commit/ddcbaa79213a060bc88e9167e3cee005af6ada8b))
- **sonar:** TK-6103 - update ci - move merge coverage to test step ([20f28d0](https://github.com-resola/resola-ai/deca-apps/commit/20f28d0963d1de89cb90136f047031e6bbfc6526))
- **sonar:** update coverage path for sonar ([f0df848](https://github.com-resola/resola-ai/deca-apps/commit/f0df848941af013ee5be8197f536b1575254c53b))
- **workshop:** TK-2291 - Add components for RichTextEditor, ToastMessage, and Avatar ([#4491](https://github.com-resola/resola-ai/deca-apps/issues/4491)) ([c16edfd](https://github.com-resola/resola-ai/deca-apps/commit/c16edfd771353adbac64d897f269c3c7c9656b7d))
- **workshop:** TK-2322 TK-2324 TK-2325 TK-2326 TK-2336 - Add components for tabbar, popover, inputs, and inline message ([#4198](https://github.com-resola/resola-ai/deca-apps/issues/4198)) ([57d2873](https://github.com-resola/resola-ai/deca-apps/commit/57d2873e3b2f9451e4d36a5c560991bd3cde920e))

### Bug Fixes

- **general:** update amplify yml and vite version ([#4069](https://github.com-resola/resola-ai/deca-apps/issues/4069)) ([fc70d59](https://github.com-resola/resola-ai/deca-apps/commit/fc70d59521e4ab060612bf92cdb6d430135bd051))
- **kb:** [TK-3972] Correct aria-hidden blocking error when focus in/out from BlockNote tools ([#4413](https://github.com-resola/resola-ai/deca-apps/issues/4413)) ([557e978](https://github.com-resola/resola-ai/deca-apps/commit/557e9783bd6e267312f225c22d4da1da207b9bfe))
- **kb:** [TK-4621] UI break when filename too long ([#4272](https://github.com-resola/resola-ai/deca-apps/issues/4272)) ([b203ebf](https://github.com-resola/resola-ai/deca-apps/commit/b203ebf35269f294758978bac8260d2b9a450e46))
- **kb:** [TK-4800] dont reload articles after import ([#4247](https://github.com-resola/resola-ai/deca-apps/issues/4247)) ([8f477d9](https://github.com-resola/resola-ai/deca-apps/commit/8f477d9f371a1942fedb8227a8f8cd805c82f67b))
- **kb:** [TK-4800] make component ImportModal can dyanmic with multiple usecase ([#4244](https://github.com-resola/resola-ai/deca-apps/issues/4244)) ([d3f6c7c](https://github.com-resola/resola-ai/deca-apps/commit/d3f6c7c4573e5c64c7d59b35252066b5edc994f5))
- **kb:** [TK-4800] show error when upload success + update position dropdown + update public link for template ([#4237](https://github.com-resola/resola-ai/deca-apps/issues/4237)) ([831f409](https://github.com-resola/resola-ai/deca-apps/commit/831f40992e79bad7d8563b61fdd9e0b50707dd8e))
- **kb:** [TK-4800] update template import article ([#4294](https://github.com-resola/resola-ai/deca-apps/issues/4294)) ([ec22089](https://github.com-resola/resola-ai/deca-apps/commit/ec220897790d7dd7b378b43e12a6c2963299561b))
- **kb:** [TK-4800] update text KB name + lineClamp file name + handle success ([#4241](https://github.com-resola/resola-ai/deca-apps/issues/4241)) ([b9cfa1d](https://github.com-resola/resola-ai/deca-apps/commit/b9cfa1d3f1f3674ecd9a3746879ddd05cf24d36d))
- **kb:** [TK-5147] prevent double click when upload files ([#4213](https://github.com-resola/resola-ai/deca-apps/issues/4213)) ([cc06903](https://github.com-resola/resola-ai/deca-apps/commit/cc0690317e9718707e516d0a42d1e3794cf89352))
- **kb:** [TK-5147] prevent double click when upload files ([#4220](https://github.com-resola/resola-ai/deca-apps/issues/4220)) ([1983519](https://github.com-resola/resola-ai/deca-apps/commit/19835190e0b0e6bd0f30e817e29b969eb17360eb))
- **kb:** [TK-5307] add default prompt title ([160e5c5](https://github.com-resola/resola-ai/deca-apps/commit/160e5c539acd64d89f57771075729cea565ac95a))
- **kb:** [TK-5307] data dont reload after delete prompt ([42beb49](https://github.com-resola/resola-ai/deca-apps/commit/42beb49bac9f7f24d5e33994741c841ddf4ca53a))
- **kb:** [TK-5307] handle limit prompt create ([67d02a4](https://github.com-resola/resola-ai/deca-apps/commit/67d02a4184cfd2a47bf84ac3336804c0b7e31c9a))
- **kb:** [TK-5307] handle set default prompt with latest prompt apply ([b928cbb](https://github.com-resola/resola-ai/deca-apps/commit/b928cbb3bd5e9fa55a91fb4ca0587b03d3499d0e))
- **kb:** [TK-5307] update condition check reset custom prompt ([e62af48](https://github.com-resola/resola-ai/deca-apps/commit/e62af4888ac55c5f982c5f7301ce1024266c3090))
- **kb:** [TK-5423] Should direct to parent folder after deleting file ([c293a4d](https://github.com-resola/resola-ai/deca-apps/commit/c293a4de9556868ba31638ef14b9d75218dde11c))
- **kb:** [TK-5574] Correct language detection when unsupporting case ([#4395](https://github.com-resola/resola-ai/deca-apps/issues/4395)) ([144b9eb](https://github.com-resola/resola-ai/deca-apps/commit/144b9ebfb90b8584fc900210e2bd802489334e2c))
- **kb:** [TK-5583] cant select file md on window ([c49d80b](https://github.com-resola/resola-ai/deca-apps/commit/c49d80bd298b2ba25a80e69416c313510fe11be8))
- **kb:** [TK-5583] cant upload file md ([2ca296b](https://github.com-resola/resola-ai/deca-apps/commit/2ca296b61ec2570a31e546b98f48a4c2052ac947))
- **kb:** [TK-5583] handle error when server response error ([3cd9e36](https://github.com-resola/resola-ai/deca-apps/commit/3cd9e36aa1548eaf287a83923c6d7aef19da12d5))
- **kb:** fix locator ([a7c7e87](https://github.com-resola/resola-ai/deca-apps/commit/a7c7e87b2db8f0509412434c83ffbb0216994ea6))
- **kb:** TK-2530 Improve paste to Blocknote editor behavior ([#4511](https://github.com-resola/resola-ai/deca-apps/issues/4511)) ([bdbdcfa](https://github.com-resola/resola-ai/deca-apps/commit/bdbdcfad8240305953a99805fe3ae7fa5d626a75))
- **kb:** TK-2530 Normalize japanese breakine characters when pasting to editor ([#4514](https://github.com-resola/resola-ai/deca-apps/issues/4514)) ([e7ea5e3](https://github.com-resola/resola-ai/deca-apps/commit/e7ea5e3d4dda82bffc68a73ed642dd31b08965a5))
- **kb:** TK-3972 Clean up unnecessary focus in editor and update new tab checkbox position ([#4399](https://github.com-resola/resola-ai/deca-apps/issues/4399)) ([3e943c8](https://github.com-resola/resola-ai/deca-apps/commit/3e943c8283956ae206e76d73e4aa560f165526a9))
- **kb:** TK-3972 Correct editor initial content updating ([#4279](https://github.com-resola/resola-ai/deca-apps/issues/4279)) ([f2f18cb](https://github.com-resola/resola-ai/deca-apps/commit/f2f18cbfdf7f85ca0c4dda0d6a80be271a8f9cd8))
- **kb:** TK-4886 Correct missing label after selecting search option ([#4315](https://github.com-resola/resola-ai/deca-apps/issues/4315)) ([f99c663](https://github.com-resola/resola-ai/deca-apps/commit/f99c663b6cebd03174858abc08d99ba2dd32beed))
- **kb:** TK-4961 Correct break line in template title ([#4250](https://github.com-resola/resola-ai/deca-apps/issues/4250)) ([d63f911](https://github.com-resola/resola-ai/deca-apps/commit/d63f91108f1128f2c4ecb896a92b1b4cec709de5))
- **kb:** TK-4961 Correct error from SVG style when loading in article modal ([#4227](https://github.com-resola/resola-ai/deca-apps/issues/4227)) ([072da9f](https://github.com-resola/resola-ai/deca-apps/commit/072da9fef427f08f9fcb33c21bb4fcaef07ffb4e))
- **kb:** TK-4961 Correct long text break line in Job Article ([#4254](https://github.com-resola/resola-ai/deca-apps/issues/4254)) ([7389850](https://github.com-resola/resola-ai/deca-apps/commit/738985026df8e6cd321f2169abf84eb10237202f))
- **kb:** TK-4961 Correct SVG size rendering error and sorting in KB selection ([#4246](https://github.com-resola/resola-ai/deca-apps/issues/4246)) ([0a3421a](https://github.com-resola/resola-ai/deca-apps/commit/0a3421a02cbe61b2ebdd97cdaf324a6fca8156cb))
- **kb:** TK-5030 Corrected cannot save article with empty text content ([#4181](https://github.com-resola/resola-ai/deca-apps/issues/4181)) ([17cf74e](https://github.com-resola/resola-ai/deca-apps/commit/17cf74e35df8b8d46744b4438cf0c27771b75af0))
- **kb:** TK-5030 Improve article editor validation to fix saving error ([#4197](https://github.com-resola/resola-ai/deca-apps/issues/4197)) ([84ccb5f](https://github.com-resola/resola-ai/deca-apps/commit/84ccb5f34b72501929e2ea0e535c54633232a802))
- **kb:** TK-5244 Render target attribute in markdown and unload page confirmation ([#4441](https://github.com-resola/resola-ai/deca-apps/issues/4441)) ([8bd7986](https://github.com-resola/resola-ai/deca-apps/commit/8bd7986213bd9622f1efaabff910bcb79f7b3d3d))
- **kb:** TK-5248 Correct circular dependency in BlockNote Link component ([#4387](https://github.com-resola/resola-ai/deca-apps/issues/4387)) ([0020a37](https://github.com-resola/resola-ai/deca-apps/commit/0020a372be9855b06f080a053f7ab858329227de))
- **kb:** TK-5700, TK-3534 Improve article validation behavior when creating and updating ([#4425](https://github.com-resola/resola-ai/deca-apps/issues/4425)) ([10fbbc0](https://github.com-resola/resola-ai/deca-apps/commit/10fbbc0cff16e2bb9dff3a2db21fd06d67fe9eb8))
- **kb:** TK-6099 Add permission to Job Delete actions and improve breadcrumb logic ([#4582](https://github.com-resola/resola-ai/deca-apps/issues/4582)) ([fe52355](https://github.com-resola/resola-ai/deca-apps/commit/fe52355dedab65060e787c1e7eee7fb6e7a2c734))
- **kb:** TK-6099 Remove unused root item from breadcrumb from search result ([#4596](https://github.com-resola/resola-ai/deca-apps/issues/4596)) ([b0f7009](https://github.com-resola/resola-ai/deca-apps/commit/b0f700915949bd30c8d7d00b769ff5776bcd1a5d))
- **kb:** update locator ([01c79b3](https://github.com-resola/resola-ai/deca-apps/commit/01c79b3586d785aa964c4a2ee4240d240bcebc2f))
- **kb:** update locator for Templates ([f608bba](https://github.com-resola/resola-ai/deca-apps/commit/f608bbac479490eb3b56ea8290757f493a9079fb))
- **security:** update dompurify packages ([#4207](https://github.com-resola/resola-ai/deca-apps/issues/4207)) ([9500f08](https://github.com-resola/resola-ai/deca-apps/commit/9500f08457b4933ffb34c146ef8d3c07c7d15bbf))
- **shared:** fix unit test fail with no tests ([fd4a69a](https://github.com-resola/resola-ai/deca-apps/commit/fd4a69a406936abd109ae6581d0330edc315c9b1))
- **shared:** TK-5885 default left/right section pointer events to 'none' to make the whole component clickable ([#4558](https://github.com-resola/resola-ai/deca-apps/issues/4558)) ([92fb89c](https://github.com-resola/resola-ai/deca-apps/commit/92fb89cfade114b4f658183c26039bb1fe9546a8))
- **sonar:** remove redundant test inclusions from sonar-project.properties to streamline coverage analysis ([c4e2e07](https://github.com-resola/resola-ai/deca-apps/commit/c4e2e077fd4eaebfe634c9850cbae388ea0cf8f5))
- **sonar:** TK-6301 - Update reportPaths in sonar-project.properties ([076a128](https://github.com-resola/resola-ai/deca-apps/commit/076a1289876ee1eeca5318c3ce57d9acf07e4a21))

### [0.8.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.8.1) (2025-02-26)

### Features

- **ai-widgets:** TK-5206 update prompt validation ([#4309](https://github.com/resola-ai/deca-apps/issues/4309)) ([936b786](https://github.com/resola-ai/deca-apps/commit/936b7869adb1eabbc5d458f587eeabfcc5f2dbac))
- **chatbot:** tk-3339 add internation for node name ([#4261](https://github.com/resola-ai/deca-apps/issues/4261)) ([82384bf](https://github.com/resola-ai/deca-apps/commit/82384bfb6c5f2cbdd6046afb652d951e4b1c5c64))
- **chatbot:** tk-3339 confirm modal when deleting node ([#4176](https://github.com/resola-ai/deca-apps/issues/4176)) ([37fe068](https://github.com/resola-ai/deca-apps/commit/37fe0682980ef0299c02a665740316e401faadb0))
- **chatbot:** tk-3339 update international for action node ([#4281](https://github.com/resola-ai/deca-apps/issues/4281)) ([d134764](https://github.com/resola-ai/deca-apps/commit/d13476410538aaa3d523ce6534cc86bd16e699ac))
- **chatbot:** tk-3339 update toglee and delete email node name ([#4305](https://github.com/resola-ai/deca-apps/issues/4305)) ([3b9819b](https://github.com/resola-ai/deca-apps/commit/3b9819baea1d794e51f76e62027c5f0178f0a52a))
- **chatbot:** tk-4332 insert record does work in type number ([#4255](https://github.com/resola-ai/deca-apps/issues/4255)) ([c10e0ba](https://github.com/resola-ai/deca-apps/commit/c10e0ba4d36b247958c888a8eaedbc836a2bd71e))
- **chatbot:** tk-4332 update sync data in update and find record ([#4288](https://github.com/resola-ai/deca-apps/issues/4288)) ([ff1cafc](https://github.com/resola-ai/deca-apps/commit/ff1cafcf55ae95ee97c1223847d117c01a0c3a52))
- **chatbot:** tk-4619 improve ui ([#4113](https://github.com/resola-ai/deca-apps/issues/4113)) ([f4be807](https://github.com/resola-ai/deca-apps/commit/f4be8073a6a89c5e994b83ece46bfd9fabeadfd8))
- **chatbot:** tk-4619 improve ui ([#4120](https://github.com/resola-ai/deca-apps/issues/4120)) ([994455e](https://github.com/resola-ai/deca-apps/commit/994455ebcc8c6210d6045f2fa1b88d48c45ae948))
- **chatbot:** tk-4790 add submit in value and title ([#4236](https://github.com/resola-ai/deca-apps/issues/4236)) ([ebae9c3](https://github.com/resola-ai/deca-apps/commit/ebae9c37e0c4574ae1f3b684bb1e75da997c3afd))
- **chatbot:** tk-4790 dynamic button ([#4210](https://github.com/resola-ai/deca-apps/issues/4210)) ([aac0f47](https://github.com/resola-ai/deca-apps/commit/aac0f473cb05b41dca4d77459c67a1e1bce4a21d))
- **chatbot:** tk-4790 update init value and field of dynamic node ([#4226](https://github.com/resola-ai/deca-apps/issues/4226)) ([39aeef5](https://github.com/resola-ai/deca-apps/commit/39aeef511ae573b13594022dcbb8726497d27dfb))
- **chatbot:** tk-4790 update text desc in dynamic node ([#4310](https://github.com/resola-ai/deca-apps/issues/4310)) ([491c4c2](https://github.com/resola-ai/deca-apps/commit/491c4c25777be0a5fc3534edd189c7c1673d728b))
- **chatbot:** tk-4790 update text desc in dynamic node ([#4312](https://github.com/resola-ai/deca-apps/issues/4312)) ([2aea8d0](https://github.com/resola-ai/deca-apps/commit/2aea8d02bbf4618b3ad3e66ec9fdea3adfb2887c))
- **chatbot:** tk-5211 button setting text field update wrong ([#4277](https://github.com/resola-ai/deca-apps/issues/4277)) ([29a1e4c](https://github.com/resola-ai/deca-apps/commit/29a1e4c83739481137523f500f850fe0c32f315e))
- **chatbot:** tk-5211 fix get wrong data to update button node ([#4284](https://github.com/resola-ai/deca-apps/issues/4284)) ([3247cbe](https://github.com/resola-ai/deca-apps/commit/3247cbe621dff14aff744dd1c3a763d57bd88c8d))
- **chatbox:** TK-2438: setting to hide livechat history screen ([#4148](https://github.com/resola-ai/deca-apps/issues/4148)) ([2c2e6fb](https://github.com/resola-ai/deca-apps/commit/2c2e6fb4bdb859b1858026e4c6136993a7eb564a))
- **chatbox:** TK-4065: send display events to livechat ([#4159](https://github.com/resola-ai/deca-apps/issues/4159)) ([8fff0df](https://github.com/resola-ai/deca-apps/commit/8fff0dfbb2c99c22b79be2c8fadb9544463e2a43))
- **chatbox:** TK-4065: update payload, improve functions ([#4215](https://github.com/resola-ai/deca-apps/issues/4215)) ([7f7bf07](https://github.com/resola-ai/deca-apps/commit/7f7bf0710ca47e5e497d98e05908d74534f74cc9))
- **chatbox:** TK-4075: add buttonValue to payload ([#4257](https://github.com/resola-ai/deca-apps/issues/4257)) ([9740f08](https://github.com/resola-ai/deca-apps/commit/9740f08f08062618c49390f1de2e5b43f55a7732))
- **chatbox:** TK-4497: fix button ([#4131](https://github.com/resola-ai/deca-apps/issues/4131)) ([453f7fb](https://github.com/resola-ai/deca-apps/commit/453f7fb2bb81dba095f3ff7608c15b200af3552e))
- **chatbox:** TK-4497: improve form message, mobile UI for cw client ([#4123](https://github.com/resola-ai/deca-apps/issues/4123)) ([63da5ad](https://github.com/resola-ai/deca-apps/commit/63da5aded05c9562e13655b9fcdbd177c77cd444))
- **chatbox:** TK-4497: update text ([#4130](https://github.com/resola-ai/deca-apps/issues/4130)) ([31b72ca](https://github.com/resola-ai/deca-apps/commit/31b72cacb9d2b04cd9289e4b8362e9e1ce3972f9))
- **crm:** CRM - TK-5079 - update default value select type ([#4192](https://github.com/resola-ai/deca-apps/issues/4192)) ([dc8da80](https://github.com/resola-ai/deca-apps/commit/dc8da8092e622ef0c6c7915793ce69f08e797aa2))
- **crm:** improve context and component loading ([5ee891e](https://github.com/resola-ai/deca-apps/commit/5ee891ea61af8542ff10cc7857d73156fecc3e20))
- **crm:** improve context and component loading ([80b5737](https://github.com/resola-ai/deca-apps/commit/80b5737bb0e6b1740eeb7ce24e1d9ecac37ceb7f))
- **crm:** TK-3395 - Add export csv to view ([#4187](https://github.com/resola-ai/deca-apps/issues/4187)) ([137dcc2](https://github.com/resola-ai/deca-apps/commit/137dcc2bbd23c01729889d4b836bffc8048d5d9a))
- **crm:** TK-4351 Enhance table filter with relative date functionality ([#4282](https://github.com/resola-ai/deca-apps/issues/4282)) ([6bc97a6](https://github.com/resola-ai/deca-apps/commit/6bc97a6f7132d8884d72c7799d0c114c0532f760))
- **crm:** TK-4351 relative date with anchorField ([#4303](https://github.com/resola-ai/deca-apps/issues/4303)) ([786fe0a](https://github.com/resola-ai/deca-apps/commit/786fe0a7726e605b92f86519d296bb07650d7df5))
- **crm:** TK-5088 Improve CRM performance with reference object ([10c3416](https://github.com/resola-ai/deca-apps/commit/10c34164ad5bd9fd53f98dda581ed25fe02ccf6d))
- **form-admin:** TK-4297 Implement undo/redo feature for form builder ([#4185](https://github.com/resola-ai/deca-apps/issues/4185)) ([5a7750e](https://github.com/resola-ai/deca-apps/commit/5a7750eb6305c90e2c29ad25d55efe8751b552a3))
- **form-admin:** TK-5059 defined shortcut, hotkey for undo redo ([#4204](https://github.com/resola-ai/deca-apps/issues/4204)) ([065f731](https://github.com/resola-ai/deca-apps/commit/065f731084df8176990de3a78d961b9c5fa90f07))
- **form-admin:** TK-5099 - Handle null dates when updating form settings ([#4208](https://github.com/resola-ai/deca-apps/issues/4208)) ([d7e8479](https://github.com/resola-ai/deca-apps/commit/d7e8479e52b07c133690db1279dd1d4da334db02))
- **form-admin:** TK-5167 Reset History when out builder, fix auto trigger of Richtext ([#4235](https://github.com/resola-ai/deca-apps/issues/4235)) ([395965f](https://github.com/resola-ai/deca-apps/commit/395965ff6ba961fd08e78d509eaa478a9cceabd4))
- **general:** TK-4939 TK-5353 TK-5355 TK-5368 - add `resola-ai` as prefix to all packages and setup publish package ([#4287](https://github.com/resola-ai/deca-apps/issues/4287)) ([700b0a1](https://github.com/resola-ai/deca-apps/commit/700b0a14b52b8439186e105ca0b5da9796efea33))
- **kb:** [TK-4621] allow update access level before upload file ([#4145](https://github.com/resola-ai/deca-apps/issues/4145)) ([f4479df](https://github.com/resola-ai/deca-apps/commit/f4479df47f3cf9e8d4035bc39e5b5d33c57c7a70))
- **kb:** [TK-4800] implement import article via csv ([#4206](https://github.com/resola-ai/deca-apps/issues/4206)) ([207656b](https://github.com/resola-ai/deca-apps/commit/207656bf1dc1b0ed7978aa6da97a40611c498781))
- **kb:** Correct search with part of modifier was typing ([#4308](https://github.com/resola-ai/deca-apps/issues/4308)) ([a5e695e](https://github.com/resola-ai/deca-apps/commit/a5e695e344fcc51d174924a5a753be5ee203a522))
- **kb:** TK-2412 Correct reset payload when user change page size in Articles list ([#4153](https://github.com/resola-ai/deca-apps/issues/4153)) ([fe0be83](https://github.com/resola-ai/deca-apps/commit/fe0be83a830b2b5b2d0405a03f9e9bda34014e6a))
- **kb:** TK-2412 Implement page size selection in articles list ([7d1f1cd](https://github.com/resola-ai/deca-apps/commit/7d1f1cdb78f4411f06db3e1b09fccf5fb5ba303c))
- **kb:** TK-2883 Correct validation article content when creating new ([#4325](https://github.com/resola-ai/deca-apps/issues/4325)) ([4f7e815](https://github.com/resola-ai/deca-apps/commit/4f7e8157767efb3398543e982613dd59bd85ce6f))
- **kb:** TK-4728 Correct search entities map and sort in kb selection ([#4239](https://github.com/resola-ai/deca-apps/issues/4239)) ([ba8cd5d](https://github.com/resola-ai/deca-apps/commit/ba8cd5d13310bd2978b413f360999c0c171739d4))
- **kb:** TK-4728 Correct warning in DynamicAutocomplete ([#4314](https://github.com/resola-ai/deca-apps/issues/4314)) ([f4d2527](https://github.com/resola-ai/deca-apps/commit/f4d2527f1e25dbdf6d148ae000d7b897c8f0a7ab))
- **kb:** TK-4728 Implement advanced search component and replace to KB Homepage ([#4179](https://github.com/resola-ai/deca-apps/issues/4179)) ([de9d252](https://github.com/resola-ai/deca-apps/commit/de9d2524b034b57183d6c8ad93291a0c2804608b))
- **kb:** TK-4728 Improve behavior when user types modifier in search ([#4307](https://github.com/resola-ai/deca-apps/issues/4307)) ([ddebe4e](https://github.com/resola-ai/deca-apps/commit/ddebe4e4a18c5dae5f0550071c9d396fc96347c3))
- **kb:** TK-4728 Update search with translation label in search box and suggestion ([#4311](https://github.com/resola-ai/deca-apps/issues/4311)) ([109bb32](https://github.com/resola-ai/deca-apps/commit/109bb32ac8ed403676a8fcc1aa20f0bffc82905f))
- **kb:** TK-4732 Implement article generator activities and saving history ([#4150](https://github.com/resola-ai/deca-apps/issues/4150)) ([c40c6f5](https://github.com/resola-ai/deca-apps/commit/c40c6f5c6c0d5f769e678348652e18d6420fbf24))
- **kb:** TK-4886 Implemented search behavior and integrated search filter API ([#4221](https://github.com/resola-ai/deca-apps/issues/4221)) ([10915c6](https://github.com/resola-ai/deca-apps/commit/10915c6d7d7a48679050094143df2998182ef9eb))
- **kb:** TK-4886 Update keyphrase field in search filter to keyword ([#4319](https://github.com/resola-ai/deca-apps/issues/4319)) ([bdfee02](https://github.com/resola-ai/deca-apps/commit/bdfee02f47e491e1b5cdc5dcd2d734ad5de86760))
- **livechat:** TK-3853 display-read-status ([#4203](https://github.com/resola-ai/deca-apps/issues/4203)) ([e592904](https://github.com/resola-ai/deca-apps/commit/e592904ef90aacddbb82815faccbe93ef167d3ec))
- **livechat:** TK-3853 update-implementation-due-be-change ([#4214](https://github.com/resola-ai/deca-apps/issues/4214)) ([d4ea3fe](https://github.com/resola-ai/deca-apps/commit/d4ea3fea17cd0833bd1ab03678ff554b1cab7945))
- **livechat:** TK-4063 indicating user typing ([#4189](https://github.com/resola-ai/deca-apps/issues/4189)) ([66e7e01](https://github.com/resola-ai/deca-apps/commit/66e7e014012c705bcfbf9c36be28552150887aa2))
- **livechat:** TK-4063 TK-4064 indicating-user-typing ([#4144](https://github.com/resola-ai/deca-apps/issues/4144)) ([b90175b](https://github.com/resola-ai/deca-apps/commit/b90175ba2d06277f40ddffad5aca5b5a3498f8bf))
- **livechat:** TK-4063 typing-effect-update-timeout ([849c030](https://github.com/resola-ai/deca-apps/commit/849c0303d5c95443b5f3e82e6b6e2bae3517fcad))
- **livechat:** TK-4064 implement-focus-status ([#4229](https://github.com/resola-ai/deca-apps/issues/4229)) ([9c056a9](https://github.com/resola-ai/deca-apps/commit/9c056a973f34e5aa6cb306d3416b07731e96b63c))
- **livechat:** TK-4064 small-update-to-notify-immediately ([#4231](https://github.com/resola-ai/deca-apps/issues/4231)) ([4c17278](https://github.com/resola-ai/deca-apps/commit/4c17278ae64d5985f76ea4fd6a2ab722aa9f094b))
- **livechat:** TK-4064 update-to-persist-data ([#4248](https://github.com/resola-ai/deca-apps/issues/4248)) ([1db4c85](https://github.com/resola-ai/deca-apps/commit/1db4c8597c2e73ade8051f39f46b1bf081cd5e46))
- **livechat:** TK-4804 update-fifo-flag-automation ([#4164](https://github.com/resola-ai/deca-apps/issues/4164)) ([18035e9](https://github.com/resola-ai/deca-apps/commit/18035e9ca66874af2e072db82a31a08703bf9024))
- **livechat:** TK-4834 add-line-emoji-support ([#4183](https://github.com/resola-ai/deca-apps/issues/4183)) ([d199bf4](https://github.com/resola-ai/deca-apps/commit/d199bf4120ad40cc75b740a852ba315b996ade3f))
- **livechat:** TK-4835 fìfo-flag-for-automation ([#4122](https://github.com/resola-ai/deca-apps/issues/4122)) ([5e0500b](https://github.com/resola-ai/deca-apps/commit/5e0500ba3b160ec00f8187a8f9f1cad8d73b5834))
- **page-admin:** TK-3812 Add Active state for Menu ([#4119](https://github.com/resola-ai/deca-apps/issues/4119)) ([1112f83](https://github.com/resola-ai/deca-apps/commit/1112f8376a93174e98aff750362dc9bb686ef2bc))
- **page-admin:** TK-3826 - Add site settings management and asset upload functionality ([#4216](https://github.com/resola-ai/deca-apps/issues/4216)) ([4d18f4e](https://github.com/resola-ai/deca-apps/commit/4d18f4e2bb7307b8838f8f569c24045033abcb62))
- **page-admin:** TK-4234 add sample section ([#4173](https://github.com/resola-ai/deca-apps/issues/4173)) ([f1f950b](https://github.com/resola-ai/deca-apps/commit/f1f950beb7ba611c07fe4531b5c7227a9c27b58e))
- **page-admin:** TK-4235 fix closing toolbar when dropping elements ([#4147](https://github.com/resola-ai/deca-apps/issues/4147)) ([55982cf](https://github.com/resola-ai/deca-apps/commit/55982cf21f58955f30b2f2b96e3f3be107b34265))
- **page-admin:** TK-4243 - Implement version history management with API integration ([#4230](https://github.com/resola-ai/deca-apps/issues/4230)) ([205bfe5](https://github.com/resola-ai/deca-apps/commit/205bfe5a955f636d0da21e8bd90d76335577e3a6))
- **page-admin:** TK-4364 update text submenu ([#4103](https://github.com/resola-ai/deca-apps/issues/4103)) ([9e13619](https://github.com/resola-ai/deca-apps/commit/9e1361937a91c6bc137ad8af7f4627d26a77afd2))
- **page-admin:** TK-4384 add FAQ element ([#4195](https://github.com/resola-ai/deca-apps/issues/4195)) ([87260aa](https://github.com/resola-ai/deca-apps/commit/87260aa7ba14cbe6a018a4b5f364b23a256b896f))
- **page-admin:** TK-4384 add temp save button ([#4234](https://github.com/resola-ai/deca-apps/issues/4234)) ([468fdb9](https://github.com/resola-ai/deca-apps/commit/468fdb9425251b3d4fd5c37e6ab39bb92517daa3))
- **page-admin:** TK-4384 update translation for FAQ ([#4205](https://github.com/resola-ai/deca-apps/issues/4205)) ([d5cb892](https://github.com/resola-ai/deca-apps/commit/d5cb892b4967b170535ce8d03bd0fc16a98dc58b))
- **page-admin:** TK-4385 Add picture category element with settings ([#4286](https://github.com/resola-ai/deca-apps/issues/4286)) ([5cfc202](https://github.com/resola-ai/deca-apps/commit/5cfc202b3342318c77eec6f32c313916fa0302be))
- **page-admin:** TK-4574 Implement create new page ([#4172](https://github.com/resola-ai/deca-apps/issues/4172)) ([ba9ec01](https://github.com/resola-ai/deca-apps/commit/ba9ec0156bfcff074ab39644e94217fc766ec454))
- **page-admin:** TK-4857 add utils header ([#4196](https://github.com/resola-ai/deca-apps/issues/4196)) ([015671a](https://github.com/resola-ai/deca-apps/commit/015671accc2c0eff1841084ffbd5ef59834f2617))
- **page-admin:** TK-4861 Fix issue with opening toolbar ([#4290](https://github.com/resola-ai/deca-apps/issues/4290)) ([9643eec](https://github.com/resola-ai/deca-apps/commit/9643eece26c0ce03ccc8fb10feca606b90f1f566))
- **page-admin:** TK-4861 Implement page management ([#4278](https://github.com/resola-ai/deca-apps/issues/4278)) ([7a8d600](https://github.com/resola-ai/deca-apps/commit/7a8d60047dc159b79064a5bfea251047a199215e))
- **page-admin:** TK-4867 add article detail embed ([#4262](https://github.com/resola-ai/deca-apps/issues/4262)) ([d694eaf](https://github.com/resola-ai/deca-apps/commit/d694eaf18554a2e9946948677d5bdb14fe2c9805))
- **page-admin:** TK-4943 - Refine the container element ([#4271](https://github.com/resola-ai/deca-apps/issues/4271)) ([0ea7097](https://github.com/resola-ai/deca-apps/commit/0ea709752bf1cf371dc103ddb08edc4e93a289ba))
- **tables:** handle infinite scroll for table ([b8c3d6b](https://github.com/resola-ai/deca-apps/commit/b8c3d6bb2a458dbcb547384012f39c9168363efc))
- **tables:** TK-2790 fix wrong total count filter ([516389a](https://github.com/resola-ai/deca-apps/commit/516389ae532614730868854b964443c4612c2061))
- **tables:** TK-3627: create a fixed status bar ui with mock data ([#4306](https://github.com/resola-ai/deca-apps/issues/4306)) ([8dcc3fa](https://github.com/resola-ai/deca-apps/commit/8dcc3fa8718f4df9f42f5b46885eea71fc69ccdd))
- **tables:** TK-4128 handle insert column ([0714f02](https://github.com/resola-ai/deca-apps/commit/0714f0260037507d156a200e60b8120d879922e4))
- **tables:** TK-4639 update custom fields state to not show deleted fields ([8cd449c](https://github.com/resola-ai/deca-apps/commit/8cd449cc153dda25b3aa2edfb1cb8f20d44353f9))
- **tables:** TK-4640 decompress record data ([10976c6](https://github.com/resola-ai/deca-apps/commit/10976c66fd1e877c4acc50e90ec764ea0d0c17f8))
- **tables:** TK-4768 remove unused code ([15c97cf](https://github.com/resola-ai/deca-apps/commit/15c97cf89bea244fd9bf86bd74362faaec99ee18))
- **tables:** TK-4768 support resize table column ([43b1ffb](https://github.com/resola-ai/deca-apps/commit/43b1ffb3efc696c964d0c692a387b23062930b89))
- **widget-builder:** TK-3825 fix initial state ([928d52f](https://github.com/resola-ai/deca-apps/commit/928d52fb1b6c9b1c4e570500533f125905203e21))
- **widget-builder:** TK-3825 handle file change properly ([66b0103](https://github.com/resola-ai/deca-apps/commit/66b0103b4550317864bf9100780600fa971865fe))
- **widget-builder:** TK-3825 implement file upload ([3401d11](https://github.com/resola-ai/deca-apps/commit/3401d115ee0980ff2b196d7e37ca4ce1fb697bb0))
- **widget-builder:** TK-3900 update translations ([5b72d35](https://github.com/resola-ai/deca-apps/commit/5b72d3539fe5b8110cef58767a0d39216940b8ee))
- **widget-builder:** TK-3900 use widget settings for meta info ([151ced3](https://github.com/resola-ai/deca-apps/commit/151ced38f708a5390e1a09f8dd2dda6f1852df0c))
- **widget-builder:** TK-4015 implement file auto save ([c1f544c](https://github.com/resola-ai/deca-apps/commit/c1f544c95f4d7e1961984a9eebc6d9cabe640aaf))
- **widget-builder:** TK-4015 implement file auto save logic ([e2ff5c2](https://github.com/resola-ai/deca-apps/commit/e2ff5c23d1ff85abf2d3b7817d1baa0843cb684a))
- **widget-builder:** TK-4015 reorganise services ([264af39](https://github.com/resola-ai/deca-apps/commit/264af399e8780749ee3a5d96d204f9e3ffde5b1a))
- **widget-builder:** TK-4750 update no result UI for search widget ([#4126](https://github.com/resola-ai/deca-apps/issues/4126)) ([7e78cc7](https://github.com/resola-ai/deca-apps/commit/7e78cc70ea2d3dc8573d79e7b3b9b4f5389cb9c6))
- **widget-builder:** TK-4831 integrate publish widget ([#4222](https://github.com/resola-ai/deca-apps/issues/4222)) ([dea2942](https://github.com/resola-ai/deca-apps/commit/dea294216b1f80c9cbbef4159fcbf0aefc973fed))
- **widget-builder:** TK-5018 update widget builder UI and text resources ([#4193](https://github.com/resola-ai/deca-apps/issues/4193)) ([4418799](https://github.com/resola-ai/deca-apps/commit/4418799f378ab22b7242a772fec6b17f1d6e132f))
- **workshop:** TK-2322 TK-2324 TK-2325 TK-2326 TK-2336 - Add components for tabbar, popover, inputs, and inline message ([#4198](https://github.com/resola-ai/deca-apps/issues/4198)) ([57d2873](https://github.com/resola-ai/deca-apps/commit/57d2873e3b2f9451e4d36a5c560991bd3cde920e))

### Bug Fixes

- **ai-widgets:** TK-4378 trigger validate after restore default prompt ([#4228](https://github.com/resola-ai/deca-apps/issues/4228)) ([9e74103](https://github.com/resola-ai/deca-apps/commit/9e7410391788941a43f010a80e5c8d4a70a064ce))
- **ai-widgets:** TK-5057 validate reply assistant prompt setting ([#4209](https://github.com/resola-ai/deca-apps/issues/4209)) ([bef8378](https://github.com/resola-ai/deca-apps/commit/bef83783979008640ac298f393651af5ea2defb7))
- **chatbot:** TK-3988 Set limit relate article ([#4125](https://github.com/resola-ai/deca-apps/issues/4125)) ([9224696](https://github.com/resola-ai/deca-apps/commit/9224696a8600d66fc7754d73f664fd450cba44ab))
- **chatbot:** TK-4620 Mapping error code for chatbot ([#4180](https://github.com/resola-ai/deca-apps/issues/4180)) ([e75fbb1](https://github.com/resola-ai/deca-apps/commit/e75fbb1d62aaee8be9a34ea7bd1d129a790d029d))
- **chatbot:** TK-4921 Add dot color to ChatLogs page and update responsive ([#4270](https://github.com/resola-ai/deca-apps/issues/4270)) ([86fa004](https://github.com/resola-ai/deca-apps/commit/86fa00404bd433326945b07b3f74378d1b952f8f))
- **chatbot:** TK-4921 Display Session ID in Log UI with Color Coding ([#4240](https://github.com/resola-ai/deca-apps/issues/4240)) ([e8d23ee](https://github.com/resola-ai/deca-apps/commit/e8d23ee10b6b199bd3b83bfe2f858b0e243d08d7))
- **chatbot:** TK-4949 Update FE to disable intents related feature ([#4140](https://github.com/resola-ai/deca-apps/issues/4140)) ([0af2e59](https://github.com/resola-ai/deca-apps/commit/0af2e599f27e12941f02bc73d2594d7463509c29))
- **chatbot:** TK-5022 Update min max number of reference article ([#4165](https://github.com/resola-ai/deca-apps/issues/4165)) ([1fc2812](https://github.com/resola-ai/deca-apps/commit/1fc281252a90c61531212308c09f205b86dce0f7))
- **chatbot:** tk-5347 revert to text field in table ([#4313](https://github.com/resola-ai/deca-apps/issues/4313)) ([aa1b584](https://github.com/resola-ai/deca-apps/commit/aa1b584f03f5d3e4c718d40882252937463c51b8))
- **chatbot:** TK-5349 Add mapping for new error code Chatbot ([#4285](https://github.com/resola-ai/deca-apps/issues/4285)) ([0527a08](https://github.com/resola-ai/deca-apps/commit/0527a08b50cfd6d6203a6f36d25cc1b5dc829fb2))
- **chatbox:** TK-3962: avoid email address when replace non-protocol url ([0ef5c09](https://github.com/resola-ai/deca-apps/commit/0ef5c09d8225bd6e849fa65d2976580d3aca2bb9))
- **crm:** make cell render correctly on virtual column ([3499fed](https://github.com/resola-ai/deca-apps/commit/3499fed2cad27b3509117bbd97cd639c53b84186))
- **crm:** make relative dates work correctly with timezone ([26d31e1](https://github.com/resola-ai/deca-apps/commit/26d31e14bc71ee9e911bed1a2681edb545f09aa3))
- **crm:** TK-4459 - update mutate object when deleting colums ([#4243](https://github.com/resola-ai/deca-apps/issues/4243)) ([753053f](https://github.com/resola-ai/deca-apps/commit/753053fdd4e9d7b8ed5382b479c875698b0a1dec))
- **crm:** TK-4459 - update mutate object when deleting colums ([#4243](https://github.com/resola-ai/deca-apps/issues/4243)) ([2213d69](https://github.com/resola-ai/deca-apps/commit/2213d69d456c1096d21e31744a302aa9f616c8b0))
- **crm:** TK-5169 TK-5171 cannot move the column ([#4233](https://github.com/resola-ai/deca-apps/issues/4233)) ([cf5b016](https://github.com/resola-ai/deca-apps/commit/cf5b016392edd7cae10b314cd0dd822a28be347d))
- **crm:** TK-5178 revalidate when update view ([0621709](https://github.com/resola-ai/deca-apps/commit/0621709d5760d4b70461d2b0392d051826e4c5a8))
- **crm:** tk-5202 ([#4265](https://github.com/resola-ai/deca-apps/issues/4265)) ([b6867c0](https://github.com/resola-ai/deca-apps/commit/b6867c09d73ac7210dbf9b049c14361e1bebcd20))
- **form-admin:** TK-2875 Fix field chekcbox and radio color picker overlap ([#4107](https://github.com/resola-ai/deca-apps/issues/4107)) ([de028dc](https://github.com/resola-ai/deca-apps/commit/de028dc5100e3ad6c38d4a62ffc3d030a0109981))
- **form-admin:** TK-3146 Back to Home on create template page ([#4158](https://github.com/resola-ai/deca-apps/issues/4158)) ([edb10f2](https://github.com/resola-ai/deca-apps/commit/edb10f25943c8d59f8e4fa7e519f05fc6229f257))
- **form-admin:** TK-3689 Update integration icon ([#4117](https://github.com/resola-ai/deca-apps/issues/4117)) ([237e0f6](https://github.com/resola-ai/deca-apps/commit/237e0f6484fde43d68b6c4682aa1459e485691ca))
- **form-admin:** TK-4402 apply button comps ([#4157](https://github.com/resola-ai/deca-apps/issues/4157)) ([c004dbd](https://github.com/resola-ai/deca-apps/commit/c004dbdf7de482ba4aab40a4fb51eab9ce462df0))
- **form-admin:** TK-4481 Add missing icons in List View ([#4190](https://github.com/resola-ai/deca-apps/issues/4190)) ([51de8a1](https://github.com/resola-ai/deca-apps/commit/51de8a11d8f939f6edaffbe100199db5f3e2a47d))
- **form-admin:** TK-4481 Center align the number ([#4212](https://github.com/resola-ai/deca-apps/issues/4212)) ([653633a](https://github.com/resola-ai/deca-apps/commit/653633a12f1a68c6260007787161230f41eefd57))
- **form-admin:** TK-4809 Open detail on mount ([#4295](https://github.com/resola-ai/deca-apps/issues/4295)) ([84c4c3f](https://github.com/resola-ai/deca-apps/commit/84c4c3fb8b88b31114b78aab720aca9440cd03c5))
- **form-admin:** TK-4817 Using closet because in select option could be element, not text ([#4259](https://github.com/resola-ai/deca-apps/issues/4259)) ([82680a8](https://github.com/resola-ai/deca-apps/commit/82680a86c8a2ae353e944512bee324a944413195))
- **form-admin:** TK-4819 Adjustment for template deletion ([#4118](https://github.com/resola-ai/deca-apps/issues/4118)) ([4636abb](https://github.com/resola-ai/deca-apps/commit/4636abb68a79ddf7b6d84d0e8b357bc0b2db5fa2))
- **form-admin:** TK-4833 - The user cannot add email notification on the integration page ([#4129](https://github.com/resola-ai/deca-apps/issues/4129)) ([b644286](https://github.com/resola-ai/deca-apps/commit/b644286b20852d1bb07e6f5e84505576e9cbfc67))
- **form-admin:** TK-4971 Dont allow deslect datetime and upload file options ([#4264](https://github.com/resola-ai/deca-apps/issues/4264)) ([df45161](https://github.com/resola-ai/deca-apps/commit/df4516171f9475b3e9745894fdd58de697effdeb))
- **form-admin:** TK-4971 Dont allow deslect datetime and upload file options ([#4266](https://github.com/resola-ai/deca-apps/issues/4266)) ([ca0119f](https://github.com/resola-ai/deca-apps/commit/ca0119f996b17d07f30774ae9a2bd1782645eaf5))
- **form-admin:** TK-4972 - Fix the form field couldn't search ([#4163](https://github.com/resola-ai/deca-apps/issues/4163)) ([1178aab](https://github.com/resola-ai/deca-apps/commit/1178aabfe6932dc981ec69b07c58585a7c1b3c33))
- **form-admin:** TK-5009 update height size in result page ([#4194](https://github.com/resola-ai/deca-apps/issues/4194)) ([5d0e67f](https://github.com/resola-ai/deca-apps/commit/5d0e67f79f5b582cc979895de29f2348941f3a36))
- **form-admin:** TK-5167 Add default flag to not adding to history ([#4258](https://github.com/resola-ai/deca-apps/issues/4258)) ([42204fa](https://github.com/resola-ai/deca-apps/commit/42204fa1d89bd758bc3d67f97b3846194d4fb509))
- **form-admin:** TK-5167 Fix history duplicated ([#4256](https://github.com/resola-ai/deca-apps/issues/4256)) ([b10ed12](https://github.com/resola-ai/deca-apps/commit/b10ed12e9f6e2d9703cd49775c7f965dd12d6a14))
- **form-admin:** TK-5167 Init json is not added into history middleware ([#4296](https://github.com/resola-ai/deca-apps/issues/4296)) ([0bb513e](https://github.com/resola-ai/deca-apps/commit/0bb513e8b276c9d0336ff21e4cf01339b604e5de))
- **general:** update amplify yml and vite version ([#4069](https://github.com/resola-ai/deca-apps/issues/4069)) ([fc70d59](https://github.com/resola-ai/deca-apps/commit/fc70d59521e4ab060612bf92cdb6d430135bd051))
- **kb:** [TK-4621] UI break when filename too long ([#4272](https://github.com/resola-ai/deca-apps/issues/4272)) ([b203ebf](https://github.com/resola-ai/deca-apps/commit/b203ebf35269f294758978bac8260d2b9a450e46))
- **kb:** [TK-4800] dont reload articles after import ([#4247](https://github.com/resola-ai/deca-apps/issues/4247)) ([8f477d9](https://github.com/resola-ai/deca-apps/commit/8f477d9f371a1942fedb8227a8f8cd805c82f67b))
- **kb:** [TK-4800] make component ImportModal can dyanmic with multiple usecase ([#4244](https://github.com/resola-ai/deca-apps/issues/4244)) ([d3f6c7c](https://github.com/resola-ai/deca-apps/commit/d3f6c7c4573e5c64c7d59b35252066b5edc994f5))
- **kb:** [TK-4800] show error when upload success + update position dropdown + update public link for template ([#4237](https://github.com/resola-ai/deca-apps/issues/4237)) ([831f409](https://github.com/resola-ai/deca-apps/commit/831f40992e79bad7d8563b61fdd9e0b50707dd8e))
- **kb:** [TK-4800] update template import article ([#4294](https://github.com/resola-ai/deca-apps/issues/4294)) ([ec22089](https://github.com/resola-ai/deca-apps/commit/ec220897790d7dd7b378b43e12a6c2963299561b))
- **kb:** [TK-4800] update text KB name + lineClamp file name + handle success ([#4241](https://github.com/resola-ai/deca-apps/issues/4241)) ([b9cfa1d](https://github.com/resola-ai/deca-apps/commit/b9cfa1d3f1f3674ecd9a3746879ddd05cf24d36d))
- **kb:** [TK-5147] prevent double click when upload files ([#4213](https://github.com/resola-ai/deca-apps/issues/4213)) ([cc06903](https://github.com/resola-ai/deca-apps/commit/cc0690317e9718707e516d0a42d1e3794cf89352))
- **kb:** [TK-5147] prevent double click when upload files ([#4220](https://github.com/resola-ai/deca-apps/issues/4220)) ([1983519](https://github.com/resola-ai/deca-apps/commit/19835190e0b0e6bd0f30e817e29b969eb17360eb))
- **kb:** TK-3972 Correct editor initial content updating ([#4279](https://github.com/resola-ai/deca-apps/issues/4279)) ([f2f18cb](https://github.com/resola-ai/deca-apps/commit/f2f18cbfdf7f85ca0c4dda0d6a80be271a8f9cd8))
- **kb:** TK-4886 Correct missing label after selecting search option ([#4315](https://github.com/resola-ai/deca-apps/issues/4315)) ([f99c663](https://github.com/resola-ai/deca-apps/commit/f99c663b6cebd03174858abc08d99ba2dd32beed))
- **kb:** TK-4961 Correct break line in template title ([#4250](https://github.com/resola-ai/deca-apps/issues/4250)) ([d63f911](https://github.com/resola-ai/deca-apps/commit/d63f91108f1128f2c4ecb896a92b1b4cec709de5))
- **kb:** TK-4961 Correct error from SVG style when loading in article modal ([#4227](https://github.com/resola-ai/deca-apps/issues/4227)) ([072da9f](https://github.com/resola-ai/deca-apps/commit/072da9fef427f08f9fcb33c21bb4fcaef07ffb4e))
- **kb:** TK-4961 Correct long text break line in Job Article ([#4254](https://github.com/resola-ai/deca-apps/issues/4254)) ([7389850](https://github.com/resola-ai/deca-apps/commit/738985026df8e6cd321f2169abf84eb10237202f))
- **kb:** TK-4961 Correct SVG size rendering error and sorting in KB selection ([#4246](https://github.com/resola-ai/deca-apps/issues/4246)) ([0a3421a](https://github.com/resola-ai/deca-apps/commit/0a3421a02cbe61b2ebdd97cdaf324a6fca8156cb))
- **kb:** TK-5030 Corrected cannot save article with empty text content ([#4181](https://github.com/resola-ai/deca-apps/issues/4181)) ([17cf74e](https://github.com/resola-ai/deca-apps/commit/17cf74e35df8b8d46744b4438cf0c27771b75af0))
- **kb:** TK-5030 Improve article editor validation to fix saving error ([#4197](https://github.com/resola-ai/deca-apps/issues/4197)) ([84ccb5f](https://github.com/resola-ai/deca-apps/commit/84ccb5f34b72501929e2ea0e535c54633232a802))
- **livechat:** fix-could-not-load-more-message-next-page ([b68f948](https://github.com/resola-ai/deca-apps/commit/b68f9484e863fb276556e6a8239186171b3f3d3f))
- **livechat:** TK-4723 - Fix English text displaying as Japanese ([#4260](https://github.com/resola-ai/deca-apps/issues/4260)) ([7377647](https://github.com/resola-ai/deca-apps/commit/73776474ed95c251b211f22ed8b84fb8d7a5e02c))
- **livechat:** TK-4984 TK-4983 hotfix/livechat/could-not-load-more-conversation ([af0daa1](https://github.com/resola-ai/deca-apps/commit/af0daa15ae1a075bd16c60a9fb7a373505868324))
- **livechat:** TK-4984 TK-4983 hotfix/livechat/could-not-load-more-conversation ([e075d85](https://github.com/resola-ai/deca-apps/commit/e075d85bbbd97beb858fc05967b864594f14844f))
- **livechat:** TK-4984 TK-4983 hotfix/livechat/could-not-load-more-conversation ([9b90693](https://github.com/resola-ai/deca-apps/commit/9b906932e92af972597d1e956ed3ebff53f85d8b))
- **livechat:** TK-4984 TK-4983 some-issues ([#4152](https://github.com/resola-ai/deca-apps/issues/4152)) ([c4ab793](https://github.com/resola-ai/deca-apps/commit/c4ab79325e8fb324356a7656e6bf5f674268f863))
- **livechat:** TK-4997 bookmark-issue ([#4267](https://github.com/resola-ai/deca-apps/issues/4267)) ([655d86f](https://github.com/resola-ai/deca-apps/commit/655d86feff6ce1778a326273fdb5951c4d71015b))
- **livechat:** TK-4997 mismatch-bookmark-when-wrapup ([#4161](https://github.com/resola-ai/deca-apps/issues/4161)) ([fa6d3f9](https://github.com/resola-ai/deca-apps/commit/fa6d3f917c4e4f3b4e50c00c208ce7e916030fc5))
- **livechat:** TK-5274 fix-refetch-list-when-wrapping-up ([#4275](https://github.com/resola-ai/deca-apps/issues/4275)) ([21f78fa](https://github.com/resola-ai/deca-apps/commit/21f78fae528179085b9284f2954ca953e326fd0d))
- **livechat:** tk-fix-cannot-load-old-messages  ([#4166](https://github.com/resola-ai/deca-apps/issues/4166)) ([ef74b47](https://github.com/resola-ai/deca-apps/commit/ef74b47841082f68f80cb9962258d4c267959561))
- **livechat:** tk-improve-timer-typing-effect ([#4292](https://github.com/resola-ai/deca-apps/issues/4292)) ([02f03c6](https://github.com/resola-ai/deca-apps/commit/02f03c68a6bf9bbee5ee2a3282c0738fb69d8387))
- **livechat:** TK-next-page-bookmark-screen-disappear ([#4283](https://github.com/resola-ai/deca-apps/issues/4283)) ([721a5bf](https://github.com/resola-ai/deca-apps/commit/721a5bf22c992f8dca96e2f6e2a8b7e1cf60b85e))
- **page-admin:** TK-4366 Update translation update size settings ([#4106](https://github.com/resola-ai/deca-apps/issues/4106)) ([b6d2a55](https://github.com/resola-ai/deca-apps/commit/b6d2a5575338a36cc9d3c7c9ba5aa7a5c85dbbc6))
- **page-admin:** TK-4367 Update settings for buttons and divider ([#4162](https://github.com/resola-ai/deca-apps/issues/4162)) ([5dd4352](https://github.com/resola-ai/deca-apps/commit/5dd4352153e6798f8ad68a301917021b7be52a43))
- **security:** update dompurify packages ([#4207](https://github.com/resola-ai/deca-apps/issues/4207)) ([9500f08](https://github.com/resola-ai/deca-apps/commit/9500f08457b4933ffb34c146ef8d3c07c7d15bbf))
- **shared:** fix unit test fail with no tests ([fd4a69a](https://github.com/resola-ai/deca-apps/commit/fd4a69a406936abd109ae6581d0330edc315c9b1))
- **tables:** TK-5309 fix filter and sort modal ([f68fd4a](https://github.com/resola-ai/deca-apps/commit/f68fd4a39f16f7e413c943577c2fbd4c8a4649c2))
- **widget-builder:** TK-4715 reload widget list after widget details update ([f25973c](https://github.com/resola-ai/deca-apps/commit/f25973c9454d62e99886a5994acadb7ebcfaed75))
- **widget-builder:** TK-4831 update publish widget flow ([#4245](https://github.com/resola-ai/deca-apps/issues/4245)) ([68cb9c4](https://github.com/resola-ai/deca-apps/commit/68cb9c41d8c6669639ff7b50a30706fc18173c2e))
- **widget-builder:** TK-5023 update editor config and behavior ([#4171](https://github.com/resola-ai/deca-apps/issues/4171)) ([9fc7f07](https://github.com/resola-ai/deca-apps/commit/9fc7f07dbb7ffca3c7568e03f086bab2577eb0e5))
- **widget-builder:** TK-5066 update search and remove old tab editor when switch widgets ([#4188](https://github.com/resola-ai/deca-apps/issues/4188)) ([dc066f3](https://github.com/resola-ai/deca-apps/commit/dc066f304cfa0f54900867c894ab9403569dced0))
- **widget-builder:** TK-5067 update widget details and editor ([#4211](https://github.com/resola-ai/deca-apps/issues/4211)) ([507fabf](https://github.com/resola-ai/deca-apps/commit/507fabf5d917935c71bb4d11a5b5b5bb3b1e612d))
- **widget-builder:** TK-5098 add type and settings field to widget creation payload ([766ce42](https://github.com/resola-ai/deca-apps/commit/766ce423dab50d147c9b441b3a07c027a7482382))

## [0.8.0](https://github.com-resola/resola-ai/deca-apps/compare/<EMAIL>@0.8.0) (2025-02-25)

### Features

- **general:** TK-4939 TK-5353 TK-5355 TK-5368 - add `resola-ai` as prefix to all packages and setup publish package ([#4287](https://github.com-resola/resola-ai/deca-apps/issues/4287)) ([700b0a1](https://github.com-resola/resola-ai/deca-apps/commit/700b0a14b52b8439186e105ca0b5da9796efea33))
- **kb:** [TK-4621] allow update access level before upload file ([#4145](https://github.com-resola/resola-ai/deca-apps/issues/4145)) ([f4479df](https://github.com-resola/resola-ai/deca-apps/commit/f4479df47f3cf9e8d4035bc39e5b5d33c57c7a70))
- **kb:** [TK-4800] implement import article via csv ([#4206](https://github.com-resola/resola-ai/deca-apps/issues/4206)) ([207656b](https://github.com-resola/resola-ai/deca-apps/commit/207656bf1dc1b0ed7978aa6da97a40611c498781))
- **kb:** Correct search with part of modifier was typing ([#4308](https://github.com-resola/resola-ai/deca-apps/issues/4308)) ([a5e695e](https://github.com-resola/resola-ai/deca-apps/commit/a5e695e344fcc51d174924a5a753be5ee203a522))
- **kb:** TK-2412 Correct reset payload when user change page size in Articles list ([#4153](https://github.com-resola/resola-ai/deca-apps/issues/4153)) ([fe0be83](https://github.com-resola/resola-ai/deca-apps/commit/fe0be83a830b2b5b2d0405a03f9e9bda34014e6a))
- **kb:** TK-2412 Implement page size selection in articles list ([7d1f1cd](https://github.com-resola/resola-ai/deca-apps/commit/7d1f1cdb78f4411f06db3e1b09fccf5fb5ba303c))
- **kb:** TK-4728 Correct search entities map and sort in kb selection ([#4239](https://github.com-resola/resola-ai/deca-apps/issues/4239)) ([ba8cd5d](https://github.com-resola/resola-ai/deca-apps/commit/ba8cd5d13310bd2978b413f360999c0c171739d4))
- **kb:** TK-4728 Correct warning in DynamicAutocomplete ([#4314](https://github.com-resola/resola-ai/deca-apps/issues/4314)) ([f4d2527](https://github.com-resola/resola-ai/deca-apps/commit/f4d2527f1e25dbdf6d148ae000d7b897c8f0a7ab))
- **kb:** TK-4728 Implement advanced search component and replace to KB Homepage ([#4179](https://github.com-resola/resola-ai/deca-apps/issues/4179)) ([de9d252](https://github.com-resola/resola-ai/deca-apps/commit/de9d2524b034b57183d6c8ad93291a0c2804608b))
- **kb:** TK-4728 Improve behavior when user types modifier in search ([#4307](https://github.com-resola/resola-ai/deca-apps/issues/4307)) ([ddebe4e](https://github.com-resola/resola-ai/deca-apps/commit/ddebe4e4a18c5dae5f0550071c9d396fc96347c3))
- **kb:** TK-4728 Update search with translation label in search box and suggestion ([#4311](https://github.com-resola/resola-ai/deca-apps/issues/4311)) ([109bb32](https://github.com-resola/resola-ai/deca-apps/commit/109bb32ac8ed403676a8fcc1aa20f0bffc82905f))
- **kb:** TK-4732 Implement article generator activities and saving history ([#4150](https://github.com-resola/resola-ai/deca-apps/issues/4150)) ([c40c6f5](https://github.com-resola/resola-ai/deca-apps/commit/c40c6f5c6c0d5f769e678348652e18d6420fbf24))
- **kb:** TK-4886 Implemented search behavior and integrated search filter API ([#4221](https://github.com-resola/resola-ai/deca-apps/issues/4221)) ([10915c6](https://github.com-resola/resola-ai/deca-apps/commit/10915c6d7d7a48679050094143df2998182ef9eb))
- **kb:** TK-2883 Correct validation article content when creating new ([#4325](https://github.com/resola-ai/deca-apps/pull/4325)) ([36779a3](https://github.com-resola/resola-ai/deca-apps/commit/36779a3389982b663e6302296798b07c602fa250))

### Bug Fixes

- **general:** update amplify yml and vite version ([#4069](https://github.com-resola/resola-ai/deca-apps/issues/4069)) ([fc70d59](https://github.com-resola/resola-ai/deca-apps/commit/fc70d59521e4ab060612bf92cdb6d430135bd051))
- **kb:** [TK-4621] UI break when filename too long ([#4272](https://github.com-resola/resola-ai/deca-apps/issues/4272)) ([b203ebf](https://github.com-resola/resola-ai/deca-apps/commit/b203ebf35269f294758978bac8260d2b9a450e46))
- **kb:** [TK-4800] dont reload articles after import ([#4247](https://github.com-resola/resola-ai/deca-apps/issues/4247)) ([8f477d9](https://github.com-resola/resola-ai/deca-apps/commit/8f477d9f371a1942fedb8227a8f8cd805c82f67b))
- **kb:** [TK-4800] make component ImportModal can dyanmic with multiple usecase ([#4244](https://github.com-resola/resola-ai/deca-apps/issues/4244)) ([d3f6c7c](https://github.com-resola/resola-ai/deca-apps/commit/d3f6c7c4573e5c64c7d59b35252066b5edc994f5))
- **kb:** [TK-4800] show error when upload success + update position dropdown + update public link for template ([#4237](https://github.com-resola/resola-ai/deca-apps/issues/4237)) ([831f409](https://github.com-resola/resola-ai/deca-apps/commit/831f40992e79bad7d8563b61fdd9e0b50707dd8e))
- **kb:** [TK-4800] update template import article ([#4294](https://github.com-resola/resola-ai/deca-apps/issues/4294)) ([ec22089](https://github.com-resola/resola-ai/deca-apps/commit/ec220897790d7dd7b378b43e12a6c2963299561b))
- **kb:** [TK-4800] update text KB name + lineClamp file name + handle success ([#4241](https://github.com-resola/resola-ai/deca-apps/issues/4241)) ([b9cfa1d](https://github.com-resola/resola-ai/deca-apps/commit/b9cfa1d3f1f3674ecd9a3746879ddd05cf24d36d))
- **kb:** [TK-5147] prevent double click when upload files ([#4213](https://github.com-resola/resola-ai/deca-apps/issues/4213)) ([cc06903](https://github.com-resola/resola-ai/deca-apps/commit/cc0690317e9718707e516d0a42d1e3794cf89352))
- **kb:** [TK-5147] prevent double click when upload files ([#4220](https://github.com-resola/resola-ai/deca-apps/issues/4220)) ([1983519](https://github.com-resola/resola-ai/deca-apps/commit/19835190e0b0e6bd0f30e817e29b969eb17360eb))
- **kb:** TK-3972 Correct editor initial content updating ([#4279](https://github.com-resola/resola-ai/deca-apps/issues/4279)) ([f2f18cb](https://github.com-resola/resola-ai/deca-apps/commit/f2f18cbfdf7f85ca0c4dda0d6a80be271a8f9cd8))
- **kb:** TK-4886 Correct missing label after selecting search option ([#4315](https://github.com-resola/resola-ai/deca-apps/issues/4315)) ([f99c663](https://github.com-resola/resola-ai/deca-apps/commit/f99c663b6cebd03174858abc08d99ba2dd32beed))
- **kb:** TK-4961 Correct break line in template title ([#4250](https://github.com-resola/resola-ai/deca-apps/issues/4250)) ([d63f911](https://github.com-resola/resola-ai/deca-apps/commit/d63f91108f1128f2c4ecb896a92b1b4cec709de5))
- **kb:** TK-4961 Correct error from SVG style when loading in article modal ([#4227](https://github.com-resola/resola-ai/deca-apps/issues/4227)) ([072da9f](https://github.com-resola/resola-ai/deca-apps/commit/072da9fef427f08f9fcb33c21bb4fcaef07ffb4e))
- **kb:** TK-4961 Correct long text break line in Job Article ([#4254](https://github.com-resola/resola-ai/deca-apps/issues/4254)) ([7389850](https://github.com-resola/resola-ai/deca-apps/commit/738985026df8e6cd321f2169abf84eb10237202f))
- **kb:** TK-4961 Correct SVG size rendering error and sorting in KB selection ([#4246](https://github.com-resola/resola-ai/deca-apps/issues/4246)) ([0a3421a](https://github.com-resola/resola-ai/deca-apps/commit/0a3421a02cbe61b2ebdd97cdaf324a6fca8156cb))
- **kb:** TK-5030 Corrected cannot save article with empty text content ([#4181](https://github.com-resola/resola-ai/deca-apps/issues/4181)) ([17cf74e](https://github.com-resola/resola-ai/deca-apps/commit/17cf74e35df8b8d46744b4438cf0c27771b75af0))
- **kb:** TK-5030 Improve article editor validation to fix saving error ([#4197](https://github.com-resola/resola-ai/deca-apps/issues/4197)) ([84ccb5f](https://github.com-resola/resola-ai/deca-apps/commit/84ccb5f34b72501929e2ea0e535c54633232a802))
- **kb:** TK-4886 Update keyPhrase field in search filter to keyword ([#4319](https://github.com-resola/resola-ai/deca-apps/issues/4319)) ([5ba9aa9](https://github.com/resola-ai/deca-apps/pull/4319/commits/5ba9aa94af5b1c07771fbe1d9212d9b5752cbab4))
- **security:** update dompurify packages ([#4207](https://github.com-resola/resola-ai/deca-apps/issues/4207)) ([9500f08](https://github.com-resola/resola-ai/deca-apps/commit/9500f08457b4933ffb34c146ef8d3c07c7d15bbf))
- **shared:** fix unit test fail with no tests ([fd4a69a](https://github.com-resola/resola-ai/deca-apps/commit/fd4a69a406936abd109ae6581d0330edc315c9b1))

## [0.7.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.7.0) (2025-02-05)

### Features

- **kb:** TK-4647 Update document author name logic ([#4042](https://github.com/resola-ai/deca-apps/issues/4042)) ([7311b47](https://github.com/resola-ai/deca-apps/commit/7311b4709479494c22244ab32219819345a87fff))
- **kb:** TK-4650 Update default prompt in Job Generator ([#4041](https://github.com/resola-ai/deca-apps/issues/4041)) ([2e4929d](https://github.com/resola-ai/deca-apps/commit/2e4929da0ff38675a6fc87cba00646a4d2610941))
- **kb:** TK-4754 Apply permission to article generator and document ([#4059](https://github.com/resola-ai/deca-apps/issues/4059)) ([b9eef85](https://github.com/resola-ai/deca-apps/commit/b9eef8575fcf733124cd3c1453d4e9fb325b329a))

### Bug Fixes

- **general:** update amplify yml and vite version ([#4069](https://github.com/resola-ai/deca-apps/issues/4069)) ([eabc3ec](https://github.com/resola-ai/deca-apps/commit/eabc3ec66d0598f054b767774028be21de0a79e3))
- **general:** update amplify yml and vite version ([#4069](https://github.com/resola-ai/deca-apps/issues/4069)) ([0577593](https://github.com/resola-ai/deca-apps/commit/057759385f6ea53782b0053228ee231c8231ca20))
- **kb:** TK-4647 Correct Markdown detect in blocknote editor ([#4047](https://github.com/resola-ai/deca-apps/issues/4047)) ([ae72d4e](https://github.com/resola-ai/deca-apps/commit/ae72d4eb93145ad76b436ba3b8df552cc9cfcc71))
- **kb:** TK-4647 Correct retry job success message ([#4037](https://github.com/resola-ai/deca-apps/issues/4037)) ([37f9e0e](https://github.com/resola-ai/deca-apps/commit/37f9e0e11cff712e39c4279ef79bba2b55dc3540))
- **kb:** TK-4650, TK-4643, TK-4647 Update default prompt and adjust UI for Job Generator ([#4034](https://github.com/resola-ai/deca-apps/issues/4034)) ([803b783](https://github.com/resola-ai/deca-apps/commit/803b783a49589d19c02c406300fb4de060e34cc7))
- **kb:** TK-4824 Correct cannot move article when has related articles ([#4090](https://github.com/resola-ai/deca-apps/issues/4090)) ([4d16516](https://github.com/resola-ai/deca-apps/commit/4d165168d2acabef1f59b4a59753bb223f3e6901))
- **security:** bump zod version ([#4092](https://github.com/resola-ai/deca-apps/issues/4092)) ([d15a888](https://github.com/resola-ai/deca-apps/commit/d15a888904fed5b7237366e5a9f982b2ea1f1eaf))
- **security:** node-fetch forwards secure headers to untrusted sites ([#4097](https://github.com/resola-ai/deca-apps/issues/4097)) ([4998742](https://github.com/resola-ai/deca-apps/commit/49987427737618dabce2d983189c013046a28724))
- **security:** update library version ([ba47139](https://github.com/resola-ai/deca-apps/commit/ba47139c07009c24d565810ceda279a9e237618e))
- **security:** update package to handle ReDOS in cross-spawn ([#4096](https://github.com/resola-ai/deca-apps/issues/4096)) ([72c3088](https://github.com/resola-ai/deca-apps/commit/72c3088fea8daa2366b7ab47915cca4457004f50))
- **security:** update vitest version ([#4088](https://github.com/resola-ai/deca-apps/issues/4088)) ([99ae5c9](https://github.com/resola-ai/deca-apps/commit/99ae5c98edb0c7c3a486065facbcc8b2b70728f2))

### [0.6.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.6.2) (2025-01-25)

### Bug Fixes

- **kb:** TK-4647 Correct Markdown detect in blocknote editor ([#4047](https://github.com/resola-ai/deca-apps/issues/4047)) ([be9508e](https://github.com/resola-ai/deca-apps/commit/be9508e1f83b18a1117bab7fc5076459c77be433))

### [0.6.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.6.1) (2025-01-24)

### Features

- **kb:** TK-4647 Update document author name logic ([#4042](https://github.com/resola-ai/deca-apps/issues/4042)) ([98092f3](https://github.com/resola-ai/deca-apps/commit/98092f324d48dc47f5f4d1587ff01f7cb1b7165c))
- **kb:** TK-4650 Update default prompt in Job Generator ([#4041](https://github.com/resola-ai/deca-apps/issues/4041)) ([cda6f43](https://github.com/resola-ai/deca-apps/commit/cda6f43ae581ab0d9f11440a44133ca22ff4b093))

### Bug Fixes

- **kb:** TK-4647 Correct retry job success message ([#4037](https://github.com/resola-ai/deca-apps/issues/4037)) ([da4d4e4](https://github.com/resola-ai/deca-apps/commit/da4d4e4ca1c3d10864057018d7466713053b4ee8))
- **kb:** TK-4650, TK-4643, TK-4647 Update default prompt and adjust UI for Job Generator ([#4034](https://github.com/resola-ai/deca-apps/issues/4034)) ([429fba2](https://github.com/resola-ai/deca-apps/commit/429fba2644bd69b02e67a490471ab8c5404e02ab))

## [0.6.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.6.0) (2025-01-23)

### Features

- **general:** migrate mantine v7 ([#3423](https://github.com/resola-ai/deca-apps/issues/3423)) ([ccc3551](https://github.com/resola-ai/deca-apps/commit/ccc3551a2a17ee4d848b3dcaf00a561295f71747)), closes [#3622](https://github.com/resola-ai/deca-apps/issues/3622)
- **general:** TK-4358 - can select env from the given list ([ffc6a08](https://github.com/resola-ai/deca-apps/commit/ffc6a0853ba5e3a5a85ad02ddc3c9cb8d697fcfb))
- **general:** TK-4358 - create workflow for manually trigger any amplify build - with update on scripts and constants ([#3982](https://github.com/resola-ai/deca-apps/issues/3982)) ([d78ffb8](https://github.com/resola-ai/deca-apps/commit/d78ffb8941c4017f6b87cbc7d3f78d9855f7bc76))
- **general:** TK-4358 - improve manual-trigger-amplify-build script ([7dcf90e](https://github.com/resola-ai/deca-apps/commit/7dcf90ede479b558d204b82916ab299f0e5f7fc2))
- **general:** TK-4358 - improve manual-trigger-amplify-build script ([ec76197](https://github.com/resola-ai/deca-apps/commit/ec76197adcd790881c086a9a4269008f33c151f1))
- **general:** TK-4358 - improve manual-trigger-amplify-build script ([8df2327](https://github.com/resola-ai/deca-apps/commit/8df2327121a80b7a7da4c6044988fac1ebf479fb))
- **general:** TK-4358 - improve manual-trigger-amplify-build script ([956cc39](https://github.com/resola-ai/deca-apps/commit/956cc396e91a61b98565decc26803cb624911962))
- **general:** Upgrade mantine to latest version v7.16.1, fix storybook issue, upgrade storybook to latest verison, migrate demo app to mantine latest version ([#4013](https://github.com/resola-ai/deca-apps/issues/4013)) ([733e4c1](https://github.com/resola-ai/deca-apps/commit/733e4c14a0414e0c04db09f7c50a41d32ebbbdf3))
- **kb:** [TK-3763][TK-3766] Handle view list + detail document files ([#3948](https://github.com/resola-ai/deca-apps/issues/3948)) ([d95c658](https://github.com/resola-ai/deca-apps/commit/d95c65864e3f65579d307236078ea17db8d0e39f))
- **kb:** [TK-3766] handle download file via dialog ([#4024](https://github.com/resola-ai/deca-apps/issues/4024)) ([342ce00](https://github.com/resola-ai/deca-apps/commit/342ce00f85e16ca134b84bed9f8007ba3b1b0721))
- **kb:** [TK-3766] integrate API download + handle reload + intergrate update access level ([#3991](https://github.com/resola-ai/deca-apps/issues/3991)) ([2270d29](https://github.com/resola-ai/deca-apps/commit/2270d290d13effb2f22c9d964365c1ff4c4ef7a6))
- **kb:** [TK-3767] update search result show files ([#3965](https://github.com/resola-ai/deca-apps/issues/3965)) ([b44df37](https://github.com/resola-ai/deca-apps/commit/b44df3709876e52af37fe957619c70fb7b369a63))
- **kb:** TK-3700 Correct width in Drawer and improve loading state ([#3995](https://github.com/resola-ai/deca-apps/issues/3995)) ([c803374](https://github.com/resola-ai/deca-apps/commit/c803374efbd0194b038c69b5169c1328ba58b086))
- **kb:** TK-3700 Implement job article editing and saving and correct some UI issues ([#3993](https://github.com/resola-ai/deca-apps/issues/3993)) ([0fb6b99](https://github.com/resola-ai/deca-apps/commit/0fb6b99f0f4ae1ef0bf202b5227c6c5691180db5))
- **kb:** TK-3759 Implement select and save Job Articles to KB ([#3946](https://github.com/resola-ai/deca-apps/issues/3946)) ([02864ef](https://github.com/resola-ai/deca-apps/commit/02864ef728e624548a05a2ad4bed906a77f2d1b0))
- **kb:** TK-4282 Enable article generator feature after Sprint 2 released ([#3907](https://github.com/resola-ai/deca-apps/issues/3907)) ([b5a1d79](https://github.com/resola-ai/deca-apps/commit/b5a1d79b9d278309df2e9b8338992b0138e123a1))
- **kb:** TK-4282 Enabled document upload feature ([#3910](https://github.com/resola-ai/deca-apps/issues/3910)) ([e00ac98](https://github.com/resola-ai/deca-apps/commit/e00ac9829d14b6bc4e968b2b4215ecbfaad3745a))
- **kb:** TK-4282 Implement loading state and improve drawer width styles ([#4022](https://github.com/resola-ai/deca-apps/issues/4022)) ([5a5c78e](https://github.com/resola-ai/deca-apps/commit/5a5c78ee513bf80a9ad54a13fe2a8eef13131676))
- **kb:** TK-4282 Integrate Job createdBy and correct some feedback ([#4007](https://github.com/resola-ai/deca-apps/issues/4007)) ([8c68f0a](https://github.com/resola-ai/deca-apps/commit/8c68f0a59f69da39eb12fc79f26a10988c885e7c))
- **kb:** TK-4282 Integrate Job generation error message and handle job deleting ([#3957](https://github.com/resola-ai/deca-apps/issues/3957)) ([5b2d0c6](https://github.com/resola-ai/deca-apps/commit/5b2d0c65e50c38b9240b9e56bbd1d6816eae35f6))
- **kb:** TK-4282 integrate retry API to job generator ([#4002](https://github.com/resola-ai/deca-apps/issues/4002)) ([0c3117e](https://github.com/resola-ai/deca-apps/commit/0c3117eab7fb5a1334163e93b40c38015d5d5577))
- **kb:** TK-4467 Correct some UI issues in KB after synced new Mantine version ([#3928](https://github.com/resola-ai/deca-apps/issues/3928)) ([db9c078](https://github.com/resola-ai/deca-apps/commit/db9c078e59169764f62bda1cb3d26d72019635d8))
- **kb:** TK-4467, TK-4544 Correct date range picker feedback and adjust UI related to Mantine ([#3967](https://github.com/resola-ai/deca-apps/issues/3967)) ([5739818](https://github.com/resola-ai/deca-apps/commit/57398182232a6af13f70e24bbe444c9dd14d2724))
- **kb:** TK-4544 Correct and force filter date in Japan timezone ([#3973](https://github.com/resola-ai/deca-apps/issues/3973)) ([9376b76](https://github.com/resola-ai/deca-apps/commit/9376b7637e759a8bb9898f9060f73a6125066a5c))
- **kb:** TK-4544 Correct end of date time for Japan query time ([#3970](https://github.com/resola-ai/deca-apps/issues/3970)) ([f9175bc](https://github.com/resola-ai/deca-apps/commit/f9175bc97e1b0a51bb4dfe32b941fc02f1d72454))

### Bug Fixes

- **kb:** [TK-4593] The file was not uploaded to the correct folder selected ([#4003](https://github.com/resola-ai/deca-apps/issues/4003)) ([7b6bff3](https://github.com/resola-ai/deca-apps/commit/7b6bff334dcfc5b74b36081b8147df89a404972a))
- **kb:** [TK-4606][TK-4633] load more articles + kbs into article selector, missing folder in folder page ([#4014](https://github.com/resola-ai/deca-apps/issues/4014)) ([6ddc546](https://github.com/resola-ai/deca-apps/commit/6ddc54617b4cc35e59c62526fdcfd5f50907dd2a))
- **kb:** TK-4282 Fix search layout and resolve conflicts ([#4012](https://github.com/resola-ai/deca-apps/issues/4012)) ([aaf7ca5](https://github.com/resola-ai/deca-apps/commit/aaf7ca5b3ff58f7630e8c7a261ecca1a9fec3ec3))
- **kb:** TK-4467 Adjust some UI issues related to Mantine component ([#4001](https://github.com/resola-ai/deca-apps/issues/4001)) ([c97ebf9](https://github.com/resola-ai/deca-apps/commit/c97ebf960eb4a4993570f9bab271aad5302df23d))

## [0.5.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.0) (2025-01-14)

### Features

- **kb:** [TK-3695] Update UI to support Related Articles field ([98c4655](https://github.com/resola-ai/deca-apps/commit/98c4655e8d1c3dff5e4c176833aabd4786dec818))
- **kb:** [TK-3699] integrate api job detail ([#3851](https://github.com/resola-ai/deca-apps/issues/3851)) ([ab7c687](https://github.com/resola-ai/deca-apps/commit/ab7c687549b1a06e77eda36f94a6d0d207ebfba9))
- **kb:** TK-3159 Add Chatwindow to kb article analytic product ([#3706](https://github.com/resola-ai/deca-apps/issues/3706)) ([0ff8cb9](https://github.com/resola-ai/deca-apps/commit/0ff8cb9063c51840ff7dc52d447c8b27609b4914))
- **kb:** TK-3159 Add Chatwindow to kb article analytic product ([#3706](https://github.com/resola-ai/deca-apps/issues/3706)) ([32abf95](https://github.com/resola-ai/deca-apps/commit/32abf9534cf1fe9a776bf588b4c58ab91f55b846))
- **kb:** TK-3574 Implement generator jobs page with grid layout ([#3796](https://github.com/resola-ai/deca-apps/issues/3796)) ([c73fad7](https://github.com/resola-ai/deca-apps/commit/c73fad7cb49c3d0ed37f2cd330b773a02e709a96))
- **kb:** TK-3698 Apply KB Access Control permission logic to UI ([#3830](https://github.com/resola-ai/deca-apps/issues/3830)) ([5d5e859](https://github.com/resola-ai/deca-apps/commit/5d5e859c271fa696c0b529b35e6a5c6a3d73187c))
- **kb:** TK-3698 Correct message namespace in article moving ([#3890](https://github.com/resola-ai/deca-apps/issues/3890)) ([2e9ba4c](https://github.com/resola-ai/deca-apps/commit/2e9ba4c4a27c49924e86e027e6807929b7e2f227))
- **kb:** TK-3698 Grant access member to Comment CRUD ([#3841](https://github.com/resola-ai/deca-apps/issues/3841)) ([2766a85](https://github.com/resola-ai/deca-apps/commit/2766a8527a106c47d2282f77c70eb8cd99de5e1d))
- **kb:** TK-3699 Implement UI and prepared context generator Job detail page ([#3813](https://github.com/resola-ai/deca-apps/issues/3813)) ([4955c3c](https://github.com/resola-ai/deca-apps/commit/4955c3c2d059322aae40768dad8228be9c827338))
- **kb:** TK-3716 Implement Document type files in KB ([e481451](https://github.com/resola-ai/deca-apps/commit/e4814518f07cff2344bcaf8b4977f324a48c7d81))
- **kb:** TK-3720 Implement skeleton for article analytic loading state ([#3712](https://github.com/resola-ai/deca-apps/issues/3712)) ([255480b](https://github.com/resola-ai/deca-apps/commit/255480bab60694067693297c4cba93ea8493fda6))
- **kb:** TK-3720 Implement skeleton for article analytic loading state ([#3712](https://github.com/resola-ai/deca-apps/issues/3712)) ([72e2afd](https://github.com/resola-ai/deca-apps/commit/72e2afdbfdaca9978e3a73c28e27d8ec38a8c8fe))
- **kb:** TK-3757 Add article generator button to KB detail ([#3768](https://github.com/resola-ai/deca-apps/issues/3768)) ([b1c1eff](https://github.com/resola-ai/deca-apps/commit/b1c1effdd01e63849b4cff5cd741e17427e5683d))
- **kb:** TK-3757 Correct article text translation in Comments and Analytics component ([#3770](https://github.com/resola-ai/deca-apps/issues/3770)) ([6ab6bc6](https://github.com/resola-ai/deca-apps/commit/6ab6bc699666458d1dca0df05f695a0d4cf43ed8))
- **kb:** TK-3758 Implement prompt customize modal and refactor modal manager ([#3779](https://github.com/resola-ai/deca-apps/issues/3779)) ([185de3a](https://github.com/resola-ai/deca-apps/commit/185de3a791418fdd04d4d3ebbe006223c8a1b108))
- **kb:** TK-4282 Correct infinite scrolling bottom in Jobs page ([#3881](https://github.com/resola-ai/deca-apps/issues/3881)) ([b10de8b](https://github.com/resola-ai/deca-apps/commit/b10de8b1e1ff538653812512d5763fef20e32f55))
- **kb:** TK-4282 Implement infinite scrolling in Jobs page ([#3859](https://github.com/resola-ai/deca-apps/issues/3859)) ([0fac9a4](https://github.com/resola-ai/deca-apps/commit/0fac9a4cb34c546673183b589af8118de1502766))
- **kb:** TK-4282 Integrate API in Document Selector and refactor Selector context ([#3849](https://github.com/resola-ai/deca-apps/issues/3849)) ([2355a61](https://github.com/resola-ai/deca-apps/commit/2355a616e72cb756a90e573f6e28d49e72f1aa3a))
- **kb:** TK-4282 Integrate Job generation and hide the article generator feature ([#3853](https://github.com/resola-ai/deca-apps/issues/3853)) ([777e015](https://github.com/resola-ai/deca-apps/commit/777e0157528b79a7dae260720263de39341f940a))
- **kb:** TK-4882 Apply context hook creator and handle open Job Article Detail ([#3878](https://github.com/resola-ai/deca-apps/issues/3878)) ([f82e65f](https://github.com/resola-ai/deca-apps/commit/f82e65fb9c22854042541e9ae4e0a6523f652a9d))

### Bug Fixes

- **kb:** [TK-3695] hover item moves slightly ([#3864](https://github.com/resola-ai/deca-apps/issues/3864)) ([0ae4f0a](https://github.com/resola-ai/deca-apps/commit/0ae4f0a59309221f36e7f7dc9cdd8409dfeb938a))
- **kb:** [TK-3695] Update link item, translate, access action, style + limit select article ([83a145d](https://github.com/resola-ai/deca-apps/commit/83a145d170a0ab4cf243e3de17c0e2794769444d))
- **kb:** TK-2884 Correct missing callback after created Article ([#3739](https://github.com/resola-ai/deca-apps/issues/3739)) ([5629d30](https://github.com/resola-ai/deca-apps/commit/5629d3065bc44ebb25c61522358a11915e2c820b))

### [0.4.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.1) (2024-12-23)

### Bug Fixes

- **kb:** TK-2884 Correct missing callback after created Article ([#3739](https://github.com/resola-ai/deca-apps/issues/3739)) ([a834a05](https://github.com/resola-ai/deca-apps/commit/a834a056401587f0ebc721faa8803e1f1708237f))

## [0.4.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.0) (2024-12-23)

### Features

- **kb:** TK-2530 Implement parse and render multiple lines when pasting to Editor ([e501acf](https://github.com/resola-ai/deca-apps/commit/e501acfc39b1be385defd68cdbca8c7974b3aacf))
- **kb:** TK-2884 Move Article Creating actions to drawer header and improve article detail providers ([96c7f87](https://github.com/resola-ai/deca-apps/commit/96c7f871b033a056e613bca91f350efe2a53f336))
- **kb:** TK-3036 Implement UI component for Article Analytics in KB ([cf62674](https://github.com/resola-ai/deca-apps/commit/cf6267476fa2b4c2ea1b9fc44f7fe467c420ee78))
- **kb:** TK-3159 Add Chatwindow to kb article analytic product ([#3706](https://github.com/resola-ai/deca-apps/issues/3706)) ([8b9b849](https://github.com/resola-ai/deca-apps/commit/8b9b8495a4c2b860398d38f7feeab608661f107a))
- **kb:** TK-3159 Implement Article Analytics schema and API Mock API integration ([a0820af](https://github.com/resola-ai/deca-apps/commit/a0820af1b8e35d3ff7a76a4fdb371af0768992d3))
- **kb:** TK-3159 Update date range format to adapt with API and improve Article detail provider hoc with forwardRef ([#3705](https://github.com/resola-ai/deca-apps/issues/3705)) ([e65198d](https://github.com/resola-ai/deca-apps/commit/e65198d318b85456fa777c4075f0bb25b96a1312))
- **kb:** TK-3160 Implement three dots menu in article detail with Move and Delete actions ([e7a3824](https://github.com/resola-ai/deca-apps/commit/e7a3824528f5705d34200043431f11e5e7fcb7e7))
- **kb:** TK-3382 Apply i18n locale setting to Mantine date picker ([0c1fb06](https://github.com/resola-ai/deca-apps/commit/0c1fb068fdcc226f400d148ac60f5590ba09ebc6))
- **kb:** TK-3720 Implement skeleton for article analytic loading state ([#3712](https://github.com/resola-ai/deca-apps/issues/3712)) ([e75254c](https://github.com/resola-ai/deca-apps/commit/e75254c0a61fc24b4dbd84117cecd2580368445d))

### Bug Fixes

- **kb:** CKL-733 Correct pasting behavior in Editor to prevent block replacement ([4de28b4](https://github.com/resola-ai/deca-apps/commit/4de28b47bd4add74343b496780785716c6cddc00))
- **kb:** TK-2886 Improved and corrected UI feedback in KB from biz team ([6244204](https://github.com/resola-ai/deca-apps/commit/6244204eb99f8af8f3449f297b005da6cce22836))
- **kb:** TK-3036 Correct date range validation before loading new data ([#3727](https://github.com/resola-ai/deca-apps/issues/3727)) ([7475082](https://github.com/resola-ai/deca-apps/commit/74750822c36eae20465bbbad127801c1551809a2))
- **kb:** TK-3090 Correct breadcrumb mapping field from API update ([476d9eb](https://github.com/resola-ai/deca-apps/commit/476d9ebdd5bd4ad497eb450af3fbb12270ba6f26))

### [0.3.13](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.13) (2024-11-12)

### Features

- **kb:** CKL-607 Created patch for new BlockNote to correct the type error and custom SideMenu from Editor ([276e82f](https://github.com/resola-ai/deca-apps/commit/276e82f0a76223f378c854d47046f7ca735040f4))
- **kb:** CKL-607 Downgrade BlockNote 0.18.0 to 0.17.1 to ignore build error related to regex rewrite ([e7b2097](https://github.com/resola-ai/deca-apps/commit/e7b2097e6a6ce8d37c87ef6364d91af61968eeb1))
- **kb:** CKL-607 Remove unused package in kb ([b884fd6](https://github.com/resola-ai/deca-apps/commit/b884fd6d99765e0afb38868c3bde2f7850b6fa4b))
- **kb:** CKL-607 Upgrade BlockNote version from 0.15.5 to 0.18.0 ([d3e7794](https://github.com/resola-ai/deca-apps/commit/d3e779455873e028bc36405d8ec70a8d0a79fa97))
- **kb:** CKL-679 Remove undo action and implement confirmation before moving article ([e4a72b5](https://github.com/resola-ai/deca-apps/commit/e4a72b59f5aa96f4d1ef701caa8c01e772c2cbae))
- **kb:** CKL-679 Update wording and breakline position for confirm message ([59902a3](https://github.com/resola-ai/deca-apps/commit/59902a3396d71d0405ebdabe0f0f435347b383e6))
- **kb:** CKL-690 Handle show warning modal when article is not ready to updated ([cfa68bf](https://github.com/resola-ai/deca-apps/commit/cfa68bf4566ec251f69accee6d9d2e31b949d17d))

### Bug Fixes

- **kb:** CKL-607 Refactor props in confirm modal component in KB app ([c630b0a](https://github.com/resola-ai/deca-apps/commit/c630b0ae0611e938634b1d1909b574629834528c))
- **kb:** CKL-694 Downgrade BlockNote 0.17.1 to 0.15.10 and correct issue cannot type text after link ([fb8f638](https://github.com/resola-ai/deca-apps/commit/fb8f638ee7ba7bd8d4f176985ce43ea14c18c5ee))
- **kb:** CKL-694 Revert CSS style of shadcn from old version 0.15.5 ([c4bd306](https://github.com/resola-ai/deca-apps/commit/c4bd3062ce59710f7efb7d5f1191706dc4419a90))
- **kb:** CKL-694 Reverted BlockNote version to 0.15.5 as before upgrading ([3382bc0](https://github.com/resola-ai/deca-apps/commit/3382bc038e12b6393133bcc3ad84e2f432429acf))
- **kb:** CKL-706 Implement search folder in moving article dialog ([30fa85b](https://github.com/resola-ai/deca-apps/commit/30fa85bcfe0e48c9b54992b0d8487f310c3e7028))
- **kb:** CKL-717 Show warning inform when user move or delete not ready Article ([fdb95cd](https://github.com/resola-ai/deca-apps/commit/fdb95cdfeb7b44b7d0e76d8cc4e29fefd9fb12c4))

### [0.3.12](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.12) (2024-10-29)

### Features

- **kb:** CKL-388 Implement UI for Folder and KB selection modals when moving KB and Article ([#3135](https://github.com/resola-ai/deca-apps/issues/3135)) ([4e14d4e](https://github.com/resola-ai/deca-apps/commit/4e14d4ed139a4e4fbb5bba68982865e2c2e6caeb))
- **kb:** CKL-388 Temp disabled moving action from KB card for next sprint ([#3138](https://github.com/resola-ai/deca-apps/issues/3138)) ([ed24b97](https://github.com/resola-ai/deca-apps/commit/ed24b97a791f4e017d6baf52a30970695d13e2ee))
- **kb:** CKL-419 Add notification and adjust auto focus in Template settings ([#2980](https://github.com/resola-ai/deca-apps/issues/2980)) ([e19f458](https://github.com/resola-ai/deca-apps/commit/e19f458ad847f3aaf3a8fc96d62b0d52e846924a))
- **kb:** CKL-419 Update saving behavior in Article template setting fields ([#2977](https://github.com/resola-ai/deca-apps/issues/2977)) ([bcc1865](https://github.com/resola-ai/deca-apps/commit/bcc18652ed8d508922e4c511b7d981b38ad72636))
- **kb:** CKL-421 Improve save button in keypharses input and custom field form ([64adaa7](https://github.com/resola-ai/deca-apps/commit/64adaa7c70d3178079d9a419f3ac9aaaf1194b0d))
- **kb:** CKL-440 Add validation message to keypharse input and improve validation ([b9d9318](https://github.com/resola-ai/deca-apps/commit/b9d9318937e6dcf2c09f03fdc7ee008012bd4ee9))
- **kb:** CKL-440 Improve keypharse validation from keywond to keyup binding ([ad7ab3d](https://github.com/resola-ai/deca-apps/commit/ad7ab3d6701e4ac799939648bcbc02eb6414eefc))
- **kb:** CKL-520 Support edit static field in Article Template ([#2878](https://github.com/resola-ai/deca-apps/issues/2878)) ([8df6ddb](https://github.com/resola-ai/deca-apps/commit/8df6ddbb3b8bc8a7a02594af79cf0e2f125cfabc))
- **kb:** CKL-521 Integrate and apply static field values from Article Template to article creating form ([#2918](https://github.com/resola-ai/deca-apps/issues/2918)) ([7139d2d](https://github.com/resola-ai/deca-apps/commit/7139d2d038794ee9f9b3be09d0681697c3221a96))
- **kb:** CKL-523 Add side bar menu to KB Mobile view ([#2901](https://github.com/resola-ai/deca-apps/issues/2901)) ([6a5fe0c](https://github.com/resola-ai/deca-apps/commit/6a5fe0c889f3069251bf95637a6e8d5ab938f543))
- **kb:** CKL-547 Adjust the break line in content when coping from BlockNote viewer ([#2954](https://github.com/resola-ai/deca-apps/issues/2954)) ([2a0d2c5](https://github.com/resola-ai/deca-apps/commit/2a0d2c53ff0cc90635472c0f25d605d7ce9f34dd))
- **kb:** CKL-547 Correct eslint warning in BlockNote viewer ([#2960](https://github.com/resola-ai/deca-apps/issues/2960)) ([c7fd9f1](https://github.com/resola-ai/deca-apps/commit/c7fd9f15384242430147c62e106d987cf8c56d93))
- **kb:** CKL-571 Integrate API for Article moving and update moving flow ([9001f2d](https://github.com/resola-ai/deca-apps/commit/9001f2d6679a9afc707249eb9f7893466b111029))
- **kb:** CKL-624 Correct function naming and whitespace in plain text ([86a455c](https://github.com/resola-ai/deca-apps/commit/86a455c0f64d1dfaa317b5eab4a9d0fbd8533ae7))
- **kb:** CKL-624 Support saving multiple spaces in BlockNote Editor and pasting from clipboard ([0f54c8d](https://github.com/resola-ai/deca-apps/commit/0f54c8d2cf7fef9f9d5b1a2c737dd229fb595c3f))
- **kb:** CKL-628 Support pasting markdown content into Block Editor ([0a49774](https://github.com/resola-ai/deca-apps/commit/0a49774cff4db699a31e1abd7040d4bf8e661928))
- **kb:** CKL-639 Implement searching function in Folder and KB selector when moving article ([17fefd7](https://github.com/resola-ai/deca-apps/commit/17fefd726e1586c744b346af6d18e8a3c1e92c30))
- **kb:** CKL-641 Implement undo action after moving to other KB ([0f8c1ea](https://github.com/resola-ai/deca-apps/commit/0f8c1ea47eb974a3164b57de684de3283609ee95))

### Bug Fixes

- **kb:** CKL-24 Correct the whitespace adjustment when copy from Editor ([a48d700](https://github.com/resola-ai/deca-apps/commit/a48d70066292d198371b6f26e3ccd7dc8ff70b3a))
- **kb:** CKL-419 Correct flicking issue when open popup in Content editor ([#3003](https://github.com/resola-ai/deca-apps/issues/3003)) ([8311715](https://github.com/resola-ai/deca-apps/commit/831171507d6e5944a1aba820f040e8eaa53f0299))
- **kb:** CKL-419 Correct input focusing in Content Editor ([#3007](https://github.com/resola-ai/deca-apps/issues/3007)) ([1dbdf1c](https://github.com/resola-ai/deca-apps/commit/1dbdf1c29f17ae89c84caa8eba84e0969446b0a0))
- **kb:** CKL-444 Correct descriptiuon and field orders in article template ([98ef588](https://github.com/resola-ai/deca-apps/commit/98ef588145385cbd8c71d7e50aa121c7e26b6d49))
- **kb:** CKL-445 Correct description label base on data type ([abfce4c](https://github.com/resola-ai/deca-apps/commit/abfce4ca4406865ec1b23604c19441a96b8bdfb7))
- **kb:** CKL-537 Correct some issues related to Article Template custom fields ([#2935](https://github.com/resola-ai/deca-apps/issues/2935)) ([f4dbffd](https://github.com/resola-ai/deca-apps/commit/f4dbffdf79a8159ae2eb70694da6ff83fa50128c))
- **kb:** CKL-543 Correct navigation when searching in kb ([#3016](https://github.com/resola-ai/deca-apps/issues/3016)) ([731f902](https://github.com/resola-ai/deca-apps/commit/731f90221c6959d14cc37f6122bbf6ac806e12e4))
- **kb:** CKL-571 Correct the draggable target when handling onBlur event ([#3034](https://github.com/resola-ai/deca-apps/issues/3034)) ([4c16d42](https://github.com/resola-ai/deca-apps/commit/4c16d422f82a15127bcc7a452f23719e684c15b5))
- **kb:** CKL-573 Correct default direction from articles collection ([#3052](https://github.com/resola-ai/deca-apps/issues/3052)) ([c57d7fc](https://github.com/resola-ai/deca-apps/commit/c57d7fcac7b57deb322caa685fa91bf0cde6e5ba))
- **kb:** CKL-573 Correct the sorting and adjust the breadcrumb rendering in Folder and KB ([#3037](https://github.com/resola-ai/deca-apps/issues/3037)) ([606e29b](https://github.com/resola-ai/deca-apps/commit/606e29b1bfc0410d33fc7d4a0615c4eae93eee78))
- **kb:** CKL-573 Custom pagination and improve the Articles fetching direction based on query ([#3042](https://github.com/resola-ai/deca-apps/issues/3042)) ([dc27ef4](https://github.com/resola-ai/deca-apps/commit/dc27ef49e41515478f7df6d5abadb0882eef53aa))
- **kb:** CKL-667 Correct infinite scrolling in article moving to load more kb ([5a3ce96](https://github.com/resola-ai/deca-apps/commit/5a3ce9650e22c1b2a6e3a65ff9cfa0a3489c6aeb))

### [0.3.11](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.11) (2024-10-15)

### Features

- **kb:** CKL-419 Add notification and adjust auto focus in Template settings ([#2980](https://github.com/resola-ai/deca-apps/issues/2980)) ([e19f458](https://github.com/resola-ai/deca-apps/commit/e19f458ad847f3aaf3a8fc96d62b0d52e846924a))
- **kb:** CKL-419 Update saving behavior in Article template setting fields ([#2977](https://github.com/resola-ai/deca-apps/issues/2977)) ([bcc1865](https://github.com/resola-ai/deca-apps/commit/bcc18652ed8d508922e4c511b7d981b38ad72636))
- **kb:** CKL-421 Improve save button in keypharses input and custom field form ([64adaa7](https://github.com/resola-ai/deca-apps/commit/64adaa7c70d3178079d9a419f3ac9aaaf1194b0d))
- **kb:** CKL-440 Add validation message to keypharse input and improve validation ([b9d9318](https://github.com/resola-ai/deca-apps/commit/b9d9318937e6dcf2c09f03fdc7ee008012bd4ee9))
- **kb:** CKL-440 Improve keypharse validation from keywond to keyup binding ([ad7ab3d](https://github.com/resola-ai/deca-apps/commit/ad7ab3d6701e4ac799939648bcbc02eb6414eefc))
- **kb:** CKL-520 Support edit static field in Article Template ([#2878](https://github.com/resola-ai/deca-apps/issues/2878)) ([8df6ddb](https://github.com/resola-ai/deca-apps/commit/8df6ddbb3b8bc8a7a02594af79cf0e2f125cfabc))
- **kb:** CKL-521 Integrate and apply static field values from Article Template to article creating form ([#2918](https://github.com/resola-ai/deca-apps/issues/2918)) ([7139d2d](https://github.com/resola-ai/deca-apps/commit/7139d2d038794ee9f9b3be09d0681697c3221a96))
- **kb:** CKL-523 Add side bar menu to KB Mobile view ([#2901](https://github.com/resola-ai/deca-apps/issues/2901)) ([6a5fe0c](https://github.com/resola-ai/deca-apps/commit/6a5fe0c889f3069251bf95637a6e8d5ab938f543))
- **kb:** CKL-547 Adjust the break line in content when coping from BlockNote viewer ([#2954](https://github.com/resola-ai/deca-apps/issues/2954)) ([2a0d2c5](https://github.com/resola-ai/deca-apps/commit/2a0d2c53ff0cc90635472c0f25d605d7ce9f34dd))
- **kb:** CKL-547 Correct eslint warning in BlockNote viewer ([#2960](https://github.com/resola-ai/deca-apps/issues/2960)) ([c7fd9f1](https://github.com/resola-ai/deca-apps/commit/c7fd9f15384242430147c62e106d987cf8c56d93))

### Bug Fixes

- **kb:** CKL-419 Correct flicking issue when open popup in Content editor ([#3003](https://github.com/resola-ai/deca-apps/issues/3003)) ([8311715](https://github.com/resola-ai/deca-apps/commit/831171507d6e5944a1aba820f040e8eaa53f0299))
- **kb:** CKL-419 Correct input focusing in Content Editor ([#3007](https://github.com/resola-ai/deca-apps/issues/3007)) ([1dbdf1c](https://github.com/resola-ai/deca-apps/commit/1dbdf1c29f17ae89c84caa8eba84e0969446b0a0))
- **kb:** CKL-444 Correct descriptiuon and field orders in article template ([98ef588](https://github.com/resola-ai/deca-apps/commit/98ef588145385cbd8c71d7e50aa121c7e26b6d49))
- **kb:** CKL-445 Correct description label base on data type ([abfce4c](https://github.com/resola-ai/deca-apps/commit/abfce4ca4406865ec1b23604c19441a96b8bdfb7))
- **kb:** CKL-537 Correct some issues related to Article Template custom fields ([#2935](https://github.com/resola-ai/deca-apps/issues/2935)) ([f4dbffd](https://github.com/resola-ai/deca-apps/commit/f4dbffdf79a8159ae2eb70694da6ff83fa50128c))
- **kb:** CKL-543 Correct navigation when searching in kb ([#3016](https://github.com/resola-ai/deca-apps/issues/3016)) ([731f902](https://github.com/resola-ai/deca-apps/commit/731f90221c6959d14cc37f6122bbf6ac806e12e4))
- **kb:** CKL-571 Correct the draggable target when handling onBlur event ([#3034](https://github.com/resola-ai/deca-apps/issues/3034)) ([4c16d42](https://github.com/resola-ai/deca-apps/commit/4c16d422f82a15127bcc7a452f23719e684c15b5))
- **kb:** CKL-573 Correct default direction from articles collection ([#3052](https://github.com/resola-ai/deca-apps/issues/3052)) ([c57d7fc](https://github.com/resola-ai/deca-apps/commit/c57d7fcac7b57deb322caa685fa91bf0cde6e5ba))
- **kb:** CKL-573 Correct the sorting and adjust the breadcrumb rendering in Folder and KB ([#3037](https://github.com/resola-ai/deca-apps/issues/3037)) ([606e29b](https://github.com/resola-ai/deca-apps/commit/606e29b1bfc0410d33fc7d4a0615c4eae93eee78))
- **kb:** CKL-573 Custom pagination and improve the Articles fetching direction based on query ([#3042](https://github.com/resola-ai/deca-apps/issues/3042)) ([dc27ef4](https://github.com/resola-ai/deca-apps/commit/dc27ef49e41515478f7df6d5abadb0882eef53aa))

### [0.3.10](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.10) (2024-10-01)

### Features

- **kb:** CKL-421 Improve save button in keypharses input and custom field form ([64adaa7](https://github.com/resola-ai/deca-apps/commit/64adaa7c70d3178079d9a419f3ac9aaaf1194b0d))
- **kb:** CKL-440 Add validation message to keypharse input and improve validation ([b9d9318](https://github.com/resola-ai/deca-apps/commit/b9d9318937e6dcf2c09f03fdc7ee008012bd4ee9))
- **kb:** CKL-440 Improve keypharse validation from keywond to keyup binding ([ad7ab3d](https://github.com/resola-ai/deca-apps/commit/ad7ab3d6701e4ac799939648bcbc02eb6414eefc))

### Bug Fixes

- **kb:** CKL-444 Correct description and field orders in article template ([98ef588](https://github.com/resola-ai/deca-apps/commit/98ef588145385cbd8c71d7e50aa121c7e26b6d49))
- **kb:** CKL-445 Correct description label base on data type ([abfce4c](https://github.com/resola-ai/deca-apps/commit/abfce4ca4406865ec1b23604c19441a96b8bdfb7))

### [0.3.9](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.9) (2024-09-19)

### Features

- **kb:** CKL-363 Implement the break lines in Markdown text card ([996a89a](https://github.com/resola-ai/deca-apps/commit/996a89a95191ef605d2f450238d6c4f4983db3fa))
- **kb:** CKL-438 Correct default value for Custom Field when creating ([6addc22](https://github.com/resola-ai/deca-apps/commit/6addc224e8110175c14947b12a2fb5c72e5b35c6))

### Bug Fixes

- **kb:** CKL-438 Update default value when update and change dataType in Custom field ([c30fec2](https://github.com/resola-ai/deca-apps/commit/c30fec2c956d21e1fb206411c61a4be852ab146d))
- **kb:** CKL-438 Update list value from string to array to adapt to API ([d482a5f](https://github.com/resola-ai/deca-apps/commit/d482a5fdba44d9e9c226139859504095ca8be592))

### [0.3.8](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.8) (2024-09-17)

### Features

--

### Bug Fixes

- **kb:** CKL-396 Correct render Custom Fields issue in Template Settings ([314ca9b](https://github.com/resola-ai/deca-apps/commit/314ca9b4bbde771f46703072107146ff9135ebf1))

### [0.3.7](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.7) (2024-09-17)

### Features

- **kb:** CKL-286 Implement the Settings Templates page with UI components only ([29360dd](https://github.com/resola-ai/deca-apps/commit/29360ddd9fda12e04fb09c77c0ff6de76262ecb1))
- **kb:** CKL-286 Implement the Templates Setting page ([8ee4877](https://github.com/resola-ai/deca-apps/commit/8ee4877a55c2935b110e24962fa63c46010566ad))
- **kb:** CKL-300 Implement the Template custom fields data ([4925897](https://github.com/resola-ai/deca-apps/commit/49258970d496ff5199c7626a11e1c23954c8e66c))
- **kb:** CKL-300 Implement the Template Detail page with template info editing form ([cd608f5](https://github.com/resola-ai/deca-apps/commit/cd608f57e25dae61c090abcdf174694053330aad))
- **kb:** CKL-301 Integrated API and adjust data render in Template Detail settings ([ac949a0](https://github.com/resola-ai/deca-apps/commit/ac949a0cab65eebcedd6591b2ae3bf3d4f09ab8b))
- **kb:** CKL-302 Create context and integrate some firstly APIs to Article Templates settings ([4c71a61](https://github.com/resola-ai/deca-apps/commit/4c71a6101f74e81b5908b9d4c0239b134b4e6e34))
- **kb:** CKL-360 Correct mapping data from Custom Data and adjust some UI issues ([7d86f3b](https://github.com/resola-ai/deca-apps/commit/7d86f3bd214aafdbb453431c64bc07bfa71926a2))
- **kb:** CKL-360 Implement UI and apply Default Template settings to Article Content form ([99ead1a](https://github.com/resola-ai/deca-apps/commit/99ead1a7951befafd266ad268fa8a2e43048d046))
- **kb:** CKL-396 Correct mapping order to custom fields ([acb5609](https://github.com/resola-ai/deca-apps/commit/acb5609bb335d84746a31bd89f5ee7f5194eb7b8))
- **kb:** CKL-396 Correct some UI issues and content related to Article Template ([cfdffd2](https://github.com/resola-ai/deca-apps/commit/cfdffd2c5663d0e6340e524bd900117cc9e004d5))
- **kb:** CKL-396 Correct update sorted fields after delete all fields ([339295a](https://github.com/resola-ai/deca-apps/commit/339295a07a9a174612f4cc2c1ba3df855186bc48))
- **kb:** CKL-398 Update flow for Article Template when applying to article create new ([bc07a66](https://github.com/resola-ai/deca-apps/commit/bc07a668e920c9942f5e6e484a0c0b43a864c18d))
- **kb:** CKL-399 Adjust Init custom data flow in Article when creating and updating ([806020c](https://github.com/resola-ai/deca-apps/commit/806020cd92701d40891c0e69b18e81e7810a3276))
- **kb:** CKL-399 Handle set default values for Custom Field Template ([039f21b](https://github.com/resola-ai/deca-apps/commit/039f21bb31b133fcd86f9097fed4ea78f9266a43))
- **kb:** CKL-399 Remove console.log ([2f2f992](https://github.com/resola-ai/deca-apps/commit/2f2f992e79c51a8ea92a3169f301312d000dc3aa))
- **kb:** CKL-404 Correct custom data missing when updating article ([9398ba6](https://github.com/resola-ai/deca-apps/commit/9398ba6f9edd5b70a94e0f4942a5ca237b9bfe0f))
- **kb:** CKL-404 Correct issue empty orders make the empty custom fiels ([fbf4ef7](https://github.com/resola-ai/deca-apps/commit/fbf4ef74d8dc3edfe598e68742bf7523361b8d44))
- **kb:** CKL-404 Correct issue when order field contain bad data from API executed ([b16ad54](https://github.com/resola-ai/deca-apps/commit/b16ad54c152c891d44f500c342bcefa8916dfda8))
- **kb:** CKL-404 Correct mismatched data and order in Custom Fields ([f6d6d6d](https://github.com/resola-ai/deca-apps/commit/f6d6d6dc7462c9f13f219d08b8b73944898ddaff))
- **kb:** CKL-404 Handle save and apply field orders in Article Template ([c55beaa](https://github.com/resola-ai/deca-apps/commit/c55beaa30dca5c8303879762e1d993846c36c33b))

### Bug Fixes

- **kb:** Show article feedback to Home page search ([#2532](https://github.com/resola-ai/deca-apps/issues/2532)) ([16649a8](https://github.com/resola-ai/deca-apps/commit/16649a8b546c14c54bf7bf58c17cd2d5a7ba5800))

### [0.3.6](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.6) (2024-09-04)

### Features

- **kb:** CKL-242 - Add Operator feedback feature ([#2417](https://github.com/resola-ai/deca-apps/issues/2417)) ([1c17b1c](https://github.com/resola-ai/deca-apps/commit/1c17b1cf4697e2d7427b2f7b9664a948fc877304))

### Bug Fixes

- **kb:** CKL-112 Adjust Router Navigation to work with Browser back/forward button ([9942d3b](https://github.com/resola-ai/deca-apps/commit/9942d3b73ca258cbe10e28be6c0b6616ad49c140))

### [0.3.5](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.5) (2024-08-26)

### Features

- **kb:** CKL-115 Support applying new logo to KB for previewing from design team ([df6f38b](https://github.com/resola-ai/deca-apps/commit/df6f38b08e23dd72daa9080baceec7d312396923))
- **kb:** CKL-147 Add label in the Link Toolbar form ([87578a8](https://github.com/resola-ai/deca-apps/commit/87578a863d169140c4e122b574cf09089c6b8f6d))
- **kb:** CKL-147 Correct callback dependencies in EditLinkMenuItems ([ad72f5e](https://github.com/resola-ai/deca-apps/commit/ad72f5e900bcb7cd3e211ccf5f491bf09c74a8e9))
- **kb:** CKL-147 Implement tel number hyperlink and customize the link toolbar ([1c85533](https://github.com/resola-ai/deca-apps/commit/1c855338b7188a6ab9ec0056e56309281b65a0ce))
- **kb:** CKL-213 Correct popover position for Edit Link Toolbar in Editor ([f0d0b7b](https://github.com/resola-ai/deca-apps/commit/f0d0b7bfb3349134ce7d016010a0041b3b6e0164))
- **kb:** CKL-213 Implement the checkbox to handle switching international format for Phone Number Input ([78d5679](https://github.com/resola-ai/deca-apps/commit/78d56798ef7de9e3973a32ee11e707bbcbdda098))
- **ui:** CKL-158 Apply new logo design to Forms, Tables, Meet, CRM ([169e062](https://github.com/resola-ai/deca-apps/commit/169e062b49fe9ea26647b88d4d5cdff1b7a6c4d9))
- **ui:** CKL-158 Correct dependencies in useMemo when detect logo by language ([b7cef98](https://github.com/resola-ai/deca-apps/commit/b7cef98d93332e5c0ab3c4174fc7f4039241c147))
- **ui:** CKL-158 Update new logos design for Chatwindow, Chatbot and KB ([6a04a93](https://github.com/resola-ai/deca-apps/commit/6a04a93ab9b679e8850c9f86b94ba46d7fe371c4))

### Bug Fixes

- **kb:** CB-1456 Correct bottom spacing and default access level in KB ([b6ad183](https://github.com/resola-ai/deca-apps/commit/b6ad1831dd2c208ded03a5d44fd2a7b855ab8086))
- **kb:** CB-1705 Correct typing issue in Color Input and tag H2 for Article Title ([b9eb75b](https://github.com/resola-ai/deca-apps/commit/b9eb75bb0ed0d49589b301d1e38c62befe5de178))
- **kb:** CB-1736 Corrected infinite scrolling behavior in KB Homepage ([0f9028b](https://github.com/resola-ai/deca-apps/commit/0f9028bd11f2d96b7fe0c6160142b4a44e53153c))
- **kb:** CKL-110 Keep pagination state after updated Article in page 2 ([0720465](https://github.com/resola-ai/deca-apps/commit/07204650dd0fc846c495d7e72dacbdd28e2f9f2d))
- **kb:** CKL-111 Correct dependencies and logic of useEffect in Articles context ([6186495](https://github.com/resola-ai/deca-apps/commit/61864952731781749d550b5063757344293311d4))
- **kb:** CKL-111 Improve state management in Articles context and correct search params behavior ([71dab77](https://github.com/resola-ai/deca-apps/commit/71dab77a8819d4d2fc866a9778ee43764430982b))
- **kb:** CKL-161 Remove unused logic related BlockNote break line issue because of new version corrected ([5c7a576](https://github.com/resola-ai/deca-apps/commit/5c7a576017af29864bffd5726597cd27c0908870))
- **kb:** CKL-161 Upgrade BlockNote version from 0.14.4 to 0.15.5 with pacthed to correct type and window ([c73b205](https://github.com/resola-ai/deca-apps/commit/c73b2055dc9e514eea10ef03e3f3489ccb5cf3de))
- **kb:** CKL-213 Correct input mask and phone number length for JP phone format ([8c64f91](https://github.com/resola-ai/deca-apps/commit/8c64f9150b4ab6c87f2b928379a862f34e0fdf17))
- **kb:** CKL-213 Correct text in label Link Toolbar ([ace90e5](https://github.com/resola-ai/deca-apps/commit/ace90e5834dfb687137b801118f7d9b7c3f9e70c))
- **kb:** CKL-213 Correct text transform in editor locales ([66156a2](https://github.com/resola-ai/deca-apps/commit/66156a28e34bdb6cac5b1456b7d2e50dba67cecb))
- **kb:** CKL-220 update save article notification when failed ([5e6d342](https://github.com/resola-ai/deca-apps/commit/5e6d342d6f969e59cfd16c083c566b9d22985485))
- **kb:** CKL-221 Fix the target empty inside content to handle break line correctly ([8dc8f5f](https://github.com/resola-ai/deca-apps/commit/8dc8f5f63020cb988922c22e69a22f3ec852f621))
- **kb:** CKL-230 Implement the infinite scroll in folder page to load more KB ([566a721](https://github.com/resola-ai/deca-apps/commit/566a721ea696d01efc7a26f95bd7809663248e60))
- **kb:** CKL-73 Adjusting spoiler for Article content that has media tags inside ([be999d3](https://github.com/resola-ai/deca-apps/commit/be999d3dd1b156df5a2403c9a6b69b795124a5fd))
- **kb:** CKL-73 Correct lint error in HTML message widget ([39989d8](https://github.com/resola-ai/deca-apps/commit/39989d80af66faa0e18ddf4a6e02ea69f9d7b6f7))
- **kb:** CKL-73 Correct the height of content for sploiler checking and icon size in Link Toolbar ([08307b4](https://github.com/resola-ai/deca-apps/commit/08307b4c22a4a79014d9869a9b7e9a29834d144d))
- **kb:** CKL-73 Correct width of html message in chatbox ([b506c96](https://github.com/resola-ai/deca-apps/commit/b506c96ef6afb510eb0f271c206cb21bdb41615a))

### [0.3.4](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.4) (2024-07-25)

### Features

- **kb:** CB-1661 Implement Color Picker and custom the styleSpec in BlockNote to handle hexacolor code ([b4bff67](https://github.com/resola-ai/deca-apps/commit/b4bff6763159ea4ff8e3a37d61b460bf568192f5))
- **kb:** CB-1661 Fixed eslint warnint related to color picker in package ui ([0d709d1](https://github.com/resola-ai/deca-apps/commit/0d709d17f2a64147f1c0b2d87120967031a79b78))
- **kb:** CB-1661 Fixed feedbacks about the Article Editor layout ([04a6d68](https://github.com/resola-ai/deca-apps/commit/04a6d68f4ffbcb9005873283dfb65b3ed7d218aa))
- **kb:** CB-1691 Adjust the breadcrumbs for parent folder in Search result ([7015757](https://github.com/resola-ai/deca-apps/commit/7015757ff4c2f9065ccc295228a9d6e4ce2ef600))
- **kb:** CB-1701 Correct locales and scrolling in Article fullview ([5cb674f](https://github.com/resola-ai/deca-apps/commit/5cb674f437595621d39fe5e62e66265062888247))
- **kb:** CB-1703 Correct color style in Editor with color setting ([c66850a](https://github.com/resola-ai/deca-apps/commit/c66850acdfab6fa8922d2730246a83ec0487d5fb))
- **kb:** CB-1703 Correct some feedback about Search Breadcrumb and Editor viewer content ([08d441c](https://github.com/resola-ai/deca-apps/commit/08d441c408b99cfa11a46f493b57b470640d1f48))
- **kb:** CB-1704 Correct key phrase validation and break lines ([06c9f96](https://github.com/resola-ai/deca-apps/commit/06c9f96ecff398fb2424aea567c5c7870affcda2))

### [0.3.3](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.3) (2024-07-18)

### Features

- **kb:** CB-1627 update folder delete and kb scope changed behavior ([9c88c04](https://github.com/resola-ai/deca-apps/commit/9c88c04e3315a5b2474252bc175a55e585316325))
- **kb:** CB-1669 Add ID to BlockNote View with shadow root to support automation ([d447d3e](https://github.com/resola-ai/deca-apps/commit/d447d3e073c326f1d177db423c7805a9adccedf6))
- **kb:** CB-1671 Handle upload image when creating Article ([ff35e75](https://github.com/resola-ai/deca-apps/commit/ff35e75bbcba82cd3aaaf7a21b0307acdd8bb1e0))
- **kb:** CB-1677 Adjust the empty block in BlockNote Editor and enhance UI in Full View Editor ([7c0876a](https://github.com/resola-ai/deca-apps/commit/7c0876a959030db5da5b1dd54ade37cb67077a5b))

### Bug Fixes

- **kb:** CB-1666 update close article drawer with unsaved check ([0c87ef9](https://github.com/resola-ai/deca-apps/commit/0c87ef9c887238bae007db65cd8bc2c6d8798bac))
- **kb:** CB-1673 update expanded explorer height ([ba9bfab](https://github.com/resola-ai/deca-apps/commit/ba9bfabcaf418be55ddfadb841bdfc70d5de5e60))
- **kb:** CB-1680 update kb explorer delete folder behavior ([45ae933](https://github.com/resola-ai/deca-apps/commit/45ae933fb4e16c0da535108f3e5e3558b8b7593d))
- **kb:** CB-1681 Correct link color style ([f358e90](https://github.com/resola-ai/deca-apps/commit/f358e9074f3f83c6b78c3d4f37082171ac22e39d))
- **kb:** CB-1681 Fix missing provider in Search page ([20e4e8f](https://github.com/resola-ai/deca-apps/commit/20e4e8ffe20d8124963d7ebd582e29275b1b91da))
- **kb:** update access level change condition ([202017f](https://github.com/resola-ai/deca-apps/commit/202017fada2c176cc2e5e252889932e0043fa463))

### [0.3.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.2) (2024-07-12)

### Features

- **kb:** CB-1593 handle goto article directly by url ([c8ab21c](https://github.com/resola-ai/deca-apps/commit/c8ab21c573595999c815c9652e032602dde8ceca))
- **kb:** CB-1606 Improve code base and add comments for TODO ([0514480](https://github.com/resola-ai/deca-apps/commit/051448016b606346bd1490e5eab354889e9a0d8b))
- **kb:** CB-1606 Temp disable new Color Picker input and upgrade BlockNote to 14.4 ([cde479d](https://github.com/resola-ai/deca-apps/commit/cde479d92d59f1f725c938bf73bdd871354e0a7f))
- **kb:** CB-1606 Upgrade BlockNote version and custom Color Picker component with Color Input ([5dd4cdb](https://github.com/resola-ai/deca-apps/commit/5dd4cdb6487f85bf551e3440079206b741cd41ba))
- **kb:** CB-1607 Correct spacing in FilePanel ([268d3e2](https://github.com/resola-ai/deca-apps/commit/268d3e248708dfc6865af717f3843cb227af36ac))
- **kb:** CB-1607 Custom UI for BlockNote upload tab and integrate API ([8e8e9b4](https://github.com/resola-ai/deca-apps/commit/8e8e9b41fe0d9a2db245bc3a1832508de6818e8e))
- **kb:** CB-1607 Onverride Image component in BlockNote to make some custom ([b8e1ab1](https://github.com/resola-ai/deca-apps/commit/b8e1ab173fe9a7431c8ce25246de1669a6b36f6f))
- **kb:** CB-1620 Add default accessLevel to pass the create KB flow on Dev ([00f468d](https://github.com/resola-ai/deca-apps/commit/00f468d8b34faa920007f13bebbeca3f62eadfa4))
- **kb:** CB-1621 Add async await in Upload file for promise handling ([ee3785e](https://github.com/resola-ai/deca-apps/commit/ee3785e2149e53c1add594fdfc4a81fdc71fdb0c))
- **kb:** CB-1621 UI and integrate temp server for Image uploading in Article Editor ([9c9551a](https://github.com/resola-ai/deca-apps/commit/9c9551a4d541287f74baa69c5e7dacf49d428e52))
- **kb:** CB-1641 handle kb scope in kb detail modal ([3b382ab](https://github.com/resola-ai/deca-apps/commit/3b382ab886c157e8b33d095ad2f3a1123ea0ff49))
- **kb:** CB-1645 Adjust the KB Name in breadcrumb of Search result ([9552fc8](https://github.com/resola-ai/deca-apps/commit/9552fc820fa5f488f734a0852415047306e93dab))
- **kb:** CB-1653 Override BlockNote FileCaptionButton component to correct behavior and handle validation in File Upload ([5e0686f](https://github.com/resola-ai/deca-apps/commit/5e0686fc479f25936a908ec73c2a2ffa600d27ce))
- **kb:** CB-1656 Add inline CSS for BlockNoteViewer ([6795436](https://github.com/resola-ai/deca-apps/commit/6795436f94c11c7e17d3aff3bdb2b085dd239af1))
- **kb:** CB-1656 Enable inline CSS for chatbot and KB without CDN font ([5ee781c](https://github.com/resola-ai/deca-apps/commit/5ee781c4bcc863ba36204d26e56f08566791b7c3))
- **kb:** CB-1659 handle kb scope disable when scope change still processing ([0ad12bb](https://github.com/resola-ai/deca-apps/commit/0ad12bb862f40e41ad2a620f2d39a2af0a9fee46))
- **kb:** CB-1664 update kb card with scope ([4ba8105](https://github.com/resola-ai/deca-apps/commit/4ba81058096e44dd016701b569f1650ad973fdc6))

### Bug Fixes

- **kb:** CB-1573 update kb explorer on empty view ([e4c958c](https://github.com/resola-ai/deca-apps/commit/e4c958cd3fcaa99270bfeed526d5c38dbe39fb6a))
- **kb:** CB-1593 update goto article by url and fix collection pagination bug ([3093b3a](https://github.com/resola-ai/deca-apps/commit/3093b3a106c6359f8f81138bf2825d5d6b1533c2))
- **kb:** CB-1598 show loading when load article ([c0098c1](https://github.com/resola-ai/deca-apps/commit/c0098c1941c5eb751028a8ce4280761c1e4eda60))
- **kb:** CB-1602 update kb browser back button ([52d01f3](https://github.com/resola-ai/deca-apps/commit/52d01f347938557ca0bb7eaebe9a9b9d13990008))
- **kb:** CB-1621 Add null checking in useStyles of BlockNote ([a546370](https://github.com/resola-ai/deca-apps/commit/a5463701f1135051ba16f4200bc1127dcdd24a44))
- **kb:** CB-1644 remove recent card text decoration ([0c315c7](https://github.com/resola-ai/deca-apps/commit/0c315c7948f2f61362004e80b349611de39f26f8))
- **kb:** CB-1644 update link color ([68d2b17](https://github.com/resola-ai/deca-apps/commit/68d2b174ea50001830634482ce963ea1b7b1c736))
- **kb:** CB-1647 correct folder fetching id after create kb ([7551867](https://github.com/resola-ai/deca-apps/commit/7551867e2022619c6c138a0f486c8fb848e87f50))
- **kb:** CB-1652 update explorer scrollbar and breadcrumb align ([a94bc61](https://github.com/resola-ai/deca-apps/commit/a94bc61e28cd671e132cc53cf5ad02c33c1be223))
- **kb:** CB-1658 update fetch folder data correctly after create kb ([7259e3c](https://github.com/resola-ai/deca-apps/commit/7259e3cdb2bc58df17295243e50976f92ae5cf6c))
- **kb:** fix kb empty state styling ([d521f36](https://github.com/resola-ai/deca-apps/commit/d521f36c25ba1c4e44efa02003ed2a5d99f3fcfa))

### [0.3.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.1) (2024-07-01)

### Features

- **kb:** CB-1456 Correct translation and validation message in KB ([ace9461](https://github.com/resola-ai/deca-apps/commit/ace9461e50fe70b47497b4c89b6254ebb03ffcd6))
- **kb:** CB-1534 Correct some feedback in KB with improving scroller and kb description ([cbc9f52](https://github.com/resola-ai/deca-apps/commit/cbc9f52895005e9c668bcf6bcf5edb515b71720c))
- **kb:** CB-1535 Correct infinte scrolling issue and update translation ([80a6210](https://github.com/resola-ai/deca-apps/commit/80a6210e0d45312aecbaddef3143c791a3efe7a5))
- **kb:** CB-1535 Implement pagination via infinite scrolling in KB Homepage ([2d71f87](https://github.com/resola-ai/deca-apps/commit/2d71f8786f9ae1abfe0549354c1e0d291b236f16))
- **kb:** CB-1543 Create BlockNote Viewer for Chatbox client Article viewing and adjust the infinite scrolling ([623a90f](https://github.com/resola-ai/deca-apps/commit/623a90f798b279f78944c78bc49d9c6883f0e699))
- **kb:** CB-1544 Adjust ref in infinite scroll for the second time loading ([5e5df18](https://github.com/resola-ai/deca-apps/commit/5e5df18035085fcc3ac648272921300b25fc9d4b))
- **kb:** CB-1544 Integrate Search API to Folders and Articles ([0e55304](https://github.com/resola-ai/deca-apps/commit/0e553044ee906de26738d24c099753c443092f8b))
- **kb:** CB-1547 improve kb explorer ([0af439a](https://github.com/resola-ai/deca-apps/commit/0af439a718b79823959c2a9479e3653a0457349f))
- **kb:** CB-1553 Correct infinite scrolling init and add logs ([7889c49](https://github.com/resola-ai/deca-apps/commit/7889c4995d738339499bd4be0146d94b834430ec))
- **kb:** CB-1553 Handle display Display Name for Article and Comment user ([6345412](https://github.com/resola-ai/deca-apps/commit/6345412a4ac9f552325a2adb330ba3d0a459328e))
- **kb:** CB-1554 Corrected infinite scrolling issue, adjust breadcrumbs text ([4930801](https://github.com/resola-ai/deca-apps/commit/4930801bfa82b6d852ca12c9ce1cdf6435f2673a))
- **kb:** CB-1558 Show search box in empty state ([3a3b033](https://github.com/resola-ai/deca-apps/commit/3a3b0331d6607c661606c1831497a7270db258cf))
- **kb:** CB-1568 Remove KB description required ([5b3efd0](https://github.com/resola-ai/deca-apps/commit/5b3efd05853df0518209c7a5be9571aa95707374))

### Bug Fixes

- **chatbox, kb:** Adjust Article render UI in chatbox and improve the infinite scrolling ([b1c463c](https://github.com/resola-ai/deca-apps/commit/b1c463c8a1adefd1b1ef25378a55baced965e404))
- **chatbox, kb:** CB-1561 Correct some feedbacks related to Article viewer in Chatbox ([f7d9bfb](https://github.com/resola-ai/deca-apps/commit/f7d9bfbad9730d002a07698b6f03833904f2da06))
- **chatbox, kb:** CB-1561 Remove unused falsy check syntax ([0246bcc](https://github.com/resola-ai/deca-apps/commit/0246bcc4b7f10e1f48131c74a6ea5b0407c12f1f))
- **chatbox, kb:** Remove unused style and add dependencies ([3ab3b3c](https://github.com/resola-ai/deca-apps/commit/3ab3b3c8caedfb99dd5ceedac5d1c66503b93d6d))
- **kb:** CB-1528 update breadcrumb fixed position ([baf8aa5](https://github.com/resola-ai/deca-apps/commit/baf8aa55113ef860a2340cd1e1b3ea0c0a682cb3))
- **kb:** CB-1552 update kb explorer and uiux ([0657d5a](https://github.com/resola-ai/deca-apps/commit/0657d5a00d9d5b6d434e1aeb8e2763ec0441ef58))
- **kb:** CB-1552 update root folder data ([086f396](https://github.com/resola-ai/deca-apps/commit/086f3962c6dcdc457327b5beba3343cd5e8b8ea4))
- **kb:** CB-1555 improve kb ui ux ([782ed64](https://github.com/resola-ai/deca-apps/commit/782ed642369d075511dcef420d9ff0503b64d9eb))
- **kb:** CB-1556 update kb item card ([312d9eb](https://github.com/resola-ai/deca-apps/commit/312d9eb536ccb560d495875c7f324224feea7048))
- **kb:** CB-1556 update kb item card and scroll top when go to detail page ([af5ece6](https://github.com/resola-ai/deca-apps/commit/af5ece6530bf2273e99d4bacd7d6dc9be71de5b0))
- **kb:** CB-1558 update Article KB status to published ([b5ebf0c](https://github.com/resola-ai/deca-apps/commit/b5ebf0c58dcdc0598dadfd31b7dc6212c02f180e))
- **kb:** CB-1569 improve kb feedbacks ([52405a6](https://github.com/resola-ai/deca-apps/commit/52405a63a8fd9de0aa9210a4ce771589c1b3ec68))
- **kb:** CB-1569 update kb ([2a48608](https://github.com/resola-ai/deca-apps/commit/2a48608bf53af922fbc9f47b3f8ea7faf53d9914))

## [0.3.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.0) (2024-06-20)

### Features

- **kb:** CB-1405 handle job page ([1e458f4](https://github.com/resola-ai/deca-apps/commit/1e458f486f83cb8d4838d81bb5b6ba2b37d2a22b))
- **kb:** CB-1405 update job page ([b783d3a](https://github.com/resola-ai/deca-apps/commit/b783d3a2f64e686f472411384ab013fd2fcfd580))
- **kb:** CB-1405 update job page ([7169512](https://github.com/resola-ai/deca-apps/commit/7169512cf99a845138413f12399cfc922628d1ed))
- **kb:** CB-1405 update job page with custom status and duration ([1e6fd4e](https://github.com/resola-ai/deca-apps/commit/1e6fd4e94882102a1e49af2a32c6f89d29dc72ce))
- **kb:** CB-1445 handle kb explorer ([befa29d](https://github.com/resola-ai/deca-apps/commit/befa29d6f7c2bae19cb59cf6f6b40b86d1ab0d32))
- **kb:** CB-1448 Correct default documentId in create KB v2 ([8a3ae4b](https://github.com/resola-ai/deca-apps/commit/8a3ae4b5408fff3125c6fef73a80a771f5e874a0))
- **kb:** CB-1448 Correct plural translation for Time Ago text and remove unused default value in Article viewer ([ceae441](https://github.com/resola-ai/deca-apps/commit/ceae441bb2ba4a77567ee87ec7ecc8f1d0286869))
- **kb:** CB-1448 Implement the comments component ([43554ec](https://github.com/resola-ai/deca-apps/commit/43554ec5a7c7bee68b40048c43eb66b5a239a174))
- **kb:** CB-1448 Integrate Comments API and handle rendering in comment message ([615e480](https://github.com/resola-ai/deca-apps/commit/615e480750742ac31bf073f7591000a7ada22e7e))
- **kb:** CB-1448 Remove default state value for backTitle in Drawer ([3abd493](https://github.com/resola-ai/deca-apps/commit/3abd493d13fb9efb4dcf5603cd04f214c2aebc34))
- **kb:** CB-1454 handle folder and update modal create flow ([b6fb479](https://github.com/resola-ai/deca-apps/commit/b6fb4791a1db301b2c70ad1a65215ff0092fcec8))
- **kb:** CB-1454 update kb create modal with two steps ([0e5709e](https://github.com/resola-ai/deca-apps/commit/0e5709eaf94a1675159c19ac743e88642d63d49f))
- **kb:** CB-1456 Adjust the validation content in Article ([ff0c0f4](https://github.com/resola-ai/deca-apps/commit/ff0c0f486c0c6c9d5f5722a5181ab271fa53b9a1))
- **kb:** CB-1456 Correct some feedbacks related to Editor and cloned and custom some BlockNote component ([7788030](https://github.com/resola-ai/deca-apps/commit/7788030e7759f2fe68ba9779c924bd1db27d2508))
- **kb:** CB-1456 Improve gap in article info ([2caa402](https://github.com/resola-ai/deca-apps/commit/2caa402953bab95fee4c7d59913cae71dd8d6e97))
- **kb:** CB-1466 Correct the new schema update for baseType and add pagination to homepage ([1b5cb55](https://github.com/resola-ai/deca-apps/commit/1b5cb55da327eaf6d5a31dad804346680e095379))
- **kb:** CB-1475 Add reidrect for folder search and correct some issues in KB ([fce3b44](https://github.com/resola-ai/deca-apps/commit/fce3b44e2f304ad1e89fb3937e4e273805f95327))
- **kb:** CB-1475 Improve behavior for Document KB ver 2 to adapt with UI component ([38700a0](https://github.com/resola-ai/deca-apps/commit/38700a06cdb1517af5b134d9ca715c9a61bfd89c))
- **kb:** CB-1475 Prevent behavior with new KB Documents ([9d8b208](https://github.com/resola-ai/deca-apps/commit/9d8b20885f79b5ce09209a0e26dd79d8d5d1eb40))
- **kb:** CB-1477 Adjust layout for Full Screen of Article Detail ([9568a3a](https://github.com/resola-ai/deca-apps/commit/9568a3a7689533524acb5481b054975bd3d69e0d))
- **kb:** CB-1477 Correct Content Raw field payload in Article ([f35649b](https://github.com/resola-ai/deca-apps/commit/f35649be100bde52ce13f8851f0d3e8299738598))
- **kb:** CB-1477 Correct style in Article detail by className ([007d074](https://github.com/resola-ai/deca-apps/commit/007d074fd9a95bd7b28db1b56cc2e4c7c59594e6))
- **kb:** CB-1478 Quick update API integrate for Comment user info ([53a070a](https://github.com/resola-ai/deca-apps/commit/53a070a1bad633996447b6c8698d688b8eb5ed9d))
- **kb:** CB-1478 Remove unused field in Comment type ([aac2c31](https://github.com/resola-ai/deca-apps/commit/aac2c31533e98fc256fe4d8bc5328f37f53bb7b9))
- **kb:** CB-1479 Custom the BlockNote Video Block to handle Youtube embed and correct some minor issues ([1ee5458](https://github.com/resola-ai/deca-apps/commit/1ee5458cdc112696d5fc90c069ef9327aa6b1652))
- **kb:** CB-1482 Implement the Edit and Delete comment actions in Article ([b01286e](https://github.com/resola-ai/deca-apps/commit/b01286ecf4ac603645d3306854d6bfad3bf87f71))
- **kb:** CB-1483 Adjust and correct some issues from Search KB function ([0b4759e](https://github.com/resola-ai/deca-apps/commit/0b4759ebd1cfced440ecf5a16da63b5503f1bec6))
- **kb:** CB-1483 Adjust type and dependencies ([bad62ed](https://github.com/resola-ai/deca-apps/commit/bad62edb7a45cf90e92975686a022577a3ff644b))
- **kb:** CB-1483 Correct some feedbacks on Title using ([980e4db](https://github.com/resola-ai/deca-apps/commit/980e4dbe44a40c94858041ca5631cfab6ad27abb))
- **kb:** CB-1483 Implement components and integrated API for KB Search ([ec8abf2](https://github.com/resola-ai/deca-apps/commit/ec8abf2581c35b8e0ef485834b0fb0c138554975))
- **kb:** CB-1483 Improve Article detail component structure ([7f456d3](https://github.com/resola-ai/deca-apps/commit/7f456d39d3f2f206287132e970ef90ebe31fc732))
- **kb:** CB-1501 Correct button style for Comment loadmore ([5219b40](https://github.com/resola-ai/deca-apps/commit/5219b4001b3d453789c10dcc44e6ddbc388b25e1))
- **kb:** CB-1501 Integrate pagination and load more for articles and comments ([0c36b62](https://github.com/resola-ai/deca-apps/commit/0c36b620d835f4529df588e1979be36ce72fd744))
- **kb:** CB-1501 Replace BlockNote scss by useThemes in component ([3390459](https://github.com/resola-ai/deca-apps/commit/3390459655be9bc3da0141c03450006e537c3d17))
- **kb:** CB-1504 add folder detail page ([1928a33](https://github.com/resola-ai/deca-apps/commit/1928a33b59dc9194f774797d30e6c5908048fbc7))
- **kb:** CB-1505 handle kb context menu and improve kb flow and UI ([ef34a46](https://github.com/resola-ai/deca-apps/commit/ef34a46b49a61c59a058692aae91be50eb9c6f6d))
- **kb:** CB-1512 Correct build errors with declared but its value is never read in DecaTable ([af1f3bc](https://github.com/resola-ai/deca-apps/commit/af1f3bc5a47ee86075a11a119bc32075cc9fbd7d))
- **kb:** CB-1512 Handle confirm unsave changed and correct some feedbacks for release ([de3ccdf](https://github.com/resola-ai/deca-apps/commit/de3ccdfeb059fcda4bb5d30dc3453ee6d3c69fd1))
- **kb:** CB-1512 Show confirm dialog when user unsave Article changes ([bb2fd46](https://github.com/resola-ai/deca-apps/commit/bb2fd4655df6f4a39d54d563735c40c82614255a))
- **kb:** CB-1518 handle kb recent view ([3bcdb9e](https://github.com/resola-ai/deca-apps/commit/3bcdb9efdc034bd8f310936f1d24003d9691ad9e))
- **kb:** CB-1523 Correct the user name render by locale rule in article ([562d514](https://github.com/resola-ai/deca-apps/commit/562d514d4347670c89955b8454cf5f740a6087df))

### Bug Fixes

- **kb:** CB-1257 improve kb explorer ([1fdf480](https://github.com/resola-ai/deca-apps/commit/1fdf4801af0fa05948f995f87f192e236cac9789))
- **kb:** CB-1257 open root folder at the first time ([b81f33d](https://github.com/resola-ai/deca-apps/commit/b81f33d8fd3450f5a64012045877f7544b2cbffd))
- **kb:** CB-1257 update explorer behavior ([08148c8](https://github.com/resola-ai/deca-apps/commit/08148c857670aaddd6b50ea410ca89a370792535))
- **kb:** CB-1445 remove mock data ([03f9abc](https://github.com/resola-ai/deca-apps/commit/03f9abccd321913235148715291c28af524eda1c))
- **kb:** CB-1454 update article type datetime ([1422940](https://github.com/resola-ai/deca-apps/commit/14229408e004236e6fb31d343689d4a98fdfe92a))
- **kb:** CB-1484 update kb navbar and sidebar ([287a6da](https://github.com/resola-ai/deca-apps/commit/287a6da2a09c58a66baee79aea7af3ed61e2ff3d))
- **kb:** CB-1489 update kb api payload and homepage ([389c96f](https://github.com/resola-ai/deca-apps/commit/389c96f8135d6af614635179958ab1e4886ea206))
- **kb:** CB-1505 update recently viewed ([390c639](https://github.com/resola-ai/deca-apps/commit/390c639bccf19f3c53317b1dfad655d3739d1165))
- **kb:** CB-1517 improve kb flow ([32d7873](https://github.com/resola-ai/deca-apps/commit/32d7873ce4e21f51887863c01a325a1690532f73))
- **kb:** CB-1526 refresh after delete ([c3076a6](https://github.com/resola-ai/deca-apps/commit/c3076a6581f3dd448b1e137760b2b3b1cd764020))
- **kb:** CB-1526 update fetching data after delete ([ac09120](https://github.com/resola-ai/deca-apps/commit/ac09120c13197e12f4626862ce4d48c32ee88ae4))
- **kb:** CB-1526 update kb flow in subfolder ([b9bb3a5](https://github.com/resola-ai/deca-apps/commit/b9bb3a5f47740921a26ead73c49108dbb604a69e))
- **kb:** CB-1529 remove unused import ([e27ddbf](https://github.com/resola-ai/deca-apps/commit/e27ddbfd9684946f6015afdc18c4b680d60b041a))
- **kb:** CB-1529 update kb create modal and improve explorer ([576b913](https://github.com/resola-ai/deca-apps/commit/576b9138bc1424d83ffdb39411c74d948fdbd421))

### [0.2.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.2) (2024-06-07)

### Features

- **kb:** CB-1455 Correct API for version 1 and kbSchema for create new ([f97349c](https://github.com/resola-ai/deca-apps/commit/f97349cf2b4d5ceea59599d5e05ce22682aca015))
- **kb:** CB-1455 Correct missing slash in API request ([baa26ac](https://github.com/resola-ai/deca-apps/commit/baa26accaf4840740207c55c3abc374eff12d608))

### [0.2.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.1) (2024-06-06)

### Features

- **kb:** CB-1455 Correct condition to render empty state in homepage ([44164cb](https://github.com/resola-ai/deca-apps/commit/44164cb79894df4640893034ad01c79a2147e91f))
- **kb:** CB-1455 Update create new modal ([4c370ec](https://github.com/resola-ai/deca-apps/commit/4c370ecfc3ad70bd275ac40ed03399950953cf1a))

## [0.2.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.0) (2024-06-06)

### Features

- **kb:** CB-1265 update no access page ([37c2196](https://github.com/resola-ai/deca-apps/commit/37c2196f532c0233752345d4c0fbcec7dd00e35a))
- **kb:** CB-1399 Add BlockNote editor back to KB ([61be83b](https://github.com/resola-ai/deca-apps/commit/61be83b7b040047a848bc6295131b4a1910ca75d))
- **kb:** CB-1399 Implement components for KB article collection page and correct the BlockNote conflict ([2416dae](https://github.com/resola-ai/deca-apps/commit/2416dae44cbfcf4447274ecaf33af0c75a9d9780))
- **kb:** CB-1400 Create Article Viewer component and define some main flow in the Viewer ([a34b5bf](https://github.com/resola-ai/deca-apps/commit/a34b5bff91616a1ea5074806112f8ef409635eb8))
- **kb:** CB-1400 Update position to props for dynamic drawer ([01028db](https://github.com/resola-ai/deca-apps/commit/01028db8e1a548e28903ed61d6c161db8c3ee02b))
- **kb:** CB-1400 Update some style from develop after sync ([1f7f783](https://github.com/resola-ai/deca-apps/commit/1f7f783e2e27986f60d7582056e5f81516135abc))
- **kb:** CB-1401 update kb navbar ([077f776](https://github.com/resola-ai/deca-apps/commit/077f7762cd8ce8c215c981854212bc74d148b805))
- **kb:** CB-1402 add tree view sidebar ([7043973](https://github.com/resola-ai/deca-apps/commit/70439731d05865a50a0312576bbe4e90090356ab))
- **kb:** CB-1403 update new kb homepage ([7382355](https://github.com/resola-ai/deca-apps/commit/7382355901f16f783054977e2fb4b7bda00e6aa3))
- **kb:** CB-1432 Handle i18n for BlockNote toolbars and continue implement the article detail form ([56cb614](https://github.com/resola-ai/deca-apps/commit/56cb614247d0fb6ba962c5ac56305b6b7cd56b6f))
- **kb:** CB-1433 apply design system ([15a40ec](https://github.com/resola-ai/deca-apps/commit/15a40ecdc6e44211df5ecad6d4172a9655205f10))
- **kb:** CB-1435 Correct translation for From Datasource ([408cb66](https://github.com/resola-ai/deca-apps/commit/408cb667f2a1adaed15d14c2e99f32e82c169b60))
- **kb:** CB-1435 Improve the API structure by version and correct update KB issue ([0296bb5](https://github.com/resola-ai/deca-apps/commit/0296bb55802966983cb64e0bfec209e4bbc237fb))
- **kb:** CB-1435 Replace KB API v1 by v2 and integrate API for Articles ([42d07b1](https://github.com/resola-ai/deca-apps/commit/42d07b12d45edf2dd15877180c0cfacf04d47fa0))
- **kb:** CB-1435 Revert old QnA page and integrate API for new Homepage ([bfd204a](https://github.com/resola-ai/deca-apps/commit/bfd204a187348988b25ddb4300a500073fd7d395))
- **kb:** CB-1435 Update payload and handle API version ([823dbba](https://github.com/resola-ai/deca-apps/commit/823dbbae0c5e09b35b6c15af8b687b72a55d86fd))
- **kb:** CB-1443 update leaf context menu ([68d6840](https://github.com/resola-ai/deca-apps/commit/68d68403a0288a79b92a71b1b2d437acbc87a0ba))
- **kb:** CB-1444 update empty box ([2d6f4e1](https://github.com/resola-ai/deca-apps/commit/2d6f4e13b630022fb65771fe00405ca59ae5addc))
- **kb:** CB-1449 Correct Block Note initial content in KB Article ([57b3ea9](https://github.com/resola-ai/deca-apps/commit/57b3ea9f100733e8584a15b40a7503884c5b7dc1))
- **kb:** CB-1449 Create Article Empty state and rename the ArticleCollection component ([7120f02](https://github.com/resola-ai/deca-apps/commit/7120f0210fe3508d8aa4ecd713e0863d27d847a1))
- **kb:** CB-1451 Add the loading state and improve viewer content with Editor ([3abebe1](https://github.com/resola-ai/deca-apps/commit/3abebe12976ad9ac74b82537dd1472017d398897))
- **kb:** CB-1451 Adjust the API integration and correct the toolbar in BlockNote ([6d866d3](https://github.com/resola-ai/deca-apps/commit/6d866d3c549bb7827d407b979a0a4214e35b25db))
- **kb:** CB-1451 Update type for Article in content form ([68be5cf](https://github.com/resola-ai/deca-apps/commit/68be5cf7e420dd03e1ec8c82f459300b0219e9fd))
- **kb:** CB-1455 Fixed navigation for KB version 1 and correct some issues from new Article ([544d373](https://github.com/resola-ai/deca-apps/commit/544d373a371f4b3510b5e8ed75f998452d8b953b))
- **kb:** CB-1455 Hide Article search box and keep enter in Keyphrase only ([69da181](https://github.com/resola-ai/deca-apps/commit/69da181059e983942cf7ee714dac45503e96b45d))

### Bug Fixes

- **kb:** CB-1422 move qna kb to new route ([acc98d0](https://github.com/resola-ai/deca-apps/commit/acc98d0d28d5c015d9692647c3db5603c6925379))
- **kb:** CB-1429 update kb home page responsive and icons ([ef667d0](https://github.com/resola-ai/deca-apps/commit/ef667d07130a95bbfed44488981a90da801f7797))
- **kb:** CB-1433 remove unused import ([9017f5c](https://github.com/resola-ai/deca-apps/commit/9017f5c2068bec6ee698743750478eb3813c5a0c))

## 0.1.0 (2024-04-29)

### Features

- **kb:** add standard version for release ([326b1a4](https://github.com/resola-ai/deca-apps/commit/326b1a430f9fdddec8039f53b6f71d7b754b99d4))
- **kb:** CB-1129 refactor kb type, schema, and integrate with api on home page ([d9bdebd](https://github.com/resola-ai/deca-apps/commit/d9bdebdef9f4e0b78a58afba8986316301add0d6))
- **kb:** CB-1136 integrate api with kb qna detail ([eee56bb](https://github.com/resola-ai/deca-apps/commit/eee56bb63feebbaf733be40a8784a1d486338bdb))
- **kb:** CB-1147 update generate qna first and second step ([0d07bfd](https://github.com/resola-ai/deca-apps/commit/0d07bfddfd33c4a5fa7fa82f55ea3c0dd9169ed9))
- **kb:** CB-1152 Add notification for Not found document ([823ba58](https://github.com/resola-ai/deca-apps/commit/823ba580c298ea2948fac48d1b4fe03e77ff5ce3))
- **kb:** CB-1152 Correct API payload cannot send File via AxiosService with presignedURL ([fce8322](https://github.com/resola-ai/deca-apps/commit/fce83222b76606b1a9be44b1890cb73897176780))
- **kb:** CB-1152 Improved the Loading status and notification in KB Document ([a2a32fa](https://github.com/resola-ai/deca-apps/commit/a2a32facbb7af4e06190dfd1ad57a7fddc4f7809))
- **kb:** CB-1152 Update text for Document file note ([ec0b789](https://github.com/resola-ai/deca-apps/commit/ec0b7892ca8c1e1471b1df6792a2186ebf574fe5))
- **kb:** CB-1153 handle upload qna from csv ([360be1e](https://github.com/resola-ai/deca-apps/commit/360be1e4780633b19fefa4b295ebc56c4ee5229a))
- **kb:** CB-1179 Implement and integrate API for Document upload status and progress ([ebd23ac](https://github.com/resola-ai/deca-apps/commit/ebd23ac44b0518f61f642e9dc747c054975070b8))
- **kb:** CB-1181 handle qna selection in generate flow ([a921f76](https://github.com/resola-ai/deca-apps/commit/a921f76ef7081c82cfd5b70a117a5201c1fc0910))
- **kb:** CB-1196 Check file and presignedURL before execute API ([7278d72](https://github.com/resola-ai/deca-apps/commit/7278d72591f483430bdfe5c4da6bb0aa0649f844))
- **kb:** CB-1196 Implement upload multiple Document files ([745277c](https://github.com/resola-ai/deca-apps/commit/745277c3944f4242d9ca7c35c5728dc510081411))
- **kb:** CB-1196 Improve condition checking in progress bar ([906747b](https://github.com/resola-ai/deca-apps/commit/906747b63b91c4ab411408169bfc265f441e94de))
- **kb:** CB-1206 improve kb status ([81bb4bb](https://github.com/resola-ai/deca-apps/commit/81bb4bb4a5f3a664ce9eb28b8e96bac1a3285725))
- **kb:** CB-1206 update save qna payload ([65dc387](https://github.com/resola-ai/deca-apps/commit/65dc387eab5a7918accdb25dc0d9684c9142cafa))
- **kb:** CB-1215 update failed status for qna generation ([6a9c27f](https://github.com/resola-ai/deca-apps/commit/6a9c27fd3fc76290812054a0d55a00acf57ad205))
- **kb:** cb-396: add burger and drawer in small screen ([9574465](https://github.com/resola-ai/deca-apps/commit/95744656006841fd395860e7a6289d07c5a8e6a9))
- **kb:** cb-396: init app ([2f4728d](https://github.com/resola-ai/deca-apps/commit/2f4728d508c22c8213368ab00a21d51760f4b811))
- **kb:** cb-396: setup multilanuages, integrate auth0, pages routing ([3789dfd](https://github.com/resola-ai/deca-apps/commit/3789dfd662613eb284e3a6d69c437d3dee17d750))
- **kb:** cb-396: setup testing, tsconfig, vite config, install neccessary packages ([25e2c52](https://github.com/resola-ai/deca-apps/commit/25e2c52dbc7a7b9ca50611fb7c649532ea1b17df))
- **kb:** CB-470 update japanese locale and refactor common components ([fb14f0a](https://github.com/resola-ai/deca-apps/commit/fb14f0a37f28ce1fe9ff039fc97b05d3e8b2e114))
- **kb:** CB-483 - implement kb document detail page, add upload file component ([28733d9](https://github.com/resola-ai/deca-apps/commit/28733d9d7da1531333cff0b62c8e05272d79ecd8))
- **kb:** CB-483 - update document detail page with mock data, add custom pagination ([46bf330](https://github.com/resola-ai/deca-apps/commit/46bf3303ab396ec616a1a16a647af9a0ad4d416b))
- **kb:** CB-498 - link homepage card to kb detail page ([39e66e4](https://github.com/resola-ai/deca-apps/commit/39e66e4db712421cb19744af08f5528b4d0ed58b))
- **kb:** CB-498 - update qna detail page, update common components ([33fb7a3](https://github.com/resola-ai/deca-apps/commit/33fb7a3b04057e4d53be3519cf327bd95a3e8660))
- **kb:** CB-509 - add qna table ([5d6d9c2](https://github.com/resola-ai/deca-apps/commit/5d6d9c264f4927dc54a9fceb44c272b0c275fe84))
- **kb:** CB-509 - update qna detail page with mock data ([163336d](https://github.com/resola-ai/deca-apps/commit/163336ddc1c9d1186f7d8566a45d63bb0a8d9338))
- **kb:** CB-511 - update kb form and upload component ([716a81e](https://github.com/resola-ai/deca-apps/commit/716a81e5ed746bcc1dca01500e135f9b68dbda10))
- **kb:** CB-518 - update kb model and move form to share component ([769b857](https://github.com/resola-ai/deca-apps/commit/769b8577cb2c459f378ec70e02f2ab61875a06ce))
- **kb:** CB-523 - implement custom stepper component, link generate page ([5743d11](https://github.com/resola-ai/deca-apps/commit/5743d111440883acab2106b6cf1c5d95a904c68f))
- **kb:** CB-558 - update first step screen with document list ([554c6d8](https://github.com/resola-ai/deca-apps/commit/554c6d8fcf8f42c22a12dfcad9a12360a8ab8c85))
- **kb:** CB-558 - update first step screen with no document state ([c260dd6](https://github.com/resola-ai/deca-apps/commit/c260dd684272706043fac417024e286dd925c61c))
- **kb:** CB-569 - update all step screen in generate qna page ([945b2d7](https://github.com/resola-ai/deca-apps/commit/945b2d7caf442917bbb860db5a615f35ec247f64))
- **kb:** cb-649: add customize prompt modal ([0b297eb](https://github.com/resola-ai/deca-apps/commit/0b297eb6e4668000ff550ea36e461c95070a3376))
- **kb:** CB-650 add confetti animation when successfully generate questions ([cd16fd0](https://github.com/resola-ai/deca-apps/commit/cd16fd04eac7b3dddcd2ac14061bf15aab14d4aa))
- **kb:** CB-664 add confirm modal and update search not found screen ([909930d](https://github.com/resola-ai/deca-apps/commit/909930d8ebaf774b34ff44ab8a980e23ee0ddf97))
- **kb:** CB-664 update confirm modal when delete document and qna ([9757c8d](https://github.com/resola-ai/deca-apps/commit/9757c8dcee00feee6e510ec67333a237b8081d66))
- **kb:** CB-773 add document upload progress modal ([0b29caa](https://github.com/resola-ai/deca-apps/commit/0b29caa1cc20826e78a69938e076f3a04b36b5f3))
- **kb:** fix build fail ([4dd0d90](https://github.com/resola-ai/deca-apps/commit/4dd0d90098352da9ec1fb14587bfbbf309d7d81e))
- **kb:** fix build fail ([0c29895](https://github.com/resola-ai/deca-apps/commit/0c2989544031b12a5e038ee3818d714e4aa8aaf2))

### Bug Fixes

- **kb:** CB-1129 update kb form, kb pagination and split components ([413d9cf](https://github.com/resola-ai/deca-apps/commit/413d9cf2b1e0e92af23ab751fe387b1fdf5e67b0))
- **kb:** CB-1136 remove swr, update create qna with status ([0680597](https://github.com/resola-ai/deca-apps/commit/0680597d5b841f60780d163ad565366974405df9))
- **kb:** CB-1160 improve kb card and qna form ([6fb0650](https://github.com/resola-ai/deca-apps/commit/6fb0650a60936e2363a3fdd9dccce3d065d476b1))
- **kb:** CB-1180 improve qna detail page ([036716d](https://github.com/resola-ai/deca-apps/commit/036716dc8e9bc42b0470a012ebf3f48712d3cd0f))
- **kb:** CB-1181 fix lodash build error ([62ebb18](https://github.com/resola-ai/deca-apps/commit/62ebb185e84c704261249f62effeef7188788452))
- **kb:** CB-1181 update status and selection all ([cab9bd8](https://github.com/resola-ai/deca-apps/commit/cab9bd8e2b8b87b57b72fec2e80b92cc007dfa3e))
- **kb:** CB-1184 update generate qna api request payload ([aa39d4e](https://github.com/resola-ai/deca-apps/commit/aa39d4ec796c3f18155aa81059a910f993941ee4))
- **kb:** CB-1189 update cancel generate qna and improve status ([e8c12dc](https://github.com/resola-ai/deca-apps/commit/e8c12dcf0ee88167884a03d7e848ebb4e74daf28))
- **kb:** CB-1194 improve custom prompt ([a69edc4](https://github.com/resola-ai/deca-apps/commit/a69edc447373b58a34a819a67611f5568e8e359f))
- **kb:** CB-1194 improve custom prompt and qna api ([c5a41fc](https://github.com/resola-ai/deca-apps/commit/c5a41fc61c56cfbd35da6769afea7ae83bc2f5b4))
- **kb:** CB-1194 remove unused import ([1dff788](https://github.com/resola-ai/deca-apps/commit/1dff7881de259cb1eabfb391eae248c90454e95e))
- **kb:** CB-1194 update job status result ([fa5fcb9](https://github.com/resola-ai/deca-apps/commit/fa5fcb941678100ad25be7ebc6a7a2958be666df))
- **kb:** CB-1194 update kb card ([d392e4f](https://github.com/resola-ai/deca-apps/commit/d392e4f0239f66865a5ebc434684e841ea6f44d6))
- **kb:** CB-1811 update select all ([be970c1](https://github.com/resola-ai/deca-apps/commit/be970c19bf2ac7e5d0d8be9ce4608ad13666cbb8))
- **kb:** CB-569 - update step and cache the qna list component ([0e8a2a2](https://github.com/resola-ai/deca-apps/commit/0e8a2a29f1b504f13ac6118ba229517a55e4bd4b))
- **kb:** CB-580 - update step label line break and screen layout ([a27a14d](https://github.com/resola-ai/deca-apps/commit/a27a14d3b1b130b43056bcc4c6d9ff370a3bbf34))
- **kb:** CB-643 - update qna edit modal ([339f735](https://github.com/resola-ai/deca-apps/commit/339f735f5a840afd8004683d5a25d8df7fb26a00))
- **kb:** CB-662 update radio and checkbox button behavior ([3aea267](https://github.com/resola-ai/deca-apps/commit/3aea2678277a3e7bbd94d4fe1c68c3014e508999))
- **kb:** CB-764 update layout kb and logo ([d750dfa](https://github.com/resola-ai/deca-apps/commit/d750dfa937e09d1c1413621140d8d4140e4499c5))
