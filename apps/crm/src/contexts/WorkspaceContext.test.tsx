import { act, renderHook } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { WorkspaceContextProvider, useWorkspaceContext } from './WorkspaceContext';

// Mock all dependencies
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
  useParams: vi.fn(),
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(() => ({ 
    activeWorkspaceId: 'ws1',
    setReloadObject: vi.fn(),
    reloadObject: '',
    mutateObjects: vi.fn(),
  })),
}));

vi.mock('@/hooks', () => ({
  useData: vi.fn(() => ({
    records: [],
    totalRecords: 0,
    isLoading: false,
    mutate: vi.fn(),
    setSize: vi.fn(),
    isValidating: false,
  })),
  useObject: vi.fn(() => ({
    object: { id: 'obj1', name: 'Test Object', fields: [], permission: {}, userconfig: { viewId: 'view1' } },
    mutate: vi.fn(),
    objectLoading: false,
  })),
  useRecordsCount: vi.fn(() => ({ recordsCount: 0 })),
  useTags: vi.fn(() => ({ tags: [], mutate: vi.fn() })),
  useView: vi.fn(() => ({
    view: { id: 'view1', name: 'Default View' },
    mutateView: vi.fn(),
  })),
  useViews: vi.fn(() => ({
    views: [{ id: 'view1', name: 'Default View' }],
    isLoading: false,
    mutate: vi.fn(),
  })),
}));

vi.mock('@/services/api', () => ({
  FieldAPI: { save: vi.fn(), update: vi.fn(), delete: vi.fn() },
  ObjectAPI: { update: vi.fn() },
  RecordAPI: { updateByField: vi.fn(), delete: vi.fn() },
  ViewAPI: { save: vi.fn(), update: vi.fn(), delete: vi.fn() },
  WorkspaceAPI: { get: vi.fn() },
}));

vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: vi.fn(() => ({
    createPathWithLngParam: vi.fn((path) => path),
  })),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({ t: vi.fn((key) => key) })),
  Tolgee: vi.fn(() => ({
    init: vi.fn(),
    run: vi.fn(),
  })),
  TolgeeProvider: ({ children }: any) => children,
}));

vi.mock('@/utils', () => ({
  createNewRow: vi.fn(() => ({ id: 'new-row' })),
  findViewInList: vi.fn(),
  getSelectedRowDetails: vi.fn(() => ({ selectedCount: 0, allSelected: false })),
  handleActionRow: vi.fn(),
  isFieldInFiltersOrSorts: vi.fn(() => false),
  normalizeViewGroups: vi.fn(() => ({ views: [], viewGroups: [] })),
  sortPinnedRecords: vi.fn((records) => records),
  useShowRecordUpdateNotification: vi.fn(() => vi.fn()),
}));

vi.mock('@mantine/hooks', () => ({
  useDisclosure: vi.fn(() => [false, { open: vi.fn(), close: vi.fn() }]),
}));

vi.mock('@resola-ai/ui/components', () => ({
  FieldTypes: {
    SINGLE_LINE_TEXT: 'single-line-text',
    NUMBER: 'number',
    EMAIL: 'email',
    RELATIONSHIP: 'relationship',
  },
  ViewType: {
    GROUP: 'group',
    VIEW: 'view',
  },
}));

vi.mock('@resola-ai/ui/components/DecaTable/components/Toolbar', () => ({
  DefaultTableToolbarChangeTypes: {
    UPDATE_VIEW: 'UPDATE_VIEW',
  },
  TableCustomFieldsChangeTypes: {
    EDIT_COLUMN: 'EDIT_COLUMN',
    DELETE_COLUMN: 'DELETE_COLUMN',
  },
  TableFilterChangeTypes: {
    FILTER_VIEW: 'FILTER_VIEW',
  },
  TableSelectViewChangeTypes: {
    DELETE_VIEW: 'DELETE_VIEW',
    LOCK_VIEW: 'LOCK_VIEW',
  },
  TableSortChangeTypes: {
    SORT_FILTER_VIEW: 'SORT_FILTER_VIEW',
  },
}));

vi.mock('@resola-ai/ui/components/DecaTable/constants', () => ({
  TableMenuViewChangeTypes: {
    CREATE_VIEW: 'CREATE_VIEW',
    SWITCH_VIEW: 'SWITCH_VIEW',
  },
}));

vi.mock('@resola-ai/ui/components/DecaTable/utils', () => ({
  showCustomNotification: vi.fn(),
  PERMISSION_KEYS: {
    OBJECT_UPDATE: 'object:update',
  },
  isPermissionAllowed: vi.fn(() => true),
}));

vi.mock('@/configs', () => ({
  default: {
    BASE_PATH: '/app/',
  },
}));

vi.mock('@/constants/workspace', () => ({
  ColumnWidth: {
    text: 200,
    number: 150,
  },
  UN_ADDROW_FIELDS: ['id', 'createdAt', 'updatedAt'],
}));

describe('WorkspaceContext - Simplified Tests', () => {
  const mockNavigate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
    vi.mocked(useParams).mockReturnValue({
      wsId: 'ws1',
      id: 'obj1', // Use 'id' not 'objId'
      viewId: 'view1',
    });
  });

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <WorkspaceContextProvider>{children}</WorkspaceContextProvider>
  );

  describe('Basic Context Functionality', () => {
    it('should provide workspace context values', () => {
      const { result } = renderHook(() => useWorkspaceContext(), {
        wrapper: TestWrapper,
      });

      // Check the actual properties returned by WorkspaceContext
      expect(Array.isArray(result.current.data)).toBe(true);
      expect(Array.isArray(result.current.columns)).toBe(true);
      expect(typeof result.current.openProfile).toBe('function');
      expect(typeof result.current.handleViewChange).toBe('function');
      expect(typeof result.current.onSaveData).toBe('function');
      expect(typeof result.current.handleActionRow).toBe('function');
      expect(typeof result.current.handleAddColumn).toBe('function');
    });

    it('should handle profile opening with correct navigation', async () => {
      const { result } = renderHook(() => useWorkspaceContext(), {
        wrapper: TestWrapper,
      });

      await act(async () => {
        // Use the correct method name from the context
        result.current.openProfile('record1', { id: 'record1' } as any);
      });

      expect(mockNavigate).toHaveBeenCalledWith('/app/workspace/ws1/objects/obj1/views/view1/record1');
    });
  });

  describe('View Management - Basic Cases', () => {
    it('should handle view switching navigation', async () => {
      const { result } = renderHook(() => useWorkspaceContext(), {
        wrapper: TestWrapper,
      });

      await act(async () => {
        await result.current.handleViewChange('view2', { id: 'view2', name: 'Second View' } as any, 'SWITCH_VIEW');
      });

      expect(mockNavigate).toHaveBeenCalledWith('/app/workspace/ws1/objects/obj1/views/view2', { replace: true });
    });
  });

  describe('Error Handling', () => {
    it('should handle missing object ID gracefully', () => {
      vi.mocked(useParams).mockReturnValue({
        wsId: 'ws1',
        id: undefined,
        viewId: 'view1',
      });

      const { result } = renderHook(() => useWorkspaceContext(), {
        wrapper: TestWrapper,
      });

      // Should still provide basic context without crashing
      expect(Array.isArray(result.current.data)).toBe(true);
      expect(Array.isArray(result.current.columns)).toBe(true);
      expect(typeof result.current.openProfile).toBe('function');
    });
  });
});
