{"12h": "12 hours (3:32pm)", "24h": "24 hours (15:32)", "8to15Digits": "Enter 8 to 15 digits.", "activities": "Activities", "activityCreated": "Activity created", "activityDatetime": "Date Time", "activityDescription": "Description", "activityEditText": "Edit text", "activityGuideline": "Input offline interaction with the customer into activity for record tracking.", "activityTitle": "Event Title", "activityTitleRequired": "Please input the event title", "add": "Add", "addAction": "Add Action", "addCategory": "Add Category", "addColumn": "Add a column", "addGroup": "Add Group Name", "addOption": "Add option", "addParameter": "Add Parameter", "advancedSettings": "Advanced Settings", "allTimeZones": "ALL TIME ZONES", "apply": "Apply", "attachments": "Attachments", "avatar": "Avatar", "back": "Back", "bar": "Bar", "belongsTo": "Belongs to", "cancel": "Cancel", "changePicture": "Change Picture", "characters": "characters", "chooseImage": "Choose an image", "close": "Close", "color": "Color", "colorCode": "Color-code", "comma": "Number with commas", "commaOnly": "Comma only", "commaPeriod": "Comma, period", "confirm": "Confirm", "consultation": "Online Consultation", "copyToClipboard": "Copy to Clipboard", "create": "Create", "createActivity": "Create Activity", "createdAttachment": "Created Attachment", "createdProfile": "Created Profile", "createNewField": "Create new field", "createNewObject": "Create a new object", "crmFieldType": "CRM Field Type", "csvField": "CSV Field", "csvFieldMappingInfo": "We have automatically matched CSV fields to CRM. Only the data with matching field name and field type will be import directly to object, otherwise it will be save as a new column.", "currency": "<PERSON><PERSON><PERSON><PERSON>", "customAction": "Custom Action", "customCategories": "Customize Categories", "customObjectFields": "Custom Object Fields", "customObjectFieldsDescription": "Customize your fields, Create groups, reorder, and show/hide fields to organize your view. Changes will be applied to all records in this object.", "dateFormat": "Date Format", "dateOnly": "Date only", "dateWithStartEndTime": "Date with Start time and End time", "dateWithTime": "Date with time", "decimal": "Decimal", "decimalPlaces": "Decimal Places", "defaultCurrDate": "Default to current date", "defaultOption": "Default Option", "defaultView": "<PERSON><PERSON><PERSON>", "delete": "Delete", "deleteConfirmation": "Delete Group", "deletedAttachment": "Deleted Attachment", "deletedFieldWarning": "This variable references a deleted field. Please remove or replace it.", "deleteFile": "Delete File", "deleteGroupConfirmation": "Are you sure you want to delete the group?", "deleteTemplate": "Delete Template", "deleteTemplateWarning": "Are you sure you want to delete {template} ? The data will be deleted and will not be able to recover", "delImage": "Delete Image", "displayAsAProgressBar": "Display as progress bar", "displayLabel": "Display Label", "displayTimezone": "Display timezone", "done": "Done", "download": "Download", "dragAndDropText": "Drag and drop to upload CSV file or download sample file here.", "duplicateGroupName": "A group with this name already exists. Please change to another name. ", "edit": "Edit", "editObject": "Edit Object", "email": "Email", "emailAttachmentRule": "Limits: Max 10 files, 10MB per file, 25MB total. Formats: No .exe, .bat, .zip, etc.", "emailContent": "Email Content", "emailInValid": "Please enter a valid email address.", "emailPrimary": "Primary email", "emailSecondary": "Secondary email", "emailSmsSendFailed": "{editorType} fails to send.", "emailSubject": "Email Subject", "emailSubjectRequired": "email subject is required", "emailTemplate": "<PERSON>ail Te<PERSON>late", "emptyWs": "This Object is empty.\nPlease create a column to get started!", "enterCategoryName": "Enter Category Name", "enterObjectName": "Enter object name", "enterUrl": "Enter URL", "enterValue": "Enter value", "euFormat": "European: 13/06/2024", "eventCategory": "Event Category:", "fieldName": "Field Name", "fieldProtected": "Protected field", "fieldProtectedDesc": "Only the manager could edit the field.", "fields": "fields", "fieldsShownInGroup": "Fields Shown in Group", "fieldType": "Field Type", "fieldTypeDesc": {"autoNumber": "Automatically generate unique incremental numbers for each record.", "checkbox": "Check or uncheck to indicate status.", "createdBy": "See which user created the record.", "createdTime": "See the date and time each record was created.", "dateTime": "Enter a date or choose one from a calendar.", "email": "Enter an email address.", "image": "Add images to be viewed or downloaded.", "line": "LINE field", "longText": "Enter multiple lines of text.", "modifiedBy": "See which user made the most recent edit to some or all fields in a record.", "modifiedTime": "See the date and time each record was last modified.", "mulSelect": "Select one or more predefined options in a list.", "number": "Enter a number, or prefill each new cell with a default value.", "phoneNumber": "Enter a telephone number.", "singleSelect": "Select one predefined option from a list, or prefill each new cell with a default option.", "url": "Enter a URL."}, "fieldTypes": {"autoNumber": "Autonumber", "checkbox": "Checkbox", "createdBy": "Created By", "createdTime": "Created Time", "currency": "<PERSON><PERSON><PERSON><PERSON>", "dateTime": "Date and Time", "email": "Email", "image": "Image", "line": "LINE", "linkToRecord": "Link to another record", "longText": "Long Text", "modifiedBy": "Modified By", "modifiedTime": "Modified Time", "mulSelect": "Multiple Select", "number": "Number", "percent": "Percent", "phoneNumber": "Phone Number", "singleSelect": "Single Select", "singleText": "Single Line Text", "time": "Time", "url": "URL", "users": "User"}, "fileDeleted": "File deleted", "files": "Files", "filesSelected": "files selected", "fileUploadFailed": "File fail to upload. Please try again.", "filterNoResultFound": "No result found. Please adjust your filter.", "format": "Format", "forms": "Forms", "general": "General", "groupNameRequired": "Group name cannot be empty.", "headerName": "Header Name", "headerParameters": "Header parameters", "headerValue": "Header Value", "hearingForm": "Hearing Form Content", "history": "History", "icon": "Icon", "identities": "Identities", "identity": {"email": "Email", "lineId": "Line", "phone": "Phone", "webuserId": "Web"}, "image": "Image", "import": "Import", "importing": "Importing data, please wait a few moments...", "importSummary": "You're importing <strong> {count} new records </strong> from {fileName} to {objectName}", "importToExistingObject": "Import to existing object", "in5s": "in 5 seconds", "includeTime": "Include time", "inputDescription": "input template description", "inputEmailContent": "input email content", "inputFromUrl": "Input from URL", "inputHeaderName": "Input name", "inputHeaderValue": "Input value", "inputLabel": "Input Label", "inputMessageContent": "input SMS content", "inputName": "input template name", "inputSubject": "input email subject", "inputUrl": "Input URL", "integer": "Integer", "international": "International", "invalidImageUrl": "Invalid Image URL", "isoFormat": "ISO: YYYY-MM-DD", "jaFormat": "Japan : 2024/06/13", "japan": "Japan", "kataName": "Name (kana)", "keepBoth": "Keep both", "line": "LINE", "lineChannelDescription": "Channel ID to tie with LINE ID", "lineChannelName": "Select Line Channel", "linkTo": "Link to", "local": "Local", "longDate": "Long Date (June 13, 2024)", "longText": "Long Text", "manageCategoryGroups": "Customize Category", "manageEmailTemplates": "Manage Email Template", "manageSMSTemplates": "Manage SMS Templates", "manageTemplates": "Manage Templates", "manageView": "Manage Views", "manual": "Manual", "manuallyMerge": "manually merge", "matchingFields": "Matching fields", "maxFileSize": "Maximum file size is 5MB", "maximumSize": "The maximum size is 5MB", "maxLengthErr": "Input exceeds maximum character limit.", "menuName": "<PERSON>u Name", "merge": "<PERSON><PERSON>", "mergeProfileDescription": "The selected fields in master record will convert to be primary after merging.", "mergeRecord": "<PERSON>rge <PERSON>", "method": "Method", "moveTo": "Move to", "moveToFirstCol": "Move to the first column", "name": "Name", "newEmail": "New Email Message", "newGroup": "New Group", "newLine": "New Line Message", "newRecordAdded": "New records added.", "newSms": "New SMS Message", "newTag": "New Tag", "newTemplate": "New template", "next": "Next", "no": "No", "noAttachment": "No Attachments", "noColumnHasBeenAddedYet": "No column has been added yet.", "noFilesInCategory": "No files yet. Choose a file to add it to this category.", "noLineChannel": "No Line Channel", "nonManagerEditDesc": "This field is protected. Only the manager could edit it.", "northUS": "North America", "notSelected": "Not selected", "noViewPermissionDesc": "You don't have permission to access this view. \nPlease reach out to your team admin or manager to request permission", "numberFormat": "Number Format", "numberValidateErr": "Please enter a valid number.", "objectFields": "Object Fields", "offlineEvent": "Manual Record", "openLink": "Open link", "openLinkDesc": "Create a custom action link to an URL or a parameters related to the user profile.", "openLiveChat": "Open Livechat", "optional": "Optional", "overwrite": "Overwrite", "periodComma": "Period, comma", "phone": "Phone Number", "phoneInvalid": "Please enter 8 to 15 digits.", "pinToTabbar": "Pin to tabbar", "pleaseChooseObjectToImport": "Please choose the object to import into", "preview": "Preview", "primaryEmail": "Primary email", "primaryEmailDesc": "Used as primary email when sending a direct message.", "primaryPhone": "Primary phone number", "primaryPhoneDesc": "Used as primary phone number when sending a direct SMS.", "primaryText": "Primary Field", "receiverRequired": "receiver is required", "recordSelected": "{num} records selected", "recordUpdatedWithFilters": "Your records with filters have been updated.", "removeFile": "Remove File", "requiredField": " This field is requried", "reservationDate": "Reservation Date", "resetUpload": "Reset Upload", "ring": "Ring", "sameTimezone": "Use the same time zone for all collaborators", "sampleFile": "See how it looks.", "save": "Save", "search": "Search", "searchOrAddAnOpt": "Search or add an option..", "seeMore": "See More", "select": "Select", "selectAll": "Select All", "selectAttachment": "Select attachment", "selectFieldsToMerge": "Select the fields to merge", "selectHeader": "Select header", "selectMasterRecord": "Select master record", "selectMethod": "Select Method", "selectObject": "Select object", "selectOrAddAnOpt": "Select or add an option", "selectPreferences": "Select preferences", "sendBody": "Send body", "sendEmail": "Send Email", "sendHeaders": "Send header ", "sending": "Sending", "sendingIn5s": "Sending {editorType} in 5 seconds.", "sendLine": "Send Line", "sendSMS": "Send SMS", "showAs": "Show as", "showHiddenFields": "Show hidden fields", "showThousandsSeparator": "Show thousands separator", "skipThisField": "Skip this field", "sms": "SMS", "smsContent": "SMS Content", "smsPrimary": "Primary sms", "smsSecondary": "Secondary sms", "smsTemplate": "SMS Template", "sort": "Sort", "space": "Space", "spaceComma": "Space, comma", "spacePeriod": "Space, period", "specifyHeaders": "Specific Headers", "storeName": "Store Name", "subject": "Subject", "subjectRequired": "subject is required", "suggestTimeZone": "SUGGESTED TIME ZONES", "tag": "Tag", "tags": "Tags", "templateContent": "Template Content", "templateContentRequired": "template content is required", "templateDescription": "Template Description", "templateName": "Template Name", "templateNameRequired": "template name is required", "templateSaved": "Template Saved", "thisWeek": "This week", "thousandAndDecimalSeparator": "Thousand and decimal separator", "timeFormat": "Time format", "timeRange": "Time Range", "timeZone": "Time Zone", "timeZoneSearch": "Find a locale or timezone", "to": "To", "today": "Today", "totalFields": "Show {num} of {total} fields", "undo": "Undo", "ungroupedFields": "Move outside this group", "unpinTabbar": "Unpin", "unpinTabbarDescription": "Are you sure to unpin this tab, the unsaved change filed content will not be saved after this step.", "updatedField": "Updated Field", "updatedProfile": "Updated Profile", "updateLinkedObject": "Update Linked Object", "updateLinkedObjectDesc1": "Changing the Linked Object will clear existing linked record data.", "updateLinkedObjectDesc2": "When you select a new Linked Object, all current linked records will be removed.\nMake sure you no longer need the existing links before continuing.", "updateView": "Update view.", "upload": "Upload", "uploadCsvFile": "Upload CSV File", "uploadFile": "Upload file", "uploadImage": "Upload Image", "upTo50Characters": "Up to 50 characters", "urlInValid": "Please enter a valid URL.", "usd": "USD - $ (US Dollar)", "useFirstRowAsHeaders": "Use first row as headers", "usFormat": "United States: 06/13/2024", "usingFieldsBelow": "Using <PERSON> Bellow", "usingJson": "Using JSON", "vehicleCond": "Vehicle Condition", "view": "View Original", "viewAll": "View All", "viewDuplicated": "{name} view duplicated", "viewLocked": "{name} view locked", "viewUnlocked": "{name} view unlocked", "wasSent": "was sent. ", "webhook": "Webhook action", "webhookDesc": "Enter the details of the external service you wish to send the request to.", "yen": "YEN - ¥ (Japan Yen)", "yes": "Yes"}