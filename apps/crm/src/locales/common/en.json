{"activities": "Activities", "additionalRecordInformation": "Record Panel", "avatar": "Avatar", "cancel": "Cancel", "clear": "Clear", "clearData": "Clear Data", "clearDataObj": "Clear Data Object", "clearDesc": "Please make sure you wish to take this action before clicking the button below.", "clearLockWarning": "There is a locked view of the object. Please unlock the view to clear data object.", "clearWarning": "By Clicking the button “Clear Data” below, you will be clearing the data from the table, the data will be removed and not be recoverable. \nClearing data in this view will also apply to related views that share the same table's data.", "close": "Close", "copy": "Copy", "createNew": "Create New", "createNewWs": "Create a new workspace", "createObject": "Create Object", "crmViewLockDenied": "You don't have permission to lock this view. Please reach out to your team admin or manager to request permission.", "csvUpload": "CSV Upload", "customerManagement": "Customer Management", "customObject": "Custom Object", "delete": "Delete", "deleteConfirmMsg": "Are you sure you want to delete {name}?", "deleteLockWarning": "There is a locked view of the object. Please unlock the view to delete object.", "deleteObj": "Delete Object", "deleteWs": "Delete Workspace", "deleteWsDesc": "Please make sure you wish to take this action before clicking the button below.", "deleteWsWarning": "Are you sure you want to delete {name} workspace ? \nThe data will be removed and not be recoverable.", "dropFileHere": "Drop your file here...", "duplicate": "Duplicate", "edit": "Edit", "editObject": "Edit Object", "email": "Email", "errors": {"activityFilterRequired": "Activity Filter Required", "activityNotFound": "Activity Not Found", "activityUnsupportedOperation": "Activity Unsupported Operation ", "attachmentCreateFailed": "Attachment Create Failed", "attachmentNotFound": "Attachment Not Found", "attachmentOrgInfoNotFound": "Attachment Organization Information Not Found", "crmObjectCreateDenied": "You don't have permission to create this object. Please reach out to your team admin or manager to request permission.", "crmObjectDeleteDenied": "You don't have permission to delete this object. Please reach out to your team admin or manager to request permission.", "crmObjectReadDenied": "You don’t have permission to access this object. Please reach out to your team admin or manager to request permission.", "crmObjectUpdateDenied": "You don't have permission to update this object. Please reach out to your team admin or manager to request permission.", "crmViewCreateDenied": "You don't have permission to create the view. Please reach out to your team admin or manager to request permission.", "crmViewDeleteDenied": "You don't have permission to delete this view. Please reach out to your team admin or manager to request permission.", "crmViewListDenied": "You don’t have permission to access this view. Please reach out to your team admin or manager to request permission.", "crmViewReadDenied": "You don’t have permission to access this view. Please reach out to your team admin or manager to request permission.", "crmViewUpdateDenied": "You don't have permission to update this view. Please reach out to your team admin or manager to request permission.", "ERR_NETWORK": "Network Error", "historyDeleteFailed": "History Delete failed", "historyUpdateFailed": "History Update failed", "messengerGetFailed": "Messenger Get failed", "messengerSendFailed": "Messenger Send failed", "messengerUnknownError": "Messenger Unknown error", "objectInvalidFieldConfig": "Object Invalid Field Config", "objectInvalidReferenceConfig": "Object Invalid Reference Config", "objectNotFound": "Object Not Found", "recordFilterRequired": "Record Filter Required", "recordIdOrFilterRequired": "Record Id Or Filter Required", "recordNotFound": "Record Not Found", "recordUnknownError": "Record Unknown Error", "recordUnsupportedOperation": "Record Unsupported Operation", "recordValidationError": "Record Validation Error", "tagDuplicate": "Tag Duplicate", "tagNotFound": "Tag Not Found", "templateInvalidContent": "Template Invalid Content", "templateNotFound": "Template Not Found", "templateUnknownError": "Template <PERSON>", "viewInvalidConfig": "View Invalid Config", "viewLocked": "This view is locked, you must unlock it before making any edits. Only a manager can unlock a view.", "viewLockedOnlyManagerAllowed": "Only a manager can modify lock feature of the view", "viewNotFound": "View Not Found", "workspaceNotFound": "Workspace Not Found"}, "failed": "failed", "files": "Files", "formName": "Form Name", "general": "General", "history": "History", "icon": "Icon", "identities": "Identities", "importCsvFile": "Import CSV File", "inflowRoute": "INFLOW ROUTE", "integrations": "Integrations", "keepAtleastOne": "Please keep at least one object", "lastUpdated": "Last updated", "logout": "Logout", "longText": "Long Text", "manualRecord": "Manual Record", "matchingFields": "Matching fields", "mergeRecord": "<PERSON>rge <PERSON>", "message": "Messaging", "messaging": "Messaging", "myAccount": "MY ACCOUNT", "noObjectPermission": "Setting Access Denied", "objectIcon": "Icon", "objectName": "Name", "objects": "Objects", "objectSetting": "Object Setting", "page": "Page", "people": "People", "profile": "Profile", "profileCreation": "Profile Creation", "profileMatching": "Profile Matching", "recordDetail": "Record Details", "remove": "Remove", "rename": "<PERSON><PERSON>", "requiredField": " This field is requried", "reservationDate": "RESERVATION DATE", "reservationMade": "RESERVATION MADE", "reservationMenuType": "RESERVATION MENU TYPE", "resetUpload": "Reset Upload", "save": "Save", "saveChanges": "Save Changes", "searchPlaceholder": "Type to add parameter", "selectObject": "Select object", "sent": "sent", "sentAt": "SEND AT", "setting": "Setting", "settings": "Settings", "source": "SOURCE", "submittedDate": "SUBMITTED DATE ", "tags": "Tags", "update": "Update", "uploadFile": "Upload file", "url": "URL", "websiteVisit": "WEBSITE VISIT", "welcomeTitle": "Welcome to DECA CRM", "widget": "Widget", "workspace": "WORKSPACE"}