import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { describe, expect, it, vi } from 'vitest';

// Import App directly
import App from '../App';

// Mock the components that are rendered by App
vi.mock('../components/BaseLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='base-layout'>{children}</div>
  ),
}));

vi.mock('../components/Home', () => ({
  default: () => <div data-testid='home-component'>Home Component</div>,
}));

vi.mock('../components/Workspace', () => ({
  default: () => <div data-testid='workspace-component'>Workspace Component</div>,
}));

describe('App Component', () => {
  it('should be defined', () => {
    expect(App).toBeDefined();
  });

  it('should render BaseLayout wrapper', () => {
    renderWithMantine(
      <MemoryRouter initialEntries={['/crm']}>
        <App />
      </MemoryRouter>
    );

    expect(screen.getByTestId('base-layout')).toBeInTheDocument();
  });

  it('should render Home component for /crm route', () => {
    renderWithMantine(
      <MemoryRouter initialEntries={['/crm']}>
        <App />
      </MemoryRouter>
    );

    expect(screen.getByTestId('home-component')).toBeInTheDocument();
    expect(screen.getByText('Home Component')).toBeInTheDocument();
  });

  it('should render Home component for /crm/workspace/:wsId route', () => {
    renderWithMantine(
      <MemoryRouter initialEntries={['/crm/workspace/ws123']}>
        <App />
      </MemoryRouter>
    );

    expect(screen.getByTestId('home-component')).toBeInTheDocument();
    expect(screen.getByText('Home Component')).toBeInTheDocument();
  });

  it('should render Workspace component for /crm/workspace/:wsId/objects/:id route', () => {
    renderWithMantine(
      <MemoryRouter initialEntries={['/crm/workspace/ws123/objects/obj456']}>
        <App />
      </MemoryRouter>
    );

    expect(screen.getByTestId('workspace-component')).toBeInTheDocument();
    expect(screen.getByText('Workspace Component')).toBeInTheDocument();
  });

  it('should render Workspace component for /crm/workspace/:wsId/objects/:id/views/:viewId route', () => {
    renderWithMantine(
      <MemoryRouter initialEntries={['/crm/workspace/ws123/objects/obj456/views/view789']}>
        <App />
      </MemoryRouter>
    );

    expect(screen.getByTestId('workspace-component')).toBeInTheDocument();
    expect(screen.getByText('Workspace Component')).toBeInTheDocument();
  });

  it('should render Workspace component for /crm/workspace/:wsId/objects/:id/:recordId route', () => {
    renderWithMantine(
      <MemoryRouter initialEntries={['/crm/workspace/ws123/objects/obj456/record123']}>
        <App />
      </MemoryRouter>
    );

    expect(screen.getByTestId('workspace-component')).toBeInTheDocument();
    expect(screen.getByText('Workspace Component')).toBeInTheDocument();
  });

  it('should render Workspace component for /crm/workspace/:wsId/objects/:id/views/:viewId/:recordId route', () => {
    renderWithMantine(
      <MemoryRouter
        initialEntries={['/crm/workspace/ws123/objects/obj456/views/view789/record123']}
      >
        <App />
      </MemoryRouter>
    );

    expect(screen.getByTestId('workspace-component')).toBeInTheDocument();
    expect(screen.getByText('Workspace Component')).toBeInTheDocument();
  });

  it('should handle nested routes correctly', () => {
    renderWithMantine(
      <MemoryRouter
        initialEntries={['/crm/workspace/ws123/objects/obj456/views/view789/record123']}
      >
        <App />
      </MemoryRouter>
    );

    // Should render both BaseLayout and Workspace
    expect(screen.getByTestId('base-layout')).toBeInTheDocument();
    expect(screen.getByTestId('workspace-component')).toBeInTheDocument();
  });

  it('should render correctly without any specific route', () => {
    renderWithMantine(
      <MemoryRouter initialEntries={['/some-other-route']}>
        <App />
      </MemoryRouter>
    );

    // Should still render BaseLayout wrapper
    expect(screen.getByTestId('base-layout')).toBeInTheDocument();
    // But should not render any specific component since route doesn't match
    expect(screen.queryByTestId('home-component')).not.toBeInTheDocument();
    expect(screen.queryByTestId('workspace-component')).not.toBeInTheDocument();
  });
});
