import { beforeEach, describe, expect, it } from 'vitest';

// Polyfill implementation for testing
const addPolyfill = () => {
  if (typeof Promise.withResolvers !== 'function') {
    Promise.withResolvers = <T>() => {
      let resolve!: (value: T | PromiseLike<T>) => void;
      let reject!: (reason?: any) => void;
      const promise = new Promise<T>((res, rej) => {
        resolve = res;
        reject = rej;
      });
      return { promise, resolve, reject };
    };
  }
};

describe('promiseWithResolvers polyfill', () => {
  let originalPromiseWithResolvers: any;

  beforeEach(() => {
    // Store original implementation if it exists
    originalPromiseWithResolvers = (Promise as any).withResolvers;
  });

  afterEach(() => {
    // Restore original implementation
    if (originalPromiseWithResolvers !== undefined) {
      (Promise as any).withResolvers = originalPromiseWithResolvers;
    } else {
      delete (Promise as any).withResolvers;
    }
  });

  it('should not add polyfill when Promise.withResolvers already exists', () => {
    // Setup: Promise.withResolvers already exists
    const mockWithResolvers = () => ({ promise: Promise.resolve(), resolve: () => {}, reject: () => {} });
    (Promise as any).withResolvers = mockWithResolvers;

    // Run polyfill logic
    addPolyfill();

    // Should not override existing implementation
    expect((Promise as any).withResolvers).toBe(mockWithResolvers);
  });

  it('should add polyfill when Promise.withResolvers does not exist', () => {
    // Setup: Remove Promise.withResolvers
    delete (Promise as any).withResolvers;

    // Run polyfill logic
    addPolyfill();

    // Should have added the polyfill
    expect(typeof (Promise as any).withResolvers).toBe('function');
  });

  describe('polyfill functionality', () => {
    beforeEach(() => {
      // Remove existing implementation to test polyfill
      delete (Promise as any).withResolvers;
      
      // Add the polyfill
      addPolyfill();
    });

    it('should return an object with promise, resolve, and reject', () => {
      const result = (Promise as any).withResolvers();

      expect(result).toHaveProperty('promise');
      expect(result).toHaveProperty('resolve');
      expect(result).toHaveProperty('reject');
      
      expect(result.promise).toBeInstanceOf(Promise);
      expect(typeof result.resolve).toBe('function');
      expect(typeof result.reject).toBe('function');
    });

    it('should resolve the promise when resolve is called', async () => {
      const { promise, resolve } = (Promise as any).withResolvers();
      const testValue = 'test value';

      resolve(testValue);

      await expect(promise).resolves.toBe(testValue);
    });

    it('should reject the promise when reject is called', async () => {
      const { promise, reject } = (Promise as any).withResolvers();
      const testError = new Error('test error');

      reject(testError);

      await expect(promise).rejects.toBe(testError);
    });

    it('should work with different types', async () => {
      const { promise: numberPromise, resolve: resolveNumber } = (Promise as any).withResolvers();
      const { promise: objectPromise, resolve: resolveObject } = (Promise as any).withResolvers();

      resolveNumber(42);
      resolveObject({ id: 1 });

      await expect(numberPromise).resolves.toBe(42);
      await expect(objectPromise).resolves.toEqual({ id: 1 });
    });

    it('should work with PromiseLike values', async () => {
      const { promise, resolve } = (Promise as any).withResolvers();
      const thenable = {
        then: (onFulfilled: (value: string) => void) => {
          setTimeout(() => onFulfilled('thenable value'), 0);
        },
      };

      resolve(thenable);

      await expect(promise).resolves.toBe('thenable value');
    });

    it('should handle rejection without reason', async () => {
      const { promise, reject } = (Promise as any).withResolvers();

      reject();

      await expect(promise).rejects.toBeUndefined();
    });

    it('should handle multiple calls to withResolvers', () => {
      const result1 = (Promise as any).withResolvers();
      const result2 = (Promise as any).withResolvers();

      expect(result1.promise).not.toBe(result2.promise);
      expect(result1.resolve).not.toBe(result2.resolve);
      expect(result1.reject).not.toBe(result2.reject);
    });

    it('should maintain proper promise state', async () => {
      const { promise, resolve, reject } = (Promise as any).withResolvers();

      // Promise should be pending initially
      expect(promise).toBeInstanceOf(Promise);

      // Resolve should work
      resolve('resolved');
      await expect(promise).resolves.toBe('resolved');

      // Should not be affected by subsequent reject calls
      reject(new Error('should not affect resolved promise'));
      await expect(promise).resolves.toBe('resolved');
    });
  });

  describe('TypeScript type checking', () => {
    beforeEach(() => {
      // Remove existing implementation to test polyfill
      delete (Promise as any).withResolvers;
      
      // Add the polyfill
      addPolyfill();
    });

    it('should work with typed promises', async () => {
      // Test that TypeScript types work correctly
      interface TestInterface {
        name: string;
        value: number;
      }

      // Use proper typing by casting the result
      const { promise, resolve } = Promise.withResolvers() as {
        promise: Promise<TestInterface>;
        resolve: (value: TestInterface) => void;
        reject: (reason?: any) => void;
      };
      
      const testObject: TestInterface = { name: 'test', value: 123 };

      resolve(testObject);

      const result = await promise;
      expect(result.name).toBe('test');
      expect(result.value).toBe(123);
    });
  });
}); 