import { describe, expect, it, vi, beforeEach } from 'vitest';
import crmAxiosService from './index';

// Mock dependencies
vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
  },
}));

vi.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    init: vi.fn(),
    setAccessToken: vi.fn(),
    getAccessToken: vi.fn(() => 'mock-token'),
    setOrgId: vi.fn(),
    getOrgId: vi.fn(() => 'mock-org-id'),
    instance: {
      interceptors: {
        response: {
          use: vi.fn(),
        },
      },
    },
  },
}));

vi.mock('@resola-ai/ui/constants', () => ({
  HTTP_ERROR_STATUS: {
    UNAUTHORIZED: 401,
  },
}));

vi.mock('@tabler/icons-react', () => ({
  IconAlertCircle: vi.fn(),
}));

vi.mock('react', () => ({
  createElement: vi.fn((component, props) => ({ component, props })),
}));

vi.mock('../../tolgee', () => ({
  tolgee: {
    t: vi.fn((key: string) => `translated-${key}`),
  },
}));

vi.mock('../../utils/workspace', () => ({
  customNotificationStyles: vi.fn(() => ({ color: 'red' })),
}));

// Import mocked modules
import { axiosService } from '@resola-ai/services-shared';
import { tolgee } from '../../tolgee';

describe('AxiosService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize the global axios service with apiServerUrl and tolgee.t', () => {
      const apiServerUrl = 'https://api.example.com';
      
      crmAxiosService.init(apiServerUrl);

      expect(axiosService.init).toHaveBeenCalledWith(apiServerUrl, tolgee.t);
    });

    it('should complete initialization without errors', () => {
      const apiServerUrl = 'https://api.example.com';
      
      expect(() => {
        crmAxiosService.init(apiServerUrl);
      }).not.toThrow();
    });
  });

  describe('token management', () => {
    it('should set access token', () => {
      const token = 'test-access-token';
      
      crmAxiosService.setAccessToken(token);

      expect(axiosService.setAccessToken).toHaveBeenCalledWith(token);
    });

    it('should get access token', () => {
      const result = crmAxiosService.getAccessToken();

      expect(axiosService.getAccessToken).toHaveBeenCalled();
      expect(result).toBe('mock-token');
    });
  });

  describe('organization management', () => {
    it('should set organization ID', () => {
      const orgId = 'test-org-123';
      
      crmAxiosService.setOrgId(orgId);

      expect(axiosService.setOrgId).toHaveBeenCalledWith(orgId);
    });

    it('should get organization ID', () => {
      const result = crmAxiosService.getOrgId();

      expect(axiosService.getOrgId).toHaveBeenCalled();
      expect(result).toBe('mock-org-id');
    });
  });

  describe('instance access', () => {
    it('should provide access to axios instance', () => {
      const result = crmAxiosService.instance;

      expect(result).toBe(axiosService.instance);
    });
  });

  describe('service class structure', () => {
    it('should be a class instance with required methods', () => {
      expect(typeof crmAxiosService.init).toBe('function');
      expect(typeof crmAxiosService.setAccessToken).toBe('function');
      expect(typeof crmAxiosService.getAccessToken).toBe('function');
      expect(typeof crmAxiosService.setOrgId).toBe('function');
      expect(typeof crmAxiosService.getOrgId).toBe('function');
      expect(typeof crmAxiosService.instance).toBe('object');
    });

    it('should handle multiple init calls without errors', () => {
      const apiServerUrl = 'https://api.example.com';
      
      expect(() => {
        crmAxiosService.init(apiServerUrl);
        crmAxiosService.init(apiServerUrl);
      }).not.toThrow();
    });

    it('should handle method calls in any order', () => {
      expect(() => {
        crmAxiosService.setAccessToken('token1');
        crmAxiosService.setOrgId('org1');
        crmAxiosService.init('https://api.example.com');
        crmAxiosService.getAccessToken();
        crmAxiosService.getOrgId();
      }).not.toThrow();
    });
  });

  describe('error handling', () => {
    it('should handle invalid initialization parameters gracefully', () => {
      expect(() => {
        crmAxiosService.init('');
        crmAxiosService.init(null as any);
        crmAxiosService.init(undefined as any);
      }).not.toThrow();
    });

    it('should handle invalid token values gracefully', () => {
      expect(() => {
        crmAxiosService.setAccessToken('');
        crmAxiosService.setAccessToken(null as any);
        crmAxiosService.setAccessToken(undefined as any);
      }).not.toThrow();
    });

    it('should handle invalid organization ID values gracefully', () => {
      expect(() => {
        crmAxiosService.setOrgId('');
        crmAxiosService.setOrgId(null as any);
        crmAxiosService.setOrgId(undefined as any);
      }).not.toThrow();
    });
  });
}); 