import { describe, expect, it } from 'vitest';
import { SwrCustomConfig, Workspace } from './index';

describe('Components Index', () => {
  it('should export Workspace component', () => {
    expect(Workspace).toBeDefined();
    expect(typeof Workspace).toBe('function');
  });

  it('should export SwrCustomConfig component', () => {
    expect(SwrCustomConfig).toBeDefined();
    expect(typeof SwrCustomConfig).toBe('function');
  });
});
