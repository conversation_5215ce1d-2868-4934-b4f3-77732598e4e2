import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import { describe, expect, it, vi } from 'vitest';
import { FilterEmptyState } from './FilterEmptyState';

// Mock EmptyState component
vi.mock('./EmptyState', () => ({
  EmptyState: ({ imageUrl, message }: { imageUrl: string; message: string }) => (
    <div data-testid='empty-state'>
      <div data-testid='empty-state-image'>{imageUrl}</div>
      <div data-testid='empty-state-message'>{message}</div>
    </div>
  ),
}));

// Mock translations
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
}));

describe('FilterEmptyState', () => {
  beforeEach(() => {
    // Mock the useTranslate hook
    (useTranslate as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      t: (key: string) => (key === 'filterNoResultFound' ? 'No results found' : key),
    });
  });

  it('renders EmptyState with correct props', () => {
    renderWithMantine(<FilterEmptyState />);

    // Check that EmptyState is rendered
    const emptyState = screen.getByTestId('empty-state');
    expect(emptyState).toBeInTheDocument();

    // Check that the correct image URL is passed
    const imageElement = screen.getByTestId('empty-state-image');
    expect(imageElement).toHaveTextContent('images/no_search_result.png');

    // Check that the correct message is passed
    const messageElement = screen.getByTestId('empty-state-message');
    expect(messageElement).toHaveTextContent('No results found');
  });
});
