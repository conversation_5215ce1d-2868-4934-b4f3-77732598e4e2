import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import { describe, expect, it, vi } from 'vitest';
import { WorkspaceEmptyState } from './WorkspaceEmptyState';

// Mock EmptyState component
vi.mock('./EmptyState', () => ({
  EmptyState: ({
    imageUrl,
    message,
    children,
  }: { imageUrl: string; message: string; children: React.ReactNode }) => (
    <div data-testid='empty-state'>
      <div data-testid='empty-state-image'>{imageUrl}</div>
      <div data-testid='empty-state-message'>{message}</div>
      <div data-testid='empty-state-children'>{children}</div>
    </div>
  ),
}));

// Mock DecaButton component
vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick }: { children: React.ReactNode; onClick: () => void }) => (
    <button data-testid='deca-button' onClick={onClick}>
      {children}
    </button>
  ),
}));

// Mock IconPlus
vi.mock('@tabler/icons-react', () => ({
  IconPlus: () => <div data-testid='icon-plus'>PlusIcon</div>,
}));

// Mock translations
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
}));

describe('WorkspaceEmptyState', () => {
  const mockClickToAdd = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock the useTranslate hook
    (useTranslate as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      t: (key: string) => {
        if (key === 'emptyWs') return 'Workspace is empty';
        if (key === 'addColumn') return 'Add Column';
        return key;
      },
    });
  });

  it('renders EmptyState with correct props', () => {
    renderWithMantine(<WorkspaceEmptyState clickToAdd={mockClickToAdd} />);

    // Check that EmptyState is rendered
    const emptyState = screen.getByTestId('empty-state');
    expect(emptyState).toBeInTheDocument();

    // Check that the correct image URL is passed
    const imageElement = screen.getByTestId('empty-state-image');
    expect(imageElement).toHaveTextContent('images/img_edgecase.png');

    // Check that the correct message is passed
    const messageElement = screen.getByTestId('empty-state-message');
    expect(messageElement).toHaveTextContent('Workspace is empty');
  });

  it('renders a DecaButton with the "Add Column" text', () => {
    renderWithMantine(<WorkspaceEmptyState clickToAdd={mockClickToAdd} />);

    const button = screen.getByTestId('deca-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Add Column');
  });

  it('calls clickToAdd when the button is clicked', () => {
    renderWithMantine(<WorkspaceEmptyState clickToAdd={mockClickToAdd} />);

    const button = screen.getByTestId('deca-button');
    fireEvent.click(button);

    expect(mockClickToAdd).toHaveBeenCalledTimes(1);
  });
});
