import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { EmptyState } from './EmptyState';

// Mock the CustomImage component from @resola-ai/ui
vi.mock('@resola-ai/ui', () => ({
  CustomImage: ({ url, className }: { url: string; className?: string }) => (
    <div data-testid='custom-image' className={className}>
      <span data-testid='image-url'>{url}</span>
    </div>
  ),
}));

describe('EmptyState', () => {
  const mockProps = {
    imageUrl: 'test-image.png',
    message: 'Test empty state message',
    className: 'test-class',
  };

  it('renders with the correct image URL and message', () => {
    renderWithMantine(<EmptyState {...mockProps} />);

    const image = screen.getByTestId('custom-image');
    expect(image).toBeInTheDocument();

    const imageUrl = screen.getByTestId('image-url');
    expect(imageUrl).toHaveTextContent(mockProps.imageUrl);

    const message = screen.getByText(mockProps.message);
    expect(message).toBeInTheDocument();
  });

  it('applies custom className', () => {
    renderWithMantine(<EmptyState {...mockProps} />);

    const rootElement = screen.getByTestId('custom-image').parentElement;
    expect(rootElement).toHaveClass('test-class');
  });

  it('renders children when provided', () => {
    renderWithMantine(
      <EmptyState {...mockProps}>
        <button data-testid='child-button'>Test Button</button>
      </EmptyState>
    );

    const childButton = screen.getByTestId('child-button');
    expect(childButton).toBeInTheDocument();
    expect(childButton).toHaveTextContent('Test Button');
  });
});
