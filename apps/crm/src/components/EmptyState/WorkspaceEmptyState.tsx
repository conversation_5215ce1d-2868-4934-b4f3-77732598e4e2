import { IconPlus } from '@tabler/icons-react';

import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { EmptyState } from './EmptyState';

const emptyImage = 'images/img_edgecase.png';

export const WorkspaceEmptyState = ({ clickToAdd }: { clickToAdd: () => void }) => {
  const { t } = useTranslate('workspace');

  return (
    <EmptyState imageUrl={emptyImage} message={t('emptyWs')}>
      <DecaButton radius='xl' leftSection={<IconPlus size={18} />} onClick={clickToAdd}>
        {t('addColumn')}
      </DecaButton>
    </EmptyState>
  );
};
