import { Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type { FC, PropsWithChildren } from 'react';

import { CustomImage } from '@resola-ai/ui';

const useStyles = createStyles(() => ({
  root: {
    height: '65%',
    width: '100%',
  },
  emptyImage: {
    height: 'fit-content',
    maxHeight: rem(240),
    maxWidth: rem(240),
  },
  message: {
    textAlign: 'center',
    whiteSpace: 'pre-line',
  },
}));

interface EmptyStateProps {
  imageUrl: string;
  message: string;
  className?: string;
}

export const EmptyState: FC<PropsWithChildren<EmptyStateProps>> = (props) => {
  const { children, imageUrl, message, className } = props;
  const { classes, cx } = useStyles();

  return (
    <Flex
      className={cx(classes.root, className)}
      justify='center'
      align='center'
      direction='column'
    >
      <CustomImage className={classes.emptyImage} url={imageUrl} />
      <Text className={classes.message} c='decaGrey.6' fw={500} ta='center' my={rem(20)}>
        {message}
      </Text>
      {children}
    </Flex>
  );
};
