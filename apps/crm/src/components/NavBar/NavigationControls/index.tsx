import { NAVBAR_MIN_WIDTH, NAVBAR_WIDTH } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { NavbarContextProvider, useNavbarContext } from '@/contexts/NavbarContext';
import type { WSObject } from '@/models';
import { ObjectAPI } from '@/services/api/object';
import { DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  rectSortingStrategy,
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable';
import { ActionIcon, Box, Divider, Flex, Menu, ScrollArea, Stack, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { Modal } from '@resola-ai/ui';
import { IconChevronsLeft, IconMenu2, IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import CsvImport from '../../CsvImport';
import { NameForm } from './NameForm';
import { ObjectSettings } from './ObjectSettings';
import WorkspaceItem from './WorkspaceItem';
import { useNavigateObject } from './useNavigateObject';

const COLLAPSED_HEIGHT = 170;

const useStyles = createStyles((theme) => ({
  container: {
    color: theme.colors.decaGrey[8],
    fontWeight: 500,
    width: '100%',
    height: '100%',
    justifyContent: 'space-between',
    flexDirection: 'column',
  },
  navLink: {
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center',
    fontSize: rem(14),
    gap: rem(12),
    color: theme.colors.decaGrey[8],
    padding: rem(8),

    '&:hover': {
      borderRadius: rem(8),
      backgroundColor: theme.colors.decaNavy[0],
      color: theme.colors.decaNavy[5],

      '& svg': {
        color: theme.colors.decaNavy[5],
      },
    },
  },
  activeLink: {
    borderRadius: rem(8),
    backgroundColor: theme.colors.decaNavy[0],
    color: theme.colors.decaNavy[5],

    '& svg': {
      color: theme.colors.decaNavy[5],
    },
  },
  iconStyle: {
    '&:hover': {
      color: theme.colors.decaNavy[5],
    },
  },
  text: {
    overflow: 'hidden',
    wordBreak: 'break-all',
  },
  objects: {
    '.mantine-ScrollArea-viewport': {
      '& > div:first-of-type': {
        display: 'block !important',
      },
    },
  },
  modal: {
    '.mantine-Modal-content': {
      height: '80vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      '.mantine-Modal-body': {
        height: `calc(100% - ${rem(60)})`,
        '> div': {
          marginRight: rem(-8),
          marginLeft: rem(-8),
          height: '100%',
        },
      },
    },
  },
}));

const Navigation: React.FC<any> = () => {
  const { classes } = useStyles();
  const { t } = useTranslate('common');
  const {
    objects,
    onToggleSidebar,
    formOpened,
    setFormOpened,
    setSidebarWidth,
    sidebarWidth,
    mutateObjects,
  } = useAppContext();
  const { settingOpened, onCloseSetting } = useNavbarContext();
  const { handleNavigateObject } = useNavigateObject();

  const [isImportModalOpened, { open: onOpenImportModal, close: onCloseImportModal }] =
    useDisclosure(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  const { id, wsId } = useParams();
  const itemRefs = useRef({});
  const hasScrolledRef = useRef<string | null>(null);
  const [highlightedItemId, setHighlightedItemId] = useState<string | null>(null);
  const [dropPosition, setDropPosition] = useState<{
    id: string;
    position: 'top' | 'bottom';
  } | null>(null);
  const mouseTrackerRef = useRef<(() => void) | null>(null);
  const currentTargetRef = useRef<string | null>(null);

  useEffect(() => {
    if (id && itemRefs.current[id] && hasScrolledRef.current !== id) {
      itemRefs.current[id].scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
      hasScrolledRef.current = id;
    }
  }, [id, objects]);

  const calculateDropPosition = useCallback((itemId: string) => {
    // Only set up new tracker if target changed
    if (currentTargetRef.current === itemId) {
      return;
    }

    // Clean up previous mouse tracker
    if (mouseTrackerRef.current) {
      mouseTrackerRef.current();
    }

    currentTargetRef.current = itemId;

    const element = document.querySelector(`[data-testid="workspace-item-${itemId}"]`);
    if (element) {
      const handleMouseMove = (event: MouseEvent) => {
        const rect = element.getBoundingClientRect();
        const elementCenter = rect.top + rect.height / 2;
        const mouseY = event.clientY;
        const position = mouseY < elementCenter ? 'top' : 'bottom';

        setDropPosition((prev) => {
          // Only update if position actually changed
          if (prev?.id === itemId && prev?.position === position) {
            return prev;
          }
          return { id: itemId, position };
        });
      };

      // Add mouse move listener for continuous updates
      document.addEventListener('mousemove', handleMouseMove, { passive: true });

      // Store cleanup function
      mouseTrackerRef.current = () => {
        document.removeEventListener('mousemove', handleMouseMove);
      };

      // Trigger initial calculation with a default position
      setDropPosition({ id: itemId, position: null as any });
    }
  }, []);

  const clearDropPosition = useCallback(() => {
    setDropPosition(null);
    currentTargetRef.current = null;
    if (mouseTrackerRef.current) {
      mouseTrackerRef.current();
      mouseTrackerRef.current = null;
    }
  }, []);

  const handleSave = (obj: WSObject) => {
    setFormOpened(false);
    handleNavigateObject(obj.id);
  };

  const handleDragEnd = async (active: any, over: any) => {
    clearDropPosition();
    if (over && active.id !== over.id) {
      const oldIndex = objects.findIndex((obj) => obj.id === active.id);
      const newIndex = objects.findIndex((obj) => obj.id === over.id);
      if (oldIndex === -1 || newIndex === -1) return;

      const updatedObjects = arrayMove(objects, oldIndex, newIndex);
      await mutateObjects(updatedObjects, { revalidate: false });

      setHighlightedItemId(active.id);
      setTimeout(() => {
        setHighlightedItemId(null);
      }, 1500);

      // Get previous and next object IDs of the moved item
      const prevId = updatedObjects?.[newIndex - 1]?.id ?? '';
      const nextId = updatedObjects?.[newIndex + 1]?.id ?? '';

      await ObjectAPI.reorder(wsId ?? '', active.id, {
        prevId,
        nextId,
      });
      await mutateObjects();
    }
  };

  const isCollapsed = sidebarWidth <= NAVBAR_MIN_WIDTH;

  return (
    <Flex
      w={'100%'}
      h={'100%'}
      className={classes.container}
      data-testid='navigation-container'
      py={rem(12)}
    >
      {formOpened ? (
        <Stack px={rem(4)} h={'100%'}>
          <NameForm onCancel={() => setFormOpened(false)} onSave={handleSave} />
        </Stack>
      ) : (
        <React.Fragment>
          <Box data-testid='navigation-container-box' px={rem(12)} pb={rem(4)}>
            <Box w={'100%'}>
              <Flex justify={!isCollapsed ? 'space-between' : 'center'} p={rem(8)} align={'center'}>
                <Text fw={500} size={rem(12)} c={'decaGrey.9'} w={rem(80)} truncate>
                  {t('objects')}
                </Text>
                <ActionIcon
                  data-testid='toggle-sidebar-button'
                  variant='subtle'
                  c={'decaGrey.4'}
                  onClick={() => {
                    onToggleSidebar();
                  }}
                >
                  {!isCollapsed ? <IconChevronsLeft size={14} /> : <IconMenu2 size={14} />}
                </ActionIcon>
              </Flex>

              {objects.length > 0 && (
                <ScrollArea
                  h={`calc(100vh - ${rem(COLLAPSED_HEIGHT)})`}
                  type='never'
                  className={classes.objects}
                >
                  <Stack gap={0} p={rem(2)}>
                    <DndContext
                      sensors={sensors}
                      onDragEnd={({ active, over }) => {
                        clearDropPosition();
                        if (over && active.id !== over.id) {
                          handleDragEnd(active, over);
                        }
                      }}
                      onDragOver={({ over, active }) => {
                        if (over && active.id !== over.id) {
                          calculateDropPosition(over.id as string);
                        } else {
                          clearDropPosition();
                        }
                      }}
                    >
                      <SortableContext items={objects || []} strategy={rectSortingStrategy}>
                        <Flex
                          mah={rem(280)}
                          direction={'column'}
                          mt={objects?.length ? rem(10) : 0}
                        >
                          {objects.map((item, index) => (
                            <Box ref={(el) => (itemRefs.current[item.id] = el)} key={index}>
                              <WorkspaceItem
                                key={item.id}
                                item={item}
                                isHighlighted={highlightedItemId === item.id}
                                dropPosition={
                                  dropPosition?.id === item.id ? dropPosition.position : null
                                }
                              />
                            </Box>
                          ))}
                        </Flex>
                      </SortableContext>
                    </DndContext>
                  </Stack>
                </ScrollArea>
              )}
            </Box>
          </Box>
          <Box w={'100%'} c={'decaGrey.9'} p={rem(12)} pt={rem(0)}>
            <Divider w={'100%'} />
            <Flex
              justify={!isCollapsed ? 'space-between' : 'center'}
              px={rem(8)}
              pt={rem(8)}
              align={'center'}
            >
              <Text size={rem(12)} c={'decaGrey.9'} w={rem(80)} truncate>
                {t('createNew').toUpperCase()}
              </Text>
              <Menu width={rem(185)} position='right-end'>
                <Menu.Target>
                  <ActionIcon
                    variant='subtle'
                    radius={'xl'}
                    c={'decaGrey.4'}
                    data-testid='create-new-button'
                  >
                    <IconPlus size={14} />
                  </ActionIcon>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item
                    fz={rem(14)}
                    onClick={() => {
                      if (isCollapsed) {
                        onToggleSidebar();
                      }
                      setSidebarWidth(NAVBAR_WIDTH);
                      setFormOpened(true);
                    }}
                  >
                    {t('createObject')}
                  </Menu.Item>
                  <Menu.Item fz={rem(14)} onClick={onOpenImportModal}>
                    {t('importCsvFile')}
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </Flex>
          </Box>
          <Modal
            size={rem(720)}
            opened={settingOpened}
            onClose={onCloseSetting}
            title={t('objectSetting')}
            centered
            className={classes.modal}
            closeOnClickOutside
          >
            <ObjectSettings />
          </Modal>
          <CsvImport opened={isImportModalOpened} onClose={onCloseImportModal} />
        </React.Fragment>
      )}
    </Flex>
  );
};

const NavigationControls = () => {
  return (
    <NavbarContextProvider>
      <Navigation />
    </NavbarContextProvider>
  );
};

export default NavigationControls;
