import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Home from './index';

// Mock all the hooks and dependencies
const mockNavigate = vi.fn();
const mockCreatePathWithLngParam = vi.fn();
const mockUseAppContext = vi.fn();
const mockUseObjects = vi.fn();

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock AppContext
vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => mockUseAppContext(),
}));

// Mock hooks
vi.mock('@/hooks', () => ({
  useObjects: (wsId: string) => mockUseObjects(wsId),
}));

// Mock UI hooks
vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({
    createPathWithLngParam: mockCreatePathWithLngParam,
  }),
}));

// Mock AppConfig
vi.mock('@/configs', () => ({
  default: {
    BASE_PATH: '/test-base/',
  },
}));

// Mock MainContainer component
vi.mock('../Common/MainContainer', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='main-container'>{children}</div>
  ),
}));

describe('Home Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementations
    mockUseAppContext.mockReturnValue({
      wsDefault: 'workspace-123',
    });

    mockUseObjects.mockReturnValue({
      firstObject: null,
    });

    mockCreatePathWithLngParam.mockImplementation((path) => `lng-param-${path}`);
  });

  describe('component rendering', () => {
    it('should render Home component with loading overlay', () => {
      renderWithMantine(<Home />);

      expect(screen.getByTestId('main-container')).toBeInTheDocument();
      // LoadingOverlay renders with specific class names
      expect(document.querySelector('.mantine-LoadingOverlay-root')).toBeInTheDocument();
    });

    it('should render with MainContainer wrapper', () => {
      renderWithMantine(<Home />);

      const mainContainer = screen.getByTestId('main-container');
      expect(mainContainer).toBeInTheDocument();
      // Check for loading overlay within main container
      expect(mainContainer.querySelector('.mantine-LoadingOverlay-root')).toBeInTheDocument();
    });
  });

  describe('hook integration', () => {
    it('should call useAppContext to get workspace default', () => {
      renderWithMantine(<Home />);

      expect(mockUseAppContext).toHaveBeenCalled();
    });

    it('should call useObjects with workspace ID from context', () => {
      mockUseAppContext.mockReturnValue({
        wsDefault: 'test-workspace-456',
      });

      renderWithMantine(<Home />);

      expect(mockUseObjects).toHaveBeenCalledWith('test-workspace-456');
    });

    it('should call usePathParams hook', () => {
      renderWithMantine(<Home />);

      // Verify that createPathWithLngParam is available (from usePathParams)
      expect(mockCreatePathWithLngParam).toBeDefined();
    });
  });

  describe('navigation behavior', () => {
    it('should navigate when firstObject is available', async () => {
      mockUseObjects.mockReturnValue({
        firstObject: { id: 'object-789' },
      });

      mockCreatePathWithLngParam.mockReturnValue(
        'lng-param-/test-base/workspace/workspace-123/objects/object-789/'
      );

      renderWithMantine(<Home />);

      await waitFor(() => {
        expect(mockCreatePathWithLngParam).toHaveBeenCalledWith(
          '/test-base/workspace/workspace-123/objects/object-789/'
        );
        expect(mockNavigate).toHaveBeenCalledWith(
          'lng-param-/test-base/workspace/workspace-123/objects/object-789/',
          { replace: true }
        );
      });
    });

    it('should not navigate when firstObject is null', async () => {
      mockUseObjects.mockReturnValue({
        firstObject: null,
      });

      renderWithMantine(<Home />);

      // Wait a bit to ensure useEffect has run
      await waitFor(() => {
        expect(mockNavigate).not.toHaveBeenCalled();
        expect(mockCreatePathWithLngParam).not.toHaveBeenCalled();
      });
    });

    it('should not navigate when firstObject is undefined', async () => {
      mockUseObjects.mockReturnValue({
        firstObject: undefined,
      });

      renderWithMantine(<Home />);

      await waitFor(() => {
        expect(mockNavigate).not.toHaveBeenCalled();
        expect(mockCreatePathWithLngParam).not.toHaveBeenCalled();
      });
    });
  });

  describe('workspace path construction', () => {
    it('should construct correct workspace path with different workspace IDs', () => {
      mockUseAppContext.mockReturnValue({
        wsDefault: 'custom-workspace',
      });

      mockUseObjects.mockReturnValue({
        firstObject: { id: 'test-object' },
      });

      renderWithMantine(<Home />);

      waitFor(() => {
        expect(mockCreatePathWithLngParam).toHaveBeenCalledWith(
          '/test-base/workspace/custom-workspace/objects/test-object/'
        );
      });
    });

    it('should handle workspace ID with special characters', () => {
      mockUseAppContext.mockReturnValue({
        wsDefault: 'workspace-with-dashes',
      });

      mockUseObjects.mockReturnValue({
        firstObject: { id: 'object_with_underscores' },
      });

      renderWithMantine(<Home />);

      waitFor(() => {
        expect(mockCreatePathWithLngParam).toHaveBeenCalledWith(
          '/test-base/workspace/workspace-with-dashes/objects/object_with_underscores/'
        );
      });
    });
  });

  describe('useEffect behavior', () => {
    it('should trigger useEffect when component mounts with firstObject', async () => {
      mockUseObjects.mockReturnValue({
        firstObject: { id: 'test-object' },
      });

      renderWithMantine(<Home />);

      await waitFor(() => {
        expect(mockCreatePathWithLngParam).toHaveBeenCalledWith(
          '/test-base/workspace/workspace-123/objects/test-object/'
        );
        expect(mockNavigate).toHaveBeenCalledWith(expect.stringContaining('test-object'), {
          replace: true,
        });
      });
    });

    it('should handle useEffect with different object IDs', async () => {
      mockUseObjects.mockReturnValue({
        firstObject: { id: 'specific-object-id' },
      });

      renderWithMantine(<Home />);

      await waitFor(() => {
        expect(mockCreatePathWithLngParam).toHaveBeenCalledWith(
          '/test-base/workspace/workspace-123/objects/specific-object-id/'
        );
        expect(mockNavigate).toHaveBeenCalledWith(expect.stringContaining('specific-object-id'), {
          replace: true,
        });
      });
    });
  });

  describe('edge cases', () => {
    it('should handle missing wsDefault gracefully', () => {
      mockUseAppContext.mockReturnValue({
        wsDefault: null,
      });

      expect(() => renderWithMantine(<Home />)).not.toThrow();
      expect(mockUseObjects).toHaveBeenCalledWith(null);
    });

    it('should handle empty string wsDefault', () => {
      mockUseAppContext.mockReturnValue({
        wsDefault: '',
      });

      expect(() => renderWithMantine(<Home />)).not.toThrow();
      expect(mockUseObjects).toHaveBeenCalledWith('');
    });

    it('should handle firstObject without id', async () => {
      mockUseObjects.mockReturnValue({
        firstObject: {}, // Object without id
      });

      renderWithMantine(<Home />);

      await waitFor(() => {
        expect(mockCreatePathWithLngParam).toHaveBeenCalledWith(
          '/test-base/workspace/workspace-123/objects/undefined/'
        );
      });
    });

    it('should handle createPathWithLngParam returning empty string', async () => {
      mockUseObjects.mockReturnValue({
        firstObject: { id: 'test-object' },
      });

      mockCreatePathWithLngParam.mockReturnValue('');

      renderWithMantine(<Home />);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('', { replace: true });
      });
    });
  });

  describe('loading overlay', () => {
    it('should always show loading overlay', () => {
      renderWithMantine(<Home />);

      const loadingOverlay = document.querySelector('.mantine-LoadingOverlay-root');
      expect(loadingOverlay).toBeInTheDocument();
    });

    it('should show loading overlay even when firstObject is available', () => {
      mockUseObjects.mockReturnValue({
        firstObject: { id: 'test-object' },
      });

      renderWithMantine(<Home />);

      const loadingOverlay = document.querySelector('.mantine-LoadingOverlay-root');
      expect(loadingOverlay).toBeInTheDocument();
    });
  });
});
