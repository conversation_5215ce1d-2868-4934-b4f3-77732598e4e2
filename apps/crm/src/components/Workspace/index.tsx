import { ColumnIcon, ColumnWidth } from '@/constants/workspace';
import { BreadcrumbProvider } from '@/contexts/BreadcrumbContext';
import { useAppContext } from '@/contexts/AppContext';
import { WorkspaceContextProvider, useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { RecordAPI, TasksAPI } from '@/services/api';
import {
  createNewRow,
  handleColumnOrderChange,
  handleColumnSizingChange,
  mapColumnsToView,
  useShowNewRecordNotification,
} from '@/utils';
import { Box, Center, Loader, LoadingOverlay, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import {
  DecaTable,
  FieldTypes,
  SortOrder,
  type View,
  type ViewColumnFields,
  type ViewGroup,
} from '@resola-ai/ui/components';
import {
  ConfirmHeaderContextMenuItem,
  ConfirmRowContextMenuItem,
  HeaderContextMenuItem,
  type HeaderContextMenuRenderProps,
  HeaderContextMenuSeparator,
  type RowContextMenuRenderProps,
} from '@resola-ai/ui/components/DecaTable/components/ContextMenu';
import {
  CustomFieldsToolbarItem,
  ExportCSVToolbarItem,
  MenuViewToolbarItem,
  SearchBoxToolbarItem,
  SelectViewToolbarItem,
  TableCustomFieldsChangeTypes,
  TableFilterChangeTypes,
  TableFilterToolbarItem,
  TableHeightToolbarItem,
  TableSelectViewChangeTypes,
  TableSortChangeTypes,
  TableSortToolbarItem,
} from '@resola-ai/ui/components/DecaTable/components/Toolbar';
import TableAddRow from '@resola-ai/ui/components/DecaTable/components/Toolbar/TableAddRow';
import {
  CUSTOM_SELECT_COL_ID,
  DEFAULT_TABLE_FILTERS,
  RowActions,
} from '@resola-ai/ui/components/DecaTable/constants';
import { NEWCOL_ID } from '@resola-ai/ui/components/DecaTable/constants';
import { PERMISSION_KEYS, isPermissionAllowed } from '@resola-ai/ui/components/DecaTable/utils';
import {
  IconArrowLeft,
  IconArrowRight,
  IconCopy,
  IconEdit,
  IconEyeOff,
  IconSortAscendingLetters,
  IconSortDescendingLetters,
  IconTrash,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { type UIEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import MainContainer from '../Common/MainContainer';
import { FilterEmptyState } from '../EmptyState/FilterEmptyState';
import { WorkspaceEmptyState } from '../EmptyState/WorkspaceEmptyState';
import AddNewColumnButton from '../FieldSettings/AddFieldForm/AddNewColumnButton';
import EditColumnField from '../FieldSettings/EditFieldForm';
import { MergeProfile } from '../MergeProfile';
import NoPermissionAccess from '../NoPermissionAccess';
import Profile from '../Profile';
import Cell from '../TableCellRendering/Cell';
import EditCell from '../TableCellRendering/EditCell';
import useObjectStyles from './useObjectStyles';

const UnEditColumns = [
  FieldTypes.CHECKBOX,
  FieldTypes.AUTONUMBER,
  FieldTypes.CREATED_TIME,
  FieldTypes.MODIFIED_TIME,
  FieldTypes.CREATED_BY,
  FieldTypes.MODIFIED_BY,
];

const UnOrderedColumns = ['mrt-row-actions', CUSTOM_SELECT_COL_ID];

const MemoizedCell = React.memo(
  ({ cell, column }: { cell; column }) => {
    return cell.getValue() !== 'undefined' ? (
      <Cell cell={cell} type={column.type} column={column} />
    ) : null;
  },
  (prevProps, nextProps) => {
    // Compare cell value
    if (prevProps.cell.getValue() !== nextProps.cell.getValue()) {
      return false;
    }

    // Compare column configuration that affects rendering
    if (prevProps.column.type !== nextProps.column.type) {
      return false;
    }

    // Compare field options (config) for cases like datetime format changes
    if (JSON.stringify(prevProps.column.options) !== JSON.stringify(nextProps.column.options)) {
      return false;
    }

    return true;
  }
);

const MemoizedEditCell = React.memo(
  ({ cell, table, column }: { cell; table; column }) => (
    <EditCell table={table} cell={cell} column={column} />
  ),
  (prevProps, nextProps) => {
    return prevProps.cell.getValue() === nextProps.cell.getValue();
  }
);

MemoizedCell.displayName = 'MemoizedCell';
MemoizedEditCell.displayName = 'MemoizedEditCell';
const SCROLL_BOTTOM_THRESHOLD = 480;
const DEFAULT_COLUMN_WIDTH = 200;
const DEFAULT_ROW_HEIGHT = 50;
const WorkspaceSettings = () => {
  const {
    columns,
    data,
    activeView,
    views,
    loading,
    handleActionRow,
    openProfile,
    recordsLoading,
    object,
    rowSelection,
    setRowSelection,
    selectedRowDetails,
    tags,
    size,
    setSize,
    totalRecords,
    handleViewChange,
    viewLoading,
    viewGroups,
    handleApplyManageView,
    onSavingCell,
    textSearch,
    handleSearch,
    objectSettings,
    setPinnedRecords,
    mutateRecord,
  } = useWorkspaceContext();
  const { classes } = useObjectStyles();
  const [onAddingCol, setOnAddingCol] = useState(false);
  const { t } = useTranslate('workspace');
  const { wsId, id: objId, recordId } = useParams();
  const showNewRecordNotification = useShowNewRecordNotification();
  const { importLoading } = useAppContext();

  const handleAddRow = useCallback(
    async (fields?: any): Promise<void> => {
      try {
        const hasFilterOrSort = Boolean(
          (activeView?.filters && Object.keys(activeView.filters).length > 0) ||
            (activeView?.sort && activeView.sort.length > 0)
        );

        const newRow = createNewRow(fields || columns);
        const addedRow = await RecordAPI.save(wsId || '', objId || '', newRow);

        if (hasFilterOrSort) {
          // Add to pinned records when there are filters or sorts
          setPinnedRecords((prev) => [{ ...addedRow, id: addedRow?.id }, ...prev]);
          // Show notification when there are filters or sorts
          setTimeout(() => {
            showNewRecordNotification({
              callBack: async () => {
                await mutateRecord();
                setPinnedRecords([]);
              },
            });
          }, 3000);
        } else {
          // Add to the beginning of the data when no filters/sorts
          mutateRecord((currData) => {
            const firstPage = currData?.[0];
            if (firstPage) {
              const updatedFirstPage = {
                ...firstPage,
                records: [{ ...addedRow, id: addedRow?.id }, ...(firstPage.records || [])],
                totalRecordsCount: (firstPage.totalRecordsCount ?? totalRecords ?? 0) + 1,
              };
              return [updatedFirstPage, ...currData.slice(1)];
            }
            return [
              {
                records: [{ ...addedRow, id: addedRow?.id }],
                totalRecordsCount: currData?.[0]?.totalRecordsCount ?? 1,
              },
            ];
          }, false);
        }

        // Scroll to the top of the table to show the new row
        setTimeout(() => {
          if (tableContainerRef.current) {
            tableContainerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
          }
        }, 100);
      } catch (error) {
        console.error('Failed to add new row:', error);
        throw error;
      }
    },
    [
      columns,
      wsId,
      objId,
      totalRecords,
      mutateRecord,
      activeView?.filters,
      activeView?.sort,
      setPinnedRecords,
    ]
  );

  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [columnSizing, setColumnSizing] = useState();
  const [opened, { open, close }] = useDisclosure(false);
  const [openedFilterMenu, { close: closeFilterMenu, open: openFilterMenu }] = useDisclosure(false);
  const [openedSortMenu, { close: closeSortMenu, open: openSortMenu }] = useDisclosure(false);
  const totalFetched = data.length;

  // permissions check
  const canUpdateObject = isPermissionAllowed(
    object?.permission || {},
    PERMISSION_KEYS.OBJECT_UPDATE
  );
  const canReadObject = isPermissionAllowed(object?.permission || {}, PERMISSION_KEYS.OBJECT_READ);
  const canReadView = isPermissionAllowed(activeView?.permission || {}, PERMISSION_KEYS.VIEW_READ);
  const canListView = isPermissionAllowed(object?.permission || {}, PERMISSION_KEYS.VIEW_LIST);

  const getRowEstimateSize = useCallback((): number => {
    switch (activeView?.rowHeight) {
      case 'sm':
        return 40;
      case 'md':
      default:
        return DEFAULT_ROW_HEIGHT;
    }
  }, [activeView?.rowHeight]);

  const handleExportCSV = () => {
    TasksAPI.exportCSV(wsId || '', objId || '', activeView?.id || '');
  };

  const onColumnSizingChange = useCallback(
    (state) => {
      const newSize = state();
      handleColumnSizingChange(newSize, activeView, setColumnSizing, handleViewChange);
    },
    [activeView, setColumnSizing, handleViewChange]
  );

  const handleViewDuplicate = useCallback(
    (view: View) => {
      handleViewChange(view.id, view, TableSelectViewChangeTypes.DUPLICATE_VIEW);
    },
    [handleViewChange]
  );

  const isFiltering =
    activeView?.filters &&
    !recordsLoading &&
    !viewLoading &&
    JSON.stringify(activeView.filters) !== JSON.stringify(DEFAULT_TABLE_FILTERS);

  const handleRowSelectionChange = (value) => {
    setRowSelection(value);
  };

  const emptyObj = useMemo(() => !columns || !columns.length, [columns]);
  const tableColumns = useMemo(() => {
    return columns.map<ViewColumnFields>((column: any) => {
      return {
        ...column,
        accessorKey: column.id,
        header: column.name,
        headerIcon: ColumnIcon[column.type] || null,
        enableEditing:
          !UnEditColumns.includes(column.type) && !activeView?.locked && !column?.isProtected,
        Cell: ({ cell }) => <MemoizedCell cell={cell} column={column} />,
        Edit: ({ cell, table }) => <MemoizedEditCell table={table} cell={cell} column={column} />,
        enableColumnOrdering: !column.options?.moveToFirst,
        minSize: 120,
        maxSize: 400,
      };
    });
  }, [JSON.stringify(columns), activeView?.locked]);

  const renderEditColumnField = (props: any) => {
    return <EditColumnField {...props} />;
  };

  const renderRowContextMenu = useCallback(
    (props: RowContextMenuRenderProps) => {
      const { contextMenuT } = props;

      return (
        <ConfirmRowContextMenuItem
          color='red'
          icon={<IconTrash />}
          label={contextMenuT('delete')}
          onConfirm={async ({ rowIds }) => {
            await handleActionRow(RowActions.DELETE_ROW, rowIds);
          }}
          confirmModalProps={{
            title: contextMenuT('deleteRow'),
            message: (rowName, currentView) =>
              contextMenuT('deleteRowWarning', {
                rows: rowName,
                objectName: currentView?.objectName?.singular || currentView?.objectName?.plural,
              }),
          }}
        />
      );
    },
    [t, handleActionRow]
  );

  const renderHeaderContextMenu = useCallback(
    (props: HeaderContextMenuRenderProps) => {
      const {
        enableInsertLeft,
        enableInsertRight,
        handleOpenEdit,
        handleOpenInsert,
        getSortByColumnUpdate,
        getFilterByColumnUpdate,
        getDuplicateColumnUpdate,
        getToggleColumnUpdate,
        getDeleteColumnUpdate,
        contextMenuT,
        currentHeader,
      } = props;

      return (
        <>
          <HeaderContextMenuItem
            icon={<IconEdit />}
            label={contextMenuT('editProperty')}
            onSelect={(params) => {
              handleOpenEdit(params.event);
            }}
            closeOnClick={false}
          />
          <HeaderContextMenuItem
            icon={<IconCopy />}
            label={contextMenuT('duplicateField')}
            onSelect={() => {
              const update = getDuplicateColumnUpdate();
              if (update) {
                handleViewChange(
                  update.id,
                  update.view,
                  TableCustomFieldsChangeTypes.DUPLICATE_COLUMN
                );
              }
            }}
          />
          <HeaderContextMenuItem
            icon={<IconEyeOff />}
            label={contextMenuT('hideField')}
            onSelect={() => {
              const update = getToggleColumnUpdate();
              if (update) {
                handleViewChange(update.view.id, update.view);
              }
            }}
          />
          <HeaderContextMenuSeparator />
          {enableInsertLeft && (
            <HeaderContextMenuItem
              icon={<IconArrowLeft />}
              label={contextMenuT('insertLeft')}
              onSelect={(params) => {
                handleOpenInsert(params.event, 'left');
              }}
            />
          )}
          {enableInsertRight && (
            <HeaderContextMenuItem
              icon={<IconArrowRight />}
              label={contextMenuT('insertRight')}
              onSelect={(params) => {
                handleOpenInsert(params.event, 'right');
              }}
            />
          )}
          {(enableInsertLeft || enableInsertRight) && <HeaderContextMenuSeparator />}
          <HeaderContextMenuItem
            icon={<IconSortAscendingLetters />}
            label={contextMenuT('sortFirst')}
            onSelect={() => {
              const update = getSortByColumnUpdate(SortOrder.Ascending);
              if (update) {
                handleViewChange(update.id, update.view, TableFilterChangeTypes.FILTER_VIEW);
              }
            }}
          />
          <HeaderContextMenuItem
            icon={<IconSortDescendingLetters />}
            label={contextMenuT('sortLast')}
            onSelect={async () => {
              const update = getSortByColumnUpdate(SortOrder.Descending);
              if (update) {
                handleViewChange(update.id, update.view, TableSortChangeTypes.FILTER_VIEW);
              }
            }}
          />
          <HeaderContextMenuSeparator />
          <HeaderContextMenuItem
            icon={<IconSortDescendingLetters />}
            label={contextMenuT('filterBy')}
            onSelect={async () => {
              const update = getFilterByColumnUpdate();
              if (update) {
                handleViewChange(update.id, update.view, TableFilterChangeTypes.FILTER_VIEW);
              }
            }}
          />
          <HeaderContextMenuSeparator />
          <ConfirmHeaderContextMenuItem
            color='red'
            icon={<IconTrash />}
            label={contextMenuT('deleteColumn')}
            onConfirm={async () => {
              const update = getDeleteColumnUpdate();
              update &&
                (await handleViewChange(
                  update.id,
                  update.view,
                  TableCustomFieldsChangeTypes.DELETE_COLUMN
                ));
            }}
            confirmModalProps={{
              title: contextMenuT('deleteColumn'),
              message: contextMenuT('deleteColumnWarning', {
                column: currentHeader?.column.columnDef.header,
              }),
            }}
          />
        </>
      );
    },
    [t, handleViewChange]
  );

  const selectedView: View = useMemo(() => {
    if (tableColumns)
      return mapColumnsToView(tableColumns, activeView, object?.name?.singular, ColumnWidth);
  }, [activeView, tableColumns, object?.name]);

  const columnOrder = useMemo(() => {
    return [...UnOrderedColumns, ...(selectedView?.fieldOrder || [])];
  }, [selectedView?.fieldOrder]);

  const defaultColumnSizing = useMemo(() => {
    return selectedView?.fields.reduce((acc, column) => {
      acc[column.id] = column.size;
      return acc;
    }, {});
  }, [selectedView?.fieldOrder]);

  const onColumnOrderChange = useCallback(
    (newOrders) => {
      if (activeView) {
        handleColumnOrderChange(newOrders, activeView, selectedView, handleViewChange);
      }
    },
    [handleColumnOrderChange, activeView, selectedView, handleViewChange]
  );

  const fetchNextPage = useCallback(async () => {
    setSize(size + 1);
  }, [size, setSize]);

  const fetchMoreOnBottomReached = useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
        const offsetToBottom = scrollHeight - scrollTop - clientHeight;
        const hasNextPage = totalFetched < (totalRecords || 0);

        if (offsetToBottom < SCROLL_BOTTOM_THRESHOLD && !recordsLoading && hasNextPage) {
          fetchNextPage();
        }
      }
    },
    [fetchNextPage, recordsLoading, totalFetched, totalRecords]
  );

  const renderAddColumnFormButton = useCallback(() => {
    if (canUpdateObject) {
      return <AddNewColumnButton />;
    }
    return <></>;
  }, [canUpdateObject]);

  useEffect(() => {
    Object.keys(rowSelection).length === 2 ? open() : close();
  }, [rowSelection]);

  if (loading)
    return (
      <Center maw='100%' h='100%' mx='auto'>
        <LoadingOverlay visible overlayProps={{ blur: 2 }} />
      </Center>
    );
  const tagsFilter = tags?.map((tag) => ({ label: tag.name, value: tag.id })) || [];

  return (
    <MainContainer className={classes.container}>
      {!canReadObject ? (
        <NoPermissionAccess customText={t('errors.crmObjectReadDenied', { ns: 'common' })} />
      ) : (
        <>
          {importLoading && (
            <Center maw='100%' h='100%' mx='auto'>
              <LoadingOverlay
                visible
                overlayProps={{ blur: 0.1 }}
                loaderProps={{
                  children: (
                    <Text ta='center' mx='auto'>
                      <Loader size='md' />
                      <Text mt='md'>{t('importing')}</Text>
                    </Text>
                  ),
                }}
              />
            </Center>
          )}
          {recordId && <Profile />}
          {selectedRowDetails.length === 2 && <MergeProfile opened={opened} close={close} />}
          <Box>
            <DecaTable
              views={views}
              viewGroups={(viewGroups as ViewGroup[]) || []}
              selectedView={selectedView}
              isFull={false}
              key={tableColumns?.length || data.length}
              data={activeView && !emptyObj ? data : []}
              dataCount={totalRecords}
              objectPermissions={object?.permission}
              objectSettings={objectSettings}
              enablePermissions
              addingNewRow={handleAddRow}
              editFieldForm={renderEditColumnField}
              onViewDetail={openProfile}
              onCellUpdate={({ columnId, value, rowIndex }) => {
                onSavingCell(value, { row: { index: rowIndex }, column: { id: columnId } });
              }}
              state={{
                columnOrder,
                columnSizing: columnSizing || defaultColumnSizing,
                columnPinning: { left: [NEWCOL_ID] },
                rowSelection: rowSelection,
              }}
              onColumnOrderChange={onColumnOrderChange}
              enableColumnOrdering={canUpdateObject}
              enableColumnPinning
              enableColumnResizing={canUpdateObject}
              columnResizeMode='onEnd'
              onColumnSizingChange={onColumnSizingChange}
              isViewLoading={viewLoading}
              onRowSelectionChange={handleRowSelectionChange}
              tagsFilter={tagsFilter}
              showAddColumn={!!activeView}
              addColumnFormButton={renderAddColumnFormButton}
              rowContextMenuProps={{
                menuItemComponents: renderRowContextMenu,
              }}
              headerContextMenuProps={{
                menuItemComponents: renderHeaderContextMenu,
              }}
              toolbarProps={{
                leftToolbarComponents: (
                  <>
                    <MenuViewToolbarItem
                      i18nMessages={{ buttonText: t('manageView') }}
                      useGroup
                      onApply={handleApplyManageView}
                    />
                    <SelectViewToolbarItem />
                    <CustomFieldsToolbarItem />
                    <TableFilterToolbarItem
                      opened={openedFilterMenu}
                      onOpenChange={(opened) => (opened ? openFilterMenu() : closeFilterMenu())}
                    />
                    <TableSortToolbarItem
                      opened={openedSortMenu}
                      onOpenChange={(opened) => (opened ? openSortMenu() : closeSortMenu())}
                    />
                    <TableHeightToolbarItem />
                    {isPermissionAllowed(
                      selectedView?.permission || {},
                      PERMISSION_KEYS.VIEW_EXPORT
                    ) && <ExportCSVToolbarItem onExport={handleExportCSV} />}
                  </>
                ),
                rightToolbarComponents: (
                  <>
                    <SearchBoxToolbarItem searchQuery={textSearch} onSearch={handleSearch} />
                    <TableAddRow
                      onAddNewRow={() => handleAddRow()}
                      disabled={
                        tableColumns.length < 1 ||
                        activeView?.locked ||
                        !canUpdateObject ||
                        recordsLoading
                      }
                    />
                  </>
                ),
                onToolbarViewChange: handleViewChange,
              }}
              onViewDuplicate={handleViewDuplicate}
              enableColumnVirtualization={true}
              columnVirtualizerOptions={{
                overscan: 5,
                estimateSize: () => DEFAULT_COLUMN_WIDTH,
              }}
              rowVirtualizerOptions={{
                overscan: 5,
                estimateSize: () => getRowEstimateSize(),
              }}
              mantineTableContainerProps={{
                ref: tableContainerRef,
                onScroll: (
                  event: UIEvent<HTMLDivElement> //add an event listener to the table container element
                ) => fetchMoreOnBottomReached(event.target as HTMLDivElement),
              }}
            />
          </Box>
          {emptyObj && !recordsLoading && !viewLoading && !onAddingCol && (
            <WorkspaceEmptyState clickToAdd={() => setOnAddingCol(true)} />
          )}
          {isFiltering && !recordsLoading && !viewLoading && data.length === 0 && (
            <FilterEmptyState />
          )}
          {(!canListView || (!canReadView && activeView && !viewLoading)) && <NoPermissionAccess />}
        </>
      )}
    </MainContainer>
  );
};

export const Workspace = () => {
  return (
    <WorkspaceContextProvider>
      <BreadcrumbProvider>
        <WorkspaceSettings />
      </BreadcrumbProvider>
    </WorkspaceContextProvider>
  );
};

export default Workspace;
