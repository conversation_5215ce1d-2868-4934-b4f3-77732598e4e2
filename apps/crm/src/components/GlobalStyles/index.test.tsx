import { renderWithMantine } from '@/tests/utils/testUtils';
import { describe, expect, it } from 'vitest';
import GlobalStyles from './index';

describe('GlobalStyles Component', () => {
  it('should be defined', () => {
    expect(GlobalStyles).toBeDefined();
    expect(typeof GlobalStyles).toBe('function');
  });

  it('should render without crashing', () => {
    expect(() => {
      renderWithMantine(<GlobalStyles />);
    }).not.toThrow();
  });

  it('should render Global component from Mantine', () => {
    const { container } = renderWithMantine(<GlobalStyles />);
    expect(container).toBeInTheDocument();
  });

  it('should apply global styles correctly', () => {
    renderWithMantine(<GlobalStyles />);

    // Test that the component renders and applies styles through Mantine's Global component
    // The Global component injects styles into the document head
    const style = document.querySelector('style[data-emotion]');
    expect(style).toBeDefined();
  });
});
