import { Box, type ComboboxItem, ScrollArea, Stack, Text, rem } from '@mantine/core';
import { Select } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useMemo } from 'react';
import type { FieldMapping } from '../../utils';
import { useImportStyles } from './useImportStyle';

interface FieldMappingListProps {
  fieldMappings: FieldMapping[];
  mainFieldOptions: (ComboboxItem & { icon: React.ReactNode })[];
  fieldTypeOptions: (ComboboxItem & { icon: React.ReactNode })[];
  shouldShowNewFieldType: (mapping: FieldMapping) => boolean;
  getMainSelectedValue: (mapping: FieldMapping) => string;
  handleMainFieldSelectionChange: (csvField: string, value: string | null) => void;
  handleNewFieldTypeChange: (csvField: string, fieldType: string | null) => void;
  renderOption: ({
    option,
  }: { option: ComboboxItem & { icon: React.ReactNode } }) => React.ReactNode;
  getMainFieldOptionsForMapping?: (
    mapping: FieldMapping
  ) => (ComboboxItem & { icon: React.ReactNode })[];
}

const FieldMappingList: React.FC<FieldMappingListProps> = ({
  fieldMappings,
  mainFieldOptions,
  fieldTypeOptions,
  shouldShowNewFieldType,
  getMainSelectedValue,
  handleMainFieldSelectionChange,
  handleNewFieldTypeChange,
  renderOption,
  getMainFieldOptionsForMapping,
}) => {
  const { t } = useTranslate('workspace');
  const { classes } = useImportStyles();

  // Memoize the transformation of field type options
  const transformedFieldTypeGroups = useMemo(
    () => [
      {
        options: fieldTypeOptions.map((option) => ({
          value: option.value,
          label: renderOption({ option }),
          filterList: [option.label, option.value].filter(Boolean),
        })),
      },
    ],
    [fieldTypeOptions, renderOption]
  );

  // Function to transform main field options
  const transformMainFieldOptions = useMemo(
    () => (options: (ComboboxItem & { icon: React.ReactNode })[]) => [
      {
        options: options.map((option) => ({
          value: option.value,
          label: renderOption({ option }),
          filterList: [option.label, option.value].filter(Boolean),
        })),
      },
    ],
    [renderOption]
  );

  return (
    <Stack gap={rem(16)}>
      <ScrollArea h={`calc(100vh - ${rem(550)})`} scrollbarSize={6} scrollHideDelay={500}>
        <Box className={classes.mappingContainer}>
          <Box className={classes.mappingHeader}>
            <Box className={classes.headerColumn}>{t('csvField')}</Box>
            <Box className={classes.headerColumn} ml={rem(36)}>
              {t('crmFieldType')}
            </Box>
          </Box>
          {fieldMappings.map((mapping, index) => {
            const showNewFieldType = shouldShowNewFieldType(mapping);
            // Use mapping-specific options if available, otherwise fall back to general options
            const currentMainFieldOptions = getMainFieldOptionsForMapping
              ? getMainFieldOptionsForMapping(mapping)
              : mainFieldOptions;

            const transformedMainFieldGroups = transformMainFieldOptions(currentMainFieldOptions);

            return (
              <Box
                key={`${mapping.csvField}-${index}`}
                className={classes.mappingRow}
                data-testid={`field-mapping-${index}`}
              >
                <Box className={classes.csvFieldColumn}>
                  <Text className={classes.csvFieldName}>{mapping.csvField}</Text>
                </Box>
                <Text className={classes.arrow}>→</Text>
                <Box className={classes.crmFieldColumn}>
                  {showNewFieldType ? (
                    <Box className={classes.selectRow}>
                      <Select
                        width={rem(200)}
                        groups={transformedMainFieldGroups}
                        onChange={(value) =>
                          handleMainFieldSelectionChange(mapping.csvField, String(value))
                        }
                        defaultValue={getMainSelectedValue(mapping)}
                        search
                        className={classes.fieldTypeSelect}
                        data-testid={`field-select-${index}`}
                      />
                      <Select
                        width={rem(200)}
                        groups={transformedFieldTypeGroups}
                        onChange={(value) =>
                          handleNewFieldTypeChange(mapping.csvField, String(value))
                        }
                        defaultValue={mapping.systemFieldType || undefined}
                        search
                        className={classes.fieldTypeSelectSecondary}
                        data-testid={`field-type-select-${index}`}
                      />
                    </Box>
                  ) : (
                    <Box className={classes.selectRow}>
                      <Select
                        width={rem(400)}
                        groups={transformedMainFieldGroups}
                        onChange={(value) =>
                          handleMainFieldSelectionChange(mapping.csvField, String(value))
                        }
                        defaultValue={getMainSelectedValue(mapping)}
                        search
                        className={classes.fieldTypeSelectFull}
                        data-testid={`field-select-${index}`}
                      />
                    </Box>
                  )}
                </Box>
              </Box>
            );
          })}
        </Box>
      </ScrollArea>
    </Stack>
  );
};

export default FieldMappingList;
