import { Box, ScrollArea, Table, Text, rem } from '@mantine/core';
import type React from 'react';
import { useImportStyles } from './useImportStyle';

interface CsvPreviewProps {
  csvData: string[][];
  isLoading: boolean;
}

const CsvPreview: React.FC<CsvPreviewProps> = ({ csvData, isLoading }) => {
  const { classes } = useImportStyles();

  if (isLoading) {
    return (
      <div className={classes.csvPreview}>
        <Box p={rem(16)}>
          <Text size='sm' c='decaGrey.5' ta='center'>
            Loading preview...
          </Text>
        </Box>
      </div>
    );
  }

  if (csvData.length === 0) {
    return (
      <div className={classes.csvPreview}>
        <Box p={rem(16)}>
          <Text size='sm' c='decaGrey.5' ta='center'>
            No preview available
          </Text>
        </Box>
      </div>
    );
  }

  const maxDataColumns = 6;
  const headers = csvData[0] || [];
  const rows = csvData.slice(1);
  const displayHeaders = headers.slice(0, maxDataColumns);
  const hasMoreColumns = headers.length > maxDataColumns;

  // Generate Excel-like column headers (A, B, C, D, etc.)
  const generateExcelColumns = (count: number): string[] => {
    const columns: string[] = [];
    for (let i = 0; i < count; i++) {
      let columnName = '';
      let num = i;
      do {
        columnName = String.fromCharCode(65 + (num % 26)) + columnName;
        num = Math.floor(num / 26) - 1;
      } while (num >= 0);
      columns.push(columnName);
    }
    return columns;
  };

  const excelColumns = generateExcelColumns(displayHeaders.length);

  return (
    <div className={classes.csvPreview}>
      <ScrollArea>
        <Table className={classes.csvTable}>
          <Table.Thead>
            <Table.Tr>
              <Table.Th />
              {excelColumns.map((columnName, index) => (
                <Table.Th key={index}>{columnName}</Table.Th>
              ))}
              {hasMoreColumns && (
                <Table.Th>
                  <Text size='xs' c='decaGrey.5'>
                    +{headers.length - maxDataColumns}
                  </Text>
                </Table.Th>
              )}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {rows.map((row, rowIndex) => (
              <Table.Tr key={rowIndex}>
                <Table.Td>{rowIndex + 1}</Table.Td>
                {displayHeaders.map((_, cellIndex) => (
                  <Table.Td key={cellIndex} title={row[cellIndex] || ''}>
                    {row[cellIndex] || ''}
                  </Table.Td>
                ))}
                {hasMoreColumns && (
                  <Table.Td>
                    <Text size='xs' c='decaGrey.5'>
                      ...
                    </Text>
                  </Table.Td>
                )}
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </ScrollArea>
      <Box p={rem(8)} style={{ borderTop: `1px solid #E9ECEF` }}>
        <Text size='xs' c='decaGrey.5' ta='center'>
          Showing first {Math.min(csvData.length - 1, 10)} rows
          {hasMoreColumns && ` • ${headers.length} columns total`}
        </Text>
      </Box>
    </div>
  );
};

export default CsvPreview;
