import { Box, Radio, Select, Stack, Text, TextInput, rem } from '@mantine/core';
import { DecaSwitch } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useObjects } from '../../hooks/useObjects';
import { useImportStyles } from './useImportStyle';

interface CsvObjectSelectionProps {
  csvData: string[][];
  selectedObjectType?: string;
  importMode?: 'existing' | 'new';
  newObjectName?: string;
  useHeaders?: boolean;
  onObjectTypeChange: (objectType: string) => void;
  onImportModeChange: (mode: 'existing' | 'new') => void;
  onUseHeadersChange: (useHeaders: boolean) => void;
  onNewObjectNameChange: (name: string) => void;
}

const CsvObjectSelection: React.FC<CsvObjectSelectionProps> = ({
  csvData,
  selectedObjectType,
  importMode: initialImportMode,
  newObjectName: initialNewObjectName,
  useHeaders: initialUseHeaders,
  onObjectTypeChange,
  onImportModeChange,
  onUseHeadersChange,
  onNewObjectNameChange,
}) => {
  const { t } = useTranslate('workspace');
  const { classes } = useImportStyles();
  const [importMode, setImportMode] = useState<'existing' | 'new'>(initialImportMode || 'existing');
  const [selectedObject, setSelectedObject] = useState<string | null>(selectedObjectType || null);
  const [useHeaders, setUseHeaders] = useState(initialUseHeaders ?? true);
  const [newObjectName, setNewObjectName] = useState(initialNewObjectName || '');
  const { wsId } = useParams();
  const { objects } = useObjects(wsId);

  const objectOptions =
    objects?.map((object) => ({
      value: object.id,
      label: object.name.singular,
    })) || [];

  const handleImportModeChange = (value: string) => {
    const mode = value as 'existing' | 'new';
    setImportMode(mode);
    onImportModeChange(mode);
  };

  const handleObjectChange = (value: string | null) => {
    setSelectedObject(value);
    if (value) {
      onObjectTypeChange(value);
    }
  };

  const handleUseHeadersChange = (checked: boolean) => {
    setUseHeaders(checked);
    onUseHeadersChange(checked);
  };

  const handleNewObjectNameChange = (value: string) => {
    setNewObjectName(value);
    onNewObjectNameChange(value);
  };

  const getColumnsToShow = () => {
    if (csvData.length === 0) {
      return { displayColumns: [], hasMoreColumns: false, totalColumns: 0 };
    }

    const headers = csvData[0] || [];
    const maxColumns = 8;
    const displayColumns = headers.slice(0, maxColumns);
    const hasMoreColumns = headers.length > maxColumns;

    return { displayColumns, hasMoreColumns, totalColumns: headers.length };
  };

  const { displayColumns, hasMoreColumns } = getColumnsToShow();

  return (
    <Box className={classes.borderedContainer}>
      <Text className={classes.title}>{t('pleaseChooseObjectToImport')}</Text>

      <Radio.Group
        value={importMode}
        onChange={handleImportModeChange}
        className={classes.radioGroup}
      >
        <Stack className={classes.radioStack}>
          <Box
            className={classes.radioItem}
            data-selected={importMode === 'existing'}
            onClick={() => handleImportModeChange('existing')}
          >
            <div className={classes.radioContent}>
              <Radio
                value='existing'
                label={t('importToExistingObject')}
                size='md'
                checked={importMode === 'existing'}
                data-testid='existing-object-radio'
              />
            </div>
            {importMode === 'existing' && (
              <div className={classes.radioControl}>
                <Select
                  placeholder={t('selectObject')}
                  data={objectOptions}
                  value={selectedObject}
                  onChange={handleObjectChange}
                  w={rem(280)}
                  disabled={importMode !== 'existing'}
                  onClick={(e) => e.stopPropagation()}
                  data-testid='object-select'
                />
              </div>
            )}
          </Box>

          <Box
            className={classes.radioItem}
            data-selected={importMode === 'new'}
            onClick={() => handleImportModeChange('new')}
          >
            <div className={classes.radioContent}>
              <Radio
                value='new'
                label={t('createNewObject')}
                size='md'
                checked={importMode === 'new'}
                data-testid='new-object-radio'
              />
            </div>
            {importMode === 'new' && (
              <div className={classes.radioControl}>
                <TextInput
                  placeholder={t('enterObjectName')}
                  value={newObjectName}
                  onChange={(e) => handleNewObjectNameChange(e.currentTarget.value)}
                  w={rem(280)}
                  disabled={importMode !== 'new'}
                  onClick={(e) => e.stopPropagation()}
                  data-testid='new-object-name'
                />
              </div>
            )}
          </Box>
        </Stack>
      </Radio.Group>

      <DecaSwitch
        checked={useHeaders}
        onChange={(e) => handleUseHeadersChange(e.currentTarget.checked)}
        label={t('useFirstRowAsHeaders')}
      />

      {useHeaders && displayColumns.length > 0 && (
        <Box mt={rem(16)}>
          <div className={classes.columnsPreview}>
            {displayColumns.map((column, index) => (
              <span key={index} className={classes.columnTag}>
                {column}
              </span>
            ))}
            {hasMoreColumns && <span className={classes.columnTag}>...</span>}
          </div>
        </Box>
      )}
    </Box>
  );
};

export default CsvObjectSelection;
