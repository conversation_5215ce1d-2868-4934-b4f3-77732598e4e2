import { Box, Flex, Text, rem } from '@mantine/core';
import { createStyles, keyframes } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

const dotAnimation = keyframes`
  0%, 20% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
`;

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: rem(200),
    width: '100%',
  },
  dotsContainer: {
    display: 'flex',
    gap: rem(8),
    marginBottom: rem(16),
  },
  dot: {
    width: rem(12),
    height: rem(12),
    borderRadius: '50%',
    backgroundColor: theme.colors.decaBlue[5],
    animation: `${dotAnimation} 1.4s infinite ease-in-out`,
    '&:nth-of-type(1)': {
      animationDelay: '0s',
    },
    '&:nth-of-type(2)': {
      animationDelay: '0.2s',
    },
    '&:nth-of-type(3)': {
      animationDelay: '0.4s',
    },
  },
  progressText: {
    fontSize: rem(14),
    fontWeight: 500,
    color: theme.colors.decaGrey[6],
    textAlign: 'center',
  },
}));

interface CsvUploadProgressProps {
  fileName: string;
  progress: number;
}

const CsvUploadProgress: React.FC<CsvUploadProgressProps> = ({ fileName, progress }) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();

  return (
    <Box className={classes.container} data-testid='csv-upload-progress'>
      <Flex className={classes.dotsContainer} data-testid='progress-dots-container'>
        <div className={classes.dot} data-testid='progress-dot-1' />
        <div className={classes.dot} data-testid='progress-dot-2' />
        <div className={classes.dot} data-testid='progress-dot-3' />
      </Flex>

      <Text className={classes.progressText} data-testid='progress-text'>
        {fileName} {t('isUploading', 'is uploading')} ({progress}%)
      </Text>
    </Box>
  );
};

export default CsvUploadProgress;
