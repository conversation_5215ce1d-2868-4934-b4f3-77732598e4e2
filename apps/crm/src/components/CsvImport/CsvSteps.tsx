import { Stepper, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck } from '@tabler/icons-react';
import type React from 'react';

const useStyles = createStyles((theme) => ({
  stepperStep: {
    flexDirection: 'column',
    gap: rem(8),
    '& .mantine-Stepper-stepIcon': {
      fontSize: `${rem(14)} !important`,
      color: theme.colors.decaNavy[4],
      border: 'none',
    },
    '& .mantine-Stepper-stepLabel': {
      fontSize: `${rem(14)} !important`,
      color: theme.colors.decaNavy[6],
    },
    '&:not([data-progress]) .mantine-Stepper-stepLabel': {
      opacity: 0.5,
    },
    '&[data-progress] .mantine-Stepper-stepIcon': {
      color: 'white',
      backgroundColor: theme.colors.decaNavy[4],
    },
    '&[data-completed] .mantine-Stepper-stepIcon': {
      color: 'white',
      backgroundColor: theme.colors.decaGreen[5],
    },
    '&[data-completed] .mantine-Stepper-stepLabel': {
      opacity: 1,
    },
  },
}));

interface Step {
  number: number;
  label: string;
}

interface CsvStepsProps {
  steps: Step[];
  currentStep: number;
}

const CsvSteps: React.FC<CsvStepsProps> = ({ steps, currentStep }) => {
  const { classes } = useStyles();

  return (
    <Stepper active={currentStep - 1} size='sm'>
      {steps.map((step) => (
        <Stepper.Step
          key={step.number}
          label={step.label}
          className={classes.stepperStep}
          completedIcon={<IconCheck size={20} />}
        />
      ))}
    </Stepper>
  );
};

export default CsvSteps;
