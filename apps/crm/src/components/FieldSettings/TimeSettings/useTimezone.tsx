import { FormatDate, FormatTime } from '@/constants/workspace';
import { Flex, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import timezones from 'timezones-list';

const extractTextInParentheses = (text) => {
  const regex = /\(([^)]+)\)/;
  const match = text.match(regex);
  return match ? match[1] : null;
};

export const useTimezone = () => {
  const { t } = useTranslate('workspace');

  const timezoneOptions = React.useCallback((timezoneList) => {
    return timezoneList.map((timezone) => {
      return {
        value: timezone.tzCode,
        label: (
          <Flex justify='space-between' w={'100%'}>
            <Text c={'decaGrey.9'}>{timezone.tzCode}</Text>
            <Text c={'decaGrey.4'}>{extractTextInParentheses(timezone.label)}</Text>
          </Flex>
        ),
        filterList: Object.values(timezone).map((value) => String(value)),
      };
    });
  }, []);

  const suggestTimeZones = React.useMemo(() => {
    return timezoneOptions(
      timezones.filter((timezone) => ['Asia/Tokyo', 'Europe/London'].includes(timezone.tzCode))
    );
  }, [timezones, timezoneOptions]);

  const allTimeZones = React.useMemo(() => {
    return timezoneOptions(timezones);
  }, [timezones]);

  const groupTimezones = React.useMemo(() => {
    return [
      { value: 'suggest', label: t('suggestTimeZone'), options: suggestTimeZones },
      { value: 'all', label: t('allTimeZones'), options: allTimeZones },
    ];
  }, [timezones, timezoneOptions]);

  const groupDateFormat = [
    {
      options: [
        { value: FormatDate.ja, label: t('jaFormat') },
        { value: FormatDate.en, label: t('usFormat') },
        { value: FormatDate.long, label: t('longDate') },
        { value: FormatDate.eu, label: t('euFormat') },
      ],
    },
  ];
  const groupTimeFormat = [
    {
      options: [
        { value: FormatTime['12h'], label: t('12h') },
        { value: FormatTime['24h'], label: t('24h') },
      ],
    },
  ];

  return React.useMemo(() => {
    return { groupTimezones, groupDateFormat, groupTimeFormat };
  }, [groupTimezones]);
};
