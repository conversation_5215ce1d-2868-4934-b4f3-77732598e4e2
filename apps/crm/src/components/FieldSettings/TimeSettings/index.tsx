import { OptionsName } from '@/constants/workspace';
import type { DateTimeConfig } from '@/models';
import { Box, Collapse, Radio, Text, rem, useMantineTheme } from '@mantine/core';
import { HFSwitch, Select } from '@resola-ai/ui';
import { FieldTypes } from '@resola-ai/ui/components';
import { IconChevronDown, IconChevronUp } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTimezone } from './useTimezone';

type TimeRangeOption = 'dateOnly' | 'dateTime' | 'dateTimeRange';

export const TimeSettings = ({
  currentDate = false,
  options,
  fieldType,
}: {
  currentDate?: boolean;
  options?: DateTimeConfig;
  fieldType?: string;
}) => {
  const { t } = useTranslate('workspace');
  const { groupTimezones, groupDateFormat, groupTimeFormat } = useTimezone();
  const theme = useMantineTheme();
  const { setValue, watch } = useFormContext();
  const [advancedSettingsOpen, setAdvancedSettingsOpen] = useState(false);

  // Watch form values
  const timeEnabled = watch(`${OptionsName}.time.enabled`) ?? options?.time?.enabled ?? false;
  const dateRangeEnabled = watch(`${OptionsName}.dateRange`) ?? options?.dateRange ?? false;
  const timezoneEnabled =
    watch(`${OptionsName}.timezone.enabled`) ?? options?.timezone?.enabled ?? true;
  const timeFormat =
    watch(`${OptionsName}.time.format`) ??
    options?.time?.format ??
    groupTimeFormat[0].options[0].value;

  // Derive timeRange from actual form values
  const timeRange = useMemo((): TimeRangeOption => {
    if (timeEnabled && dateRangeEnabled && fieldType === FieldTypes.DATETIME) {
      return 'dateTimeRange';
    }
    if (timeEnabled) {
      return 'dateTime';
    }
    return 'dateOnly';
  }, [timeEnabled, dateRangeEnabled, fieldType]);

  const handleTimeRangeChange = (value: TimeRangeOption) => {
    // Set DateTimeConfig fields based on time range selection
    if (value === 'dateOnly') {
      setValue(`${OptionsName}.time.enabled`, false, { shouldDirty: true, shouldTouch: true });
      setValue(`${OptionsName}.dateRange`, false, { shouldDirty: true, shouldTouch: true });
    } else if (value === 'dateTime') {
      setValue(`${OptionsName}.time.enabled`, true, { shouldDirty: true, shouldTouch: true });
      setValue(`${OptionsName}.dateRange`, false, { shouldDirty: true, shouldTouch: true });
    } else if (value === 'dateTimeRange') {
      setValue(`${OptionsName}.time.enabled`, true, { shouldDirty: true, shouldTouch: true });
      setValue(`${OptionsName}.dateRange`, true, { shouldDirty: true, shouldTouch: true });
    }
  };

  return (
    <>
      {/* Date Format */}
      <Box mb={rem(20)}>
        <Text mb={rem(8)} fw={500}>
          {t('dateFormat')}
        </Text>
        <Select
          groups={groupDateFormat}
          onChange={(val) => {
            setValue(`${OptionsName}.date.format`, val);
          }}
          defaultValue={options?.date?.format || groupDateFormat[0].options[0].value}
        />
      </Box>

      {/* Time Range Selection */}
      <Box mb={rem(20)}>
        <Text mb={rem(12)} fw={500}>
          {t('timeRange')}
        </Text>
        <Radio.Group
          value={timeRange}
          onChange={(value) => handleTimeRangeChange(value as TimeRangeOption)}
        >
          <Box mt='xs'>
            <Radio value='dateOnly' label={t('dateOnly')} mb='sm' />
            <Radio value='dateTime' label={t('dateWithTime')} mb='sm' />
            {fieldType === FieldTypes.DATETIME && (
              <Radio value='dateTimeRange' label={t('dateWithStartEndTime')} />
            )}
          </Box>
        </Radio.Group>
      </Box>

      {/* Advanced Settings */}
      <Box mb={rem(20)}>
        <Box
          onClick={() => setAdvancedSettingsOpen(!advancedSettingsOpen)}
          style={{
            cursor: 'pointer',
            userSelect: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
          }}
        >
          {advancedSettingsOpen ? (
            <IconChevronUp size={16} color={theme.colors.decaBlue[5]} />
          ) : (
            <IconChevronDown size={16} color={theme.colors.decaBlue[5]} />
          )}
          <Text c={theme.colors.decaBlue[5]} fw={500}>
            {t('advancedSettings')}
          </Text>
        </Box>

        <Collapse in={advancedSettingsOpen}>
          <Box mt={rem(16)}>
            {/* Use same timezone toggle */}
            <HFSwitch
              name={`${OptionsName}.timezone.enabled`}
              label={t('sameTimezone')}
              size='sm'
              mb={rem(16)}
              defaultChecked={options?.timezone?.enabled ?? true}
            />

            {/* Timezone selector */}
            {timezoneEnabled && (
              <Box mb={rem(16)}>
                <Text mb={rem(8)} size='sm'>
                  {t('timeZone')}
                </Text>
                <Select
                  groups={groupTimezones}
                  onChange={(val) => {
                    setValue(`${OptionsName}.timezone.format`, val);
                  }}
                  placeholder={t('timeZoneSearch')}
                  defaultValue={options?.timezone?.format || 'Asia/Tokyo'}
                  search
                />
              </Box>
            )}

            {/* Display timezone toggle */}
            <HFSwitch
              name={`${OptionsName}.displayTimezone`}
              label={t('displayTimezone')}
              size='sm'
              mb={rem(16)}
              defaultChecked={options?.displayTimezone ?? false}
            />

            {/* Default to current date toggle - Only show for DateTime fields */}
            {currentDate && (
              <HFSwitch
                name={`${OptionsName}.useCurrentDate`}
                label={t('defaultCurrDate')}
                size='sm'
                mb={rem(16)}
                defaultChecked={options?.useCurrentDate ?? false}
              />
            )}

            {/* Time Format Selection - Only show when time is enabled */}
            {timeEnabled && (
              <Box mb={rem(16)}>
                <Text mb={rem(8)} fw={500}>
                  {t('timeFormat')}
                </Text>
                <Radio.Group
                  value={timeFormat}
                  onChange={(val) => setValue(`${OptionsName}.time.format`, val)}
                >
                  <Box mt='xs'>
                    <Radio value='HH:mm' label={t('24h')} mb='sm' />
                    <Radio value='hh:mm A' label={t('12h')} />
                  </Box>
                </Radio.Group>
              </Box>
            )}
          </Box>
        </Collapse>
      </Box>
    </>
  );
};
