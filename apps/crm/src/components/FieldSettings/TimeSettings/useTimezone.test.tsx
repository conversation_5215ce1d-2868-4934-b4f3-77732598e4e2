import { renderHook } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { MantineWrapper } from '@/tests/utils/testUtils';
import { useTimezone } from './useTimezone';
import { FormatDate, FormatTime } from '@/constants/workspace';

// Mock @tolgee/react
const mockT = vi.fn((key: string) => key);
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: mockT }),
}));

// Mock timezones-list
vi.mock('timezones-list', () => ({
  default: [
    {
      tzCode: 'Asia/Tokyo',
      label: 'Asia/Tokyo (JST)',
    },
    {
      tzCode: 'Europe/London',
      label: 'Europe/London (GMT)',
    },
    {
      tzCode: 'America/New_York',
      label: 'America/New_York (EST)',
    },
    {
      tzCode: 'Australia/Sydney',
      label: 'Australia/Sydney (AEDT)',
    },
  ],
}));

describe('useTimezone Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockT.mockImplementation((key: string) => key);
  });

  const renderUseTimezone = () => {
    return renderHook(() => useTimezone(), {
      wrapper: MantineWrapper,
    });
  };

  it('should return timezone configuration objects', () => {
    const { result } = renderUseTimezone();

    expect(result.current).toHaveProperty('groupTimezones');
    expect(result.current).toHaveProperty('groupDateFormat');
    expect(result.current).toHaveProperty('groupTimeFormat');
  });

  describe('groupTimezones', () => {
    it('should have correct structure with suggest and all groups', () => {
      const { result } = renderUseTimezone();
      const { groupTimezones } = result.current;

      expect(groupTimezones).toHaveLength(2);
      expect(groupTimezones[0]).toHaveProperty('value', 'suggest');
      expect(groupTimezones[0]).toHaveProperty('label', 'suggestTimeZone');
      expect(groupTimezones[0]).toHaveProperty('options');
      
      expect(groupTimezones[1]).toHaveProperty('value', 'all');
      expect(groupTimezones[1]).toHaveProperty('label', 'allTimeZones');
      expect(groupTimezones[1]).toHaveProperty('options');
    });

    it('should have suggest timezones with Asia/Tokyo and Europe/London only', () => {
      const { result } = renderUseTimezone();
      const { groupTimezones } = result.current;
      const suggestGroup = groupTimezones.find(group => group.value === 'suggest');

      expect(suggestGroup?.options).toHaveLength(2);
      expect(suggestGroup?.options[0]).toHaveProperty('value', 'Asia/Tokyo');
      expect(suggestGroup?.options[1]).toHaveProperty('value', 'Europe/London');
    });

    it('should have all timezones in the all group', () => {
      const { result } = renderUseTimezone();
      const { groupTimezones } = result.current;
      const allGroup = groupTimezones.find(group => group.value === 'all');

      expect(allGroup?.options).toHaveLength(4); // 4 mock timezones
    });

    it('should format timezone options correctly', () => {
      const { result } = renderUseTimezone();
      const { groupTimezones } = result.current;
      const allGroup = groupTimezones.find(group => group.value === 'all');
      const tokyoOption = allGroup?.options.find(opt => opt.value === 'Asia/Tokyo');

      expect(tokyoOption).toMatchObject({
        value: 'Asia/Tokyo',
        filterList: ['Asia/Tokyo', 'Asia/Tokyo (JST)'],
      });
      expect(tokyoOption?.label).toBeDefined();
    });

    it('should include filter list for each timezone option', () => {
      const { result } = renderUseTimezone();
      const { groupTimezones } = result.current;
      const allGroup = groupTimezones.find(group => group.value === 'all');

      allGroup?.options.forEach(option => {
        expect(option).toHaveProperty('filterList');
        expect(Array.isArray(option.filterList)).toBe(true);
        expect(option.filterList.length).toBeGreaterThan(0);
      });
    });

    it('should call translation function for group labels', () => {
      renderUseTimezone();

      expect(mockT).toHaveBeenCalledWith('suggestTimeZone');
      expect(mockT).toHaveBeenCalledWith('allTimeZones');
    });
  });

  describe('groupDateFormat', () => {
    it('should have correct structure with all date format options', () => {
      const { result } = renderUseTimezone();
      const { groupDateFormat } = result.current;

      expect(groupDateFormat).toHaveLength(1);
      expect(groupDateFormat[0]).toHaveProperty('options');
      expect(groupDateFormat[0].options).toHaveLength(4);
    });

    it('should include all date format types', () => {
      const { result } = renderUseTimezone();
      const { groupDateFormat } = result.current;
      const options = groupDateFormat[0].options;

      const expectedFormats = [
        { value: FormatDate.ja, label: 'jaFormat' },
        { value: FormatDate.en, label: 'usFormat' },
        { value: FormatDate.long, label: 'longDate' },
        { value: FormatDate.eu, label: 'euFormat' },
      ];

      expectedFormats.forEach(expected => {
        const option = options.find(opt => opt.value === expected.value);
        expect(option).toBeDefined();
        expect(option?.label).toBe(expected.label);
      });
    });

    it('should call translation function for date format labels', () => {
      renderUseTimezone();

      expect(mockT).toHaveBeenCalledWith('jaFormat');
      expect(mockT).toHaveBeenCalledWith('usFormat');
      expect(mockT).toHaveBeenCalledWith('longDate');
      expect(mockT).toHaveBeenCalledWith('euFormat');
    });
  });

  describe('groupTimeFormat', () => {
    it('should have correct structure with all time format options', () => {
      const { result } = renderUseTimezone();
      const { groupTimeFormat } = result.current;

      expect(groupTimeFormat).toHaveLength(1);
      expect(groupTimeFormat[0]).toHaveProperty('options');
      expect(groupTimeFormat[0].options).toHaveLength(2);
    });

    it('should include both 12h and 24h time formats', () => {
      const { result } = renderUseTimezone();
      const { groupTimeFormat } = result.current;
      const options = groupTimeFormat[0].options;

      const expectedFormats = [
        { value: FormatTime['12h'], label: '12h' },
        { value: FormatTime['24h'], label: '24h' },
      ];

      expectedFormats.forEach(expected => {
        const option = options.find(opt => opt.value === expected.value);
        expect(option).toBeDefined();
        expect(option?.label).toBe(expected.label);
      });
    });

    it('should call translation function for time format labels', () => {
      renderUseTimezone();

      expect(mockT).toHaveBeenCalledWith('12h');
      expect(mockT).toHaveBeenCalledWith('24h');
    });
  });

  describe('extractTextInParentheses utility', () => {
    it('should extract text from parentheses correctly', () => {
      const { result } = renderUseTimezone();
      const { groupTimezones } = result.current;
      const allGroup = groupTimezones.find(group => group.value === 'all');
      
      // The function is used internally, so we test its effect through timezone labels
      // Since mock timezones have format "Asia/Tokyo (JST)", the extracted text should be "JST"
      expect(allGroup?.options).toBeDefined();
      expect(allGroup?.options.length).toBeGreaterThan(0);
    });
  });

  describe('memoization behavior', () => {
    it('should memoize the result to prevent unnecessary recalculations', () => {
      const { result, rerender } = renderUseTimezone();
      const firstResult = result.current;
      
      rerender();
      const secondResult = result.current;
      
      // The results should be the same object reference due to memoization
      expect(firstResult).toBe(secondResult);
    });

    it('should maintain separate memoization for different return values', () => {
      const { result } = renderUseTimezone();
      
      expect(result.current.groupTimezones).toBeDefined();
      expect(result.current.groupDateFormat).toBeDefined();
      expect(result.current.groupTimeFormat).toBeDefined();
      
      // Each should be separate objects
      expect(result.current.groupTimezones).not.toBe(result.current.groupDateFormat);
      expect(result.current.groupTimezones).not.toBe(result.current.groupTimeFormat);
      expect(result.current.groupDateFormat).not.toBe(result.current.groupTimeFormat);
    });
  });

  describe('edge cases', () => {
    it('should handle timezone list gracefully', () => {
      const { result } = renderUseTimezone();
      const { groupTimezones } = result.current;
      
      // Should return structured data with our mock timezones
      expect(groupTimezones).toHaveLength(2);
      expect(groupTimezones[0].value).toBe('suggest');
      expect(groupTimezones[1].value).toBe('all');
      expect(groupTimezones[0].options.length).toBeGreaterThanOrEqual(0);
      expect(groupTimezones[1].options.length).toBeGreaterThanOrEqual(0);
    });

    it('should handle timezone objects with various label formats', () => {
      const { result } = renderUseTimezone();
      const { groupTimezones } = result.current;
      
      // Should handle our mock timezone data correctly
      expect(groupTimezones[1].options).toHaveLength(4);
      expect(groupTimezones[1].options.every(opt => opt.value && opt.label)).toBe(true);
      // Check that all timezone options have valid structure
      groupTimezones[1].options.forEach(option => {
        expect(option).toHaveProperty('value');
        expect(option).toHaveProperty('label');
        expect(option).toHaveProperty('filterList');
      });
    });

    it('should handle fallback translation function', () => {
      mockT.mockImplementation((key: string) => key || 'fallback');
      
      const { result } = renderUseTimezone();
      
      // Should not throw error and still return structured data
      expect(result.current).toHaveProperty('groupTimezones');
      expect(result.current).toHaveProperty('groupDateFormat');
      expect(result.current).toHaveProperty('groupTimeFormat');
    });

    it('should handle malformed timezone data', () => {
      const malformedTimezones = [
        {
          tzCode: null,
          label: null,
        },
        {
          // Missing properties
        },
      ];

      vi.doMock('timezones-list', () => ({
        default: malformedTimezones,
      }));

      const { result } = renderUseTimezone();
      
      // Should handle gracefully without throwing
      expect(result.current.groupTimezones).toBeDefined();
    });
  });

  describe('React hooks dependencies', () => {
    it('should properly depend on timezones and timezoneOptions', () => {
      const { result, rerender } = renderUseTimezone();
      const initialResult = result.current;
      
      // Multiple rerenders should return the same object due to proper memoization
      rerender();
      rerender();
      rerender();
      
      expect(result.current).toBe(initialResult);
    });
  });
}); 