import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { useFormContext } from 'react-hook-form';
import { describe, expect, it, vi } from 'vitest';
import { TimeSettings } from './index';

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
  useController: () => ({
    field: {
      value: false,
      onChange: vi.fn(),
      onBlur: vi.fn(),
      ref: { current: null },
    },
    fieldState: { error: null },
  }),
}));

vi.mock('./useTimezone', () => ({
  useTimezone: () => ({
    groupTimezones: [
      {
        options: [
          { value: 'Asia/Tokyo', label: 'Tokyo (GMT+9)' },
          { value: 'America/New_York', label: 'New York (GMT-5)' },
        ],
      },
    ],
    groupDateFormat: [
      {
        options: [
          { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
          { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
        ],
      },
    ],
    groupTimeFormat: [
      {
        options: [
          { value: 'hh:mm A', label: '12-hour' },
          { value: 'HH:mm', label: '24-hour' },
        ],
      },
    ],
  }),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key) => key,
  }),
}));

describe('TimeSettings', () => {
  const mockWatch = vi.fn();
  const mockSetValue = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue({
      watch: mockWatch,
      setValue: mockSetValue,
    });
  });

  it('renders correctly with default options', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'timeRange') return 'dateOnly';
      if (name === 'options.time.enabled') return false;
      if (name === 'options.timezone.enabled') return false;
      if (name === 'options.time.format') return 'hh:mm A';
      return undefined;
    });

    renderWithMantine(
      <TimeSettings
        options={{
          date: {
            format: 'MM/DD/YYYY',
          },
        }}
      />
    );

    expect(screen.getByText('dateFormat')).toBeInTheDocument();
    expect(screen.getByText('timeRange')).toBeInTheDocument();
    expect(screen.getByText('advancedSettings')).toBeInTheDocument();
    expect(screen.queryByText('timeFormat')).not.toBeInTheDocument();
    expect(screen.queryByText('timeZone')).not.toBeInTheDocument();
  });

  it('renders time format options when time is enabled', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'timeRange') return 'dateTime';
      if (name === 'options.time.enabled') return true;
      if (name === 'options.timezone.enabled') return true;
      if (name === 'options.time.format') return 'hh:mm A';
      return undefined;
    });

    renderWithMantine(
      <TimeSettings
        options={{
          date: {
            format: 'MM/DD/YYYY',
          },
          time: {
            enabled: true,
            format: 'hh:mm A',
          },
        }}
      />
    );

    expect(screen.getByText('timeFormat')).toBeInTheDocument();
  });

  it('renders timezone options when sameTimezone is enabled', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'timeRange') return 'dateOnly';
      if (name === 'options.time.enabled') return false;
      if (name === 'options.timezone.enabled') return true;
      if (name === 'options.time.format') return 'hh:mm A';
      return undefined;
    });

    renderWithMantine(
      <TimeSettings
        options={{
          date: {
            format: 'MM/DD/YYYY',
          },
          timezone: {
            enabled: true,
            format: 'Asia/Tokyo',
          },
        }}
      />
    );

    expect(screen.getByText('timeZone')).toBeInTheDocument();
  });

  it('renders currentDate option when currentDate prop is true', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'timeRange') return 'dateOnly';
      if (name === 'options.time.enabled') return false;
      if (name === 'options.timezone.enabled') return true;
      if (name === 'options.time.format') return 'hh:mm A';
      return undefined;
    });

    renderWithMantine(
      <TimeSettings
        currentDate={true}
        options={{
          date: {
            format: 'MM/DD/YYYY',
          },
        }}
      />
    );

    expect(screen.getByText('defaultCurrDate')).toBeInTheDocument();
  });
});
