import { OptionsName } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { FieldOptions } from '@/models';
import { rem } from '@mantine/core';
import { HFSwitch } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';

export const ImageSettings = ({ options }: { options: FieldOptions }) => {
  const { watch, setValue } = useFormContext();
  const { t } = useTranslate('workspace');
  const { columns } = useWorkspaceContext();
  const hasMoveToFirst = useMemo(() => {
    return columns.some((col) => col.options?.moveToFirst);
  }, [columns]);
  const hasAvatar = useMemo(() => {
    return columns.some((col) => col.options?.isAvatar);
  }, [columns]);
  const avatar = watch(`${OptionsName}.isAvatar`);

  const disabled = useMemo(() => {
    if (options?.isAvatar) {
      return undefined;
    }
    return hasAvatar;
  }, [options, hasAvatar]);

  const moveToFirstDisabled = useMemo(() => {
    if (options?.moveToFirst) {
      return undefined;
    }
    return !avatar || hasMoveToFirst;
  }, [options, avatar]);

  return (
    <>
      <HFSwitch
        name={`${OptionsName}.isAvatar`}
        label={t('avatar')}
        size='xs'
        my={rem(20)}
        disabled={disabled}
        onChange={(e) => {
          !e.target.checked && setValue(`${OptionsName}.moveToFirst`, e.target.checked);
        }}
      />
      <HFSwitch
        name={`${OptionsName}.moveToFirst`}
        label={t('moveToFirstCol')}
        size='xs'
        my={rem(20)}
        disabled={moveToFirstDisabled}
      />
    </>
  );
};
