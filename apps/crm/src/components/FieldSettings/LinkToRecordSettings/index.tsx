import { OptionsName } from '@/constants/workspace';
import { Box, Text, rem } from '@mantine/core';
import { type Group, Select } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { Controller, useFormContext } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { useObjectOptions } from './useObjectOptions';

export const LinkToRecordSettings = () => {
  const { t } = useTranslate('workspace');
  const { watch, control, setValue } = useFormContext();
  const { id: objId } = useParams();
  const { objectOptions, getFieldOptions } = useObjectOptions(objId);

  const objectId = watch(`${OptionsName}.objectId`);

  const fieldOptions = getFieldOptions(objectId);

  return (
    <Box>
      <Text mb={rem(5)}>{t('linkTo')}</Text>
      <Controller
        control={control}
        name={`${OptionsName}.objectId`}
        render={({ field: { value, onChange }, fieldState: { error } }) => (
          <Select
            groups={objectOptions}
            onChange={(value) => {
              objectId !== value && setValue(`${OptionsName}.fieldId`, '');
              onChange(value);
            }}
            defaultValue={value}
            error={error?.message}
          />
        )}
      />

      <Text mb={rem(5)}>{t('select')}</Text>
      <Controller
        control={control}
        name={`${OptionsName}.fieldId`}
        render={({ field: { value, onChange }, fieldState: { error } }) => (
          <Select
            groups={fieldOptions as Group[]}
            onChange={onChange}
            defaultValue={value}
            error={error?.message}
          />
        )}
      />
    </Box>
  );
};
