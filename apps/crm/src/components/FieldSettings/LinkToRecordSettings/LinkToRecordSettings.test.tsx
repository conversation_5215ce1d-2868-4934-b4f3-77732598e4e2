import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('LinkToRecordSettings', () => {
  const mockT = vi.fn((key) => key);
  const mockSetValue = vi.fn();
  const mockWatch = vi.fn();
  const mockOnChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock the hooks
    vi.doMock('@/contexts/AppContext', () => ({
      useAppContext: vi.fn(),
    }));

    vi.doMock('@tolgee/react', () => ({
      useTranslate: () => ({
        t: mockT,
      }),
    }));

    // Mock useParams from react-router-dom
    vi.doMock('react-router-dom', () => ({
      useParams: () => ({ id: 'current-object-id' }),
    }));

    // Mock constants
    vi.doMock('@/constants/workspace', () => ({
      OptionsName: 'options',
    }));

    // Mock react-hook-form
    mockWatch.mockImplementation((key) => {
      if (key === 'options.objectId') return 'obj123';
      return null;
    });

    vi.doMock('react-hook-form', () => ({
      useFormContext: () => ({
        watch: mockWatch,
        control: {},
        setValue: mockSetValue,
      }),
      Controller: ({ render: renderFn }) => {
        return renderFn({
          field: {
            value: 'test-value',
            onChange: mockOnChange,
            onBlur: vi.fn(),
            ref: vi.fn(),
          },
          fieldState: { error: null },
        });
      },
    }));

    // Mock the Select component
    vi.doMock('@resola-ai/ui', () => ({
      Group: ({ children }) => <div>{children}</div>,
      Select: ({ groups, onChange, defaultValue, error }) => (
        <div data-testid='select-component'>
          <div data-testid='select-groups'>{JSON.stringify(groups)}</div>
          <div data-testid='select-value'>{defaultValue}</div>
          {error && <div data-testid='select-error'>{error}</div>}
          <button data-testid='select-button' onClick={() => onChange?.('new-value')}>
            Select
          </button>
        </div>
      ),
    }));

    // Mock useObjectOptions hook
    vi.doMock('./useObjectOptions', () => ({
      useObjectOptions: () => ({
        objectOptions: [
          {
            options: [
              { value: 'obj123', label: 'Object 1' },
              { value: 'obj456', label: 'Object 2' },
            ],
          },
        ],
        getFieldOptions: () => [
          {
            options: [
              { value: 'field456', label: 'Field 1', type: 'text' },
              { value: 'field789', label: 'Field 2', type: 'number' },
            ],
          },
        ],
      }),
    }));

    // Reset modules to ensure our mocks are applied
    vi.resetModules();
  });

  it('renders both select components for objects and fields', async () => {
    // Import after resetting modules to ensure mock is applied
    const { LinkToRecordSettings } = await import('./index');

    renderWithMantine(<LinkToRecordSettings />);

    const selects = screen.getAllByTestId('select-component');
    expect(selects).toHaveLength(2);

    // Check labels
    expect(screen.getByText('linkTo')).toBeInTheDocument();
    expect(screen.getByText('select')).toBeInTheDocument();
  });

  it('displays object options and field options in selects', async () => {
    const { LinkToRecordSettings } = await import('./index');

    renderWithMantine(<LinkToRecordSettings />);

    const selectGroups = screen.getAllByTestId('select-groups');

    // Object options check
    expect(selectGroups[0].textContent).toContain('Object 1');
    expect(selectGroups[0].textContent).toContain('Object 2');

    // Field options check
    expect(selectGroups[1].textContent).toContain('Field 1');
    expect(selectGroups[1].textContent).toContain('Field 2');
  });

  it('resets fieldId when objectId changes', async () => {
    const { LinkToRecordSettings } = await import('./index');

    renderWithMantine(<LinkToRecordSettings />);

    const selectButtons = screen.getAllByTestId('select-button');

    // Click the object selection button to change the objectId
    fireEvent.click(selectButtons[0]);

    // The onChange handler should be called
    expect(mockOnChange).toHaveBeenCalledWith('new-value');

    // setValue should be called to reset the fieldId
    expect(mockSetValue).toHaveBeenCalledWith('options.fieldId', '');
  });

  it('passes onChange to field select without resetting anything', async () => {
    const { LinkToRecordSettings } = await import('./index');

    renderWithMantine(<LinkToRecordSettings />);

    const selectButtons = screen.getAllByTestId('select-button');

    // Clear any previous calls
    mockSetValue.mockClear();
    mockOnChange.mockClear();

    // Click the field selection button
    fireEvent.click(selectButtons[1]);

    // The onChange handler should be called
    expect(mockOnChange).toHaveBeenCalledWith('new-value');

    // setValue should not have been called for this case
    expect(mockSetValue).not.toHaveBeenCalled();
  });
});
