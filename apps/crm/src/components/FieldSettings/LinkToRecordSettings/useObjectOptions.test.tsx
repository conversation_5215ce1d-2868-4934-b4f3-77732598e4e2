import { useAppContext } from '@/contexts/AppContext';
import { renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useObjectOptions } from './useObjectOptions';

// Mock the AppContext
vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(),
}));

describe('useObjectOptions', () => {
  const mockObjects = [
    {
      id: 'obj1',
      name: { singular: 'Object 1' },
      fields: [
        { id: 'field1', name: 'Field 1', type: 'text' },
        { id: 'field2', name: 'Field 2', type: 'number' },
      ],
    },
    {
      id: 'obj2',
      name: { singular: 'Object 2' },
      fields: [{ id: 'field3', name: 'Field 3', type: 'email' }],
    },
    {
      id: 'current-id', // This should be filtered out
      name: { singular: 'Current Object' },
      fields: [{ id: 'field4', name: 'Field 4', type: 'text' }],
    },
    {
      id: 'empty-fields', // This should be filtered out because it has no fields
      name: { singular: 'Empty Fields Object' },
      fields: [],
    },
  ];

  const mockMutateObjects = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock AppContext
    (useAppContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      objects: mockObjects,
      mutateObjects: mockMutateObjects,
    });
  });

  it('calls mutateObjects on first render', () => {
    renderHook(() => useObjectOptions('current-id'));
    expect(mockMutateObjects).toHaveBeenCalledTimes(1);
  });

  it('does not call mutateObjects on re-renders', () => {
    const { rerender } = renderHook(() => useObjectOptions('current-id'));
    expect(mockMutateObjects).toHaveBeenCalledTimes(1);

    rerender();
    expect(mockMutateObjects).toHaveBeenCalledTimes(1); // Still only called once
  });

  it('filters out the current object ID and objects with no fields', () => {
    const { result } = renderHook(() => useObjectOptions('current-id'));

    const objectOptions = result.current.objectOptions[0].options;

    // Should include obj1 and obj2, but not current-id or empty-fields
    expect(objectOptions).toHaveLength(2);

    const objectIds = objectOptions.map((o) => o.value);
    expect(objectIds).toContain('obj1');
    expect(objectIds).toContain('obj2');
    expect(objectIds).not.toContain('current-id');
    expect(objectIds).not.toContain('empty-fields');
  });

  it('formats object options correctly', () => {
    const { result } = renderHook(() => useObjectOptions('current-id'));

    const objectOptions = result.current.objectOptions[0].options;
    const obj1Option = objectOptions.find((o) => o.value === 'obj1');

    expect(obj1Option).toEqual({
      value: 'obj1',
      label: 'Object 1',
    });
  });

  it('returns fields for a specific object ID', () => {
    const { result } = renderHook(() => useObjectOptions('current-id'));

    const obj1Fields = result.current.getFieldOptions('obj1')[0].options;

    expect(obj1Fields).toHaveLength(2);
    expect(obj1Fields[0]).toEqual({
      value: 'field1',
      label: 'Field 1',
      type: 'text',
    });
    expect(obj1Fields[1]).toEqual({
      value: 'field2',
      label: 'Field 2',
      type: 'number',
    });
  });

  it('returns empty array when object ID is not found', () => {
    const { result } = renderHook(() => useObjectOptions('current-id'));

    const nonExistentFields = result.current.getFieldOptions('non-existent-id')[0].options;

    expect(nonExistentFields).toHaveLength(0);
  });

  it('handles undefined objects in objectOptions', () => {
    // Mock the AppContext with undefined objects
    (useAppContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      objects: undefined,
      mutateObjects: mockMutateObjects,
    });

    const { result } = renderHook(() => useObjectOptions('current-id'));

    // Should have a properly formatted but empty result
    expect(result.current.objectOptions).toEqual([{ options: undefined }]);
  });

  it('handles empty array for getFieldOptions when objects is undefined', () => {
    // Mock the AppContext with undefined objects
    (useAppContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      objects: undefined,
      mutateObjects: mockMutateObjects,
    });

    const { result } = renderHook(() => useObjectOptions('current-id'));

    // Should return empty options array
    const fieldOptions = result.current.getFieldOptions('obj1');
    expect(fieldOptions).toEqual([{ options: [] }]);
  });
});
