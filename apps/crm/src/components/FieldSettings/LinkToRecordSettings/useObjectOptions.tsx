import { useAppContext } from '@/contexts/AppContext';
import React, { useEffect, useRef } from 'react';

export const useObjectOptions = (currentId) => {
  const { objects, mutateObjects } = useAppContext();

  const hasMounted = useRef(false);

  useEffect(() => {
    if (!hasMounted.current) {
      mutateObjects();
      hasMounted.current = true;
    }
  }, []);

  const objectOptions = React.useMemo(() => {
    return [
      {
        options: objects
          ?.filter((o) => o.id !== currentId && o.fields?.length)
          ?.map((o) => ({
            value: o.id,
            label: o.name?.singular,
          })),
      },
    ];
  }, [objects]);

  const getFieldOptions = React.useCallback(
    (objectId: string) => {
      if (!objects) {
        return [{ options: [] }];
      }
      const fields = objects.find((o) => objectId === o.id)?.fields || [];
      return [
        {
          options: fields?.map((o) => ({
            value: o.id,
            label: o.name,
            type: o.type,
          })),
        },
      ];
    },
    [objects]
  );

  return React.useMemo(() => {
    return { objectOptions, getFieldOptions };
  }, [objectOptions, getFieldOptions]);
};
