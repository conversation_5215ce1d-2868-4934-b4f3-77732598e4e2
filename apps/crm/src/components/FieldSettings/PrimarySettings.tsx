import { OptionsName } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { FieldOptions } from '@/models';
import { Flex, ThemeIcon, rem } from '@mantine/core';
import { HFSwitch } from '@resola-ai/ui';
import { DecaTooltip } from '@resola-ai/ui/components/DecaTooltip';
import { IconInfoCircle } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
export const PrimarySettings = ({
  label,
  options,
  hasPrimary,
  primaryDesc,
}: {
  hasPrimary: boolean;
  label: string;
  options: FieldOptions;
  primaryDesc?: string;
}) => {
  const { setValue } = useFormContext();
  const { t } = useTranslate('workspace');
  const { columns } = useWorkspaceContext();
  const isPrimary = useWatch({ name: `${OptionsName}.isPrimary` });
  const hasMoveToFirst = useMemo(() => {
    return columns.some((col) => col.options?.moveToFirst);
  }, [columns]);
  const disabled = useMemo(() => {
    if (options?.isPrimary) {
      return undefined;
    }
    return hasPrimary;
  }, [options, hasPrimary]);

  const moveToFirstDisabled = useMemo(() => {
    if (options?.moveToFirst) {
      return undefined;
    }
    return !isPrimary || hasMoveToFirst;
  }, [options, isPrimary]);

  return (
    <>
      <Flex gap={rem(5)} align={'center'} my={rem(20)}>
        <HFSwitch
          name={`${OptionsName}.isPrimary`}
          label={label}
          disabled={disabled}
          size='xs'
          onChange={(e) => {
            !e.target.checked && setValue(`${OptionsName}.moveToFirst`, e.target.checked);
          }}
        />
        {primaryDesc && (
          <DecaTooltip label={primaryDesc}>
            <Flex align={'center'}>
              <ThemeIcon variant='white' size={20} c='decaGrey.5' style={{ cursor: 'pointer' }}>
                <IconInfoCircle />
              </ThemeIcon>
            </Flex>
          </DecaTooltip>
        )}
      </Flex>
      <HFSwitch
        name={`${OptionsName}.moveToFirst`}
        label={t('moveToFirstCol')}
        size='xs'
        my={rem(20)}
        disabled={moveToFirstDisabled}
      />
    </>
  );
};
