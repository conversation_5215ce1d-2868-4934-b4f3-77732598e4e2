import { FormatPhone } from '@/constants/workspace';
import { Flex, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import React from 'react';
// import codes from 'country-calling-code';

export const usePhones = () => {
  const { t } = useTranslate('workspace');
  // TODO update later when confirm the country format

  // const phoneCountryOptions = React.useMemo(() => {
  //   return [
  //     {
  //       options: codes?.map(item => {
  //         return {
  //           value: item.isoCode2,
  //           label: (
  //             <Flex justify='space-between' w={'100%'}>
  //               <Text c={'decaGrey.9'}>{item.country}</Text>
  //               <Text c={'decaGrey.4'}>{item.countryCodes[0]}</Text>
  //             </Flex>
  //           ),
  //           filterList: [item.country, item.isoCode2, item.countryCodes[0]],
  //         };
  //       }),
  //     },
  //   ];
  // }, [codes]);

  const formatOptions = React.useMemo(() => {
    const formats = [
      { value: 'jp', label: t('japan'), format: FormatPhone.jp },
      { value: 'en', label: t('international'), format: FormatPhone.en },
      { value: 'northUS', label: t('northUS'), format: FormatPhone.northUS },
      { value: 'general', label: t('general'), format: FormatPhone.general },
    ];
    return [
      {
        options: formats?.map((item) => {
          return {
            value: item.value,
            label: (
              <Flex justify='space-between' w={'100%'}>
                <Text c={'decaGrey.9'}>{item.label}</Text>
                <Text c={'decaGrey.4'}>{item.format}</Text>
              </Flex>
            ),
          };
        }),
      },
    ];
  }, []);

  return React.useMemo(() => {
    return {
      formatOptions,
    };
  }, [formatOptions]);
};
