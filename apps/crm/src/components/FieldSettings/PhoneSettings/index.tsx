import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { FieldOptions } from '@/models';
import { FieldTypes } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import { useMemo } from 'react';
import { PrimarySettings } from '../PrimarySettings';
export const PhoneSettings = ({ options }: { options: FieldOptions }) => {
  const { t } = useTranslate('workspace');
  const { columns } = useWorkspaceContext();
  const hasPhonePrimary = useMemo(() => {
    return columns.some((col) => col.options?.isPrimary && col.type === FieldTypes.PHONE_NUMBER);
  }, [columns]);

  return (
    <>
      {/* <Box my={rem(10)}>
        <Text mb={rem(5)}>{t('format')}</Text>
        <Select
          groups={formatOptions}
          onChange={val => {
            setValue(`${OptionsName}.customFormat.format`, val);
          }}
          defaultValue={options?.customFormat?.format || 'jp'}
        />
      </Box> */}
      <PrimarySettings
        label={t('primaryPhone')}
        options={options}
        hasPrimary={hasPhonePrimary}
        primaryDesc={t('primaryPhoneDesc')}
      />
    </>
  );
};
