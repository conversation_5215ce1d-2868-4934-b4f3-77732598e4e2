import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { PhoneSettings } from './index';

// Mock dependencies
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

vi.mock('../PrimarySettings', () => ({
  PrimarySettings: ({ label, hasPrimary, primaryDesc }) => (
    <div data-testid='primary-settings'>
      <div data-testid='primary-label'>{label}</div>
      <div data-testid='primary-has'>{String(hasPrimary)}</div>
      <div data-testid='primary-desc'>{primaryDesc}</div>
    </div>
  ),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key) => key,
  }),
}));

describe('PhoneSettings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with no primary phone', () => {
    (useWorkspaceContext as any).mockReturnValue({
      columns: [
        { id: '1', type: 'text' },
        { id: '2', type: 'email' },
      ],
    });

    renderWithMantine(<PhoneSettings options={{}} />);

    expect(screen.getByTestId('primary-settings')).toBeInTheDocument();
    expect(screen.getByTestId('primary-label')).toHaveTextContent('primaryPhone');
    expect(screen.getByTestId('primary-has')).toHaveTextContent('false');
    expect(screen.getByTestId('primary-desc')).toHaveTextContent('primaryPhoneDesc');
  });

  it('renders with existing primary phone', () => {
    (useWorkspaceContext as any).mockReturnValue({
      columns: [
        { id: '1', type: 'text' },
        {
          id: '2',
          type: 'phone',
          options: { isPrimary: true },
        },
      ],
    });

    renderWithMantine(<PhoneSettings options={{}} />);

    expect(screen.getByTestId('primary-settings')).toBeInTheDocument();
    expect(screen.getByTestId('primary-has')).toHaveTextContent('true');
  });
});
