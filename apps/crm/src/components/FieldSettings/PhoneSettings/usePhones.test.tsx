import { renderHook } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { Man<PERSON>Wrapper } from '@/tests/utils/testUtils';
import { usePhones } from './usePhones';
import { FormatPhone } from '@/constants/workspace';

// Mock @tolgee/react
const mockT = vi.fn((key: string) => key);
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: mockT }),
}));

describe('usePhones Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockT.mockImplementation((key: string) => key);
  });

  const renderUsePhones = () => {
    return renderHook(() => usePhones(), {
      wrapper: MantineWrapper,
    });
  };

  it('should return phone configuration objects', () => {
    const { result } = renderUsePhones();

    expect(result.current).toHaveProperty('formatOptions');
    expect(Array.isArray(result.current.formatOptions)).toBe(true);
  });

  describe('formatOptions', () => {
    it('should have correct structure with phone format group', () => {
      const { result } = renderUsePhones();
      const { formatOptions } = result.current;

      expect(formatOptions).toHaveLength(1);
      expect(formatOptions[0]).toHaveProperty('options');
      expect(formatOptions[0].options).toHaveLength(4);
    });

    it('should include all phone format types', () => {
      const { result } = renderUsePhones();
      const { formatOptions } = result.current;
      const options = formatOptions[0].options;

      const expectedFormats = [
        { value: 'jp', label: 'japan', format: FormatPhone.jp },
        { value: 'en', label: 'international', format: FormatPhone.en },
        { value: 'northUS', label: 'northUS', format: FormatPhone.northUS },
        { value: 'general', label: 'general', format: FormatPhone.general },
      ];

      expectedFormats.forEach(expected => {
        const option = options.find(opt => opt.value === expected.value);
        expect(option).toBeDefined();
        expect(option?.value).toBe(expected.value);
      });
    });

    it('should format phone options correctly with label components', () => {
      const { result } = renderUsePhones();
      const { formatOptions } = result.current;
      const options = formatOptions[0].options;

      options.forEach(option => {
        expect(option).toHaveProperty('value');
        expect(option).toHaveProperty('label');
        expect(option.label).toBeDefined();
        
        // The label should be a React component (Flex with Text components)
        expect(typeof option.label).toBe('object');
      });
    });

    it('should call translation function for format labels', () => {
      renderUsePhones();

      expect(mockT).toHaveBeenCalledWith('japan');
      expect(mockT).toHaveBeenCalledWith('international');
      expect(mockT).toHaveBeenCalledWith('northUS');
      expect(mockT).toHaveBeenCalledWith('general');
    });

    it('should include correct phone format values', () => {
      const { result } = renderUsePhones();
      const { formatOptions } = result.current;
      const options = formatOptions[0].options;

      const japanOption = options.find(opt => opt.value === 'jp');
      const internationalOption = options.find(opt => opt.value === 'en');
      const northUSOption = options.find(opt => opt.value === 'northUS');
      const generalOption = options.find(opt => opt.value === 'general');

      expect(japanOption).toBeDefined();
      expect(internationalOption).toBeDefined();
      expect(northUSOption).toBeDefined();
      expect(generalOption).toBeDefined();
    });

    it('should maintain consistent order of format options', () => {
      const { result } = renderUsePhones();
      const { formatOptions } = result.current;
      const options = formatOptions[0].options;

      const expectedOrder = ['jp', 'en', 'northUS', 'general'];
      
      options.forEach((option, index) => {
        expect(option.value).toBe(expectedOrder[index]);
      });
    });
  });

  describe('memoization behavior', () => {
    it('should memoize the result to prevent unnecessary recalculations', () => {
      const { result, rerender } = renderUsePhones();
      const firstResult = result.current;
      
      rerender();
      const secondResult = result.current;
      
      // The results should be the same object reference due to memoization
      expect(firstResult).toBe(secondResult);
    });

    it('should maintain stable formatOptions reference', () => {
      const { result, rerender } = renderUsePhones();
      const firstFormatOptions = result.current.formatOptions;
      
      rerender();
      const secondFormatOptions = result.current.formatOptions;
      
      // formatOptions should be the same reference due to memoization
      expect(firstFormatOptions).toBe(secondFormatOptions);
    });
  });

  describe('React hooks dependencies', () => {
    it('should properly memoize based on dependencies', () => {
      const { result, rerender } = renderUsePhones();
      const initialResult = result.current;
      
      // Multiple rerenders should return the same object due to proper memoization
      rerender();
      rerender();
      rerender();
      
      expect(result.current).toBe(initialResult);
      expect(result.current.formatOptions).toBe(initialResult.formatOptions);
    });
  });

  describe('edge cases', () => {
    it('should handle translation function returning empty strings', () => {
      mockT.mockImplementation(() => '');
      
      const { result } = renderUsePhones();
      const { formatOptions } = result.current;
      
      // Should still return structured data even with empty translations
      expect(formatOptions).toHaveLength(1);
      expect(formatOptions[0].options).toHaveLength(4);
    });

    it('should handle fallback translation function', () => {
      mockT.mockImplementation((key: string) => key || 'fallback');
      
      const { result } = renderUsePhones();
      
      // Should not throw error and still return structured data
      expect(result.current).toHaveProperty('formatOptions');
      expect(result.current.formatOptions).toHaveLength(1);
    });

    it('should maintain consistent structure regardless of translation results', () => {
      // Test with different translation mock implementations
      const implementations = [
        (key: string) => key,
        (key: string) => key.toUpperCase(),
        (key: string) => `translated_${key}`,
        () => 'constant',
      ];

      implementations.forEach(impl => {
        mockT.mockImplementation(impl);
        
        const { result } = renderUsePhones();
        const { formatOptions } = result.current;
        
        expect(formatOptions).toHaveLength(1);
        expect(formatOptions[0].options).toHaveLength(4);
        
        formatOptions[0].options.forEach(option => {
          expect(option).toHaveProperty('value');
          expect(option).toHaveProperty('label');
        });
      });
    });
  });

  describe('phone format constants integration', () => {
    it('should use correct FormatPhone constants', () => {
      // Verify that the hook is using the actual FormatPhone constants
      expect(FormatPhone).toHaveProperty('jp');
      expect(FormatPhone).toHaveProperty('en');
      expect(FormatPhone).toHaveProperty('northUS');
      expect(FormatPhone).toHaveProperty('general');
    });

    it('should maintain consistency with FormatPhone enum', () => {
      const { result } = renderUsePhones();
      const { formatOptions } = result.current;
      const options = formatOptions[0].options;

      // Each option should correspond to a FormatPhone constant
      const formatValues = ['jp', 'en', 'northUS', 'general'];
      formatValues.forEach(formatValue => {
        const option = options.find(opt => opt.value === formatValue);
        expect(option).toBeDefined();
      });
    });
  });

  describe('component structure validation', () => {
    it('should return properly structured label components', () => {
      const { result } = renderUsePhones();
      const { formatOptions } = result.current;
      const options = formatOptions[0].options;

      options.forEach(option => {
        // Label should be a React element (Flex component)
        expect(option.label).toBeDefined();
        expect(typeof option.label).toBe('object');
        
        // Should have React element properties
        expect(option.label).toHaveProperty('type');
        expect(option.label).toHaveProperty('props');
      });
    });

    it('should maintain consistent label structure across all options', () => {
      const { result } = renderUsePhones();
      const { formatOptions } = result.current;
      const options = formatOptions[0].options;

      // All options should have the same label structure
      const firstLabelType = options[0].label.type;
      
      options.forEach(option => {
        expect(option.label.type).toBe(firstLabelType);
        expect(option.label.props).toBeDefined();
      });
    });
  });

  describe('performance considerations', () => {
    it('should not recreate options on every render', () => {
      const { result, rerender } = renderUsePhones();
      const firstOptions = result.current.formatOptions[0].options;
      
      rerender();
      const secondOptions = result.current.formatOptions[0].options;
      
      // Options array should be the same reference
      expect(firstOptions).toBe(secondOptions);
    });

    it('should efficiently handle multiple hook instances', () => {
      const { result: result1 } = renderUsePhones();
      const { result: result2 } = renderUsePhones();
      
      // Each instance should return the same structure
      expect(result1.current.formatOptions).toHaveLength(1);
      expect(result2.current.formatOptions).toHaveLength(1);
      
      // But they should be separate instances
      expect(result1.current).not.toBe(result2.current);
    });
  });
}); 