import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { useFormContext } from 'react-hook-form';
import { describe, expect, it, vi } from 'vitest';
import { NumberSettings } from './index';

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
  useController: () => ({
    field: {
      value: false,
      onChange: vi.fn(),
      onBlur: vi.fn(),
      ref: { current: null },
    },
    fieldState: { error: null },
  }),
}));

vi.mock('./useDecimalPlaces', () => ({
  useDecimalPlaces: () => ({
    decimalPlaces: [
      {
        options: [
          { value: 0, label: '0', selectedDisplay: '0 (1)' },
          { value: 1, label: '1', selectedDisplay: '1 (1.0)' },
          { value: 2, label: '2', selectedDisplay: '2 (1.00)' },
        ],
      },
    ],
  }),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key) => key,
  }),
}));

describe('NumberSettings', () => {
  const mockWatch = vi.fn();
  const mockSetValue = vi.fn();
  const mockControl = {};

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue({
      watch: mockWatch,
      setValue: mockSetValue,
      control: mockControl,
    });
  });

  it('renders correctly with decimal format', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.numberFormat') return 'decimal';
      return undefined;
    });

    renderWithMantine(
      <NumberSettings
        options={{
          numberFormat: 'decimal',
          decimalPlaces: 2,
        }}
      />
    );

    expect(screen.getByText('numberFormat')).toBeInTheDocument();
    expect(screen.getByText('decimal')).toBeInTheDocument();
    expect(screen.getByText('integer')).toBeInTheDocument();
    expect(screen.getByText('decimalPlaces')).toBeInTheDocument();
  });

  it('does not show decimal places options when integer format is selected', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.numberFormat') return 'integer';
      return undefined;
    });

    renderWithMantine(
      <NumberSettings
        options={{
          numberFormat: 'integer',
        }}
      />
    );

    expect(screen.getByText('numberFormat')).toBeInTheDocument();
    expect(screen.getByText('decimal')).toBeInTheDocument();
    expect(screen.getByText('integer')).toBeInTheDocument();
    expect(screen.queryByText('decimalPlaces')).not.toBeInTheDocument();
  });
});
