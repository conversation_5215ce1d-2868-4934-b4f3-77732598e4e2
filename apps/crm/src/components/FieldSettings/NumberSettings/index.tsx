import { OptionsName } from '@/constants/workspace';
import type { FieldOptions } from '@/models';
import { Box, Group, Text, rem } from '@mantine/core';
import { HFRadio, Select } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useFormContext } from 'react-hook-form';
import { useDecimalPlaces } from './useDecimalPlaces';

export const NumberSettings = ({ options }: { options: FieldOptions }) => {
  const { t } = useTranslate('workspace');
  const { control, watch, setValue } = useFormContext();
  const format = watch(`${OptionsName}.numberFormat`) || 'decimal';
  const { decimalPlaces } = useDecimalPlaces();

  return (
    <Box my={rem(20)}>
      <Text mb={rem(5)}>{t('numberFormat')}</Text>
      <HFRadio.Group
        control={control}
        name={`${OptionsName}.numberFormat`}
        defaultValue={'decimal'}
        mb={rem(20)}
      >
        <Group mt='xs'>
          <HFRadio label={t('decimal')} name='decimal' value='decimal' />
          <HFRadio label={t('integer')} name='integer' value='integer' />
        </Group>
      </HFRadio.Group>
      {format === 'decimal' && (
        <>
          <Text mb={rem(5)}>{t('decimalPlaces')}</Text>
          <Select
            groups={decimalPlaces}
            onChange={(val) => {
              setValue(`${OptionsName}.decimalPlaces`, val);
            }}
            defaultValue={options?.decimalPlaces || decimalPlaces[0].options[0].value}
          />
        </>
      )}
    </Box>
  );
};
