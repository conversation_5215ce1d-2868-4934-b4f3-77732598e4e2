import { Flex, Text } from '@mantine/core';
import type { Option } from '@resola-ai/ui';
import React from 'react';

export const useDecimalPlaces = () => {
  return React.useMemo(() => {
    const options: Option[] = [];
    for (let i = 0; i <= 5; i++) {
      const dp = Number.parseFloat('1').toFixed(i);
      options.push({
        label: (
          <Flex justify='space-between' w={'100%'}>
            <Text c={'decaGrey.9'}>{i}</Text>
            <Text c={'decaGrey.4'}>{dp}</Text>
          </Flex>
        ),
        value: i,
        selectedDisplay: `${i} (${dp})`,
      });
    }
    const decimalPlaces = [{ options }];
    return { decimalPlaces };
  }, []);
};
