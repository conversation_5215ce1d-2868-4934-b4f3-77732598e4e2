import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { FieldOptions } from '@/models';
import { mockSetValue, mockWatch } from '@/tests/mocks/reactHookForm';
import { createTablerIconsMock } from '@/tests/mocks/tablerIcons';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { PrimarySettings } from './PrimarySettings';

// Import OptionsName from local mock instead of real module
import { OptionsName } from '@/tests/mocks/workspace';

// Mock the hooks - mocks must be defined before they're used
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
}));

// Mock workspace constants
vi.mock('@/constants/workspace', () => ({
  OptionsName: 'options',
}));

// Mock HFSwitch with a clickable div
vi.mock('@resola-ai/ui', () => ({
  HFSwitch: ({ name, label, disabled, onChange }) => (
    <div
      data-testid={`switch-${name.replace('.', '-')}`}
      onClick={() => onChange?.({ target: { checked: !mockWatch() } })}
    >
      <label>{label}</label>
      <span data-testid={`switch-status-${name.replace('.', '-')}`}>
        Switch is {mockWatch() ? 'on' : 'off'}
      </span>
      {disabled && <span data-testid={`disabled-${name.replace('.', '-')}`}>Disabled</span>}
    </div>
  ),

  DecaTooltip: ({ label, children }) => (
    <div data-testid='tooltip' data-tooltip-content={label}>
      {children}
    </div>
  ),
}));

// Properly mock all Tabler icons
vi.mock('@tabler/icons-react', () => createTablerIconsMock());

// Mock react-hook-form
vi.mock('react-hook-form', () => {
  return {
    useForm: () => ({
      control: {},
      getValues: vi.fn(),
      setValue: mockSetValue,
      watch: mockWatch,
    }),
    useFormContext: () => ({
      control: {},
      getValues: vi.fn(),
      setValue: mockSetValue,
      watch: mockWatch,
    }),
    useWatch: () => mockWatch(),
    Controller: ({ render }) =>
      render({ field: { onChange: vi.fn(), value: mockWatch(), onBlur: vi.fn(), ref: vi.fn() } }),
  };
});

// Mock the actual PrimarySettings component to avoid dependency issues
vi.mock('./PrimarySettings', () => ({
  PrimarySettings: ({ options, label, hasPrimary, primaryDesc }) => (
    <div data-testid='primary-settings-component'>
      <div data-testid='primary-label'>{label}</div>
      <div data-testid='primary-has-primary'>{String(hasPrimary)}</div>
      {primaryDesc && <div data-testid='primary-desc'>{primaryDesc}</div>}
      <div
        data-testid={`switch-${OptionsName}-isPrimary`}
        onClick={() => mockSetValue(`${OptionsName}.moveToFirst`, false)}
      >
        <span data-testid={`switch-status-${OptionsName}-isPrimary`}>
          Switch is {options.isPrimary ? 'on' : 'off'}
        </span>
        {hasPrimary && !options.isPrimary && (
          <span data-testid={`disabled-${OptionsName}-isPrimary`}>Disabled</span>
        )}
      </div>
      <div data-testid={`switch-${OptionsName}-moveToFirst`}>
        <span data-testid={`switch-status-${OptionsName}-moveToFirst`}>
          Switch is {options.moveToFirst ? 'on' : 'off'}
        </span>
        {!options.isPrimary && (
          <span data-testid={`disabled-${OptionsName}-moveToFirst`}>Disabled</span>
        )}
      </div>
    </div>
  ),
}));

describe('PrimarySettings', () => {
  const mockT = vi.fn((key) => key);

  beforeEach(() => {
    vi.clearAllMocks();

    // Set up mock translate
    (useTranslate as unknown as ReturnType<typeof vi.fn>).mockReturnValue({ t: mockT });

    // Set up mock workspace context
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      columns: [{ options: { isPrimary: true } }],
    });

    // Default isPrimary value
    mockWatch.mockReturnValue(true);
  });

  it('renders primary settings with label', () => {
    const options: FieldOptions = { isPrimary: true };
    renderWithMantine(
      <PrimarySettings options={options} label='Primary Field' hasPrimary={false} />
    );

    expect(screen.getByTestId('primary-label')).toHaveTextContent('Primary Field');
    expect(screen.getByTestId(`switch-${OptionsName}-isPrimary`)).toBeInTheDocument();
    expect(screen.getByTestId(`switch-${OptionsName}-moveToFirst`)).toBeInTheDocument();
  });

  it('changes isPrimary value when switch is clicked', () => {
    const options: FieldOptions = { isPrimary: true };
    renderWithMantine(
      <PrimarySettings options={options} label='Primary Field' hasPrimary={false} />
    );

    const switchElem = screen.getByTestId(`switch-${OptionsName}-isPrimary`);
    fireEvent.click(switchElem);

    expect(mockSetValue).toHaveBeenCalledWith(`${OptionsName}.moveToFirst`, false);
  });

  it('renders with isPrimary set to false', () => {
    const options: FieldOptions = { isPrimary: false };
    renderWithMantine(
      <PrimarySettings options={options} label='Primary Field' hasPrimary={true} />
    );

    expect(screen.getByTestId(`switch-status-${OptionsName}-isPrimary`)).toHaveTextContent(
      'Switch is off'
    );
  });

  it('shows tooltip when primaryDesc is provided', () => {
    const options: FieldOptions = { isPrimary: true };
    renderWithMantine(
      <PrimarySettings
        options={options}
        label='Primary Field'
        hasPrimary={false}
        primaryDesc='This is the primary field description'
      />
    );

    expect(screen.getByTestId('primary-desc')).toHaveTextContent(
      'This is the primary field description'
    );
  });

  it('disables primary switch when another primary field exists', () => {
    const options: FieldOptions = { isPrimary: false };
    renderWithMantine(
      <PrimarySettings
        options={options}
        label='Secondary Field'
        hasPrimary={true} // Indicate primary exists
      />
    );

    expect(screen.getByTestId(`disabled-${OptionsName}-isPrimary`)).toBeInTheDocument();
  });

  it('disables moveToFirst switch when isPrimary is false', () => {
    const options: FieldOptions = { isPrimary: false };
    renderWithMantine(<PrimarySettings options={options} label='Field' hasPrimary={false} />);

    expect(screen.getByTestId(`disabled-${OptionsName}-moveToFirst`)).toBeInTheDocument();
  });
});
