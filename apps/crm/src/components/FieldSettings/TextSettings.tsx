import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { FieldOptions } from '@/models';
import { FieldTypes } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import { useMemo } from 'react';
import { PrimarySettings } from './PrimarySettings';

export const TextSettings = ({ options }: { options: FieldOptions }) => {
  const { t } = useTranslate('workspace');
  const { columns } = useWorkspaceContext();

  const hasTextPrimary = useMemo(() => {
    return columns.some(
      (col) => col.options?.isPrimary && col.type === FieldTypes.SINGLE_LINE_TEXT
    );
  }, [columns]);

  return <PrimarySettings label={t('primaryText')} options={options} hasPrimary={hasTextPrimary} />;
};
