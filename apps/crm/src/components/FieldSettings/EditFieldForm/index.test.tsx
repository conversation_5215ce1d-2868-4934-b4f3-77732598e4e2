import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { act, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import EditColumnField from './index';

// Mock the WorkspaceContext
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

// Mock React's useState
let stateValue;
const mockSetState = vi.fn();
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useState: vi.fn().mockImplementation((initial) => {
      stateValue = initial;
      return [initial, mockSetState];
    }),
  };
});

// Mock the AddFieldForm component
vi.mock('../AddFieldForm', () => ({
  AddFieldForm: ({ onCancel, onSave }) => (
    <div data-testid='add-field-form'>
      <span>Mocked AddFieldForm</span>
      <button data-testid='cancel-button' onClick={onCancel}>
        Cancel
      </button>
      <button
        data-testid='save-button'
        onClick={() =>
          onSave({ name: 'Test Field', type: 'text', isProtected: false, options: {} })
        }
      >
        Save
      </button>
    </div>
  ),
}));

describe('EditColumnField', () => {
  const mockHandleUpdateColumn = vi.fn();
  const mockHandleAddColumn = vi.fn();
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock WorkspaceContext
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      handleUpdateColumn: mockHandleUpdateColumn,
      handleAddColumn: mockHandleAddColumn,
    });
  });

  it('renders the AddFieldForm component', () => {
    renderWithMantine(<EditColumnField onClose={mockOnClose} />);

    expect(screen.getByTestId('add-field-form')).toBeInTheDocument();
    expect(screen.getByText('Mocked AddFieldForm')).toBeInTheDocument();
  });

  it('initializes formData state with provided data', () => {
    const testData = {
      id: 'test-id',
      name: 'Original Name',
      type: 'text',
      isProtected: false,
      options: {},
    };

    renderWithMantine(<EditColumnField onClose={mockOnClose} data={testData} />);

    expect(stateValue).toEqual(testData);
  });

  it('updates formData state when form is saved', () => {
    const testData = {
      id: 'test-id',
      name: 'Original Name',
      type: 'text',
      isProtected: false,
      options: {},
    };

    renderWithMantine(<EditColumnField onClose={mockOnClose} data={testData} />);

    const newFormData = {
      name: 'Test Field',
      type: 'text',
      isProtected: false,
      options: {},
    };

    screen.getByTestId('save-button').click();

    expect(mockSetState).toHaveBeenCalledWith(newFormData);
    expect(mockHandleAddColumn).not.toHaveBeenCalled();
  });

  it('calls handleAddColumn when id is provided but no data and form is saved', () => {
    renderWithMantine(
      <EditColumnField onClose={mockOnClose} id='new-field-id' insertType='append' />
    );

    screen.getByTestId('save-button').click();

    expect(mockHandleAddColumn).toHaveBeenCalledWith({
      name: 'Test Field',
      type: 'text',
      isProtected: false,
      options: {},
      id: 'new-field-id',
      insertType: 'append',
    });
    expect(mockHandleUpdateColumn).not.toHaveBeenCalled();
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls onClose when cancel button is clicked', () => {
    renderWithMantine(<EditColumnField onClose={mockOnClose} />);

    screen.getByTestId('cancel-button').click();

    expect(mockOnClose).toHaveBeenCalled();
    expect(mockHandleUpdateColumn).not.toHaveBeenCalled();
    expect(mockHandleAddColumn).not.toHaveBeenCalled();
  });

  it('shows confirmation modal for relationship field type change', async () => {
    const testData = {
      id: 'test-id',
      name: 'Original Name',
      type: FieldTypes.RELATIONSHIP,
      isProtected: false,
      options: { objectId: 'old-object' },
    };

    renderWithMantine(<EditColumnField onClose={mockOnClose} data={testData} />);

    expect(screen.getByTestId('update-linked-object-modal-warning')).toBeInTheDocument();
  });

  it('shows confirmation modal when objectId changes in options', async () => {
    const testData = {
      id: 'test-id',
      name: 'Original Name',
      type: FieldTypes.RELATIONSHIP,
      isProtected: false,
      options: { objectId: 'old-object' },
    };

    renderWithMantine(<EditColumnField onClose={mockOnClose} data={testData} />);

    // Simulate form save with new objectId
    const newFormData = {
      ...testData,
      options: { objectId: 'new-object' },
    };

    act(() => {
      mockSetState(newFormData);
    });

    screen.getByTestId('save-button').click();

    expect(mockSetState).toHaveBeenCalledWith(
      expect.objectContaining({
        options: expect.objectContaining({
          objectId: 'new-object',
        }),
      })
    );

    expect(screen.getByTestId('update-linked-object-modal-warning')).toBeInTheDocument();
  });
});
