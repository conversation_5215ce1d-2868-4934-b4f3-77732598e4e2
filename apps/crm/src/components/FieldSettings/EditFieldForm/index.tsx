import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { Box, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { Modal } from '@resola-ai/ui';
import { FieldTypes } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import React, { useState } from 'react';
import { AddFieldForm } from '../AddFieldForm';

type Props = {
  onClose: () => void;
  data?: any;
  id?: string;
  insertType?: string;
};
const useStyles = createStyles(() => ({
  modal: {
    '.mantine-Modal-inner': {
      top: rem(120),
    },
  },
}));

const EditColumnField = ({ data, onClose, id, insertType }: Props) => {
  const { handleUpdateColumn, handleAddColumn } = useWorkspaceContext();
  const [opened, { open, close }] = useDisclosure(false);
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();
  const [formData, setFormData] = useState<any>(data);

  const handleSave = (newData) => {
    if (data) {
      setFormData(newData);
      if (
        data.type === FieldTypes.RELATIONSHIP &&
        newData.options.objectId !== data.options.objectId
      ) {
        open();
      } else {
        handleUpdate(newData);
      }
    } else if (id) {
      handleAddColumn({ ...newData, id, insertType });
      onClose();
    }
  };

  const handleUpdate = (updatedData) => {
    handleUpdateColumn(data.id, {
      ...data,
      name: updatedData.name,
      type: updatedData.type,
      isProtected: updatedData.isProtected,
      options: updatedData.options,
    });
    onClose();
  };

  return (
    <Box w={'100%'}>
      <AddFieldForm data={data} onCancel={onClose} onSave={(newData) => handleSave(newData)} />
      <Modal
        data-testid='update-linked-object-modal-warning'
        className={classes.modal}
        zIndex={1000}
        opened={opened}
        onClose={close}
        title={t('updateLinkedObject')}
        okButtonProps={{ variant: 'negative' }}
        cancelText={t('cancel')}
        okText={t('confirm')}
        onOk={() => {
          handleUpdate(formData);
          close();
        }}
        onCancel={close}
      >
        <Box>
          <Text c='decaNavy.5' mb={rem(30)}>
            {t('updateLinkedObjectDesc1')}
          </Text>
          <Text c='decaGrey.9'>{t('updateLinkedObjectDesc2')}</Text>
        </Box>
      </Modal>
    </Box>
  );
};

export default React.memo(EditColumnField);
