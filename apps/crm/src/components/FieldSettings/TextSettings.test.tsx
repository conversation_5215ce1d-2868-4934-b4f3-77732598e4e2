import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { screen } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import { describe, expect, it, vi } from 'vitest';
import { TextSettings } from './TextSettings';

// Mock the hooks
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
}));

// Mock the PrimarySettings component
vi.mock('./PrimarySettings', () => ({
  PrimarySettings: ({ label, hasPrimary }) => (
    <div data-testid='primary-settings'>
      <div data-testid='label'>{label}</div>
      <div data-testid='has-primary'>{String(hasPrimary)}</div>
    </div>
  ),
}));

// Mock react-hook-form
vi.mock('react-hook-form', () => {
  const actual = vi.importActual('react-hook-form');
  return {
    ...actual,
    useFormContext: () => ({
      setValue: vi.fn(),
      watch: vi.fn(),
    }),
  };
});

describe('TextSettings', () => {
  const mockTranslate = vi.fn((key) => key);

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock return values
    (useTranslate as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      t: mockTranslate,
    });
  });

  it('renders with no primary text field', () => {
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      columns: [
        { type: FieldTypes.NUMBER, options: { isPrimary: true } },
        { type: FieldTypes.SINGLE_LINE_TEXT, options: { isPrimary: false } },
      ],
    });

    renderWithMantine(<TextSettings options={{}} />);

    expect(screen.getByTestId('primary-settings')).toBeInTheDocument();
    expect(screen.getByTestId('label')).toHaveTextContent('primaryText');
    expect(screen.getByTestId('has-primary')).toHaveTextContent('false');
  });

  it('renders with existing primary text field', () => {
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      columns: [
        { type: FieldTypes.SINGLE_LINE_TEXT, options: { isPrimary: true } },
        { type: FieldTypes.SINGLE_LINE_TEXT, options: { isPrimary: false } },
      ],
    });

    renderWithMantine(<TextSettings options={{}} />);

    expect(screen.getByTestId('primary-settings')).toBeInTheDocument();
    expect(screen.getByTestId('label')).toHaveTextContent('primaryText');
    expect(screen.getByTestId('has-primary')).toHaveTextContent('true');
  });
});
