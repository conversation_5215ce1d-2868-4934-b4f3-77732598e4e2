import { OptionsName } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { FieldOptions } from '@/models';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { mockSetValue, mockWatch } from '../../tests/mocks/reactHookForm';
import { ImageSettings } from './ImageSettings';

// Mock the hooks - mocks need to be before using any variables
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
}));

// Mock HFSwitch with a clickable div - test IDs use dash instead of dot
vi.mock('@resola-ai/ui', () => ({
  HFSwitch: ({ name, label, disabled, onChange }) => (
    <div
      data-testid={`switch-${name.replace('.', '-')}`}
      onClick={() => onChange?.({ target: { checked: !mockWatch() } })}
    >
      <label>{label}</label>
      <span data-testid={`switch-status-${name.replace('.', '-')}`}>
        Switch is {mockWatch() ? 'on' : 'off'}
      </span>
      {disabled && <span data-testid={`disabled-${name.replace('.', '-')}`}>Disabled</span>}
    </div>
  ),
}));

// Mock react-hook-form
vi.mock('react-hook-form', () => {
  return {
    useForm: () => ({
      control: {},
      getValues: vi.fn(),
      setValue: mockSetValue,
      watch: mockWatch,
    }),
    useFormContext: () => ({
      control: {},
      getValues: vi.fn(),
      setValue: mockSetValue,
      watch: mockWatch,
    }),
    useWatch: () => mockWatch(),
    Controller: ({ render }) =>
      render({ field: { onChange: vi.fn(), value: mockWatch(), onBlur: vi.fn(), ref: vi.fn() } }),
  };
});

describe('ImageSettings', () => {
  const mockT = vi.fn((key) => key);

  beforeEach(() => {
    vi.clearAllMocks();

    // Set up mock translate
    (useTranslate as unknown as ReturnType<typeof vi.fn>).mockReturnValue({ t: mockT });

    // Set up mock workspace context
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      columns: [{ options: { isAvatar: true } }],
    });

    // Default isAvatar value
    mockWatch.mockReturnValue(true);
  });

  it('renders image settings with avatar option', () => {
    const options: FieldOptions = { isAvatar: true };
    renderWithMantine(<ImageSettings options={options} />);

    expect(screen.getByText('avatar')).toBeInTheDocument();
    expect(screen.getByTestId(`switch-${OptionsName}-isAvatar`)).toBeInTheDocument();
  });

  it('changes isAvatar value when switch is clicked', () => {
    const options: FieldOptions = { isAvatar: true };
    renderWithMantine(<ImageSettings options={options} />);

    const switchElem = screen.getByTestId(`switch-${OptionsName}-isAvatar`);
    fireEvent.click(switchElem);

    expect(mockSetValue).toHaveBeenCalledWith(`${OptionsName}.moveToFirst`, false);
  });

  it('renders with isAvatar set to false', () => {
    mockWatch.mockReturnValue(false);

    const options: FieldOptions = { isAvatar: false };
    renderWithMantine(<ImageSettings options={options} />);

    // Use a more specific selector to avoid matching multiple elements
    expect(screen.getByTestId(`switch-status-${OptionsName}-isAvatar`)).toHaveTextContent(
      'Switch is off'
    );
  });

  it('computes correct disabled states', () => {
    // Set up a scenario where hasAvatar is true but this field is not an avatar
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      columns: [
        { options: { isAvatar: true } }, // Another column is already an avatar
        { options: { moveToFirst: false } },
      ],
    });

    mockWatch.mockReturnValue(false); // This field is not an avatar

    const options: FieldOptions = { isAvatar: false };
    renderWithMantine(<ImageSettings options={options} />);

    // Test for the disabled indicator element
    expect(screen.getByTestId(`disabled-${OptionsName}-isAvatar`)).toBeInTheDocument();
  });

  it('initializes correctly with various props', () => {
    const options: FieldOptions = { isAvatar: true, moveToFirst: true };
    renderWithMantine(<ImageSettings options={options} />);

    expect(screen.getByText('avatar')).toBeInTheDocument();
    expect(screen.getByText('moveToFirstCol')).toBeInTheDocument();
  });
});
