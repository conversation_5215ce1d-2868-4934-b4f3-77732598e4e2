import { renderHook } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { useCurrency } from './useCurrency';

// Mock constants
vi.mock('@/constants/workspace', () => ({
  Currency: {
    usd: 'usd',
    yen: 'yen',
  },
  CurrencyFormatTypes: {
    commaPeriod: 'commaPeriod',
    spacePeriod: 'spacePeriod',
  },
  CurrencySymbol: {
    usd: '$',
    yen: '¥',
  },
}));

// Mock Mantine components
vi.mock('@mantine/core', () => ({
  Flex: ({ children, justify, w }: any) => (
    <div data-testid="flex" data-justify={justify} data-w={w}>
      {children}
    </div>
  ),
  Text: ({ children, c }: any) => (
    <span data-testid="text" data-color={c}>
      {children}
    </span>
  ),
}));

// Mock translation hook
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: vi.fn((key: string) => {
      const translations: Record<string, string> = {
        usd: 'US Dollar',
        yen: 'Japanese Yen',
        comma: 'Comma',
        space: 'Space',
      };
      return translations[key] || key;
    }),
  })),
}));

describe('useCurrency', () => {
  it('should return all required currency data structures', () => {
    const { result } = renderHook(() => useCurrency());

    expect(result.current).toHaveProperty('currencyOptions');
    expect(result.current).toHaveProperty('usdDecimalPlaces');
    expect(result.current).toHaveProperty('yenDecimalPlaces');
    expect(result.current).toHaveProperty('separatorOptions');
  });

  describe('currencyOptions', () => {
    it('should return USD and YEN currency options', () => {
      const { result } = renderHook(() => useCurrency());

      expect(result.current.currencyOptions).toEqual([
        {
          options: [
            { value: 'usd', label: 'US Dollar' },
            { value: 'yen', label: 'Japanese Yen' },
          ],
        },
      ]);
    });
  });

  describe('separatorOptions', () => {
    it('should return comma and space separator options with proper formatting', () => {
      const { result } = renderHook(() => useCurrency());

      const separatorOptions = result.current.separatorOptions;
      expect(separatorOptions).toHaveLength(1);
      expect(separatorOptions[0].options).toHaveLength(2);

      const options = separatorOptions[0].options;
      expect(options[0].value).toBe('commaPeriod');
      expect(options[0].selectedDisplay).toBe('Comma (1,000,000)');
      expect(options[1].value).toBe('spacePeriod');
      expect(options[1].selectedDisplay).toBe('Space (1 000 000)');
    });

    it('should render Flex components for separator option labels', () => {
      const { result } = renderHook(() => useCurrency());

      const separatorOptions = result.current.separatorOptions;
      const firstOption = separatorOptions[0].options[0];

      // Check that the label is a React element
      expect(firstOption.label).toBeDefined();
      expect(typeof firstOption.label).toBe('object');
    });
  });

  describe('decimalPlaces', () => {
    it('should return correct decimal places for USD currency', () => {
      const { result } = renderHook(() => useCurrency());

      const usdDecimalPlaces = result.current.usdDecimalPlaces;
      expect(usdDecimalPlaces).toHaveLength(1);
      expect(usdDecimalPlaces[0].options).toHaveLength(5);

      // Test first option (0 decimal places)
      const firstOption = usdDecimalPlaces[0].options[0];
      expect(firstOption.value).toBe(0);
      expect(firstOption.selectedDisplay).toBe('0 ($ 1)');

      // Test last option (4 decimal places)
      const lastOption = usdDecimalPlaces[0].options[4];
      expect(lastOption.value).toBe(4);
      expect(lastOption.selectedDisplay).toBe('4 ($ 1.0000)');
    });

    it('should return correct decimal places for YEN currency', () => {
      const { result } = renderHook(() => useCurrency());

      const yenDecimalPlaces = result.current.yenDecimalPlaces;
      expect(yenDecimalPlaces).toHaveLength(1);
      expect(yenDecimalPlaces[0].options).toHaveLength(5);

      // Test first option (0 decimal places)
      const firstOption = yenDecimalPlaces[0].options[0];
      expect(firstOption.value).toBe(0);
      expect(firstOption.selectedDisplay).toBe('0 (¥ 1)');

      // Test option with decimal places
      const secondOption = yenDecimalPlaces[0].options[2];
      expect(secondOption.value).toBe(2);
      expect(secondOption.selectedDisplay).toBe('2 (¥ 1.00)');
    });

    it('should render Flex components for decimal place option labels', () => {
      const { result } = renderHook(() => useCurrency());

      const usdDecimalPlaces = result.current.usdDecimalPlaces;
      const firstOption = usdDecimalPlaces[0].options[0];

      // Check that the label is a React element
      expect(firstOption.label).toBeDefined();
      expect(typeof firstOption.label).toBe('object');
    });
  });

  describe('memoization', () => {
    it('should maintain stable references for currency options', () => {
      const { result, rerender } = renderHook(() => useCurrency());

      const firstRender = result.current.currencyOptions;
      rerender();
      const secondRender = result.current.currencyOptions;

      expect(firstRender).toBe(secondRender);
    });

    it('should maintain stable references for separator options', () => {
      const { result, rerender } = renderHook(() => useCurrency());

      const firstRender = result.current.separatorOptions;
      rerender();
      const secondRender = result.current.separatorOptions;

      expect(firstRender).toBe(secondRender);
    });

    it('should maintain stable references for decimal places', () => {
      const { result, rerender } = renderHook(() => useCurrency());

      const firstUsdRender = result.current.usdDecimalPlaces;
      const firstYenRender = result.current.yenDecimalPlaces;
      
      rerender();
      
      const secondUsdRender = result.current.usdDecimalPlaces;
      const secondYenRender = result.current.yenDecimalPlaces;

      expect(firstUsdRender).toBe(secondUsdRender);
      expect(firstYenRender).toBe(secondYenRender);
    });
  });

  describe('edge cases', () => {
    it('should handle missing translations gracefully', () => {
      // Re-mock with missing translations
      vi.mocked(vi.fn()).mockImplementation(() => ({
        t: vi.fn((key: string) => key), // Return key as-is when translation missing
      }));

      expect(() => {
        renderHook(() => useCurrency());
      }).not.toThrow();
    });

    it('should generate correct number of decimal place options', () => {
      const { result } = renderHook(() => useCurrency());

      // Should generate options for 0, 1, 2, 3, 4 decimal places (5 total)
      expect(result.current.usdDecimalPlaces[0].options).toHaveLength(5);
      expect(result.current.yenDecimalPlaces[0].options).toHaveLength(5);
    });

    it('should handle different currency symbols correctly', () => {
      const { result } = renderHook(() => useCurrency());

      const usdOption = result.current.usdDecimalPlaces[0].options[1];
      const yenOption = result.current.yenDecimalPlaces[0].options[1];

      expect(usdOption.selectedDisplay).toContain('$');
      expect(yenOption.selectedDisplay).toContain('¥');
    });
  });
}); 