import { Currency, OptionsName } from '@/constants/workspace';
import type { FieldOptions } from '@/models';
import { Box, Text, rem } from '@mantine/core';
import { HFSwitch, Select } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useFormContext } from 'react-hook-form';
import { useCurrency } from './useCurrency';

export const CurrencySettings = ({ options }: { options: FieldOptions }) => {
  const { t } = useTranslate('workspace');
  const { setValue, watch } = useFormContext();
  const { currencyOptions, yenDecimalPlaces, usdDecimalPlaces } = useCurrency();
  const { separatorOptions } = useCurrency();

  const showThousandsSeparator = watch(`${OptionsName}.separator.enabled`);
  const currentCurrency = watch(`${OptionsName}.currency`);
  const isYen = currentCurrency === Currency.yen;
  const decimalOptions = isYen ? yenDecimalPlaces : usdDecimalPlaces;

  return (
    <>
      <Box my={rem(10)}>
        <Text mb={rem(5)}>{t('currency')}</Text>
        <Select
          groups={currencyOptions}
          onChange={(val) => {
            setValue(`${OptionsName}.currency`, val);
          }}
          defaultValue={options?.currency || Currency.usd}
        />
      </Box>
      <Box my={rem(10)}>
        <Text mb={rem(5)}>{t('decimalPlaces')}</Text>
        <Select
          groups={decimalOptions}
          onChange={(val) => {
            setValue(`${OptionsName}.decimalPlaces`, val);
          }}
          defaultValue={options?.decimalPlaces || decimalOptions[0].options[0].value}
        />
      </Box>
      <HFSwitch
        name={`${OptionsName}.separator.enabled`}
        label={t('showThousandsSeparator')}
        size='xs'
        my={rem(20)}
      />
      {showThousandsSeparator && (
        <Box my={rem(10)}>
          <Text mb={rem(5)}>{t('thousandAndDecimalSeparator')}</Text>
          <Select
            groups={separatorOptions}
            onChange={(val) => {
              setValue(`${OptionsName}.separator.format`, val);
            }}
            defaultValue={options?.separator?.format || separatorOptions[0].options[0].value}
          />
        </Box>
      )}
    </>
  );
};
