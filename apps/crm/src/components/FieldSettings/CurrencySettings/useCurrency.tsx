import { Currency, CurrencyFormatTypes, CurrencySymbol } from '@/constants/workspace';
import { Flex, Text } from '@mantine/core';
import type { Option } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import React from 'react';

export const useCurrency = () => {
  const { t } = useTranslate('workspace');

  const currencyOptions = React.useMemo(() => {
    return [
      {
        options: [
          { value: Currency.usd, label: t('usd') },
          { value: Currency.yen, label: t('yen') },
        ],
      },
    ];
  }, []);

  const separatorOptions = React.useMemo(() => {
    const rawOptions = [
      { value: CurrencyFormatTypes.commaPeriod, label: t('comma'), subLabel: '1,000,000' },
      { value: CurrencyFormatTypes.spacePeriod, label: t('space'), subLabel: '1 000 000' },
    ];
    return [
      {
        options: rawOptions.map((option) => ({
          value: option.value,
          label: (
            <Flex justify='space-between' w={'100%'}>
              <Text c={'decaGrey.9'}>{option.label}</Text>
              <Text c={'decaGrey.4'}>{option.subLabel}</Text>
            </Flex>
          ),
          selectedDisplay: `${option.label} (${option.subLabel})`,
        })),
      },
    ];
  }, []);

  const decimalPlaces = React.useCallback((currency: string) => {
    const prefix = CurrencySymbol[currency];

    const options: Option[] = Array.from({ length: 5 }, (_, i) => {
      const dp = (1).toFixed(i);

      return {
        label: (
          <Flex justify='space-between' w='100%'>
            <Text c='decaGrey.9'>{i}</Text>
            <Text c='decaGrey.4'>{`${prefix} ${dp}`}</Text>
          </Flex>
        ),
        value: i,
        selectedDisplay: `${i} (${prefix} ${dp})`,
      };
    });

    return [{ options }];
  }, []);

  return React.useMemo(() => {
    return {
      currencyOptions,
      usdDecimalPlaces: decimalPlaces(Currency.usd),
      yenDecimalPlaces: decimalPlaces(Currency.yen),
      separatorOptions,
    };
  }, [currencyOptions, decimalPlaces, separatorOptions]);
};
