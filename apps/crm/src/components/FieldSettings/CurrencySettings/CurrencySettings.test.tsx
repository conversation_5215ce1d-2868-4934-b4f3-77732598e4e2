import { Currency } from '@/constants/workspace';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { useFormContext } from 'react-hook-form';
import { describe, expect, it, vi } from 'vitest';
import { CurrencySettings } from './index';

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
  useController: () => ({
    field: {
      value: false,
      onChange: vi.fn(),
      onBlur: vi.fn(),
      ref: { current: null },
    },
    fieldState: { error: null },
  }),
}));

vi.mock('./useCurrency', () => ({
  useCurrency: () => ({
    currencyOptions: [
      {
        options: [
          { value: 'usd', label: 'USD' },
          { value: 'yen', label: 'YEN' },
        ],
      },
    ],
    usdDecimalPlaces: [
      {
        options: [
          { value: 1, label: '1', selectedDisplay: '1 ($ 1.0)' },
          { value: 2, label: '2', selectedDisplay: '2 ($ 1.00)' },
        ],
      },
    ],
    yenDecimalPlaces: [
      {
        options: [
          { value: 1, label: '1', selectedDisplay: '1 (¥ 1.0)' },
          { value: 2, label: '2', selectedDisplay: '2 (¥ 1.00)' },
        ],
      },
    ],
    separatorOptions: [
      {
        options: [
          { value: 'comma', label: 'Comma', selectedDisplay: 'Comma (1,000,000)' },
          { value: 'space', label: 'Space', selectedDisplay: 'Space (1 000 000)' },
        ],
      },
    ],
  }),
}));

vi.mock('../AddFieldForm/useOptions', () => ({
  useOptions: () => ({
    separatorOptions: [
      {
        options: [
          { value: 'comma', label: 'Comma', selectedDisplay: 'Comma (1,000,000)' },
          { value: 'space', label: 'Space', selectedDisplay: 'Space (1 000 000)' },
        ],
      },
    ],
  }),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key) => key,
  }),
}));

describe('CurrencySettings', () => {
  const mockWatch = vi.fn();
  const mockSetValue = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue({
      watch: mockWatch,
      setValue: mockSetValue,
    });
  });

  it('renders correctly with USD currency', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.separator.enabled') return false;
      if (name === 'options.currency') return Currency.usd;
      return undefined;
    });

    renderWithMantine(
      <CurrencySettings
        options={{
          currency: Currency.usd,
          decimalPlaces: 2,
        }}
      />
    );

    expect(screen.getByText('currency')).toBeInTheDocument();
    expect(screen.getByText('decimalPlaces')).toBeInTheDocument();
    expect(screen.getByText('showThousandsSeparator')).toBeInTheDocument();
  });

  it('renders separator options when showThousandsSeparator is enabled', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.separator.enabled') return true;
      if (name === 'options.currency') return Currency.usd;
      return undefined;
    });

    renderWithMantine(
      <CurrencySettings
        options={{
          currency: Currency.usd,
          decimalPlaces: 2,
          separator: {
            enabled: true,
            format: 'comma',
          },
        }}
      />
    );

    expect(screen.getByText('thousandAndDecimalSeparator')).toBeInTheDocument();
  });

  it('renders with YEN currency', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.separator.enabled') return false;
      if (name === 'options.currency') return Currency.yen;
      return undefined;
    });

    renderWithMantine(
      <CurrencySettings
        options={{
          currency: Currency.yen,
          decimalPlaces: 1,
        }}
      />
    );

    expect(screen.getByText('currency')).toBeInTheDocument();
    expect(screen.getByText('decimalPlaces')).toBeInTheDocument();
  });
});
