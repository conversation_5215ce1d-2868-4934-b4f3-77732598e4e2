import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { FieldOptions } from '@/models';
import { FieldTypes } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import { useMemo } from 'react';
import { PrimarySettings } from './PrimarySettings';

export const EmailSettings = ({ options }: { options: FieldOptions }) => {
  const { t } = useTranslate('workspace');
  const { columns } = useWorkspaceContext();
  const hasEmailPrimary = useMemo(() => {
    return columns.some((col) => col.options?.isPrimary && col.type === FieldTypes.EMAIL);
  }, [columns]);

  return (
    <PrimarySettings
      label={t('primaryEmail')}
      options={options}
      hasPrimary={hasEmailPrimary}
      primaryDesc={t('primaryEmailDesc')}
    />
  );
};
