import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { useFormContext } from 'react-hook-form';
import { describe, expect, it, vi } from 'vitest';
import { PercentSettings } from './index';

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
  useController: () => ({
    field: {
      value: false,
      onChange: vi.fn(),
      onBlur: vi.fn(),
      ref: { current: null },
    },
    fieldState: { error: null },
  }),
}));

vi.mock('./usePercentage', () => ({
  usePercentage: () => ({
    decimalPlaces: [
      {
        options: [
          { value: 1, label: '1', selectedDisplay: '1 (1.0 %)' },
          { value: 2, label: '2', selectedDisplay: '2 (1.00 %)' },
        ],
      },
    ],
    displayOptions: [
      {
        options: [
          { value: 'ring', label: 'Ring' },
          { value: 'bar', label: 'Bar' },
        ],
      },
    ],
  }),
}));

vi.mock('../AddFieldForm/useOptions', () => ({
  useOptions: () => ({
    separatorOptions: [
      {
        options: [
          { value: 'local', label: 'Local', selectedDisplay: 'Local (1,000,000.00)' },
          {
            value: 'commaPeriod',
            label: 'Comma & Period',
            selectedDisplay: 'Comma & Period (1,000,000.00)',
          },
        ],
      },
    ],
  }),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key) => key,
  }),
}));

describe('PercentSettings', () => {
  const mockWatch = vi.fn();
  const mockSetValue = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue({
      watch: mockWatch,
      setValue: mockSetValue,
    });
  });

  it('renders correctly with default options', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.separator.enabled') return false;
      if (name === 'options.presentation.enabled') return false;
      return undefined;
    });

    renderWithMantine(
      <PercentSettings
        options={{
          decimalPlaces: 2,
        }}
      />
    );

    expect(screen.getByText('decimalPlaces')).toBeInTheDocument();
    expect(screen.getByText('showThousandsSeparator')).toBeInTheDocument();
    expect(screen.getByText('displayAsAProgressBar')).toBeInTheDocument();
    expect(screen.queryByText('thousandAndDecimalSeparator')).not.toBeInTheDocument();
    expect(screen.queryByText('showAs')).not.toBeInTheDocument();
  });

  it('renders separator options when showThousandsSeparator is enabled', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.separator.enabled') return true;
      if (name === 'options.presentation.enabled') return false;
      return undefined;
    });

    renderWithMantine(
      <PercentSettings
        options={{
          decimalPlaces: 2,
          separator: {
            enabled: true,
            format: 'commaPeriod',
          },
        }}
      />
    );

    expect(screen.getByText('thousandAndDecimalSeparator')).toBeInTheDocument();
  });

  it('renders progress bar options when displayAsBar is enabled', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.separator.enabled') return false;
      if (name === 'options.presentation.enabled') return true;
      return undefined;
    });

    renderWithMantine(
      <PercentSettings
        options={{
          decimalPlaces: 2,
          presentation: {
            enabled: true,
            type: 'bar',
          },
        }}
      />
    );

    expect(screen.getByText('showAs')).toBeInTheDocument();
  });
});
