import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { BarLoader } from './BarLoader';

describe('BarLoader', () => {
  it('should render SVG element with correct dimensions', () => {
    const { container } = render(<BarLoader />);
    
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute('width', '60');
    expect(svg).toHaveAttribute('height', '6');
    expect(svg).toHaveAttribute('viewBox', '0 0 60 6');
  });

  it('should render with correct namespace and fill attributes', () => {
    const { container } = render(<BarLoader />);
    
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('fill', 'none');
    expect(svg).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
  });

  it('should contain proper clipPath structure', () => {
    const { container } = render(<BarLoader />);
    
    const clipPath = container.querySelector('clipPath');
    expect(clipPath).toBeInTheDocument();
    expect(clipPath).toHaveAttribute('id', 'clip0_4085_1653487');
    
    const clipRect = clipPath?.querySelector('rect');
    expect(clipRect).toHaveAttribute('width', '60');
    expect(clipRect).toHaveAttribute('height', '6');
    expect(clipRect).toHaveAttribute('rx', '3');
    expect(clipRect).toHaveAttribute('fill', 'white');
  });

  it('should contain background rectangle', () => {
    const { container } = render(<BarLoader />);
    
    const bgRect = container.querySelector('g rect');
    expect(bgRect).toBeInTheDocument();
    expect(bgRect).toHaveAttribute('width', '60');
    expect(bgRect).toHaveAttribute('height', '6');
    expect(bgRect).toHaveAttribute('fill', '#E4E5EC');
  });

  it('should contain progress bar path', () => {
    const { container } = render(<BarLoader />);
    
    const progressPath = container.querySelector('path');
    expect(progressPath).toBeInTheDocument();
    expect(progressPath).toHaveAttribute('fill', '#58C11E');
    
    const pathData = progressPath?.getAttribute('d');
    expect(pathData).toBe('M0 0H20.2258C21.8827 0 23.2258 1.34315 23.2258 3C23.2258 4.65685 21.8827 6 20.2258 6H0V0Z');
  });

  it('should have proper group structure with clipPath reference', () => {
    const { container } = render(<BarLoader />);
    
    const group = container.querySelector('g');
    expect(group).toBeInTheDocument();
    
    // Verify group contains the expected elements (rect and path)
    const groupRect = group?.querySelector('rect');
    const groupPath = group?.querySelector('path');
    expect(groupRect).toBeInTheDocument();
    expect(groupPath).toBeInTheDocument();
  });

  it('should contain defs section with clipPath definition', () => {
    const { container } = render(<BarLoader />);
    
    const defs = container.querySelector('defs');
    expect(defs).toBeInTheDocument();
    
    const clipPath = defs?.querySelector('clipPath');
    expect(clipPath).toBeInTheDocument();
  });

  it('should render without any errors', () => {
    expect(() => {
      render(<BarLoader />);
    }).not.toThrow();
  });

  it('should be a pure functional component', () => {
    const { container: container1 } = render(<BarLoader />);
    const { container: container2 } = render(<BarLoader />);
    
    // Both renders should produce identical output
    expect(container1.innerHTML).toBe(container2.innerHTML);
  });

  it('should have correct color scheme', () => {
    const { container } = render(<BarLoader />);
    
    // Background should be light gray
    const bgRect = container.querySelector('g rect');
    expect(bgRect).toHaveAttribute('fill', '#E4E5EC');
    
    // Progress bar should be green
    const progressPath = container.querySelector('path');
    expect(progressPath).toHaveAttribute('fill', '#58C11E');
  });

  it('should maintain aspect ratio', () => {
    const { container } = render(<BarLoader />);
    
    const svg = container.querySelector('svg');
    const width = Number(svg?.getAttribute('width'));
    const height = Number(svg?.getAttribute('height'));
    
    // Aspect ratio should be 10:1 (60:6)
    expect(width / height).toBe(10);
  });

  describe('SVG structure validation', () => {
    it('should have exactly one SVG element', () => {
      const { container } = render(<BarLoader />);
      
      const svgs = container.querySelectorAll('svg');
      expect(svgs).toHaveLength(1);
    });

    it('should have expected number of child elements', () => {
      const { container } = render(<BarLoader />);
      
      const svg = container.querySelector('svg');
      
      // Should have g and defs as direct children
      const directChildren = svg?.children;
      expect(directChildren).toHaveLength(2);
      
      const group = svg?.querySelector(':scope > g');
      const defs = svg?.querySelector(':scope > defs');
      
      expect(group).toBeInTheDocument();
      expect(defs).toBeInTheDocument();
    });

    it('should have correct element hierarchy', () => {
      const { container } = render(<BarLoader />);
      
      // g should contain rect and path
      const group = container.querySelector('g');
      const groupChildren = group?.children;
      expect(groupChildren).toHaveLength(2);
      
      // defs should contain clipPath
      const defs = container.querySelector('defs');
      const defsChildren = defs?.children;
      expect(defsChildren).toHaveLength(1);
      
      // clipPath should contain rect
      const clipPath = defs?.querySelector('clipPath');
      const clipPathChildren = clipPath?.children;
      expect(clipPathChildren).toHaveLength(1);
    });
  });
}); 