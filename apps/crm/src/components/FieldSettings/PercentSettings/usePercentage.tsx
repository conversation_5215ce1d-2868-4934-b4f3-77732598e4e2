import { Flex, Text } from '@mantine/core';
import type { Option } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { BarLoader } from './BarLoader';
import { CircleLoader } from './CircleLoader';

export const usePercentage = () => {
  const { t } = useTranslate('workspace');
  const decimalPlaces = React.useMemo(() => {
    const options: Option[] = [];
    for (let i = 1; i <= 5; i++) {
      const dp = Number.parseFloat('1').toFixed(i);
      options.push({
        label: (
          <Flex justify='space-between' w={'100%'}>
            <Text c={'decaGrey.9'}>{i}</Text>
            <Text c={'decaGrey.4'}>{`${dp} %`}</Text>
          </Flex>
        ),
        value: i,
        selectedDisplay: `${i} (${dp} %)`,
      });
    }
    return [{ options }];
  }, []);

  const displayOptions = React.useMemo(() => {
    return [
      {
        options: [
          {
            value: 'ring',
            label: (
              <Flex justify='space-between' w={'100%'} align={'center'}>
                <Text c={'decaGrey.9'}>{t('ring')}</Text>
                <CircleLoader />
              </Flex>
            ),
          },
          {
            value: 'bar',
            label: (
              <Flex justify='space-between' w={'100%'} align={'center'}>
                <Text c={'decaGrey.9'}>{t('bar')}</Text>
                <BarLoader />
              </Flex>
            ),
          },
        ],
      },
    ];
  }, []);

  return React.useMemo(() => {
    return {
      decimalPlaces,
      displayOptions,
    };
  }, [decimalPlaces, displayOptions]);
};
