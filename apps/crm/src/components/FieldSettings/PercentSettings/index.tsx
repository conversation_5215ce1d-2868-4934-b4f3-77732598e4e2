import { OptionsName } from '@/constants/workspace';
import type { FieldOptions } from '@/models';
import { Box, Text, rem } from '@mantine/core';
import { HFSwitch, Select } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useFormContext } from 'react-hook-form';
import { useOptions } from '../AddFieldForm/useOptions';
import { usePercentage } from './usePercentage';

export const PercentSettings = ({ options }: { options: FieldOptions }) => {
  const { t } = useTranslate('workspace');
  const { setValue, watch } = useFormContext();
  const { separatorOptions } = useOptions();
  const { decimalPlaces, displayOptions } = usePercentage();

  const showThousandsSeparator = watch(`${OptionsName}.separator.enabled`);
  const displayAsBar = watch(`${OptionsName}.presentation.enabled`);

  return (
    <>
      <Box my={rem(10)}>
        <Text mb={rem(5)}>{t('decimalPlaces')}</Text>
        <Select
          groups={decimalPlaces}
          onChange={(val) => {
            setValue(`${OptionsName}.decimalPlaces`, val);
          }}
          defaultValue={options?.decimalPlaces || decimalPlaces[0].options[0].value}
        />
      </Box>
      <HFSwitch
        name={`${OptionsName}.separator.enabled`}
        label={t('showThousandsSeparator')}
        size='xs'
        my={rem(20)}
        disabled={displayAsBar}
      />
      {showThousandsSeparator && (
        <Box my={rem(10)}>
          <Text mb={rem(5)}>{t('thousandAndDecimalSeparator')}</Text>
          <Select
            groups={separatorOptions}
            onChange={(val) => {
              setValue(`${OptionsName}.separator.format`, val);
            }}
            defaultValue={options?.separator?.format || separatorOptions[0].options[0].value}
          />
        </Box>
      )}
      <HFSwitch
        name={`${OptionsName}.presentation.enabled`}
        label={t('displayAsAProgressBar')}
        size='xs'
        my={rem(20)}
        disabled={showThousandsSeparator}
      />
      {displayAsBar && (
        <Box my={rem(10)}>
          <Text mb={rem(5)}>{t('showAs')}</Text>
          <Select
            groups={displayOptions}
            onChange={(val) => {
              setValue(`${OptionsName}.presentation.type`, val);
            }}
            defaultValue={options?.presentation?.type || displayOptions[0].options[0].value}
          />
        </Box>
      )}
    </>
  );
};
