import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import * as reactHookForm from 'react-hook-form';
import type { Control, FieldValues, FormState, UseFormReturn, UseFormWatch } from 'react-hook-form';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mocks
vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => ({ isManager: true }),
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => ({
    isPermissionAllowed: () => true,
    hasAllPermissions: () => true,
    hasAnyPermission: () => true,
  }),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key) => key }),
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn(),
    changeLanguage: vi.fn(),
  })),
  FormatSimple: vi.fn(() => ({})),
  InContextTools: vi.fn(() => ({})),
}));

// Create a mock control object that satisfies the Control type
const mockControl = {
  _subjects: { watch: null, array: null, state: null },
  _removeUnmounted: vi.fn(),
  _names: { mount: new Set(), array: new Set(), watch: new Set() },
  _state: { mount: new Set(), watch: new Set(), array: new Set() },
  _options: { mode: 'onSubmit', reValidateMode: 'onChange' },
  _formState: {},
  _fields: {},
  _proxyFormState: {},
  _defaultValues: {},
  _getWatch: vi.fn(),
  _formValues: {},
  _stateFlags: { action: false, mount: false, watch: false },
  register: vi.fn(),
  unregister: vi.fn(),
  _updateValid: vi.fn(),
  _updateFieldArray: vi.fn(),
  _getFieldArray: vi.fn(),
  _isMounted: true,
  _executeSchema: vi.fn(),
  _getDirty: vi.fn(),
  _updateFormState: vi.fn(),
} as unknown as Control;

// Create a mock form state that satisfies FormState
const mockFormState: FormState<FieldValues> = {
  isDirty: false,
  isLoading: false,
  isSubmitted: false,
  isSubmitSuccessful: false,
  isSubmitting: false,
  isValidating: false,
  isValid: false,
  submitCount: 0,
  dirtyFields: {},
  touchedFields: {},
  errors: {},
  defaultValues: undefined,
  disabled: false,
  validatingFields: {},
};

// Create a mock form return object that satisfies UseFormReturn
const createMockFormReturn = (
  watchValue: string,
  handleSubmitImpl = vi.fn((cb) => cb)
): UseFormReturn => ({
  control: mockControl,
  setValue: vi.fn(),
  watch: vi.fn(() => watchValue) as unknown as UseFormWatch<FieldValues>,
  reset: vi.fn(),
  handleSubmit: handleSubmitImpl,
  getValues: vi.fn(),
  getFieldState: vi.fn(),
  setError: vi.fn(),
  clearErrors: vi.fn(),
  unregister: vi.fn(),
  trigger: vi.fn(),
  formState: mockFormState,
  register: vi.fn(),
  resetField: vi.fn(),
  setFocus: vi.fn(),
});

// Mock the form components and hooks
vi.mock('react-hook-form', () => ({
  useForm: () => createMockFormReturn(''),
  FormProvider: ({ children }) => <div data-testid='form-provider'>{children}</div>,
}));

vi.mock('react-hook-form-mantine', () => ({
  TextInput: ({ name }) => <input data-testid={`input-${name}`} />,
}));

// Mock UI components
vi.mock('@resola-ai/ui', () => ({
  CustomSelect: () => <select data-testid='field-type-select' />,
  DecaButton: ({ children, onClick }) => (
    <button data-testid={`button-${children}`} onClick={onClick}>
      {children}
    </button>
  ),
  HFSwitch: ({ name, label }) => <div data-testid={`switch-${name}`}>{label}</div>,
}));

// Mock field settings components
vi.mock('..', () => ({
  TextSettings: () => <div data-testid='text-settings'>Text Settings</div>,
  EmailSettings: () => <div data-testid='email-settings'>Email Settings</div>,
  ImageSettings: () => <div data-testid='image-settings'>Image Settings</div>,
  NumberSettings: () => <div data-testid='number-settings'>Number Settings</div>,
  CurrencySettings: () => <div data-testid='currency-settings'>Currency Settings</div>,
  PercentSettings: () => <div data-testid='percent-settings'>Percent Settings</div>,
  PhoneSettings: () => <div data-testid='phone-settings'>Phone Settings</div>,
  SingleSelectSettings: () => <div data-testid='select-settings'>Select Settings</div>,
  TimeSettings: () => <div data-testid='time-settings'>Time Settings</div>,
  LineSettings: () => <div data-testid='line-settings'>Line Settings</div>,
}));

vi.mock('../LinkToRecordSettings', () => ({
  LinkToRecordSettings: () => <div data-testid='relationship-settings'>Relationship Settings</div>,
}));

// Mock other dependencies
vi.mock('@hookform/resolvers/zod', () => ({
  zodResolver: vi.fn(() => ({})),
}));

vi.mock('@/utils/schemas', () => ({
  createObjectFieldSchema: vi.fn(() => ({})),
}));

vi.mock('./useOptions', () => ({
  useOptions: () => ({ options: [] }),
}));

// Import component after mocks
import { AddFieldForm } from './index';

describe('AddFieldForm', () => {
  const mockOnSave = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the form with basic elements', () => {
    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);

    // Check for basic form elements
    expect(screen.getByTestId('form-provider')).toBeInTheDocument();
    expect(screen.getByTestId('input-name')).toBeInTheDocument();
    expect(screen.getByTestId('field-type-select')).toBeInTheDocument();
    expect(screen.getByTestId('button-cancel')).toBeInTheDocument();
    expect(screen.getByTestId('button-create')).toBeInTheDocument();
  });

  it('has cancel and create buttons that call the correct functions', () => {
    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);

    // Test cancel button
    const cancelButton = screen.getByTestId('button-cancel');
    cancelButton.click();
    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('renders text field settings when text type is selected', () => {
    // const { rerender } = renderWithMantine(
    //   <AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />
    // );

    const formWatch = vi
      .spyOn(reactHookForm, 'useForm')
      .mockImplementation(() => createMockFormReturn('text'));

    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);
    expect(screen.getByTestId('text-settings')).toBeInTheDocument();

    formWatch.mockRestore();
  });

  it('renders email field settings when email type is selected', () => {
    const formWatch = vi
      .spyOn(reactHookForm, 'useForm')
      .mockImplementation(() => createMockFormReturn('email'));

    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);
    expect(screen.getByTestId('email-settings')).toBeInTheDocument();

    formWatch.mockRestore();
  });

  it('renders image field settings when image type is selected', () => {
    const formWatch = vi
      .spyOn(reactHookForm, 'useForm')
      .mockImplementation(() => createMockFormReturn('image'));

    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);
    expect(screen.getByTestId('image-settings')).toBeInTheDocument();

    formWatch.mockRestore();
  });

  it('renders number field settings when number type is selected', () => {
    const formWatch = vi
      .spyOn(reactHookForm, 'useForm')
      .mockImplementation(() => createMockFormReturn('number'));

    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);
    expect(screen.getByTestId('number-settings')).toBeInTheDocument();

    formWatch.mockRestore();
  });

  it('renders currency field settings when currency type is selected', () => {
    const formWatch = vi
      .spyOn(reactHookForm, 'useForm')
      .mockImplementation(() => createMockFormReturn('currency'));

    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);
    expect(screen.getByTestId('currency-settings')).toBeInTheDocument();

    formWatch.mockRestore();
  });

  // single select
  it('renders single select field settings when single select type is selected', () => {
    const formWatch = vi
      .spyOn(reactHookForm, 'useForm')
      .mockImplementation(() => createMockFormReturn('select'));

    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);
    expect(screen.getByTestId('select-settings')).toBeInTheDocument();

    formWatch.mockRestore();
  });

  it('renders time field settings when time type is selected', () => {
    const formWatch = vi
      .spyOn(reactHookForm, 'useForm')
      .mockImplementation(() => createMockFormReturn('datetime'));

    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);
    expect(screen.getByTestId('time-settings')).toBeInTheDocument();

    formWatch.mockRestore();
  });

  it('renders relationship settings when link type is selected', () => {
    // Simulate relationship type selection
    const formWatch = vi
      .spyOn(reactHookForm, 'useForm')
      .mockImplementation(() => createMockFormReturn('relationship'));

    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);
    expect(screen.getByTestId('relationship-settings')).toBeInTheDocument();

    formWatch.mockRestore();
  });

  it('resets form when reset is called', () => {
    const mockReset = vi.fn();
    const mockForm = createMockFormReturn('text');
    mockForm.reset = mockReset;

    vi.spyOn(reactHookForm, 'useForm').mockImplementation(() => mockForm);

    renderWithMantine(<AddFieldForm onSave={mockOnSave} onCancel={mockOnCancel} />);

    expect(mockReset).toHaveBeenCalled();
  });
});
