import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { mockLibraries, renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { type Mock, vi } from 'vitest';
import AddNewColumnButton from './AddNewColumnButton';

// Mock the hooks and setup libraries
mockLibraries();
vi.mock('@/contexts/WorkspaceContext');

// Create the mock function with vi.hoisted to avoid hoisting issues
const { mockUseDisclosure } = vi.hoisted(() => ({
  mockUseDisclosure: vi.fn(),
}));

vi.mock('@mantine/hooks', () => ({
  useDisclosure: mockUseDisclosure,
}));

// Mock AddFieldForm
vi.mock('.', () => ({
  AddFieldForm: ({ onSave, onCancel }: any) => (
    <div data-testid='add-field-form' data-onSave={onSave.name} data-onCancel={onCancel.name}>
      AddFieldForm
    </div>
  ),
}));

describe('AddNewColumnButton', () => {
  const mockClose = vi.fn();
  const mockToggle = vi.fn();
  const mockHandleAddColumn = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Mock useDisclosure hook - returns [opened, { close, toggle }]
    mockUseDisclosure.mockReturnValue([false, { close: mockClose, toggle: mockToggle }]);

    // Mock WorkspaceContext
    (useWorkspaceContext as Mock).mockReturnValue({
      handleAddColumn: mockHandleAddColumn,
    });
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('renders the add column button with plus icon', () => {
    renderWithMantine(<AddNewColumnButton />);

    const addButton = screen.getByRole('button');
    expect(addButton).toBeInTheDocument();
    expect(screen.getByTestId('add-new-column-button-icon')).toBeInTheDocument();
  });

  it('renders button with correct test id and icon', () => {
    renderWithMantine(<AddNewColumnButton />);

    // Check that the button has the correct test id
    const buttonIcon = screen.getByTestId('add-new-column-button-icon');
    expect(buttonIcon).toBeInTheDocument();
    expect(buttonIcon).toHaveAttribute('type', 'button');
  });

  it('renders popover component structure', () => {
    renderWithMantine(<AddNewColumnButton />);

    // Check that the popover wrapper exists
    const popoverWrapper = screen.getByTestId('add-new-column-popover');
    expect(popoverWrapper).toBeInTheDocument();
  });

  it('button is clickable and has correct event handler', () => {
    renderWithMantine(<AddNewColumnButton />);

    const addButton = screen.getByRole('button');

    // Verify button is clickable (doesn't throw error)
    expect(() => fireEvent.click(addButton)).not.toThrow();

    // Verify button has an onClick handler
    expect(addButton).toHaveProperty('onclick');
  });

  it('has workspace context properly connected', () => {
    renderWithMantine(<AddNewColumnButton />);

    // Verify that the workspace context is being accessed
    expect(useWorkspaceContext).toHaveBeenCalled();
  });
});
