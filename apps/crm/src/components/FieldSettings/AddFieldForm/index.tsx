import { Currency, CurrencyFormatTypes, FormatDate, FormatTime } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { createObjectFieldSchema } from '@/utils/schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Flex, Text, ThemeIcon, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { CustomSelect, DecaButton, HFSwitch } from '@resola-ai/ui';
import { DecaTooltip, FieldTypes } from '@resola-ai/ui/components';
import { PERMISSION_KEYS, isPermissionAllowed } from '@resola-ai/ui/components/DecaTable/utils';
import { IconInfoCircle } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useMemo } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { TextInput } from 'react-hook-form-mantine';
import {
  CurrencySettings,
  EmailSettings,
  ImageSettings,
  NumberSettings,
  PercentSettings,
  PhoneSettings,
  SingleSelectSettings,
  TextSettings,
  TimeSettings,
} from '..';
import { LineSettings } from '../LineSettings';
import { LinkToRecordSettings } from '../LinkToRecordSettings';
import { useOptions } from './useOptions';

type Props = {
  onSave: (data: any, onSuccess?: () => void) => void;
  onCancel: () => void;
  data?: any;
};

const useStyles = createStyles((theme) => ({
  form: {
    maxHeight: '70vh',
    width: 'auto',
    overflowY: 'auto',
    overflowX: 'hidden',
    fontWeight: 'normal',
    '&::-webkit-scrollbar, div::-webkit-scrollbar': {
      width: rem(4),
    },
    'form::-webkit-scrollbar-thumb, div::-webkit-scrollbar-thumb': {
      backgroundColor: theme.colors.decaLight[1],
      borderRadius: rem(4),
    },
  },
  options: {
    height: rem(280),
  },
  nonManagerEditDesc: {
    backgroundColor: theme.colors.decaLight[1],
    borderRadius: rem(8),
    padding: rem(10),
    marginBottom: rem(10),
    whiteSpace: 'pre-line',
  },
}));

export const AddFieldForm = ({ onSave, onCancel, data: initialData }: Props) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();
  const { options } = useOptions();
  const { object } = useWorkspaceContext();
  const wsSchema = createObjectFieldSchema(t);
  const formMethods = useForm({
    defaultValues: {
      name: '',
      type: '',
      options: {},
      isProtected: false,
    },
    resolver: zodResolver(wsSchema),
  });
  const { handleSubmit, control, setValue, watch, reset } = formMethods;
  const type = watch('type');

  useEffect(() => {
    // Reset value when opening form
    reset({
      name: '',
      type: '',
      options: {},
      isProtected: false,
    });
  }, []);

  useEffect(() => {
    if (initialData) {
      reset({
        name: initialData.header,
        type: initialData.type,
        options: initialData.options ?? {},
        isProtected: initialData.isProtected ?? false,
      });
    } else {
      if (
        (
          [FieldTypes.DATETIME, FieldTypes.CREATED_TIME, FieldTypes.MODIFIED_TIME] as string[]
        ).includes(type)
      ) {
        setValue('options', {
          date: { format: FormatDate.ja },
          time: { format: FormatTime['12h'] },
          timezone: { format: 'Asia/Tokyo' },
        });
      }
      if (type === FieldTypes.CURRENCY) {
        setValue('options', {
          currency: Currency.usd,
          decimalPlaces: 1,
          separator: { format: CurrencyFormatTypes.commaPeriod },
        });
      }
      if (type === FieldTypes.PERCENT) {
        setValue('options', { separator: { format: CurrencyFormatTypes.commaPeriod } });
      }
      if (type === FieldTypes.PHONE_NUMBER) {
        setValue('options', { customFormat: { enabled: true, format: 'jp' } });
      }
    }
  }, [initialData, type]);

  const onSumit = (data) => {
    if (data.type === FieldTypes.DATETIME && data.options.useCurrentDate) {
      data.options.defaultValue = new Date().toISOString();
    }
    onSave({ ...data }, () => {
      reset();
    });
  };

  const typeDescription = useMemo(() => {
    switch (type) {
      case FieldTypes.LONG_TEXT:
        return t('fieldTypeDesc.longText');
      case FieldTypes.PHONE_NUMBER:
        return t('fieldTypeDesc.phoneNumber');
      case FieldTypes.EMAIL:
        return t('fieldTypeDesc.email');
      case FieldTypes.URL:
        return t('fieldTypeDesc.url');
      case FieldTypes.CREATED_TIME:
        return t('fieldTypeDesc.createdTime');
      case FieldTypes.MODIFIED_TIME:
        return t('fieldTypeDesc.modifiedTime');
      case FieldTypes.DATETIME:
        return t('fieldTypeDesc.dateTime');
      case FieldTypes.CREATED_BY:
        return t('fieldTypeDesc.createdBy');
      case FieldTypes.MODIFIED_BY:
        return t('fieldTypeDesc.modifiedBy');
      case FieldTypes.IMAGE:
        return t('fieldTypeDesc.image');
      case FieldTypes.SINGLE_SELECT:
        return t('fieldTypeDesc.singleSelect');
      case FieldTypes.MULTI_SELECT:
        return t('fieldTypeDesc.mulSelect');
      case FieldTypes.CHECKBOX:
        return t('fieldTypeDesc.checkbox');
      case FieldTypes.NUMBER:
        return t('fieldTypeDesc.number');
      case FieldTypes.AUTONUMBER:
        return t('fieldTypeDesc.autoNumber');
      case FieldTypes.LINE:
        return t('fieldTypeDesc.line');
      default:
        return '';
    }
  }, [type]);

  const canUpdateObject = isPermissionAllowed(
    object?.permission ?? {},
    PERMISSION_KEYS.OBJECT_UPDATE
  );
  const allowEdit = canUpdateObject || !initialData?.isProtected;

  return (
    <FormProvider {...formMethods}>
      <form className={classes.form} onSubmit={(e) => e.preventDefault()}>
        {!allowEdit && (
          <Text className={classes.nonManagerEditDesc}>{t('nonManagerEditDesc')}</Text>
        )}

        {/* Protected field moved to top */}
        {allowEdit && (
          <Flex gap={rem(5)} align={'center'} mb={rem(20)}>
            <HFSwitch
              disabled={!canUpdateObject}
              name='isProtected'
              label={t('fieldProtected')}
              onChange={(e) => {
                setValue('isProtected', e.target.checked);
              }}
            />
            <DecaTooltip label={t('fieldProtectedDesc')} position='bottom'>
              <Flex align={'center'}>
                <ThemeIcon variant='white' size={20} c='decaGrey.5' style={{ cursor: 'pointer' }}>
                  <IconInfoCircle />
                </ThemeIcon>
              </Flex>
            </DecaTooltip>
          </Flex>
        )}

        <Box>
          <Text fw='500' mb={rem(5)}>
            {t('fieldName')}
          </Text>
          <TextInput
            disabled={!allowEdit}
            control={control}
            name='name'
            mb={rem(5)}
            onKeyDown={(e) => {
              e.stopPropagation();
            }}
          />
        </Box>
        <Box my={rem(10)}>
          <Text fw='500' mb={rem(5)}>
            {t('fieldType')}
          </Text>
          {typeDescription && (
            <Text c='decaGrey.4' mb={rem(5)} sx={{ whiteSpace: 'pre-line' }}>
              {typeDescription}
            </Text>
          )}
          <CustomSelect
            options={options}
            onChange={(val) => {
              setValue('type', val);
            }}
            optionsClassName={classes.options}
            disabled={!!initialData}
            defaultValue={initialData?.type ?? ''}
          />
        </Box>
        {allowEdit && (
          <>
            {type === FieldTypes.SINGLE_LINE_TEXT && (
              <TextSettings options={initialData?.options} />
            )}
            {type === FieldTypes.EMAIL && <EmailSettings options={initialData?.options} />}
            {type === FieldTypes.IMAGE && <ImageSettings options={initialData?.options} />}
            {(
              [FieldTypes.DATETIME, FieldTypes.CREATED_TIME, FieldTypes.MODIFIED_TIME] as string[]
            ).includes(type) && (
              <TimeSettings
                currentDate={type === FieldTypes.DATETIME}
                options={initialData?.options}
                fieldType={type}
              />
            )}
            {([FieldTypes.SINGLE_SELECT, FieldTypes.MULTI_SELECT] as string[]).includes(type) && (
              <SingleSelectSettings
                options={initialData?.options}
                isMulti={type === FieldTypes.MULTI_SELECT}
              />
            )}
            {type === FieldTypes.NUMBER && <NumberSettings options={initialData?.options} />}
            {type === FieldTypes.CURRENCY && <CurrencySettings options={initialData?.options} />}
            {type === FieldTypes.PERCENT && <PercentSettings options={initialData?.options} />}
            {type === FieldTypes.PHONE_NUMBER && <PhoneSettings options={initialData?.options} />}
            {type === FieldTypes.RELATIONSHIP && <LinkToRecordSettings />}
            {type === FieldTypes.LINE && <LineSettings options={initialData?.options} />}
            <Flex justify={'flex-end'} gap={rem(10)} mt={rem(20)} mb={rem(10)}>
              <DecaButton size='sm' variant='neutral' onClick={onCancel}>
                {t('cancel')}
              </DecaButton>
              <DecaButton size='sm' onClick={handleSubmit(onSumit)} disabled={!type}>
                {initialData ? t('save') : t('create')}
              </DecaButton>
            </Flex>
          </>
        )}
      </form>
    </FormProvider>
  );
};
