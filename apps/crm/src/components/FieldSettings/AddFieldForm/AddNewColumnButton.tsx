import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { ActionIcon, Box, Popover, rem } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconPlus } from '@tabler/icons-react';
import { memo, useEffect, useRef } from 'react';
import { AddFieldForm } from '.';

const AddNewColumnButton = () => {
  const [opened, { close, toggle }] = useDisclosure();
  const { handleAddColumn } = useWorkspaceContext();
  const popoverRef = useRef<HTMLDivElement>(null);
  const targetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!opened) return;

    const handleClick = (event: MouseEvent) => {
      const targetElement = event.target as HTMLElement;
      const isInsidePopover = popoverRef.current?.contains(targetElement);
      const isInsideTarget = targetRef.current?.contains(targetElement);
      const isSpecialElement =
        targetElement.closest('.mantine-Menu-dropdown') ||
        targetElement.closest('.mantine-Popover-dropdown');

      if (!isInsidePopover && !isInsideTarget && !isSpecialElement) {
        close();
      }
    };

    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [opened, close]);

  const handleClick = () => {
    setTimeout(() => toggle(), 250);
  };

  return (
    <Popover
      width={rem(320)}
      opened={opened}
      onClose={close}
      position='bottom-start'
      withinPortal
      data-testid='add-new-column-popover'
    >
      <Popover.Target>
        <Box ref={targetRef} data-testid='add-new-column-button'>
          <ActionIcon
            size={rem(20)}
            variant='transparent'
            c='decaGrey.4'
            onClick={handleClick}
            data-testid='add-new-column-button-icon'
          >
            <IconPlus />
          </ActionIcon>
        </Box>
      </Popover.Target>
      <Popover.Dropdown
        ref={popoverRef}
        data-testid='add-new-column-popover'
        style={{
          boxShadow: `0 ${rem(3)} ${rem(12)} 0 #0D11360F`,
        }}
      >
        <AddFieldForm onSave={handleAddColumn} onCancel={close} />
      </Popover.Dropdown>
    </Popover>
  );
};

export default memo(AddNewColumnButton);
