import { CurrencyFormatTypes, OptionsOrder } from '@/constants/workspace';
import { Text } from '@mantine/core';
import { Flex } from '@mantine/core';
import { FieldTypes } from '@resola-ai/ui/components';
import {
  IconArrowRightCircle,
  IconAt,
  IconBrandHipchat,
  IconCalendarDue,
  IconCalendarTime,
  IconCircleChevronDown,
  IconCoin,
  IconFloatLeft,
  IconHash,
  IconLetterCase,
  IconLink,
  IconListCheck,
  IconNumbers,
  IconPercentage,
  IconPhone,
  IconPhoto,
  IconSquareCheck,
  IconUsers,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React from 'react';

export const useOptions = () => {
  const { t } = useTranslate('workspace');

  const OPTIONS = [
    {
      value: FieldTypes.SINGLE_LINE_TEXT,
      label: t('fieldTypes.singleText'),
      icon: <IconLetterCase />,
    },
    {
      value: FieldTypes.LONG_TEXT,
      label: t('fieldTypes.longText'),
      icon: <IconFloatLeft />,
    },
    {
      value: FieldTypes.PHONE_NUMBER,
      label: t('fieldTypes.phoneNumber'),
      icon: <IconPhone />,
    },
    {
      value: FieldTypes.EMAIL,
      label: t('fieldTypes.email'),
      icon: <IconAt />,
    },
    {
      value: FieldTypes.URL,
      label: t('fieldTypes.url'),
      icon: <IconLink />,
    },
    {
      value: FieldTypes.CREATED_BY,
      label: t('fieldTypes.createdBy'),
      icon: <IconUsers />,
    },
    {
      value: FieldTypes.MODIFIED_BY,
      label: t('fieldTypes.modifiedBy'),
      icon: <IconUsers />,
    },
    {
      value: FieldTypes.CREATED_TIME,
      label: t('fieldTypes.createdTime'),
      icon: <IconCalendarDue />,
    },
    {
      value: FieldTypes.MODIFIED_TIME,
      label: t('fieldTypes.modifiedTime'),
      icon: <IconCalendarDue />,
    },
    {
      value: FieldTypes.DATETIME,
      label: t('fieldTypes.dateTime'),
      icon: <IconCalendarTime />,
    },
    {
      value: FieldTypes.IMAGE,
      label: t('fieldTypes.image'),
      icon: <IconPhoto />,
    },
    {
      value: FieldTypes.SINGLE_SELECT,
      label: t('fieldTypes.singleSelect'),
      icon: <IconCircleChevronDown />,
    },
    {
      value: FieldTypes.MULTI_SELECT,
      label: t('fieldTypes.mulSelect'),
      icon: <IconListCheck />,
    },
    {
      value: FieldTypes.CHECKBOX,
      label: t('fieldTypes.checkbox'),
      icon: <IconSquareCheck />,
    },
    {
      value: FieldTypes.NUMBER,
      label: t('fieldTypes.number'),
      icon: <IconHash />,
    },
    {
      value: FieldTypes.AUTONUMBER,
      label: t('fieldTypes.autoNumber'),
      icon: <IconNumbers />,
    },
    {
      value: FieldTypes.CURRENCY,
      label: t('fieldTypes.currency'),
      icon: <IconCoin />,
    },
    {
      value: FieldTypes.RELATIONSHIP,
      label: t('fieldTypes.linkToRecord'),
      icon: <IconArrowRightCircle />,
    },
    {
      value: FieldTypes.PERCENT,
      label: t('fieldTypes.percent'),
      icon: <IconPercentage />,
    },
    {
      value: FieldTypes.LINE,
      label: t('fieldTypes.line'),
      icon: <IconBrandHipchat />,
    },
  ];

  const CHOICE_ORDER_OPTIONS = [
    { value: OptionsOrder.MANUAL, label: t('manual') },
    { value: OptionsOrder.ASC, label: 'A -> Z' },
    { value: OptionsOrder.DESC, label: 'Z -> A' },
  ];

  const separatorOptions = React.useMemo(() => {
    const rawOptions = [
      { value: 'local', label: t('local'), subLabel: '1,000,000.00' },
      { value: CurrencyFormatTypes.commaOnly, label: t('commaOnly'), subLabel: '1,000,000' },
      { value: CurrencyFormatTypes.commaPeriod, label: t('commaPeriod'), subLabel: '1,000,000.00' },
      { value: CurrencyFormatTypes.periodComma, label: t('periodComma'), subLabel: '1.000.000,00' },
      { value: CurrencyFormatTypes.spaceComma, label: t('spaceComma'), subLabel: '1 000 000,00' },
      { value: CurrencyFormatTypes.spacePeriod, label: t('spacePeriod'), subLabel: '1 000 000.00' },
    ];
    return [
      {
        options: rawOptions.map((option) => ({
          value: option.value,
          label: (
            <Flex justify='space-between' w={'100%'}>
              <Text c={'decaGrey.9'}>{option.label}</Text>
              <Text c={'decaGrey.4'}>{option.subLabel}</Text>
            </Flex>
          ),
          selectedDisplay: `${option.label} (${option.subLabel})`,
        })),
      },
    ];
  }, []);

  return React.useMemo(() => {
    return { options: OPTIONS, choiceOrderOptions: CHOICE_ORDER_OPTIONS, separatorOptions };
  }, []);
};
