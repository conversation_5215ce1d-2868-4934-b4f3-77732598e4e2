import { OptionsName } from '@/constants/workspace';
import { useChannels } from '@/hooks';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import { useFormContext } from 'react-hook-form';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { LineSettings } from './index';

// Mock the hooks
vi.mock('@/hooks', () => ({
  useChannels: vi.fn(),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
}));

// Mock the CustomSelect component
vi.mock('@resola-ai/ui', () => ({
  CustomSelect: ({
    options,
    defaultValue,
    onChange,
  }: {
    options: Array<{ label: string; value: string }>;
    defaultValue?: string;
    onChange?: (value: string) => void;
  }) => (
    <div data-testid='custom-select'>
      <select
        data-testid='select-element'
        defaultValue={defaultValue}
        onChange={(e) => onChange?.(e.target.value)}
      >
        {options?.map((opt) => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </select>
    </div>
  ),
}));

describe('LineSettings', () => {
  const mockSetValue = vi.fn();
  const mockTranslate = vi.fn((key) => key);
  const mockChannels = [
    { id: 'channel1', name: 'Channel 1' },
    { id: 'channel2', name: 'Channel 2' },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock return values
    (useChannels as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      channels: mockChannels,
      isLoading: false,
    });

    (useTranslate as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      t: mockTranslate,
    });

    (useFormContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      setValue: mockSetValue,
    });
  });

  it('renders a loader when channels are loading', () => {
    // @ts-expect-error Mock implementation
    useChannels.mockReturnValue({
      isLoading: true,
      data: [],
    });

    renderWithMantine(<LineSettings options={{}} />);
    const loaderElement = document.querySelector('.mantine-Loader-root');
    expect(loaderElement).toBeInTheDocument();
  });

  it('renders the select with channel options', () => {
    renderWithMantine(<LineSettings options={{}} />);

    expect(screen.getByText('lineChannelName')).toBeInTheDocument();
    expect(screen.getByTestId('custom-select')).toBeInTheDocument();

    const options = screen.getAllByRole('option');
    expect(options).toHaveLength(2);
    expect(options[0]).toHaveTextContent('Channel 1');
    expect(options[1]).toHaveTextContent('Channel 2');
  });

  it('sets the default value when provided in options', () => {
    renderWithMantine(<LineSettings options={{ channelId: 'channel2' }} />);

    const selectElement = screen.getByTestId('select-element');
    expect(selectElement).toHaveValue('channel2');
  });

  it('calls setValue with correct path when a channel is selected', () => {
    renderWithMantine(<LineSettings options={{}} />);

    const selectElement = screen.getByTestId('select-element') as HTMLSelectElement;
    fireEvent.change(selectElement, { target: { value: 'channel2' } });

    expect(mockSetValue).toHaveBeenCalledWith(`${OptionsName}.channelId`, 'channel2');
  });

  it('handles empty channels array gracefully', () => {
    (useChannels as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      channels: null,
      isLoading: false,
    });

    renderWithMantine(<LineSettings options={{}} />);
    expect(screen.getByTestId('custom-select')).toBeInTheDocument();
    expect(screen.queryAllByRole('option')).toHaveLength(0);
  });
});
