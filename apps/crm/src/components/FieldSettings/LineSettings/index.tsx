import { OptionsName } from '@/constants/workspace';
import { useChannels } from '@/hooks';
import type { FieldOptions } from '@/models';
import { Loader, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { CustomSelect } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useFormContext } from 'react-hook-form';
const useStyles = createStyles((theme) => ({
  selectOptions: {
    gap: rem(4),
    ['p']: {
      fontWeight: 500,
    },
    ['span']: {
      color: theme.colors.decaGrey[4],
    },
    ['& .mantine-Flex-root']: {
      height: 'auto !important',
    },
  },
}));
export const LineSettings = ({ options }: { options: FieldOptions }) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();
  const { setValue } = useFormContext();
  const { channels, isLoading } = useChannels();
  if (isLoading) return <Loader />;
  return (
    <>
      <Text fw={500} mb={6}>
        {t('lineChannelName')}
      </Text>
      <CustomSelect
        options={
          channels?.map((channel) => ({
            label: channel.name,
            value: channel.id,
            description: channel.channelId,
          })) || []
        }
        placeholder={t('lineChannelDescription')}
        showDescription={true}
        defaultValue={options?.channelId}
        optionsClassName={classes.selectOptions}
        onChange={(value) => setValue(`${OptionsName}.channelId`, value)}
      />
    </>
  );
};
