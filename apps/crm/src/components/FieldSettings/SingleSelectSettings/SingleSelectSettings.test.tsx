import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { describe, expect, it, vi } from 'vitest';
import { SingleSelectSettings } from './index';

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
  useFieldArray: vi.fn(),
  useController: () => ({
    field: {
      value: false,
      onChange: vi.fn(),
      onBlur: vi.fn(),
      ref: { current: null },
    },
    fieldState: { error: null },
  }),
}));

vi.mock('@dnd-kit/core', () => ({
  DndContext: ({ children }) => <div data-testid='dnd-context'>{children}</div>,
  useSensor: () => ({}),
  useSensors: () => ({}),
  PointerSensor: class {},
}));

vi.mock('@dnd-kit/sortable', () => ({
  SortableContext: ({ children }) => <div data-testid='sortable-context'>{children}</div>,
}));

vi.mock('./OptionItem', () => ({
  default: ({ item }) => <div data-testid='option-item'>{item.label}</div>,
}));

vi.mock('./OptionSelect', () => ({
  OptionSelect: ({ placeholder }) => (
    <div data-testid='option-select'>
      <div data-testid='option-select-placeholder'>{placeholder}</div>
    </div>
  ),
}));

vi.mock('../AddFieldForm/useOptions', () => ({
  useOptions: () => ({
    choiceOrderOptions: [
      { value: 'manual', label: 'Manual' },
      { value: 'asc', label: 'Ascending' },
      { value: 'desc', label: 'Descending' },
    ],
  }),
}));

vi.mock('@resola-ai/ui', () => ({
  CustomSelect: ({ notSelectedLabel }) => (
    <div data-testid='custom-select'>
      <div data-testid='not-selected-label'>{notSelectedLabel}</div>
    </div>
  ),
  DecaStatus: ({ text }) => <div data-testid='deca-status'>{text}</div>,
  HFSwitch: ({ label }) => (
    <div data-testid='hf-switch'>
      <div data-testid='switch-label'>{label}</div>
    </div>
  ),
}));

vi.mock('@tabler/icons-react', () => {
  const mockIcon = (name) => {
    const Icon = () => <div data-testid={`icon-${name}`}>{name}</div>;
    Icon.displayName = `Icon${name}`;
    return Icon;
  };

  return {
    IconPlus: mockIcon('Plus'),
    IconDotsVertical: mockIcon('DotsVertical'),
    IconEdit: mockIcon('Edit'),
    IconTrash: mockIcon('Trash'),
    IconGripVertical: mockIcon('GripVertical'),
  };
});

// Mock the constants file that's importing more icons
vi.mock('@/constants/workspace', () => {
  const OptionsName = 'options';
  const OptionsOrder = {
    MANUAL: 'manual',
    ASC: 'asc',
    DESC: 'desc',
  };
  const Currency = {
    usd: 'usd',
    yen: 'yen',
  };
  const CurrencySymbol = {
    usd: '$',
    yen: '¥',
  };

  // Return the mock constants
  return {
    OptionsName,
    OptionsOrder,
    Currency,
    CurrencySymbol,
    IconList: [],
  };
});

// Mock the DecaTable component
vi.mock('@resola-ai/ui/components/DecaTable', () => ({}));

// Mock the whole field definitions dependency that's importing the icons
vi.mock('@resola-ai/ui/components', () => ({
  FieldTypes: {
    SINGLE_LINE_TEXT: 'text',
    LONG_TEXT: 'longtext',
    PHONE_NUMBER: 'phone',
    EMAIL: 'email',
    URL: 'url',
    USERS: 'users',
    TIME: 'time',
    DATE: 'date',
    NUMBER: 'number',
    CURRENCY: 'currency',
    PERCENT: 'percent',
    SINGLE_SELECT: 'select',
    MULTI_SELECT: 'multi-select',
    IMAGE: 'image',
    FILE: 'file',
    LINK_TO_RECORD: 'link-to-record',
    LINE: 'line',
  },
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key) => key,
  }),
  Tolgee: () => ({
    use: () => ({
      use: () => ({
        init: () => ({}),
      }),
    }),
  }),
}));

// Mock the tolgee index module
vi.mock('@/tolgee', () => ({
  tolgee: {
    changeLanguage: vi.fn(),
  },
}));

// Define constants locally since we're mocking the constants file
const OptionsOrder = {
  MANUAL: 'manual',
  ASC: 'asc',
  DESC: 'desc',
};

describe('SingleSelectSettings', () => {
  const mockWatch = vi.fn();
  const mockSetValue = vi.fn();
  const mockControl = {};
  const mockAppend = vi.fn();
  const mockRemove = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue({
      watch: mockWatch,
      setValue: mockSetValue,
      control: mockControl,
    });

    (useFieldArray as any).mockReturnValue({
      fields: [
        { id: '1', label: 'Option 1', color: 'grey', time: 1000 },
        { id: '2', label: 'Option 2', color: 'blue', time: 2000 },
      ],
      append: mockAppend,
      remove: mockRemove,
    });
  });

  it('renders correctly with default options', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.choices') {
        return [
          { id: '1', label: 'Option 1', color: 'grey', time: 1000 },
          { id: '2', label: 'Option 2', color: 'blue', time: 2000 },
        ];
      }
      return undefined;
    });

    renderWithMantine(
      <SingleSelectSettings
        options={{
          order: OptionsOrder.MANUAL,
        }}
      />
    );

    expect(screen.getByTestId('hf-switch')).toBeInTheDocument();
    expect(screen.getByTestId('switch-label')).toHaveTextContent('colorCode');
    expect(screen.getByText('addOption')).toBeInTheDocument();
    expect(screen.getByTestId('dnd-context')).toBeInTheDocument();
    expect(screen.getByTestId('sortable-context')).toBeInTheDocument();
    expect(screen.getAllByTestId('option-item').length).toBe(2);
    expect(screen.getByText('defaultOption')).toBeInTheDocument();
    expect(screen.getByTestId('custom-select')).toBeInTheDocument();
  });

  it('renders multi-select options when isMulti is true', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.choices') {
        return [
          { id: '1', label: 'Option 1', color: 'grey', time: 1000 },
          { id: '2', label: 'Option 2', color: 'blue', time: 2000 },
        ];
      }
      return undefined;
    });

    renderWithMantine(
      <SingleSelectSettings
        options={{
          order: OptionsOrder.MANUAL,
        }}
        isMulti={true}
      />
    );

    expect(screen.getByTestId('option-select')).toBeInTheDocument();
    expect(screen.getByTestId('option-select-placeholder')).toHaveTextContent('notSelected');
  });

  it('handles adding a new option', () => {
    mockWatch.mockImplementation((name) => {
      if (name === 'options.choices') {
        return [
          { id: '1', label: 'Option 1', color: 'grey', time: 1000 },
          { id: '2', label: 'Option 2', color: 'blue', time: 2000 },
        ];
      }
      return undefined;
    });

    vi.mock('uuid', () => ({
      v4: () => '999',
    }));

    renderWithMantine(
      <SingleSelectSettings
        options={{
          order: OptionsOrder.MANUAL,
        }}
      />
    );

    screen.getByText('addOption').click();

    expect(mockAppend).toHaveBeenCalledWith({
      id: expect.any(String),
      label: 'name',
      color: 'grey',
      time: expect.any(Number),
    });
  });
});
