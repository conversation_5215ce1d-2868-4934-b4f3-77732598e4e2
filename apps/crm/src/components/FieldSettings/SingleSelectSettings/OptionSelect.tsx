import {
  CheckIcon,
  Combobox,
  Group,
  Input,
  Pill,
  PillsInput,
  ThemeIcon,
  useCombobox,
} from '@mantine/core';
import { DecaStatus } from '@resola-ai/ui';
import { useMemo, useState } from 'react';

// Refer: https://mantine.dev/combobox/?e=MultiSelectValueRenderer

export const OptionSelect = ({
  data,
  onChange,
  placeholder,
}: {
  data: any;
  defaultValue: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
}) => {
  const normalizeData = useMemo(() => {
    return data?.map((option) => ({ ...option, value: option.id }));
  }, [data]);

  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
    onDropdownOpen: () => combobox.updateSelectedOptionIndex('active'),
  });

  const [value, setValue] = useState<string[]>([]);

  const handleValueSelect = (val: string) => {
    const _value = value.includes(val) ? value.filter((v) => v !== val) : [...value, val];

    setValue(_value);
    onChange(_value);
  };

  const handleValueRemove = (val: string) => {
    const _value = value.filter((v) => v !== val);
    setValue(_value);
    onChange(_value);
  };

  const values = value.map((i) => {
    const item = normalizeData.find((item) => item.value === i);
    if (!item) return null;
    return (
      <DecaStatus
        key={item.value}
        variant={item.color}
        text={item.label}
        showRemove
        onRemove={() => handleValueRemove(item.value)}
      />
    );
  });

  const options = normalizeData.map((item) => {
    return (
      <Combobox.Option value={item.value} key={item.value} active={value.includes(item.value)}>
        <Group gap='sm'>
          <Group gap={7}>
            <DecaStatus size='small' variant={item.color} text={item.label} />
          </Group>
          <ThemeIcon color='decaGrey.5' variant='subtle'>
            {value.includes(item.value) ? <CheckIcon size={12} /> : null}
          </ThemeIcon>
        </Group>
      </Combobox.Option>
    );
  });

  return (
    <Combobox store={combobox} onOptionSubmit={handleValueSelect} withinPortal={false}>
      <Combobox.DropdownTarget>
        <PillsInput pointer onClick={() => combobox.toggleDropdown()}>
          <Pill.Group>
            {values.length > 0 ? values : <Input.Placeholder>{placeholder}</Input.Placeholder>}
          </Pill.Group>
        </PillsInput>
      </Combobox.DropdownTarget>

      <Combobox.Dropdown>
        <Combobox.Options>{options}</Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};
