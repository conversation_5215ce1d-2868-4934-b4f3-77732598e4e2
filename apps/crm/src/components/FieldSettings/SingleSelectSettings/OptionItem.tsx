import { ColorCodes, Colors, OptionsName } from '@/constants/workspace';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ActionIcon, Box, Flex, Menu, Text, rem, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaStatus } from '@resola-ai/ui';
import { DecaTooltip } from '@resola-ai/ui/components';
import { IconGripVertical, IconX } from '@tabler/icons-react';
import React, { type CSSProperties, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { TextInput } from 'react-hook-form-mantine';

const useStyles = createStyles((theme) => ({
  button: {
    padding: rem(10),
    flex: 1,
    alignItems: 'center',
    height: rem(36),
    borderRadius: rem(4),
    backgroundColor: 'white',
    cursor: 'pointer',
  },
  sortItem: {
    cursor: 'pointer',
  },
  colorPick: {
    width: rem(16),
    height: rem(16),
    borderRadius: '100%',
    backgroundColor: theme.colors.decaLight[1],
  },
  colorPickDnD: {
    overflowY: 'auto',
    overflowX: 'hidden',
    '.mantine-Menu-itemLabel': {
      width: '100%',
    },
  },
  label: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    padding: `${rem(4)} ${rem(8)}`,
    borderRadius: theme.spacing.md,
  },
}));

type Props = {
  item: any;
  name?: string;
  remove?: (id: string) => void;
};

const OptionItem = ({ item, name, remove }: Props) => {
  const { attributes, isDragging, listeners, setNodeRef, transform, transition } = useSortable({
    id: item.id,
  });
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const [isEdit, setIsEdit] = useState(false);
  const { control, setValue, watch } = useFormContext();

  const style: CSSProperties = {
    opacity: isDragging ? 0.4 : undefined,
    transform: CSS.Translate.toString(transform),
    transition,
  };

  const enableColor = watch(`${OptionsName}.enableColor`);

  return (
    <div {...attributes} {...listeners}>
      <Flex
        justify={'space-between'}
        align={'center'}
        gap={rem(10)}
        py={rem(7)}
        ref={setNodeRef}
        style={style}
        className={classes.sortItem}
      >
        <Flex align={'center'} gap={rem(6)} w={'85%'}>
          <IconGripVertical size={16} color={theme.colors.decaGrey[2]} />
          {enableColor ? (
            <Menu shadow='md' width={rem(200)} position='bottom-start' zIndex={1000}>
              <Menu.Target>
                <Box className={classes.colorPick} sx={ColorCodes[item?.color]} />
              </Menu.Target>
              <Menu.Dropdown mah={rem(250)} className={classes.colorPickDnD}>
                {Colors.map((color) => (
                  <Menu.Item
                    key={color}
                    w={'100%'}
                    onClick={() => setValue(`${name}.color`, color)}
                  >
                    <DecaTooltip label={item.label} position='left' offset={10}>
                      <DecaStatus text={item.label || 'Name'} size='small' variant={color} />
                    </DecaTooltip>
                  </Menu.Item>
                ))}
              </Menu.Dropdown>
            </Menu>
          ) : (
            <Box className={classes.colorPick} sx={ColorCodes['grey']} />
          )}
          {isEdit ? (
            <TextInput
              autoFocus
              control={control}
              name={`${name}.label`}
              w={rem(176)}
              onBlur={() => {
                setIsEdit(false);
              }}
              onKeyDown={(e) => {
                e.stopPropagation();
                if (e.key === 'Enter') {
                  setIsEdit(false);
                }
              }}
            />
          ) : (
            <Text
              className={classes.label}
              sx={ColorCodes[item?.color]}
              onDoubleClick={() => setIsEdit(true)}
              maw={'80%'}
            >
              {item.label || 'Name'}
            </Text>
          )}
        </Flex>
        <ActionIcon onClick={() => remove?.(item.id)} size={20} variant='subtle' c='decaGrey.4'>
          <IconX />
        </ActionIcon>
      </Flex>
    </div>
  );
};

export default React.memo(OptionItem);
