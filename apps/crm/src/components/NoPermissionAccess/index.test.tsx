import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import NoPermissionAccess from './index';

// Mock the translation hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      if (key === 'errors.crmViewReadDenied') {
        return 'You do not have permission to view this workspace';
      }
      return key;
    },
  }),
}));

// Mock the CustomImage component
vi.mock('@resola-ai/ui/components/CustomImage', () => ({
  CustomImage: ({ url }: { url: string }) => <img src={url} alt='No view access' />,
}));

describe('NoViewAccess', () => {
  it('renders the component with correct structure', () => {
    renderWithMantine(<NoPermissionAccess />);

    // Check if the main container is rendered
    const container = screen.getByTestId('no-view-access');
    expect(container).toBeInTheDocument();

    // Check if the image is rendered
    const image = screen.getByAltText('No view access');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'images/no_view_access.png');

    // Check if the text is rendered with correct translation
    const text = screen.getByTestId('no-view-access-text');
    expect(text).toBeInTheDocument();
    expect(text).toHaveTextContent('You do not have permission to view this workspace');
  });

  it('applies correct styling to the container', () => {
    renderWithMantine(<NoPermissionAccess />);

    const container = screen.getByTestId('no-view-access');
    expect(container).toHaveStyle({
      width: '100%',
      height: '60vh',
    });
  });
});
