import { HEIGHT_OF_HEADER } from '@/constants';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import BaseLayout from './index';

// Mock the external components and modules
vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn(() => () => ({
    classes: {
      appWrapper: 'mocked-app-wrapper',
      drawerCustomClass: 'mocked-drawer-custom-class',
      burgerControlInDetail: 'mocked-burger-control-in-detail',
    },
  })),
}));

vi.mock('@resola-ai/ui/components', () => ({
  HeaderContainer: vi.fn(
    ({ children, logoUrl, navigationMobile, drawerCustomClass, burgerCustomClass }) => (
      <div data-testid='header-container'>
        <div data-testid='logo-url'>{logoUrl}</div>
        <div data-testid='navigation-mobile'>{navigationMobile}</div>
        <div data-testid='drawer-class'>{drawerCustomClass}</div>
        <div data-testid='burger-class'>{burgerCustomClass}</div>
        {children}
      </div>
    )
  ),
  LayoutStructure: vi.fn(({ children }) => <div data-testid='layout-structure'>{children}</div>),
}));

vi.mock('@/constants', () => ({
  HEIGHT_OF_HEADER: 60,
}));

describe('BaseLayout', () => {
  describe('component rendering', () => {
    it('should render with basic props', () => {
      renderWithMantine(
        <BaseLayout>
          <div data-testid='test-content'>Test Content</div>
        </BaseLayout>
      );

      expect(screen.getByTestId('layout-structure')).toBeInTheDocument();
      expect(screen.getByTestId('header-container')).toBeInTheDocument();
      expect(screen.getByTestId('test-content')).toBeInTheDocument();
    });

    it('should render with navigationMobile prop', () => {
      const navigationMobile = <nav data-testid='mobile-nav'>Mobile Navigation</nav>;

      renderWithMantine(
        <BaseLayout navigationMobile={navigationMobile}>
          <div data-testid='test-content'>Test Content</div>
        </BaseLayout>
      );

      expect(screen.getByTestId('mobile-nav')).toBeInTheDocument();
      expect(screen.getByTestId('navigation-mobile')).toContainElement(
        screen.getByTestId('mobile-nav')
      );
    });

    it('should render without navigationMobile prop', () => {
      renderWithMantine(
        <BaseLayout>
          <div data-testid='test-content'>Test Content</div>
        </BaseLayout>
      );

      expect(screen.getByTestId('navigation-mobile')).toBeEmptyDOMElement();
    });

    it('should render multiple children', () => {
      renderWithMantine(
        <BaseLayout>
          <div data-testid='child-1'>Child 1</div>
          <div data-testid='child-2'>Child 2</div>
          <span data-testid='child-3'>Child 3</span>
        </BaseLayout>
      );

      expect(screen.getByTestId('child-1')).toBeInTheDocument();
      expect(screen.getByTestId('child-2')).toBeInTheDocument();
      expect(screen.getByTestId('child-3')).toBeInTheDocument();
    });
  });

  describe('logo configuration', () => {
    it('should use default logo URL', () => {
      renderWithMantine(
        <BaseLayout>
          <div>Content</div>
        </BaseLayout>
      );

      expect(screen.getByTestId('logo-url')).toHaveTextContent('images/DECA-crm.svg');
    });
  });

  describe('styling and CSS classes', () => {
    it('should render with styling classes', () => {
      renderWithMantine(
        <BaseLayout>
          <div data-testid='test-content'>Test Content</div>
        </BaseLayout>
      );

      // Verify that the component renders with class information
      expect(screen.getByTestId('drawer-class')).toBeInTheDocument();
      expect(screen.getByTestId('burger-class')).toBeInTheDocument();
    });
  });

  describe('component structure', () => {
    it('should have proper component hierarchy', () => {
      renderWithMantine(
        <BaseLayout>
          <div data-testid='test-content'>Test Content</div>
        </BaseLayout>
      );

      const layoutStructure = screen.getByTestId('layout-structure');
      const headerContainer = screen.getByTestId('header-container');

      expect(layoutStructure).toContainElement(headerContainer);
    });

    it('should pass header height constant correctly', () => {
      // This test ensures HEIGHT_OF_HEADER constant is being used
      expect(HEIGHT_OF_HEADER).toBe(60);
    });
  });

  describe('props handling', () => {
    it('should handle empty children', () => {
      renderWithMantine(<BaseLayout>{null}</BaseLayout>);

      expect(screen.getByTestId('layout-structure')).toBeInTheDocument();
      expect(screen.getByTestId('header-container')).toBeInTheDocument();
    });

    it('should handle string children', () => {
      renderWithMantine(<BaseLayout>Simple text content</BaseLayout>);

      expect(screen.getByText('Simple text content')).toBeInTheDocument();
    });

    it('should handle complex navigationMobile structure', () => {
      const complexNavigation = (
        <div data-testid='complex-nav'>
          <button>Menu</button>
          <ul>
            <li>Item 1</li>
            <li>Item 2</li>
          </ul>
        </div>
      );

      renderWithMantine(
        <BaseLayout navigationMobile={complexNavigation}>
          <div>Content</div>
        </BaseLayout>
      );

      expect(screen.getByTestId('complex-nav')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Menu' })).toBeInTheDocument();
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('should handle undefined navigationMobile gracefully', () => {
      renderWithMantine(
        <BaseLayout navigationMobile={undefined}>
          <div data-testid='test-content'>Test Content</div>
        </BaseLayout>
      );

      expect(screen.getByTestId('test-content')).toBeInTheDocument();
      expect(screen.getByTestId('navigation-mobile')).toBeInTheDocument();
    });

    it('should handle empty object as navigationMobile', () => {
      renderWithMantine(
        <BaseLayout navigationMobile={<div data-testid='empty-nav' />}>
          <div data-testid='test-content'>Test Content</div>
        </BaseLayout>
      );

      expect(screen.getByTestId('empty-nav')).toBeInTheDocument();
    });
  });
});
