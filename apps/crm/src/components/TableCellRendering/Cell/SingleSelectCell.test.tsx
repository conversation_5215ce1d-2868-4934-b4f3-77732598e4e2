import { fireEvent, waitFor } from '@testing-library/react';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import type { Choice } from '@/models';

// Mock Mantine components FIRST before importing the component
vi.mock('@mantine/core', () => ({
  Box: vi.fn(({ children, sx, ...props }) => (
    <div data-testid="box-container" style={sx} {...props}>
      {children}
    </div>
  )),
}));

// Mock the workspace context
const mockOnSavingCell = vi.fn();
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => ({
    onSavingCell: mockOnSavingCell,
  }),
}));

// Mock DecaStatus component
vi.mock('@resola-ai/ui', () => ({
  DecaStatus: vi.fn(({ size, variant, text, showRemove, onRemove }) => (
    <div
      data-testid="deca-status"
      data-size={size}
      data-variant={variant}
      data-text={text}
      data-show-remove={showRemove}
    >
      <span>{text}</span>
      {showRemove && (
        <button
          data-testid="remove-button"
          onClick={() => onRemove?.()}
        >
          Remove
        </button>
      )}
    </div>
  )),
}));

import { SingleSelectCell } from './SingleSelectCell';

describe('SingleSelectCell', () => {
  const mockChoices: Choice[] = [
    { id: 'choice1', label: 'Choice 1', color: 'blue' },
    { id: 'choice2', label: 'Choice 2', color: 'red' },
    { id: 'choice3', label: 'Choice 3', color: 'green' },
  ];

  const mockCell = {
    row: { index: 0 },
    column: { id: 'test-column' },
    getValue: () => 'choice1',
  } as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic rendering', () => {
    it('should render DecaStatus when value matches a choice', () => {
      const { getByTestId, getByText } = renderWithMantine(
        <SingleSelectCell
          value="choice1"
          choices={mockChoices}
        />
      );

      const status = getByTestId('deca-status');
      expect(status).toBeInTheDocument();
      expect(status).toHaveAttribute('data-size', 'small');
      expect(status).toHaveAttribute('data-variant', 'blue');
      expect(status).toHaveAttribute('data-text', 'Choice 1');
      expect(getByText('Choice 1')).toBeInTheDocument();
    });

    it('should not render anything when value does not match any choice', () => {
      const { container } = renderWithMantine(
        <SingleSelectCell
          value="non-existent"
          choices={mockChoices}
        />
      );

      // Should not render any DecaStatus components for non-existent value
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });

    it('should not render anything when value is undefined', () => {
      const { container } = renderWithMantine(
        <SingleSelectCell
          value={undefined as any}
          choices={mockChoices}
        />
      );

      // Should not render any DecaStatus components for undefined value
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });

    it('should not render anything when value is null', () => {
      const { container } = renderWithMantine(
        <SingleSelectCell
          value={null as any}
          choices={mockChoices}
        />
      );

      // Should not render any DecaStatus components for null value
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });

    it('should not render anything when value is empty string', () => {
      const { container } = renderWithMantine(
        <SingleSelectCell
          value=""
          choices={mockChoices}
        />
      );

      // Should not render any DecaStatus components for empty string
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });
  });

  describe('Choices handling', () => {
    it('should handle empty choices array', () => {
      const { container } = renderWithMantine(
        <SingleSelectCell
          value="choice1"
          choices={[]}
        />
      );

      // Should not render any DecaStatus components when choices is empty
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });

    it('should handle undefined choices', () => {
      const { container } = renderWithMantine(
        <SingleSelectCell
          value="choice1"
          choices={undefined as any}
        />
      );

      // Should not render any DecaStatus components when choices is undefined
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });

    it('should find choice by id correctly', () => {
      const { getByTestId } = renderWithMantine(
        <SingleSelectCell
          value="choice2"
          choices={mockChoices}
        />
      );

      const status = getByTestId('deca-status');
      expect(status).toHaveAttribute('data-variant', 'red');
      expect(status).toHaveAttribute('data-text', 'Choice 2');
    });
  });

  describe('Remove functionality', () => {
    it('should show remove button when showRemove is true', () => {
      const { getByTestId } = renderWithMantine(
        <SingleSelectCell
          value="choice1"
          choices={mockChoices}
          showRemove={true}
          cell={mockCell}
        />
      );

      const status = getByTestId('deca-status');
      expect(status).toHaveAttribute('data-show-remove', 'true');
      expect(getByTestId('remove-button')).toBeInTheDocument();
    });

    it('should not show remove button when showRemove is false', () => {
      const { getByTestId, queryByTestId } = renderWithMantine(
        <SingleSelectCell
          value="choice1"
          choices={mockChoices}
          showRemove={false}
          cell={mockCell}
        />
      );

      const status = getByTestId('deca-status');
      expect(status).toHaveAttribute('data-show-remove', 'false');
      expect(queryByTestId('remove-button')).not.toBeInTheDocument();
    });

    it('should not show remove button by default when showRemove is not provided', () => {
      const { getByTestId, queryByTestId } = renderWithMantine(
        <SingleSelectCell
          value="choice1"
          choices={mockChoices}
          cell={mockCell}
        />
      );

      const status = getByTestId('deca-status');
      expect(status).not.toHaveAttribute('data-show-remove'); // undefined showRemove means no attribute
      expect(queryByTestId('remove-button')).not.toBeInTheDocument();
    });

    it('should call onSavingCell with empty string when remove button is clicked', async () => {
      const { getByTestId } = renderWithMantine(
        <SingleSelectCell
          value="choice1"
          choices={mockChoices}
          showRemove={true}
          cell={mockCell}
        />
      );

      const removeButton = getByTestId('remove-button');
      fireEvent.click(removeButton);

      await waitFor(() => {
        expect(mockOnSavingCell).toHaveBeenCalledWith('', mockCell);
      });
    });

    it('should not call onSavingCell when cell is not provided', async () => {
      const { getByTestId } = renderWithMantine(
        <SingleSelectCell
          value="choice1"
          choices={mockChoices}
          showRemove={true}
        />
      );

      const removeButton = getByTestId('remove-button');
      fireEvent.click(removeButton);

      // Wait a bit to ensure no call was made
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(mockOnSavingCell).not.toHaveBeenCalled();
    });
  });

  describe('Choice properties', () => {
    it('should handle choice with different color variants', () => {
      const colorChoices: Choice[] = [
        { id: '1', label: 'Red Choice', color: 'red' },
        { id: '2', label: 'Blue Choice', color: 'blue' },
        { id: '3', label: 'Green Choice', color: 'green' },
      ];

      // Test red choice
      const { getByTestId: getByTestId1, unmount: unmount1 } = renderWithMantine(
        <SingleSelectCell value="1" choices={colorChoices} />
      );
      expect(getByTestId1('deca-status')).toHaveAttribute('data-variant', 'red');
      unmount1();

      // Test blue choice
      const { getByTestId: getByTestId2, unmount: unmount2 } = renderWithMantine(
        <SingleSelectCell value="2" choices={colorChoices} />
      );
      expect(getByTestId2('deca-status')).toHaveAttribute('data-variant', 'blue');
      unmount2();

      // Test green choice
      const { getByTestId: getByTestId3, unmount: unmount3 } = renderWithMantine(
        <SingleSelectCell value="3" choices={colorChoices} />
      );
      expect(getByTestId3('deca-status')).toHaveAttribute('data-variant', 'green');
      unmount3();
    });

    it('should handle choice with special characters in label', () => {
      const specialChoices: Choice[] = [
        { id: '1', label: 'Choice with @#$%', color: 'blue' },
        { id: '2', label: 'Choice with émojis 🎉', color: 'red' },
      ];

      const { getByText } = renderWithMantine(
        <SingleSelectCell value="1" choices={specialChoices} />
      );

      expect(getByText('Choice with @#$%')).toBeInTheDocument();
    });

    it('should handle very long choice labels', () => {
      const longLabelChoices: Choice[] = [
        { 
          id: '1', 
          label: 'This is a very long choice label that might need truncation or special handling in the UI',
          color: 'blue'
        },
      ];

      const { getByTestId } = renderWithMantine(
        <SingleSelectCell value="1" choices={longLabelChoices} />
      );

      const status = getByTestId('deca-status');
      expect(status).toHaveAttribute('data-text', longLabelChoices[0].label);
    });
  });

  describe('Component memoization', () => {
    it('should be wrapped with React.memo', () => {
      // React.memo components may be objects or functions, just verify it's defined and renderable
      expect(SingleSelectCell).toBeDefined();
      expect(typeof SingleSelectCell === 'function' || typeof SingleSelectCell === 'object').toBe(true);
    });

    it('should handle choice selection correctly', () => {
      const { getByTestId } = renderWithMantine(
        <SingleSelectCell
          value="choice2"
          choices={mockChoices}
        />
      );

      const status = getByTestId('deca-status');
      expect(status).toHaveAttribute('data-variant', 'red');
      expect(status).toHaveAttribute('data-text', 'Choice 2');
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle choice without color property', () => {
      const choicesWithoutColor: Choice[] = [
        { id: '1', label: 'No Color Choice' } as Choice,
      ];

      const { getByTestId } = renderWithMantine(
        <SingleSelectCell value="1" choices={choicesWithoutColor} />
      );

      const status = getByTestId('deca-status');
      expect(status).toBeInTheDocument();
      expect(status).not.toHaveAttribute('data-variant'); // undefined color means no attribute
    });

    it('should handle choice without label property', () => {
      const choicesWithoutLabel: Choice[] = [
        { id: '1', color: 'blue' } as Choice,
      ];

      const { getByTestId } = renderWithMantine(
        <SingleSelectCell value="1" choices={choicesWithoutLabel} />
      );

      const status = getByTestId('deca-status');
      expect(status).not.toHaveAttribute('data-text'); // undefined label means no attribute
    });

    it('should handle numeric choice IDs', () => {
      const numericChoices: Choice[] = [
        { id: '123', label: 'Numeric ID Choice', color: 'blue' },
      ];

      const { getByText } = renderWithMantine(
        <SingleSelectCell value="123" choices={numericChoices} />
      );

      expect(getByText('Numeric ID Choice')).toBeInTheDocument();
    });
  });
});
