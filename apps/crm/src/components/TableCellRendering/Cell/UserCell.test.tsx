import { UserCell } from '@/components/TableCellRendering/Cell/UserCell';
import { renderWithMantine } from '@/tests/utils/testUtils';

describe('UserCell', () => {
  it('renders user name and image', () => {
    const user = { name: '<PERSON>', email: '<EMAIL>', picture: 'path/to/image.jpg' };
    const cell = { getValue: () => user };
    const { getByText } = renderWithMantine(<UserCell cell={cell as any} />);
    expect(getByText('John Doe')).toBeInTheDocument();
  });

  it('renders user name without image', () => {
    const user = { name: '<PERSON>', email: '<EMAIL>' };
    const cell = { getValue: () => user };
    const { getByText } = renderWithMantine(<UserCell cell={cell as any} />);
    expect(getByText('<PERSON>')).toBeInTheDocument();
  });

  it('renders empty when no name provided', () => {
    const cell = { getValue: () => ({}) };
    const { container } = renderWithMantine(<UserCell cell={cell as any} />);
    const divElement = container.querySelector('div');
    expect(divElement).toBeInTheDocument();
    expect(divElement?.textContent).toBe('');
  });
});
