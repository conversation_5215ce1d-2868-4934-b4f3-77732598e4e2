import type { FieldOptions } from '@/models';
import React from 'react';

const PhoneCellUI = ({ value: initialValue }: { config?: FieldOptions; value: string }) => {
  // TODO for later used
  // const value = useMemo(() => {
  //   if (!initialValue) return '';
  //   // const format = FormatPhone[config?.customFormat.format || 'jp'];
  //   // TODO update later when confirm the country format: currently only support JP
  //   const format = FormatPhone['jp'];
  //   const number = initialValue || '';
  //   let formattedNumber = '';
  //   let index = 0;

  //   for (const char of format) {
  //     if (char === 'x' || char === 'X') {
  //       if (index < number.length) {
  //         formattedNumber += number[index++];
  //       } else {
  //         formattedNumber += '0'; // Pad with zero if number is shorter
  //       }
  //     } else {
  //       formattedNumber += char;
  //     }
  //   }
  //   return formattedNumber;
  // }, [initialValue, config]);

  return <>{initialValue || ''}</>;
};

export const PhoneCell = React.memo(PhoneCellUI);
