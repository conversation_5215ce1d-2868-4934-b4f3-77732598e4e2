import { renderWithMantine } from '@/tests/utils/testUtils';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import type { FieldOptions, ObjectColumn, RelationshipConfig } from '@/models';
import { LinkToRecordCell } from './LinkToRecordCell';
import { useAppContext } from '@/contexts/AppContext';

// Mock the AppContext
const mockObjects = [
  {
    id: 'object1',
    name: 'Test Object',
    fields: [
      {
        id: 'field1',
        name: 'Name Field',
        type: 'text',
        header: 'Name Field',
      },
      {
        id: 'field2', 
        name: 'Email Field',
        type: 'email',
        header: 'Email Field',
      },
    ] as ObjectColumn[],
  },
  {
    id: 'object2',
    name: 'Another Object',
    fields: [
      {
        id: 'field3',
        name: 'Description Field', 
        type: 'longtext',
        header: 'Description Field',
      },
    ] as ObjectColumn[],
  },
];

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(),
}));

// Mock FieldValue component
vi.mock('../EditCell/LinkToRecordCell/FieldValue', () => ({
  default: vi.fn(({ field, value }) => (
    <div data-testid="field-value" data-field-id={field?.id} data-value={value}>
      FieldValue: {field?.name} = {value}
    </div>
  )),
}));

describe('LinkToRecordCell', () => {
  const defaultConfig: FieldOptions = {
    objectId: 'object1',
    fieldId: 'field1',
  };

  const defaultObjectValue: RelationshipConfig = {
    value: 'John Doe',
    recordId: 'rec123',
    fieldId: 'field1', // Add missing fieldId property
  };

  const createMockAppContext = (objects: any) => ({
    onToggleSidebar: vi.fn(),
    wsDefault: 'default-ws',
    reloadObject: '',
    setReloadObject: vi.fn(),
    accessToken: 'test-token',
    objects,
    setObjects: vi.fn(),
    mutateObjects: vi.fn(),
    importLoading: false,
    setImportLoading: vi.fn(),
    organizationName: 'test-org',
    getOrganizationName: vi.fn(),
    sidebarOpen: false,
    sidebarWidth: 300,
    setSidebarWidth: vi.fn(),
    formOpened: false,
    setFormOpened: vi.fn(),
  });

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset to default mock behavior
    vi.mocked(useAppContext).mockReturnValue(createMockAppContext(mockObjects));
  });

  describe('Basic rendering', () => {
    it('should render FieldValue when object and field are found', () => {
      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={defaultObjectValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).toBeInTheDocument();
      expect(fieldValue).toHaveAttribute('data-field-id', 'field1');
      expect(fieldValue).toHaveAttribute('data-value', 'John Doe');
    });

    it('should render empty when object is not found', () => {
      const config: FieldOptions = {
        objectId: 'non-existent-object',
        fieldId: 'field1',
      };

      const { container } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      // Should not render any FieldValue components when object is not found
      expect(container.querySelector('[data-testid="field-value"]')).toBeNull();
    });

    it('should render empty when object has no fields', () => {
      const objectWithoutFields = {
        id: 'object3',
        name: 'Empty Object',
        fields: [],
      };

      // Mock the context to include the object without fields
      vi.mocked(useAppContext).mockReturnValue(
        createMockAppContext([...mockObjects, objectWithoutFields])
      );

      const config: FieldOptions = {
        objectId: 'object3',
        fieldId: 'field1',
      };

      const { container } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      // Should not render any FieldValue components when object has no fields
      expect(container.querySelector('[data-testid="field-value"]')).toBeNull();
    });

    it('should render empty when objects array is empty', () => {
      vi.mocked(useAppContext).mockReturnValue(createMockAppContext([]));

      const { container } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={defaultObjectValue}
        />
      );

      // Should not render any FieldValue components when objects array is empty
      expect(container.querySelector('[data-testid="field-value"]')).toBeNull();
    });

    it('should render empty when objects is undefined', () => {
      vi.mocked(useAppContext).mockReturnValue(createMockAppContext(undefined));

      const { container } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={defaultObjectValue}
        />
      );

      // Should not render any FieldValue components when objects is undefined
      expect(container.querySelector('[data-testid="field-value"]')).toBeNull();
    });
  });

  describe('Field matching', () => {
    it('should find correct field by fieldId', () => {
      const config: FieldOptions = {
        objectId: 'object1',
        fieldId: 'field2', // Email field
      };

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).toHaveAttribute('data-field-id', 'field2');
    });

    it('should render with undefined field when fieldId is not found', () => {
      const config: FieldOptions = {
        objectId: 'object1',
        fieldId: 'non-existent-field',
      };

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).not.toHaveAttribute('data-field-id'); // undefined field means no attribute
    });

    it('should handle different objects and their fields correctly', () => {
      const config: FieldOptions = {
        objectId: 'object2',
        fieldId: 'field3', // Description field
      };

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).toHaveAttribute('data-field-id', 'field3');
    });
  });

  describe('Config handling', () => {
    it('should handle undefined config', () => {
      const { container } = renderWithMantine(
        <LinkToRecordCell
          config={undefined}
          objectValue={defaultObjectValue}
        />
      );

      // Should not render any FieldValue components when config is undefined
      expect(container.querySelector('[data-testid="field-value"]')).toBeNull();
    });

    it('should handle config without objectId', () => {
      const config: FieldOptions = {
        fieldId: 'field1',
      } as FieldOptions;

      const { container } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      // Should not render any FieldValue components when objectId is missing
      expect(container.querySelector('[data-testid="field-value"]')).toBeNull();
    });

    it('should handle config without fieldId', () => {
      const config: FieldOptions = {
        objectId: 'object1',
      } as FieldOptions;

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).not.toHaveAttribute('data-field-id'); // undefined fieldId means no attribute
    });

    it('should handle empty config object', () => {
      const config: FieldOptions = {} as FieldOptions;

      const { container } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      // Should not render any FieldValue components when config is empty
      expect(container.querySelector('[data-testid="field-value"]')).toBeNull();
    });
  });

  describe('Object value handling', () => {
    it('should pass objectValue.value to FieldValue', () => {
      const objectValue: RelationshipConfig = {
        value: 'Custom Value',
        recordId: 'rec456',
        fieldId: 'field1',
      };

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={objectValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).toHaveAttribute('data-value', 'Custom Value');
    });

    it('should handle undefined objectValue', () => {
      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={undefined}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).not.toHaveAttribute('data-value'); // undefined value means no attribute
    });

    it('should handle null objectValue', () => {
      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={null as any}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).not.toHaveAttribute('data-value'); // null value means no attribute
    });

    it('should handle objectValue without value property', () => {
      const objectValue = {
        recordId: 'rec789',
        fieldId: 'field1',
      } as RelationshipConfig;

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={objectValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).not.toHaveAttribute('data-value'); // undefined value means no attribute
    });

    it('should handle string value types', () => {
      const objectValue: RelationshipConfig = {
        value: 'string value',
        recordId: 'rec123',
        fieldId: 'field1',
      };

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={objectValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).toHaveAttribute('data-value', 'string value');
    });

    it('should handle undefined value in objectValue', () => {
      const objectValue: RelationshipConfig = {
        value: undefined,
        recordId: 'rec123',
        fieldId: 'field1',
      };

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={objectValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).not.toHaveAttribute('data-value'); // undefined value means no attribute
    });
  });

  describe('Component structure', () => {
    it('should render Cell component when object fields exist', () => {
      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={defaultConfig}
          objectValue={defaultObjectValue}
        />
      );

      // The Cell component should render FieldValue
      expect(getByTestId('field-value')).toBeInTheDocument();
    });

    it('should have correct display name', () => {
      expect(LinkToRecordCell.displayName).toBe('LinkToRecordCell');
    });

    it('should be wrapped with React.memo', () => {
      // Test that the component is memoized
      expect(LinkToRecordCell.displayName).toBe('LinkToRecordCell');
    });
  });

  describe('Integration scenarios', () => {
    it('should handle complex object structures', () => {
      const complexObject = {
        id: 'complex-object',
        name: 'Complex Object',
        fields: [
          {
            id: 'complex-field',
            name: 'Complex Field',
            type: 'relationship',
            header: 'Complex Field',
            options: {
              objectId: 'target-object',
              fieldId: 'target-field',
            },
          },
        ] as ObjectColumn[],
      };

      vi.mocked(useAppContext).mockReturnValue(
        createMockAppContext([...mockObjects, complexObject])
      );

      const config: FieldOptions = {
        objectId: 'complex-object',
        fieldId: 'complex-field',
      };

      const complexValue: RelationshipConfig = {
        value: 'complex-value',
        recordId: 'complex-rec',
        fieldId: 'complex-field',
      };

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={complexValue}
        />
      );

      const fieldValue = getByTestId('field-value');
      expect(fieldValue).toBeInTheDocument();
      expect(fieldValue).toHaveAttribute('data-field-id', 'complex-field');
    });

    it('should handle multiple objects with same field names', () => {
      const obj1Config: FieldOptions = {
        objectId: 'object1',
        fieldId: 'field1',
      };

      const obj2Config: FieldOptions = {
        objectId: 'object2',
        fieldId: 'field3',
      };

      const { rerender, getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={obj1Config}
          objectValue={defaultObjectValue}
        />
      );

      expect(getByTestId('field-value')).toHaveAttribute('data-field-id', 'field1');

      rerender(
        <LinkToRecordCell
          config={obj2Config}
          objectValue={defaultObjectValue}
        />
      );

      expect(getByTestId('field-value')).toHaveAttribute('data-field-id', 'field3');
    });
  });

  describe('Edge cases', () => {
    it('should handle object with fields property but empty array', () => {
      const emptyFieldsObject = {
        id: 'empty-fields',
        name: 'Empty Fields Object',
        fields: [],
      };

      vi.mocked(useAppContext).mockReturnValue(
        createMockAppContext([emptyFieldsObject])
      );

      const config: FieldOptions = {
        objectId: 'empty-fields',
        fieldId: 'any-field',
      };

      const { container } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      // Should not render any FieldValue components when object has empty fields array
      expect(container.querySelector('[data-testid="field-value"]')).toBeNull();
    });

    it('should handle very large objects array efficiently', () => {
      const manyObjects = Array.from({ length: 100 }, (_, i) => ({
        id: `object${i}`,
        name: `Object ${i}`,
        fields: [
          {
            id: `field${i}`,
            name: `Field ${i}`,
            type: 'text',
            header: `Field ${i}`,
          },
        ] as ObjectColumn[],
      }));

      vi.mocked(useAppContext).mockReturnValue(
        createMockAppContext(manyObjects)
      );

      const config: FieldOptions = {
        objectId: 'object50',
        fieldId: 'field50',
      };

      const { getByTestId } = renderWithMantine(
        <LinkToRecordCell
          config={config}
          objectValue={defaultObjectValue}
        />
      );

      expect(getByTestId('field-value')).toHaveAttribute('data-field-id', 'field50');
    });
  });
});
