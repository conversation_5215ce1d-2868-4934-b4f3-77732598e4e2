import type { FieldOptions } from '@/models';
import React, { useMemo } from 'react';

const NumberCellUI = ({
  config,
  value: initialValue,
}: {
  config?: FieldOptions;
  value: string;
}) => {
  const value = useMemo(() => {
    if (!initialValue) return '';

    if (config?.numberFormat === 'decimal') {
      return Number.parseFloat(initialValue).toFixed(config?.decimalPlaces);
    }
    return Number.parseInt(initialValue).toString();
  }, [initialValue, config]);

  return <div>{value}</div>;
};

export const NumberCell = React.memo(NumberCellUI);
