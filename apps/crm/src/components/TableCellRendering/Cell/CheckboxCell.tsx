import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { DecaCheckbox } from '@resola-ai/ui';
import type { MRT_Cell } from 'mantine-react-table';
import React, { useEffect, useState } from 'react';

type Props = {
  cell: MRT_Cell<any, any>;
  type: string;
};

const CheckboxCellUI = ({ cell }: Props) => {
  const [value, setValue] = useState(cell.getValue() as boolean);
  const { onSavingCell } = useWorkspaceContext();

  useEffect(() => {
    setValue(cell.getValue() as boolean);
  }, [cell.getValue()]);

  return (
    <DecaCheckbox
      checked={value}
      onChange={(e) => {
        onSavingCell(e.target.checked, cell);
        setValue(e.target.checked);
      }}
    />
  );
};
export const CheckboxCell = React.memo(CheckboxCellUI);
