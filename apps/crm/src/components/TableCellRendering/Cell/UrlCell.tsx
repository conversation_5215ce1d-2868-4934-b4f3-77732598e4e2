import type { FieldOptions } from '@/models';
import type { MRT_Cell } from 'mantine-react-table';
import React from 'react';

const UrlCellUI = ({ cell }: { config?: FieldOptions; cell: MRT_Cell<any, any> }) => {
  const value = cell.getValue() as string;
  if (!value) {
    return null;
  }

  // Ensure URL has proper protocol
  const url =
    value.startsWith('http://') || value.startsWith('https://') ? value : `https://${value}`;

  return (
    <a href={url} target='_blank' rel='noopener noreferrer' className='url-cell'>
      {value}
    </a>
  );
};

export const UrlCell = React.memo(UrlCellUI);
