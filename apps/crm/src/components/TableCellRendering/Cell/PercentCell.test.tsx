import { PercentCell } from '@/components/TableCellRendering/Cell/PercentCell';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';

describe('PercentCell', () => {
  it('renders bar when presentation type is bar', () => {
    const config = {
      presentation: { enabled: true, type: 'bar' },
      decimalPlaces: 0,
      separator: { enabled: false },
    };
    renderWithMantine(<PercentCell config={config as any} value='50' />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders circle when presentation type is circle', () => {
    const config = {
      presentation: { enabled: true, type: 'circle' },
      decimalPlaces: 0,
      separator: { enabled: false },
    };
    const { container } = renderWithMantine(<PercentCell config={config as any} value='75' />);
    expect(container.querySelector('svg')).toBeInTheDocument();
  });

  it('renders percentage string when presentation disabled', () => {
    const config = {
      presentation: { enabled: false },
      decimalPlaces: 1,
      separator: { enabled: true, format: 'commaPeriod' },
    };
    renderWithMantine(<PercentCell config={config as any} value='25' />);
    expect(screen.getByText('25.0%')).toBeInTheDocument();
  });

  it('renders empty when no value', () => {
    const { container } = renderWithMantine(<PercentCell config={{} as any} value='' />);
    const divElement = container.querySelector('div');
    expect(divElement).not.toBeInTheDocument();
  });
});
