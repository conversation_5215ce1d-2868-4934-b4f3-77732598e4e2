import { fireEvent, waitFor } from '@testing-library/react';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import type { Choice } from '@/models';

// Mock Mantine components FIRST before importing the component
vi.mock('@mantine/core', () => ({
  Flex: vi.fn(({ children, gap, sx, ...props }) => (
    <div data-testid="flex-container" style={sx} {...props}>
      {children}
    </div>
  )),
  rem: vi.fn((value: number) => `calc(${value * 0.0625}rem * var(--mantine-scale))`),
}));

// Mock the workspace context
const mockOnSavingCell = vi.fn();
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => ({
    onSavingCell: mockOnSavingCell,
  }),
}));

// Mock DecaStatus component
vi.mock('@resola-ai/ui', () => ({
  DecaStatus: vi.fn(({ size, variant, text, showRemove, onRemove, sx }) => (
    <div
      data-testid="deca-status"
      data-size={size}
      data-variant={variant}
      data-text={text}
      data-show-remove={showRemove}
      style={sx}
    >
      <span>{text}</span>
      {showRemove && (
        <button
          data-testid={`remove-button-${text}`}
          onClick={() => onRemove?.()}
        >
          Remove {text}
        </button>
      )}
    </div>
  )),
}));

import { MultiSelectCell } from './MultiSelectCell';

describe('MultiSelectCell', () => {
  const mockChoices: Choice[] = [
    { id: 'choice1', label: 'Choice 1', color: 'blue' },
    { id: 'choice2', label: 'Choice 2', color: 'red' },
    { id: 'choice3', label: 'Choice 3', color: 'green' },
    { id: 'choice4', label: 'Choice 4', color: 'yellow' },
  ];

  const mockCell = {
    row: { index: 0 },
    column: { id: 'test-column' },
    getValue: () => ['choice1', 'choice2'],
  } as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic rendering', () => {
    it('should render multiple DecaStatus components for multiple selected values', () => {
      const { getAllByTestId, getByText } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={mockChoices}
        />
      );

      const statuses = getAllByTestId('deca-status');
      expect(statuses).toHaveLength(2);
      
      expect(getByText('Choice 1')).toBeInTheDocument();
      expect(getByText('Choice 2')).toBeInTheDocument();
    });

    it('should render single DecaStatus for single selected value', () => {
      const { getAllByTestId, getByText } = renderWithMantine(
        <MultiSelectCell
          value={['choice1']}
          choices={mockChoices}
        />
      );

      const statuses = getAllByTestId('deca-status');
      expect(statuses).toHaveLength(1);
      expect(getByText('Choice 1')).toBeInTheDocument();
    });

    it('should not render anything when no values are selected', () => {
      const { container } = renderWithMantine(
        <MultiSelectCell
          value={[]}
          choices={mockChoices}
        />
      );

      // Should not render any DecaStatus components for empty array
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });

    it('should not render anything when value is undefined', () => {
      const { container } = renderWithMantine(
        <MultiSelectCell
          value={undefined as any}
          choices={mockChoices}
        />
      );

      // Should not render any DecaStatus components when value is undefined
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });

    it('should not render anything when value is null', () => {
      const { container } = renderWithMantine(
        <MultiSelectCell
          value={null as any}
          choices={mockChoices}
        />
      );

      // Should not render any DecaStatus components when value is null
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });
  });

  describe('Value filtering', () => {
    it('should filter out empty string values', () => {
      const { getAllByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', '', 'choice2', '']}
          choices={mockChoices}
        />
      );

      const statuses = getAllByTestId('deca-status');
      expect(statuses).toHaveLength(2); // Only non-empty values should be rendered
    });

    it('should handle values with non-existent choice IDs', () => {
      const { getAllByTestId, queryByText } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'non-existent', 'choice2']}
          choices={mockChoices}
        />
      );

      const statuses = getAllByTestId('deca-status');
      expect(statuses).toHaveLength(2); // Only existing choices should be rendered
      expect(queryByText('Non-existent')).not.toBeInTheDocument();
    });

    it('should handle duplicate values correctly', () => {
      const { getAllByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice1', 'choice2']}
          choices={mockChoices}
        />
      );

      // Should render based on unique choices found, not duplicates in value array
      const statuses = getAllByTestId('deca-status');
      expect(statuses).toHaveLength(2);
    });
  });

  describe('Choices handling', () => {
    it('should handle empty choices array', () => {
      const { container } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={[]}
        />
      );

      // Should not render any DecaStatus components when choices is empty
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });

    it('should handle undefined choices', () => {
      const { container } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={undefined as any}
        />
      );

      // Should not render any DecaStatus components when choices is undefined
      expect(container.querySelector('[data-testid="deca-status"]')).toBeNull();
    });

    it('should match choices correctly by ID', () => {
      const { getAllByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice2', 'choice4']}
          choices={mockChoices}
        />
      );

      const statuses = getAllByTestId('deca-status');
      expect(statuses[0]).toHaveAttribute('data-variant', 'red'); // choice2 is red
    });
  });

  describe('Remove functionality', () => {
    it('should show remove buttons when showRemove is true', () => {
      const { getByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={mockChoices}
          showRemove={true}
          cell={mockCell}
        />
      );

      expect(getByTestId('remove-button-Choice 1')).toBeInTheDocument();
      expect(getByTestId('remove-button-Choice 2')).toBeInTheDocument();
    });

    it('should not show remove buttons when showRemove is false', () => {
      const { getAllByTestId, queryByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={mockChoices}
          showRemove={false}
          cell={mockCell}
        />
      );

      const statuses = getAllByTestId('deca-status');
      statuses.forEach(status => {
        expect(status).toHaveAttribute('data-show-remove', 'false');
      });
      
      expect(queryByTestId('remove-button-Choice 1')).not.toBeInTheDocument();
      expect(queryByTestId('remove-button-Choice 2')).not.toBeInTheDocument();
    });

    it('should show remove buttons by default when showRemove is not specified', () => {
      const { getByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={mockChoices}
          cell={mockCell}
        />
      );

      expect(getByTestId('remove-button-Choice 1')).toBeInTheDocument();
      expect(getByTestId('remove-button-Choice 2')).toBeInTheDocument();
    });

    it('should call onSavingCell with filtered values when remove button is clicked', async () => {
      const { getByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2', 'choice3']}
          choices={mockChoices}
          showRemove={true}
          cell={mockCell}
        />
      );

      // Remove choice2
      const removeButton = getByTestId('remove-button-Choice 2');
      fireEvent.click(removeButton);

      await waitFor(() => {
        expect(mockOnSavingCell).toHaveBeenCalledWith(['choice1', 'choice3'], mockCell);
      });
    });

    it('should not call onSavingCell when cell is not provided', async () => {
      const { getByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={mockChoices}
          showRemove={true}
        />
      );

      const removeButton = getByTestId('remove-button-Choice 1');
      fireEvent.click(removeButton);

      // Wait a bit to ensure no call was made
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(mockOnSavingCell).not.toHaveBeenCalled();
    });

    it('should handle removing the last selected option', async () => {
      const { getByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1']}
          choices={mockChoices}
          showRemove={true}
          cell={mockCell}
        />
      );

      const removeButton = getByTestId('remove-button-Choice 1');
      fireEvent.click(removeButton);

      await waitFor(() => {
        expect(mockOnSavingCell).toHaveBeenCalledWith([], mockCell);
      });
    });
  });

  describe('Styling and layout', () => {
    it('should apply correct flex styles to container', () => {
      const { container } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={mockChoices}
        />
      );

      const flexContainer = container.querySelector('[data-testid="flex-container"]') || container.firstChild;
      expect(flexContainer).toBeInTheDocument();
    });

    it('should apply marginRight style to DecaStatus components', () => {
      const { getAllByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={mockChoices}
        />
      );

      const statuses = getAllByTestId('deca-status');
      statuses.forEach(status => {
        // Mantine rem(10) compiles to this computed value
        expect(status.style.marginRight).toBe('calc(0.625rem * var(--mantine-scale))');
      });
    });
  });

  describe('Choice properties', () => {
    it('should render choices with correct colors', () => {
      const { getAllByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2', 'choice3']}
          choices={mockChoices}
        />
      );

      const statuses = getAllByTestId('deca-status');
      expect(statuses[0]).toHaveAttribute('data-variant', 'blue');
      expect(statuses[1]).toHaveAttribute('data-variant', 'red');
      expect(statuses[2]).toHaveAttribute('data-variant', 'green');
    });

    it('should render choices with correct labels', () => {
      const { getByText } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice3']}
          choices={mockChoices}
        />
      );

      expect(getByText('Choice 1')).toBeInTheDocument();
      expect(getByText('Choice 3')).toBeInTheDocument();
    });

    it('should handle choices with special characters', () => {
      const specialChoices: Choice[] = [
        { id: '1', label: 'Choice with @#$%', color: 'blue' },
        { id: '2', label: 'Choice with émojis 🎉', color: 'red' },
      ];

      const { getByText } = renderWithMantine(
        <MultiSelectCell
          value={['1', '2']}
          choices={specialChoices}
        />
      );

      expect(getByText('Choice with @#$%')).toBeInTheDocument();
      expect(getByText('Choice with émojis 🎉')).toBeInTheDocument();
    });
  });

  describe('Performance and memoization', () => {
    it('should be wrapped with React.memo', () => {
      // React.memo components may be objects or functions, just verify it's defined and renderable
      expect(MultiSelectCell).toBeDefined();
      expect(typeof MultiSelectCell === 'function' || typeof MultiSelectCell === 'object').toBe(true);
    });

    it('should filter empty strings from values', () => {
      const { getAllByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', '', 'choice2', '']}
          choices={mockChoices}
        />
      );

      // Should only render non-empty values
      const statuses = getAllByTestId('deca-status');
      expect(statuses).toHaveLength(2);
    });

    it('should handle choices filtering correctly', () => {
      const { getAllByTestId } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'choice2']}
          choices={mockChoices}
        />
      );

      const statuses = getAllByTestId('deca-status');
      expect(statuses).toHaveLength(2);
      
      // Verify correct choices are rendered
      expect(statuses[0]).toHaveAttribute('data-variant', 'blue'); // choice1
      expect(statuses[1]).toHaveAttribute('data-variant', 'red'); // choice2
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle choices without color property', () => {
      const choicesWithoutColor: Choice[] = [
        { id: '1', label: 'No Color Choice' } as Choice,
      ];

      const { getByTestId } = renderWithMantine(
        <MultiSelectCell value={['1']} choices={choicesWithoutColor} />
      );

      const status = getByTestId('deca-status');
      expect(status).not.toHaveAttribute('data-variant'); // undefined color means no attribute
    });

    it('should handle choices without label property', () => {
      const choicesWithoutLabel: Choice[] = [
        { id: '1', color: 'blue' } as Choice,
      ];

      const { getByTestId } = renderWithMantine(
        <MultiSelectCell value={['1']} choices={choicesWithoutLabel} />
      );

      const status = getByTestId('deca-status');
      expect(status).not.toHaveAttribute('data-text'); // undefined label means no attribute
    });

    it('should handle numeric choice IDs', () => {
      const numericChoices: Choice[] = [
        { id: '123', label: 'Numeric ID Choice', color: 'blue' },
        { id: '456', label: 'Another Numeric Choice', color: 'red' },
      ];

      const { getByText } = renderWithMantine(
        <MultiSelectCell value={['123', '456']} choices={numericChoices} />
      );

      expect(getByText('Numeric ID Choice')).toBeInTheDocument();
      expect(getByText('Another Numeric Choice')).toBeInTheDocument();
    });

    it('should handle very large number of selected items', () => {
      const manyChoices: Choice[] = Array.from({ length: 20 }, (_, i) => ({
        id: `choice${i}`,
        label: `Choice ${i}`,
        color: 'blue',
      }));

      const manyValues = Array.from({ length: 20 }, (_, i) => `choice${i}`);

      const { getAllByTestId } = renderWithMantine(
        <MultiSelectCell value={manyValues} choices={manyChoices} />
      );

      const statuses = getAllByTestId('deca-status');
      expect(statuses).toHaveLength(20);
    });

    it('should handle mixed valid and invalid choice IDs', () => {
      const { getAllByTestId, getByText, queryByText } = renderWithMantine(
        <MultiSelectCell
          value={['choice1', 'invalid1', 'choice2', 'invalid2', 'choice3']}
          choices={mockChoices}
        />
      );

      const statuses = getAllByTestId('deca-status');
      expect(statuses).toHaveLength(3); // Only valid choices

      expect(getByText('Choice 1')).toBeInTheDocument();
      expect(getByText('Choice 2')).toBeInTheDocument();
      expect(getByText('Choice 3')).toBeInTheDocument();
      expect(queryByText('Invalid1')).not.toBeInTheDocument();
      expect(queryByText('Invalid2')).not.toBeInTheDocument();
    });
  });
});
