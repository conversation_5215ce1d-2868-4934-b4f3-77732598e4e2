import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { RelationshipConfig } from '@/models/workspace';
import { FieldTypes } from '@resola-ai/ui/components';
import type { MRT_Cell, MRT_TableInstance } from 'mantine-react-table';
import React from 'react';
import { DateTimeCell } from './DateTimeCell';
import { ImageCell } from './ImageCell';
import { InputCell } from './InputCell';
import { LinkToRecordCell } from './LinkToRecordCell';
import { LongTextCell } from './LongTextCell';
import { MultiSelectCell } from './MultiSelectCell';
import { SingleSelectCell } from './SingleSelectCell';

type Props = {
  cell: MRT_Cell<any, any>;
  table: MRT_TableInstance<any>;
  column?: any;
};

const EditCell = ({ cell, table, column }: Props) => {
  const type = column.type;
  const { onSavingCell } = useWorkspaceContext();

  const renderCell = () => {
    switch (type) {
      case FieldTypes.LONG_TEXT:
        return <LongTextCell cell={cell} table={table} />;
      case FieldTypes.SINGLE_SELECT:
        return (
          <SingleSelectCell
            cell={cell}
            table={table}
            config={column?.options}
            onChange={(val) => {
              val !== undefined && onSavingCell(val, cell);
              table.setEditingCell(null);
            }}
          />
        );
      case FieldTypes.MULTI_SELECT:
        return (
          <MultiSelectCell
            cell={cell}
            config={column?.options}
            onChange={(val, shouldClose) => {
              val && onSavingCell(val, cell);
              shouldClose && table.setEditingCell(null);
            }}
          />
        );
      case FieldTypes.DATETIME:
        return (
          <DateTimeCell
            config={column?.options}
            value={cell.getValue() as string}
            onChange={(val) => {
              val && onSavingCell(val, cell);
              table.setEditingCell(null);
            }}
          />
        );
      case FieldTypes.RELATIONSHIP:
        return (
          <LinkToRecordCell
            value={cell.getValue() as RelationshipConfig}
            config={column?.options}
            onChange={(val) => {
              val && onSavingCell(val, cell);
              table.setEditingCell(null);
            }}
          />
        );
      case FieldTypes.IMAGE:
        return (
          <ImageCell
            value={cell.getValue() as string}
            onChange={(val) => {
              onSavingCell(val, cell);
              table.setEditingCell(null);
            }}
          />
        );
      default:
        return <InputCell cell={cell} table={table} type={type} config={column?.options} />;
    }
  };
  return <>{renderCell()}</>;
};

export default React.memo(EditCell);

export * from './LongTextCell';
export * from './SingleSelectCell';
export * from './MultiSelectCell';
export * from './DateTimeCell';
export * from './LinkToRecordCell';
export * from './ImageCell';
export * from './InputCell';
