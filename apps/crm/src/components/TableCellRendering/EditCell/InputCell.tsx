import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { FieldOptions } from '@/models';
import { validateInput } from '@/utils';
import { Box, TextInput } from '@mantine/core';
import { ErrorMessage } from '@resola-ai/ui';
import { FieldTypes } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import type { MRT_Cell } from 'mantine-react-table';
import { useCallback, useState } from 'react';
type Props = {
  cell: MRT_Cell<any, any>;
  table: any;
  type?: string;
  config?: FieldOptions;
};

export const InputCell = ({ cell, table, type, config }: Props) => {
  const { t } = useTranslate('workspace');
  const [value, setValue] = useState(cell.getValue() as string);
  const { onSavingCell, validationErrors, setValidationErrors } = useWorkspaceContext();
  const cellError = validationErrors[cell.id];
  const format = config?.numberFormat;

  const handleBlur = () => {
    table.setEditingCell(false);
    if (!cellError && cell.getValue() !== value) {
      if (
        [FieldTypes.CURRENCY, FieldTypes.PERCENT, FieldTypes.NUMBER].includes(type as any) &&
        value !== ''
      ) {
        onSavingCell(+value, cell);
      } else {
        onSavingCell(value || '', cell);
      }
    }
    delete validationErrors[cell.id];
    setValidationErrors(validationErrors);
  };

  const handleChange = useCallback(
    (e) => {
      const value = e.target.value;
      setValue(value);
      const error = value ? validateInput(value, type, t, format) : '';
      setValidationErrors((prevErrors) => ({
        ...prevErrors,
        [cell.id]: error,
      }));
    },
    [cell]
  );

  return (
    <Box w={'100%'}>
      <TextInput
        autoFocus
        value={value || ''}
        onBlur={handleBlur}
        onChange={(e) => handleChange(e)}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            handleBlur();
          }
        }}
      />
      {cellError && <ErrorMessage message={cellError || ''} />}
    </Box>
  );
};
