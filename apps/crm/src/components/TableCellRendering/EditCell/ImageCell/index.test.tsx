import type { Asset } from '@/models';
import { AssetAPI, UploadAPI } from '@/services/api';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { ImageCell } from './index';

// Mock dependencies
vi.mock('@/utils', () => ({
  downloadImage: vi.fn(),
}));

vi.mock('@/services/api', () => ({
  AssetAPI: {
    save: vi.fn(),
  },
  UploadAPI: {
    update: vi.fn(),
  },
}));

// Since we're using a simplified mock implementation, no need to mock all parameters
vi.mock('@mantine/core', () => ({
  Avatar: () => <div data-testid='avatar'>Avatar</div>,
  Box: (props: any) => <div data-testid='box'>{props.children}</div>,
  Divider: () => <div data-testid='divider'>Divider</div>,
  FileButton: ({ onChange, children }: any) => {
    const props = {
      onClick: () => {
        const mockFile = new File(['dummy content'], 'test.png', { type: 'image/png' });
        onChange(mockFile);
      },
    };
    return <div data-testid='file-button'>{children(props)}</div>;
  },
  Flex: (props: any) => (
    <div data-testid='flex' data-direction={props.direction}>
      {props.children}
    </div>
  ),
  Loader: () => <div data-testid='loader'>Loading...</div>,
  Menu: (props: any) => (
    <div data-testid='menu' onClick={props.onClose}>
      {props.children}
    </div>
  ),
  'Menu.Target': (props: any) => <div data-testid='menu-target'>{props.children}</div>,
  'Menu.Dropdown': (props: any) => <div data-testid='menu-dropdown'>{props.children}</div>,
  'Menu.Item': (props: any) => (
    <button data-testid='menu-item' onClick={props.onClick}>
      {props.children}
    </button>
  ),
  rem: (val: any) => `${val}rem`,
  Text: (props: any) => <div data-testid='text'>{props.children || ''}</div>,
  TextInput: (props: any) => (
    <input
      data-testid='text-input'
      placeholder={props.placeholder}
      onChange={props.onChange}
      onKeyDown={props.onKeyDown}
    />
  ),
  useMantineTheme: () => ({
    colors: {
      decaRed: {
        5: '#ff0000',
      },
    },
  }),
}));

vi.mock('@tabler/icons-react', () => ({
  IconDots: () => <div data-testid='icon-dots'>Dots Icon</div>,
  IconPlus: () => <div data-testid='icon-plus'>Plus Icon</div>,
}));

vi.mock('@resola-ai/ui', () => ({
  CustomImageBackground: (props: any) => (
    <div data-testid='custom-image-background' data-url={props.url}>
      Custom Image Background
    </div>
  ),
  DecaButton: (props: any) => (
    <button
      data-testid='deca-button'
      data-variant={props.variant}
      disabled={props.disabled}
      onClick={props.onClick}
    >
      {props.leftSection && <span>{props.leftSection}</span>}
      {props.children}
    </button>
  ),
}));

// Mock global Image
const originalImage = global.Image;
const originalRequestAnimationFrame = global.requestAnimationFrame;

beforeAll(() => {
  // @ts-expect-error Mock implementation
  global.Image = class {
    onload: () => void = () => {};
    onerror: () => void = () => {};
    src = '';

    constructor() {
      setTimeout(() => {
        this.onload();
      }, 10);
    }
  };

  // Mock requestAnimationFrame for GSAP
  global.requestAnimationFrame = vi.fn((callback) => {
    setTimeout(callback, 16);
    return 1; // Return a number like the real requestAnimationFrame
  });
});

afterAll(() => {
  global.Image = originalImage;
  global.requestAnimationFrame = originalRequestAnimationFrame;
});

describe('ImageCell', () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock the API responses
    vi.mocked(AssetAPI.save).mockResolvedValue({
      file: {
        url: 'https://example.com/image.jpg',
        id: '123',
        externalId: 'ext123',
        mimeType: 'image/jpeg',
        name: 'image.jpg',
        size: 1024,
        width: 100,
        height: 100,
        path: 'path/to/image',
        target: 'target',
      } as unknown as Asset,
      uploadUrl: 'https://upload.example.com/path',
    });
    vi.mocked(UploadAPI.update).mockResolvedValue({} as any);
  });

  it('renders empty state with upload controls when no value is provided', () => {
    renderWithMantine(<ImageCell value='' onChange={mockOnChange} />);

    // Verify there's a button to choose an image
    const chooseImageButton = screen.getByText(/chooseImage/i);
    expect(chooseImageButton).toBeInTheDocument();

    // Verify URL input section exists
    const urlInputSection = screen.getByText(/inputFromUrl/i);
    expect(urlInputSection).toBeInTheDocument();

    // Verify there's an input field for URL
    const input = screen.getByPlaceholderText('enterUrl');
    expect(input).toBeInTheDocument();
  });

  it('renders with image when value is provided', () => {
    renderWithMantine(<ImageCell value='https://example.com/image.jpg' onChange={mockOnChange} />);

    // Verify the custom image background is rendered with the correct URL
    const images = screen.getAllByTestId('custom-image-background');
    expect(images.length).toBeGreaterThan(0);
    expect(images[0]).toHaveAttribute('data-url', 'https://example.com/image.jpg');
  });

  it('provides a file upload button', () => {
    renderWithMantine(<ImageCell value='' onChange={mockOnChange} />);

    // Verify the file button is rendered
    const buttons = screen.getAllByTestId('deca-button');
    expect(buttons.length).toBeGreaterThan(0);

    // The first button should be the choose image button
    expect(buttons[0]).toHaveTextContent('chooseImage');
  });

  it('handles image deletion via delete button', () => {
    // For empty images, there should be a delete button that's disabled
    renderWithMantine(
      <ImageCell value='https://example.com/image.jpg' onChange={mockOnChange} isEditable={true} />
    );

    // Find the delete image button
    const buttons = screen.getAllByTestId('deca-button');

    // Find the button that has the text 'delImage'
    const deleteButton = Array.from(buttons).find((button) =>
      button.textContent?.includes('delImage')
    );

    expect(deleteButton).toBeDefined();
    if (deleteButton) {
      fireEvent.click(deleteButton);
      expect(mockOnChange).toHaveBeenCalledWith('');
    }
  });

  it('provides a text input field for URL entry', async () => {
    renderWithMantine(<ImageCell value='' onChange={mockOnChange} />);

    // Verify there's a text input field
    const input = screen.getByPlaceholderText('enterUrl');
    expect(input).toBeInTheDocument();

    // Test entering a URL and pressing enter
    fireEvent.change(input, { target: { value: 'https://example.com/valid-image.jpg' } });
    fireEvent.keyDown(input, { key: 'Enter' });

    // The URL should be validated and onChange called
    await waitFor(
      () => {
        expect(mockOnChange).toHaveBeenCalledWith('https://example.com/valid-image.jpg');
      },
      { timeout: 2000 }
    );
  });
});
