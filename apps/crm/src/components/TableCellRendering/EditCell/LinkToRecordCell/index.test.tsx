import { useAppContext } from '@/contexts/AppContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useRecords } from '@/hooks';
import type { RelationshipConfig } from '@/models';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import { useParams } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { LinkToRecordCell } from './index';

// Mocks
vi.mock('@/hooks', () => ({
  useRecords: vi.fn(),
}));

vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
}));

// We're using renderWithMantine utility, so we don't need to mock Mantine components
// Just mock the IconX component which is used in the component
vi.mock('@tabler/icons-react', () => ({
  IconX: () => <div data-testid='icon-x' />,
}));

vi.mock('@tanstack/react-virtual', () => ({
  useVirtualizer: () => ({
    getTotalSize: () => 300,
    getVirtualItems: () => [
      {
        index: 0,
        key: 'item-0',
        start: 0,
        end: 30,
        measureElement: vi.fn(),
      },
      {
        index: 1,
        key: 'item-1',
        start: 30,
        end: 60,
        measureElement: vi.fn(),
      },
    ],
    measureElement: vi.fn(),
  }),
}));

// Mock components
vi.mock('./FieldValue', () => ({
  default: ({ field, value }) => (
    <span data-testid={`field-value-${field?.id || 'unknown'}`}>{value || ''}</span>
  ),
}));

// Create context provider wrappers
const AppContextProvider = ({ children }) => children;
const WorkspaceContextProvider = ({ children }) => children;

// Wrapper component with context providers
const renderWithContext = (ui) => {
  return renderWithMantine(
    <AppContextProvider>
      <WorkspaceContextProvider>{ui}</WorkspaceContextProvider>
    </AppContextProvider>
  );
};

describe('LinkToRecordCell Component', () => {
  const mockRecords = [
    { id: 'record1', name: 'Record 1', description: 'First record' },
    { id: 'record2', name: 'Record 2', description: 'Second record' },
  ];

  const mockFields = [
    { id: 'name', name: 'Name', type: FieldTypes.SINGLE_LINE_TEXT },
    { id: 'description', name: 'Description', type: FieldTypes.LONG_TEXT },
    { id: 'date', name: 'Date', type: FieldTypes.DATETIME },
  ];

  const mockObject = {
    id: 'obj1',
    fields: mockFields,
  };

  const mockRelationship: RelationshipConfig = {
    recordId: 'record1',
    fieldId: 'name',
  };

  const mockConfig = {
    objectId: 'obj1',
    fieldId: 'name',
  };

  const mockTranslate = vi.fn((key) => key);
  const handleChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock return values
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
    });

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      objects: [mockObject],
    });

    (useAppContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      objects: [mockObject],
    });

    (useRecords as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      records: mockRecords,
      recordsLoading: false,
    });

    (useTranslate as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      t: mockTranslate,
    });
  });

  it('renders loader when records are loading', () => {
    (useRecords as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      records: [],
      recordsLoading: true,
    });

    renderWithContext(
      <LinkToRecordCell value={mockRelationship} config={mockConfig} onChange={handleChange} />
    );

    // Check if loader is displayed
    const loaderElement = document.querySelector('.mantine-Loader-root');
    expect(loaderElement).toBeInTheDocument();
  });

  it('handles search input', async () => {
    renderWithContext(
      <LinkToRecordCell value={mockRelationship} config={mockConfig} onChange={handleChange} />
    );

    // Find the search input
    const searchInput = screen.getByPlaceholderText('search');
    fireEvent.change(searchInput, { target: { value: 'Record 2' } });

    // Wait for debounced search to complete
    await waitFor(() => {
      expect(searchInput).toHaveValue('Record 2');
    });
  });

  it('handles selecting a record', () => {
    renderWithContext(
      <LinkToRecordCell value={mockRelationship} config={mockConfig} onChange={handleChange} />
    );

    // Find and click a menu item
    const menuItems = document.querySelectorAll('.mantine-Menu-item');
    expect(menuItems.length).toBeGreaterThan(0);
    fireEvent.click(menuItems[0]);

    // Verify onChange was called
    expect(handleChange).toHaveBeenCalled();
  });

  it('displays no results message when no records match', () => {
    (useRecords as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      records: [],
      recordsLoading: false,
    });

    renderWithContext(
      <LinkToRecordCell value={mockRelationship} config={mockConfig} onChange={handleChange} />
    );

    expect(screen.getByText('filterNoResultFound')).toBeInTheDocument();
  });

  it('verifies hook calls', () => {
    // Clear mock call history before this test
    vi.clearAllMocks();

    // Render the component
    renderWithContext(
      <LinkToRecordCell value={mockRelationship} config={mockConfig} onChange={handleChange} />
    );

    // Verify that useRecord was called
    expect(useRecords).toHaveBeenCalled();
  });
});
