import type { FieldOptions } from '@/models';
import { Box, Menu, Text, rem } from '@mantine/core';
import { DecaStatus } from '@resola-ai/ui';
import type { MRT_Cell } from 'mantine-react-table';
import { OptionsMenu } from './OptionsMenu';

type Props = {
  cell: MRT_Cell<any, any>;
  table?: any;
  config?: FieldOptions;
  onChange: (value: string | undefined) => void;
};

export const SingleSelectCell = ({ cell, table, config, onChange }: Props) => {
  const defaultValue = cell.getValue();
  const defaultOpt = config?.choices.find((o) => o.id === defaultValue);

  return (
    <Menu
      position={'bottom'}
      defaultOpened={true}
      onClose={() => {
        table?.setEditingCell(false);
        onChange(undefined);
      }}
    >
      <Menu.Target>
        <Box>
          {defaultOpt ? (
            <Text sx={{ userSelect: 'none' }}>
              <DecaStatus
                showRemove
                size='small'
                variant={defaultOpt?.color}
                text={defaultOpt?.label}
                onRemove={() => onChange('')}
              />
            </Text>
          ) : (
            <Text w={rem(200)} h={rem(20)} />
          )}
        </Box>
      </Menu.Target>
      <OptionsMenu
        colId={cell.column.id}
        config={config}
        onUpdateCell={(value) => {
          onChange(value);
        }}
      />
    </Menu>
  );
};
