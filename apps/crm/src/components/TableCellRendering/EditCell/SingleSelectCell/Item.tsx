import { Colors } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { Choice } from '@/models';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  ActionIcon,
  Box,
  Divider,
  Flex,
  Menu,
  Text,
  TextInput,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { DecaButton, DecaStatus, type StatusColor } from '@resola-ai/ui';
import { PERMISSION_KEYS, isPermissionAllowed } from '@resola-ai/ui/components/DecaTable/utils';
import { IconCheck, IconEdit, IconGripVertical, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { type CSSProperties, useEffect, useState } from 'react';

interface Props {
  option: Choice;
  enableColor: boolean;
  onRemove: () => void;
  onSave: (choice: Choice) => void;
}

const Item = ({ option, enableColor = false, onRemove, onSave }: Props) => {
  const theme = useMantineTheme();
  const { t } = useTranslate('workspace');
  const [label, setLabel] = useState('');
  const [color, setColor] = useState<StatusColor>('grey');
  const [opened, setOpened] = useState(false);
  const { object } = useWorkspaceContext();

  // permissions check
  const canUpdateObject = isPermissionAllowed(
    object?.permission || {},
    PERMISSION_KEYS.OBJECT_UPDATE
  );

  const { attributes, isDragging, listeners, setNodeRef, transform, transition } = useSortable({
    id: option.id,
    disabled: !canUpdateObject,
  });
  const style: CSSProperties = {
    opacity: isDragging ? 0.4 : undefined,
    transform: CSS.Translate.toString(transform),
    transition,
  };

  useEffect(() => {
    if (option.id) {
      setLabel(option?.label);
      setColor(option?.color);
    }
  }, [option.id]);

  return (
    <Flex
      {...attributes}
      {...listeners}
      justify={'space-between'}
      align={'center'}
      ref={setNodeRef}
      style={style}
      data-testid={`select-item-${option.id}-test-id`}
    >
      <Flex align={'center'} gap={rem(6)} w={'85%'}>
        <IconGripVertical size={16} color={theme.colors.decaGrey[2]} />
        <DecaStatus text={option.label || 'Name'} size='small' variant={option.color} />
      </Flex>
      {canUpdateObject && (
        <Menu shadow='md' withinPortal={false} position='right-start' opened={opened}>
          <Menu.Target>
            <ActionIcon
              variant='white'
              c={'decaGrey.5'}
              size={22}
              p={rem(2)}
              onClick={(e) => {
                e.stopPropagation();
                setOpened(true);
              }}
            >
              <IconEdit size={16} />
            </ActionIcon>
          </Menu.Target>
          <Menu.Dropdown
            sx={{ width: `${rem(255)} !important` }}
            p={rem(12)}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <Box>
              <Text>{t('name')}</Text>
              <Flex justify={'space-between'} align={'center'}>
                <TextInput
                  value={label}
                  onKeyDown={(e) => {
                    e.stopPropagation();
                  }}
                  onChange={(e) => {
                    setLabel(e.target.value);
                  }}
                />
                <ActionIcon
                  variant='subtle'
                  size={22}
                  p={rem(2)}
                  onClick={() => {
                    onRemove();
                    setOpened(false);
                  }}
                >
                  <IconTrash color={theme.colors.decaRed[6]} />
                </ActionIcon>
              </Flex>
              {enableColor && (
                <>
                  <Divider my={rem(15)} />
                  <Text>{t('color')}</Text>
                  {Colors.map((c) => (
                    <Flex
                      p={rem(5)}
                      key={c}
                      justify={'space-between'}
                      align={'center'}
                      sx={{
                        '&:hover': {
                          borderRadius: rem(4),
                          cursor: 'pointer',
                          backgroundColor: theme.colors.decaLight[1],
                        },
                      }}
                      onClick={() => setColor(c)}
                    >
                      <DecaStatus text={label || ''} size='small' variant={c} />
                      {c === color && <IconCheck size={16} color={theme.colors.decaGreen[6]} />}
                    </Flex>
                  ))}
                </>
              )}
              <Flex gap={rem(10)} mt={rem(10)} justify={'flex-end'}>
                <DecaButton variant='primary_text' size='sm' onClick={() => setOpened(false)}>
                  {t('cancel')}
                </DecaButton>
                <DecaButton
                  size='sm'
                  onClick={() => {
                    onSave({ ...option, label, color });
                    setOpened(false);
                  }}
                >
                  {t('save')}
                </DecaButton>
              </Flex>
            </Box>
          </Menu.Dropdown>
        </Menu>
      )}
    </Flex>
  );
};

export default React.memo(Item);
