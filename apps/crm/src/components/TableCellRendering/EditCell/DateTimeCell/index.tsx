import type { DateTimeConfig } from '@/models';
import { DatePickerInput, DateTimePicker } from '@mantine/dates';
import { DecaButton } from '@resola-ai/ui';
import { useCallback, useEffect, useState } from 'react';
import 'dayjs/locale/en';
import 'dayjs/locale/ja';
import { i18nInstance } from '@/i18n';
import { rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';

type Props = {
  value: string | string[];
  config?: DateTimeConfig;
  onChange?: (value: string | string[] | undefined) => void;
  autoOpen?: boolean | undefined;
};

export const DateTimeCell = ({ config, onChange, autoOpen = true, value: initialValue }: Props) => {
  const { t } = useTranslate('workspace');
  const [value, setValue] = useState<any>();
  const [dateRangeValue, setDateRangeValue] = useState<[Date | null, Date | null]>([null, null]);

  useEffect(() => {
    if (config?.dateRange) {
      // Handle date range values
      if (Array.isArray(initialValue) && initialValue.length === 2) {
        // Check if both values are valid non-empty strings
        const startValue = initialValue[0];
        const endValue = initialValue[1];

        let startDate: Date | null = null;
        let endDate: Date | null = null;

        if (startValue && startValue.trim() !== '') {
          const parsedStart = new Date(startValue);
          startDate = Number.isNaN(parsedStart.getTime()) ? null : parsedStart;
        }

        if (endValue && endValue.trim() !== '') {
          const parsedEnd = new Date(endValue);
          endDate = Number.isNaN(parsedEnd.getTime()) ? null : parsedEnd;
        }

        setDateRangeValue([startDate, endDate]);
      } else {
        setDateRangeValue([null, null]);
      }
    } else {
      // Handle single date value
      const cellValue = Array.isArray(initialValue) ? initialValue[0] : initialValue;
      if (cellValue && cellValue.trim() !== '') {
        const parsedDate = new Date(cellValue);
        setValue(Number.isNaN(parsedDate.getTime()) ? new Date() : parsedDate);
      } else {
        setValue(new Date());
      }
    }
  }, [initialValue, config?.dateRange]);

  const handleChange = useCallback(
    (val) => {
      onChange?.((val || value).toISOString());
    },
    [value, onChange]
  );

  const handleDateRangeChange = useCallback(
    (dateRange: [Date | null, Date | null]) => {
      setDateRangeValue(dateRange);
      if (dateRange[0] && dateRange[1]) {
        const rangeValues = [dateRange[0].toISOString(), dateRange[1].toISOString()];
        onChange?.(rangeValues);
      } else if (dateRange[0] || dateRange[1]) {
        // Handle partial selection - don't call onChange until both dates are selected
        // This prevents clearing the field when user is still selecting
        return;
      } else {
        onChange?.(undefined);
      }
    },
    [onChange]
  );

  const sharingProps = {
    dropdownType: 'popover' as 'popover' | 'modal',
    popoverProps: {
      withinPortal: true,
      ...(autoOpen
        ? {
            opened: true,
          }
        : {}),
      trapFocus: true,
      closeOnClickOutside: true,
      clickOutsideEvents: ['mouseup', 'touchend'],
      onClose: () => {
        // Only clear the value if no dates are selected
        if (!dateRangeValue[0] && !dateRangeValue[1]) {
          onChange?.(undefined);
        }
      },
    },
  };

  // Date Range Picker
  if (config?.dateRange) {
    return (
      <DatePickerInput
        {...sharingProps}
        type='range'
        value={dateRangeValue}
        onChange={handleDateRangeChange}
        placeholder={t('selectDateRange') || 'Select date range'}
        locale={i18nInstance.language === 'en' ? 'en' : 'ja'}
        monthLabelFormat='YYYY/MM'
        allowSingleDateInRange={false}
      />
    );
  }

  // Single Date/DateTime Picker
  const singleDateProps = {
    ...sharingProps,
    value,
  };

  return (
    <>
      {config?.time?.enabled ? (
        <DateTimePicker
          {...singleDateProps}
          placeholder={undefined}
          onChange={setValue}
          submitButtonProps={{
            children: (
              <DecaButton onClick={() => handleChange(value)} size='sm'>
                {t('done')}
              </DecaButton>
            ),
            styles: {
              root: {
                width: 'fit-content',
                border: 'none',

                '& button': {
                  height: `${rem(36)} !important`,
                },
              },
            },
          }}
          locale={i18nInstance.language === 'en' ? 'en' : 'ja'}
          monthLabelFormat='YYYY/MM'
        />
      ) : (
        <DatePickerInput
          {...singleDateProps}
          onChange={(value) => {
            setValue(value);
            handleChange(value);
          }}
          locale={i18nInstance.language === 'en' ? 'en' : 'ja'}
          monthLabelFormat='YYYY/MM'
        />
      )}
    </>
  );
};
