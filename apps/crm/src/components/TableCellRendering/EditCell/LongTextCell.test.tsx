import * as WorkspaceContext from '@/contexts/WorkspaceContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { LongTextCell } from './LongTextCell';

describe('LongTextCell', () => {
  const mockCell = {
    getValue: vi.fn(() => 'test value'),
    id: 'test-cell',
    column: { id: 'test' },
    row: { id: 'row-1' },
  } as any;

  const mockTable = {
    setEditingCell: vi.fn(),
  };

  const mockOnSavingCell = vi.fn();

  beforeEach(() => {
    vi.spyOn(WorkspaceContext, 'useWorkspaceContext').mockReturnValue({
      onSavingCell: mockOnSavingCell,
    } as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders with initial value from cell', () => {
    renderWithMantine(<LongTextCell cell={mockCell} table={mockTable} />);

    const textarea = screen.getByRole('textbox');
    expect(textarea).toBeInTheDocument();
    expect(textarea).toHaveValue('test value');
  });

  it('updates value when typing', () => {
    renderWithMantine(<LongTextCell cell={mockCell} table={mockTable} />);

    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'new value' } });

    expect(textarea).toHaveValue('new value');
  });

  it('calls onSavingCell and setEditingCell on blur when value changed', () => {
    renderWithMantine(<LongTextCell cell={mockCell} table={mockTable} />);

    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'new value' } });
    fireEvent.blur(textarea);

    expect(mockOnSavingCell).toHaveBeenCalledWith('new value', mockCell);
    expect(mockTable.setEditingCell).toHaveBeenCalledWith(false);
  });

  it('does not call onSavingCell on blur when value has not changed', () => {
    renderWithMantine(<LongTextCell cell={mockCell} table={mockTable} />);

    const textarea = screen.getByRole('textbox');
    fireEvent.blur(textarea);

    expect(mockOnSavingCell).not.toHaveBeenCalled();
    expect(mockTable.setEditingCell).toHaveBeenCalledWith(false);
  });
});
