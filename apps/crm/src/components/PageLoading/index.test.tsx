import { renderWithMantine } from '@/tests/utils/testUtils';
import { describe, expect, it } from 'vitest';
import PageLoading from './index';

describe('PageLoading Component', () => {
  it('should be defined', () => {
    expect(PageLoading).toBeDefined();
    expect(typeof PageLoading).toBe('function');
  });

  it('should render without crashing', () => {
    expect(() => {
      renderWithMantine(<PageLoading />);
    }).not.toThrow();
  });

  it('should render Center component with correct properties', () => {
    const { container } = renderWithMantine(<PageLoading />);

    // Check if the Center component is rendered
    const centerElement = container.querySelector('.mantine-Center-root');
    expect(centerElement).toBeInTheDocument();
  });

  it('should render Loader component with default color', () => {
    const { container } = renderWithMantine(<PageLoading />);

    // Check if the Loader component is rendered
    const loaderElement = container.querySelector('.mantine-Loader-root');
    expect(loaderElement).toBeInTheDocument();
  });

  it('should render Loader with custom variant when provided', () => {
    const customVariant = 'dots';
    const { container } = renderWithMantine(<PageLoading variant={customVariant} />);

    // Check if the Loader component is rendered with custom variant
    const loaderElement = container.querySelector('.mantine-Loader-root');
    expect(loaderElement).toBeInTheDocument();
  });

  it('should render Loader without variant when not provided', () => {
    const { container } = renderWithMantine(<PageLoading />);

    // Check if the Loader component is rendered with default settings
    const loaderElement = container.querySelector('.mantine-Loader-root');
    expect(loaderElement).toBeInTheDocument();
  });

  it('should accept any props as component uses any type', () => {
    const customProps = {
      variant: 'bars',
      extraProp: 'test',
      anotherProp: 123,
    };

    expect(() => {
      renderWithMantine(<PageLoading {...customProps} />);
    }).not.toThrow();
  });
});
