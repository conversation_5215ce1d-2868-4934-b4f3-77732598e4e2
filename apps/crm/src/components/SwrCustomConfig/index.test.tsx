import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import SwrCustomConfig from './index';

describe('SwrCustomConfig Component', () => {
  it('should be defined', () => {
    expect(SwrCustomConfig).toBeDefined();
    expect(typeof SwrCustomConfig).toBe('function');
  });

  it('should render without crashing', () => {
    expect(() => {
      renderWithMantine(
        <SwrCustomConfig>
          <div>Test content</div>
        </SwrCustomConfig>
      );
    }).not.toThrow();
  });

  it('should render children correctly', () => {
    const testContent = 'Test content for SWR config';
    renderWithMantine(
      <SwrCustomConfig>
        <div data-testid='test-child'>{testContent}</div>
      </SwrCustomConfig>
    );

    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText(testContent)).toBeInTheDocument();
  });

  it('should wrap children with SWRConfig', () => {
    const { container } = renderWithMantine(
      <SwrCustomConfig>
        <div>Test content</div>
      </SwrCustomConfig>
    );

    expect(container.firstChild).toBeInTheDocument();
  });

  it('should render multiple children correctly', () => {
    renderWithMantine(
      <SwrCustomConfig>
        <div data-testid='child-1'>Child 1</div>
        <div data-testid='child-2'>Child 2</div>
        <span data-testid='child-3'>Child 3</span>
      </SwrCustomConfig>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByTestId('child-3')).toBeInTheDocument();
  });

  it('should handle empty children', () => {
    expect(() => {
      renderWithMantine(<SwrCustomConfig>{null}</SwrCustomConfig>);
    }).not.toThrow();
  });

  it('should handle undefined children', () => {
    expect(() => {
      renderWithMantine(<SwrCustomConfig>{undefined}</SwrCustomConfig>);
    }).not.toThrow();
  });
});
