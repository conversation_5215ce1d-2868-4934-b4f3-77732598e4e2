import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { ObjectColumn } from '@/models/workspace';
import { AssetAPI, UploadAPI } from '@/services/api';
import { validateInput } from '@/utils/workspace';
import { Box, Checkbox, FileButton, Radio, TextInput, rem } from '@mantine/core';
import { CustomImageBackground, ErrorMessage } from '@resola-ai/ui';
import { FieldTypes } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { type ProfileData, RenderProfileTypes } from '../Profile/ObjectFields/RenderProfileTypes';
import {
  ALLOWED_OVERWRITE_TYPES,
  OverwriteValue,
  useMergeProfileContext,
} from './MergeProfileContext';
import { useMergeStyles } from './useMergeStyles';

export const MergeTableRow = ({ col }: { col: ObjectColumn }) => {
  const { t } = useTranslate('workspace');
  const { selectedRowDetails } = useWorkspaceContext();
  const {
    mergeSelectionData,
    handleSelect,
    overwriteRecord,
    handleOverwriteChange,
    setIsLoading,
    inputErrors,
    setInputErrors,
  } = useMergeProfileContext();
  const { classes } = useMergeStyles();
  const { id = '', type } = col;
  const [value, setValue] = useState(overwriteRecord[id] || '');

  useEffect(() => {
    setValue(overwriteRecord[id] || '');
  }, [overwriteRecord]);

  const handleChange = async (file: File | null) => {
    if (!file) return;
    setIsLoading(true);
    try {
      const { file: asset, uploadUrl } = await AssetAPI.save(file);
      await UploadAPI.update({ file, url: uploadUrl });
      handleOverwriteChange(id, asset.url);
    } catch (e) {
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = useCallback(
    (e) => {
      const value = e.target.value;
      setValue(value);
      const error =
        value === '' ? undefined : validateInput(value, type, t, col.options?.numberFormat);
      setInputErrors({
        ...inputErrors,
        [id]: error,
      });
      if (!error) {
        handleOverwriteChange(id, value);
      }
    },
    [inputErrors]
  );

  const placeHolder = useMemo(() => {
    switch (type) {
      case FieldTypes.SINGLE_LINE_TEXT:
        return t('upTo50Characters');
      case FieldTypes.PHONE_NUMBER:
        return t('8to15Digits');
      case FieldTypes.EMAIL:
        return '<EMAIL>';
      case FieldTypes.URL:
        return 'https://example.com';
      default:
        return '';
    }
  }, [type, t]);

  return (
    <tr data-testid={`merge-table-row-${id}`}>
      <td style={{ width: '19%' }}>{col.name}</td>
      {selectedRowDetails.map((record) => (
        <td key={record.id} style={{ width: '27%' }}>
          {type === FieldTypes.MULTI_SELECT ? (
            <Checkbox
              data-testid={`merge-checkbox-${id}-${record.id}`}
              checked={mergeSelectionData[id]?.id?.includes(record.id)}
              onChange={() => handleSelect(id, type, record.id)}
              label={
                <Box ml={rem(32)}>
                  <RenderProfileTypes field={{ ...col, mapValue: record[id] } as ProfileData} />
                </Box>
              }
              value={record.id}
            />
          ) : (
            <Radio
              data-testid={`merge-radio-${id}-${record.id}`}
              checked={(mergeSelectionData[id]?.id || record.id) === record.id}
              onChange={() => handleSelect(id, type, record.id)}
              value={record.id}
              label={
                <Box ml={rem(32)}>
                  <RenderProfileTypes
                    field={{ ...col, mapValue: record[id] } as ProfileData}
                    imageRounded={true}
                  />
                </Box>
              }
              name={id}
            />
          )}
        </td>
      ))}
      <td style={{ width: '27%' }}>
        {ALLOWED_OVERWRITE_TYPES.includes(type as string) && (
          <Radio
            data-testid={`merge-radio-${id}-overwrite`}
            name={id}
            value={OverwriteValue}
            checked={mergeSelectionData[id]?.id === OverwriteValue}
            onChange={() => handleSelect(id, type, OverwriteValue)}
            label={
              type === FieldTypes.IMAGE ? (
                <FileButton
                  onChange={handleChange}
                  accept='image/png,image/jpeg'
                  data-testid={`merge-file-button-${id}`}
                >
                  {(props) => (
                    <Box {...props} className={classes.link}>
                      {overwriteRecord[id] ? (
                        <CustomImageBackground
                          {...props}
                          height={rem(28)}
                          width={rem(28)}
                          url={overwriteRecord[id]}
                          rounded={true}
                        />
                      ) : (
                        <> {t('uploadImage')}</>
                      )}
                    </Box>
                  )}
                </FileButton>
              ) : (
                <>
                  <TextInput
                    data-testid={`merge-text-input-${id}`}
                    autoFocus
                    placeholder={placeHolder}
                    value={value}
                    onChange={(e) => handleInputChange(e)}
                  />
                  {inputErrors[id] && <ErrorMessage mb={rem(10)} message={inputErrors[id] || ''} />}
                </>
              )
            }
          />
        )}
      </td>
    </tr>
  );
};
