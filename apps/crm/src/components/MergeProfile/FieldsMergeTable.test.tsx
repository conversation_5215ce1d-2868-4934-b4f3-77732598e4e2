import type { ObjectColumn } from '@/models/workspace';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { FieldsMergeTable } from './FieldsMergeTable';
import { MergeProfileContextProvider } from './MergeProfileContext';

// Mock required hooks and components
const mockUseWorkspaceContext = vi.fn();
const mockUseMergeProfileContext = vi.fn();
const mockUseTranslate = vi.fn();

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => mockUseWorkspaceContext(),
}));

vi.mock('./MergeProfileContext', () => ({
  MergeProfileContextProvider: ({ children }) => children,
  useMergeProfileContext: () => mockUseMergeProfileContext(),
  ALLOWED_OVERWRITE_TYPES: ['text', 'multi-select', 'number', 'phone', 'email', 'url', 'image'],
  OverwriteValue: 'overwrite',
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => mockUseTranslate(),
}));

// Mock MergeTableRow component
vi.mock('./MergeTableRow', () => ({
  MergeTableRow: ({ col }) => <div data-testid={`merge-table-row-${col.id}`}>{col.name}</div>,
}));

// Mock useMergeStyles
vi.mock('./useMergeStyles', () => ({
  useMergeStyles: () => ({
    classes: {
      tableWrapper: 'tableWrapper',
      table: 'table',
      link: 'link',
    },
  }),
}));

describe('FieldsMergeTable Component', () => {
  const mockColumns: ObjectColumn[] = [
    {
      id: 'field1',
      name: 'Field 1',
      type: 'text',
      options: { isPrimary: true },
      header: 'Field 1',
    },
    {
      id: 'field2',
      name: 'Field 2',
      type: 'number',
      options: { isPrimary: false },
      header: 'Field 2',
    },
    {
      id: 'field3',
      name: 'Field 3',
      type: FieldTypes.CREATED_BY,
      options: { isPrimary: false },
      header: 'Field 3',
    },
  ];

  const mockProfileNames = [
    { id: 'record-1', name: 'Record 1' },
    { id: 'record-2', name: 'Record 2' },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseWorkspaceContext.mockReturnValue({
      columns: mockColumns,
    });

    mockUseMergeProfileContext.mockReturnValue({
      profileNames: mockProfileNames,
      handleSelectAll: vi.fn(),
    });

    mockUseTranslate.mockReturnValue({
      t: (key: string) => key,
    });
  });

  it('renders the table with header and allowed merge columns', () => {
    renderWithMantine(
      <MergeProfileContextProvider>
        <FieldsMergeTable />
      </MergeProfileContextProvider>
    );

    // Check if table is rendered
    expect(screen.getByTestId('fields-merge-table')).toBeInTheDocument();
    expect(screen.getByTestId('fields-merge-table-table')).toBeInTheDocument();

    // Check if profile names are rendered in header
    mockProfileNames.forEach((profile) => {
      expect(screen.getByText(profile.name)).toBeInTheDocument();
    });

    // Check if "Select All" links are rendered
    mockProfileNames.forEach((profile) => {
      expect(screen.getByTestId(`merge-table-row-${profile.id}`)).toBeInTheDocument();
    });
    expect(screen.getByTestId('merge-table-row-overwrite')).toBeInTheDocument();

    // Check if only allowed merge columns are rendered (excluding CREATED_BY type)
    expect(screen.getByTestId('merge-table-row-field1')).toBeInTheDocument();
    expect(screen.getByTestId('merge-table-row-field2')).toBeInTheDocument();
    expect(screen.queryByTestId('merge-table-row-field3')).not.toBeInTheDocument();
  });

  it('handles select all functionality', () => {
    const handleSelectAll = vi.fn();
    mockUseMergeProfileContext.mockReturnValue({
      profileNames: mockProfileNames,
      handleSelectAll,
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <FieldsMergeTable />
      </MergeProfileContextProvider>
    );

    // Click on select all for first profile
    const selectAllLink = screen.getByTestId('merge-table-row-record-1');
    selectAllLink.click();
    expect(handleSelectAll).toHaveBeenCalledWith('record-1');

    // Click on select all for overwrite
    const overwriteSelectAllLink = screen.getByTestId('merge-table-row-overwrite');
    overwriteSelectAllLink.click();
    expect(handleSelectAll).toHaveBeenCalledWith('overwrite');
  });

  it('renders no rows when no columns are provided', () => {
    mockUseWorkspaceContext.mockReturnValue({
      columns: [],
    });

    renderWithMantine(
      <MergeProfileContextProvider>
        <FieldsMergeTable />
      </MergeProfileContextProvider>
    );

    // Check if table structure is still rendered
    expect(screen.getByTestId('fields-merge-table')).toBeInTheDocument();
    expect(screen.getByTestId('fields-merge-table-table')).toBeInTheDocument();

    // Verify that no merge table rows are rendered
    mockColumns.forEach((col) => {
      expect(screen.queryByTestId(`merge-table-row-${col.id}`)).not.toBeInTheDocument();
    });
  });
});
