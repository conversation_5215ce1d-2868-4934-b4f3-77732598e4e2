import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { RecordAPI } from '@/services/api';
import { FieldTypes } from '@resola-ai/ui/components';
import { act, render, renderHook, screen } from '@testing-library/react';
import { useParams } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  KeepBothType,
  MergeProfileContextProvider,
  OverwriteValue,
  useMergeProfileContext,
} from './MergeProfileContext';

// Mock hooks
vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

// Mock API calls
vi.mock('@/services/api', () => ({
  RecordAPI: {
    mergeRecord: vi.fn(),
  },
}));

describe('MergeProfileContext', () => {
  const mockSelectedRowDetails = [
    {
      id: 'record-1',
      name: 'Record 1',
      field1: 'Value 1',
      field2: 100,
      recordIndex: 1,
    },
    {
      id: 'record-2',
      name: 'Record 2',
      field1: 'Value 2',
      field2: 200,
      recordIndex: 2,
    },
  ];

  const mockColumns = [
    {
      id: 'field1',
      name: 'Field 1',
      type: FieldTypes.SINGLE_LINE_TEXT,
      options: { isPrimary: true },
    },
    { id: 'field2', name: 'Field 2', type: FieldTypes.NUMBER, options: {} },
  ];

  const mockSetRowSelection = vi.fn();
  const mockMutateRecord = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock useParams
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-1',
      id: 'object-1',
    });

    // Mock useWorkspaceContext
    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      selectedRowDetails: mockSelectedRowDetails,
      columns: mockColumns,
      mutateRecord: mockMutateRecord,
      setRowSelection: mockSetRowSelection,
    });

    // Mock API calls
    (RecordAPI.mergeRecord as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({});
  });

  const TestComponent = () => {
    const context = useMergeProfileContext();
    return (
      <div>
        <h1>Merge Profile Test Component</h1>
        <div data-testid='selected-record-id'>{context.selectedRecordId}</div>
        <div data-testid='source-record-ids'>{context.sourceRecordIds?.join(',')}</div>
        <div data-testid='profile-names-count'>{context.profileNames?.length}</div>
        <button data-testid='select-all-btn' onClick={() => context.handleSelectAll('record-2')}>
          Select All Record 2
        </button>
        <button
          data-testid='select-field-btn'
          onClick={() => context.handleSelect('field1', FieldTypes.SINGLE_LINE_TEXT, 'record-2')}
        >
          Select Field 1 from Record 2
        </button>
        <button
          data-testid='select-preference-btn'
          onClick={() => context.handlePreferenceSelect('tags', KeepBothType)}
        >
          Keep Both Tags
        </button>
        <button data-testid='merge-btn' onClick={() => context.handleMerge()}>
          Merge Records
        </button>
        <button data-testid='close-btn' onClick={() => context.onCloseModalMerge()}>
          Close Merge Modal
        </button>
      </div>
    );
  };

  it('provides merge profile context to children', async () => {
    render(
      <MergeProfileContextProvider>
        <TestComponent />
      </MergeProfileContextProvider>
    );

    expect(screen.getByText('Merge Profile Test Component')).toBeInTheDocument();
    expect(screen.getByTestId('selected-record-id').textContent).toBe('record-1');
    expect(screen.getByTestId('source-record-ids').textContent).toBe('record-1,record-2');
    expect(screen.getByTestId('profile-names-count').textContent).toBe('2');
  });

  it('initializes correctly with default values', () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MergeProfileContextProvider>{children}</MergeProfileContextProvider>
    );

    const { result } = renderHook(() => useMergeProfileContext(), { wrapper });

    expect(result.current.mergeRecord).toEqual({});
    expect(result.current.selectedRecordId).toBe('record-1');
    expect(result.current.sourceRecordIds).toEqual(['record-1', 'record-2']);
    expect(result.current.profileNames).toHaveLength(2);
    expect(result.current.profileNames[0].id).toBe('record-1');
    expect(result.current.profileNames[1].id).toBe('record-2');
    expect(result.current.isLoading).toBe(false);
    expect(result.current.inputErrors).toEqual({});
  });

  it('handles record selection properly', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MergeProfileContextProvider>{children}</MergeProfileContextProvider>
    );

    const { result } = renderHook(() => useMergeProfileContext(), { wrapper });

    act(() => {
      result.current.handelSelectRecord('record-2');
    });

    expect(result.current.selectedRecordId).toBe('record-2');
  });

  it('handles field selection properly', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MergeProfileContextProvider>{children}</MergeProfileContextProvider>
    );

    const { result } = renderHook(() => useMergeProfileContext(), { wrapper });

    act(() => {
      result.current.handleSelect('field1', FieldTypes.SINGLE_LINE_TEXT, 'record-2');
    });

    expect(result.current.mergeSelectionData.field1.id).toBe('record-2');
  });

  it('handles overwrite selection properly', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MergeProfileContextProvider>{children}</MergeProfileContextProvider>
    );

    const { result } = renderHook(() => useMergeProfileContext(), { wrapper });

    act(() => {
      result.current.handleSelect('field1', FieldTypes.SINGLE_LINE_TEXT, OverwriteValue);
    });

    expect(result.current.mergeSelectionData.field1.id).toBe(OverwriteValue);
  });

  it('handles preference selection properly', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MergeProfileContextProvider>{children}</MergeProfileContextProvider>
    );

    const { result } = renderHook(() => useMergeProfileContext(), { wrapper });

    act(() => {
      result.current.handlePreferenceSelect('tags', KeepBothType);
    });

    expect(result.current.mergePreferencesData.tags.type).toBe(KeepBothType);
  });

  it('handles merge operation successfully', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MergeProfileContextProvider>{children}</MergeProfileContextProvider>
    );

    const { result } = renderHook(() => useMergeProfileContext(), { wrapper });

    await act(async () => {
      await result.current.handleMerge();
    });

    expect(RecordAPI.mergeRecord).toHaveBeenCalledTimes(1);
    expect(RecordAPI.mergeRecord).toHaveBeenCalledWith(
      'workspace-1',
      'object-1',
      'record-1',
      expect.objectContaining({
        removeRecordIds: ['record-2'],
      })
    );
    expect(mockSetRowSelection).toHaveBeenCalledWith({});
    expect(mockMutateRecord).toHaveBeenCalled();
  });

  it('resets state when modal is closed', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MergeProfileContextProvider>{children}</MergeProfileContextProvider>
    );

    const { result } = renderHook(() => useMergeProfileContext(), { wrapper });

    // First select record-2
    act(() => {
      result.current.handelSelectRecord('record-2');
    });
    expect(result.current.selectedRecordId).toBe('record-2');

    // Then close the modal
    act(() => {
      result.current.onCloseModalMerge();
    });

    // Should reset to first record
    expect(result.current.selectedRecordId).toBe('record-1');
    expect(result.current.inputErrors).toEqual({});
  });

  it('handles overwrite change correctly', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MergeProfileContextProvider>{children}</MergeProfileContextProvider>
    );

    const { result } = renderHook(() => useMergeProfileContext(), { wrapper });

    act(() => {
      result.current.handleOverwriteChange('field1', 'Custom Value');
    });

    expect(result.current.overwriteRecord.field1).toBe('Custom Value');
  });

  it('throws error when used outside provider', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => renderHook(() => useMergeProfileContext())).toThrow(
      'useMergeProfileContext must be used inside MergeProfileContextProvider'
    );

    consoleSpy.mockRestore();
  });
});
