import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { MergeProfileContextProvider, Preferences } from './MergeProfileContext';
import { PreferencesMergeTable } from './PreferencesMergeTable';

// Mock required hooks and components
const mockUseWorkspaceContext = vi.fn();
const mockUseMergeProfileContext = vi.fn();

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => mockUseWorkspaceContext(),
}));

vi.mock('./MergeProfileContext', () => ({
  useMergeProfileContext: () => mockUseMergeProfileContext(),
  MergeProfileContextProvider: ({ children }) => children,
  Preferences: ['tags', 'language', 'timezone'],
  KeepBothType: 'merge',
  SelectType: 'select',
}));

describe('PreferencesMergeTable', () => {
  const mockProfileNames = [
    { id: 'record-1', name: 'Profile 1' },
    { id: 'record-2', name: 'Profile 2' },
  ];

  const mockSourceRecordIds = ['record-1', 'record-2'];
  const mockHandlePreferenceSelect = vi.fn();
  const mockMergePreferencesData = {
    tags: { sourceRecordId: 'record-1' },
    language: { sourceRecordId: 'record-2' },
    timezone: { sourceRecordId: null },
  };

  beforeEach(() => {
    mockUseWorkspaceContext.mockReturnValue({
      selectedRowDetails: [
        { id: 'record-1', recordIndex: 1 },
        { id: 'record-2', recordIndex: 2 },
      ],
    });

    mockUseMergeProfileContext.mockReturnValue({
      handlePreferenceSelect: mockHandlePreferenceSelect,
      mergePreferencesData: mockMergePreferencesData,
      sourceRecordIds: mockSourceRecordIds,
      profileNames: mockProfileNames,
    });
  });

  it('renders the table with correct structure', () => {
    renderWithMantine(
      <MergeProfileContextProvider>
        <PreferencesMergeTable />
      </MergeProfileContextProvider>
    );

    // Check if table headers are rendered correctly
    expect(screen.getByText('Profile 1')).toBeInTheDocument();
    expect(screen.getByText('Profile 2')).toBeInTheDocument();
    expect(screen.getByText('keepBoth')).toBeInTheDocument();

    // Check if preference rows are rendered
    Preferences.forEach((pref) => {
      expect(screen.getByTestId(`preference-row-${pref}`)).toBeInTheDocument();
    });
  });

  it('displays correct radio button states based on mergePreferencesData', () => {
    renderWithMantine(
      <MergeProfileContextProvider>
        <PreferencesMergeTable />
      </MergeProfileContextProvider>
    );

    // Check if radio buttons reflect the correct state from mergePreferencesData
    const tagsRow = screen.getByTestId('preference-row-tags');
    const languageRow = screen.getByTestId('preference-row-language');
    const timezoneRow = screen.getByTestId('preference-row-timezone');

    expect(tagsRow.querySelector('input[value="record-1"]')).toBeChecked();
    expect(languageRow.querySelector('input[value="record-2"]')).toBeChecked();
    expect(timezoneRow.querySelector('input[value="merge"]')).toBeChecked();
  });
});
