import { Box, Radio, Table } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import {
  KeepBothType,
  Preferences,
  SelectType,
  useMergeProfileContext,
} from './MergeProfileContext';
import { useMergeStyles } from './useMergeStyles';

const Row = ({ id }: { id: string }) => {
  const { t } = useTranslate('workspace');
  const { handlePreferenceSelect, mergePreferencesData, sourceRecordIds } =
    useMergeProfileContext();

  return (
    <tr data-testid={`preference-row-${id}`}>
      <td style={{ width: '19%' }}>{t(id)}</td>
      {sourceRecordIds.map((sourceId) => (
        <td key={sourceId} style={{ width: '27%' }}>
          <Radio
            checked={mergePreferencesData[id]?.sourceRecordId === sourceId}
            onChange={() => handlePreferenceSelect(id, SelectType, sourceId)}
            value={sourceId}
            name={`preference-${id}`}
          />
        </td>
      ))}
      <td style={{ width: '27%' }}>
        <Radio
          name={`preference-${id}`}
          value={KeepBothType}
          checked={!mergePreferencesData[id]?.sourceRecordId}
          onChange={() => handlePreferenceSelect(id, KeepBothType)}
        />
      </td>
    </tr>
  );
};

export const PreferencesMergeTable = () => {
  const { classes, cx } = useMergeStyles();
  const { t } = useTranslate('workspace');
  const { profileNames } = useMergeProfileContext();

  return (
    <Box className={classes.tableWrapper}>
      <Table className={cx(classes.table, classes.preferencesTable)}>
        <thead>
          <tr>
            <th />
            {profileNames.map(({ id, name }) => (
              <th key={id}>{name}</th>
            ))}
            <th>{t('keepBoth')}</th>
          </tr>
        </thead>
        <tbody>
          {Preferences.map((id) => (
            <Row key={id} id={id} />
          ))}
        </tbody>
      </Table>
    </Box>
  );
};
