import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import {
  Box,
  Drawer,
  Flex,
  Group,
  LoadingOverlay,
  Radio,
  ScrollArea,
  Text,
  rem,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { DecaButton, Modal } from '@resola-ai/ui';
import { InfoAlert } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import { FieldsMergeTable } from './FieldsMergeTable';
import { MergeProfileContextProvider, useMergeProfileContext } from './MergeProfileContext';
import { PreferencesMergeTable } from './PreferencesMergeTable';
import { useMergeStyles } from './useMergeStyles';

const MergeProfileSettings = ({ opened, close }: { opened: boolean; close: () => void }) => {
  const { t } = useTranslate('workspace');
  const { classes } = useMergeStyles();
  const { rowSelection } = useWorkspaceContext();
  const {
    handleMerge,
    isLoading,
    setIsLoading,
    profileNames,
    handelSelectRecord,
    selectedRecordId,
    inputErrors,
    onCloseModalMerge,
  } = useMergeProfileContext();
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);
  const hasErrors = !!Object.values(inputErrors).filter(Boolean).length;

  const onClose = () => {
    onCloseModalMerge();
    setIsLoading(false);
    closeModal();
  };

  return (
    <>
      <Drawer
        data-testid='merge-profile-drawer'
        withOverlay={false}
        title='Merge Profile'
        position='bottom'
        opened={opened}
        onClose={close}
        className={classes.drawer}
      >
        <Flex justify='space-between' align='center'>
          <Flex gap={rem(16)}>
            <DecaButton data-testid='drawer-merge-button' onClick={openModal}>
              {t('merge')}
            </DecaButton>
            <DecaButton data-testid='drawer-cancel-button' variant='neutral' onClick={close}>
              {t('cancel')}
            </DecaButton>
          </Flex>
          <Text data-testid='record-selected-count'>
            {t('recordSelected', { num: Object.keys(rowSelection).length })}
          </Text>
        </Flex>
      </Drawer>
      <Modal
        data-testid='merge-profile-modal'
        size={'90vw'}
        opened={modalOpened}
        onClose={() => {
          onClose();
          setIsLoading(false);
        }}
        title={t('mergeRecord')}
        centered
        closeOnClickOutside
        onOk={() => {
          if (!hasErrors) {
            handleMerge();
            closeModal();
          }
        }}
        okText={t('merge')}
        cancelText={t('cancel')}
        onCancel={onClose}
        okButtonProps={{ disabled: hasErrors }}
      >
        <Box data-testid='merge-profile-modal-content'>
          {<LoadingOverlay visible={isLoading} data-testid='merge-profile-modal-loading' />}
          <ScrollArea.Autosize mah={`calc(80vh - ${rem(80)})`}>
            <Text fz={rem(18)}>{t('selectMasterRecord')}</Text>
            <InfoAlert description={t('mergeProfileDescription')} />
            <Box className={classes.radioGroup} mt={rem(20)} mb={rem(32)}>
              <Radio.Group
                data-testid='radio-group'
                value={selectedRecordId}
                onChange={(val) => {
                  handelSelectRecord(val);
                }}
              >
                <Group sx={{ gap: rem(0) }}>
                  <Box w={'19%'} />
                  {profileNames.map(({ id, name }) => (
                    <Radio
                      data-testid={`master-record-radio-${id}`}
                      w={'27%'}
                      key={id}
                      value={id}
                      label={name}
                      px={rem(12)}
                      sx={{
                        '.mantine-Radio-label': { marginLeft: rem(32), fontSize: rem(14) },
                      }}
                    />
                  ))}
                </Group>
              </Radio.Group>
            </Box>
            <Box>
              <Text mb={rem(20)} fz={rem(18)}>
                {t('selectFieldsToMerge')}
              </Text>
              <FieldsMergeTable />
            </Box>
            <Box mt={rem(20)}>
              <Text mb={rem(20)} fz={rem(18)}>
                {t('selectPreferences')}
              </Text>
              <PreferencesMergeTable />
            </Box>
          </ScrollArea.Autosize>
        </Box>
      </Modal>
    </>
  );
};

export const MergeProfile = ({ opened, close }: { opened: boolean; close: () => void }) => {
  return (
    <MergeProfileContextProvider>
      <MergeProfileSettings opened={opened} close={close} />
    </MergeProfileContextProvider>
  );
};
