import { PREFERENCES } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { Record } from '@/models';
import { RecordAPI } from '@/services/api';
import { FieldTypes } from '@resola-ai/ui/components';
import type React from 'react';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

export const ALLOWED_OVERWRITE_TYPES: string[] = [
  FieldTypes.SINGLE_LINE_TEXT,
  FieldTypes.PHONE_NUMBER,
  FieldTypes.EMAIL,
  FieldTypes.URL,
  FieldTypes.CURRENCY,
  FieldTypes.PERCENT,
  FieldTypes.LONG_TEXT,
  FieldTypes.NUMBER,
  FieldTypes.IMAGE,
];

export const OverwriteValue = 'overwrite';
export const KeepBothType = 'merge';
export const SelectType = 'select';
export const Preferences = [
  ...Object.values(PREFERENCES).filter((pref) => pref !== PREFERENCES.longText),
  'tags',
];

export interface SelectedMergeRecord {
  id: string;
  type: string;
}

const useMergeProfile = () => {
  const { selectedRowDetails, columns, mutateRecord, setRowSelection } = useWorkspaceContext();
  const [mergeRecord, setMergeRecord] = useState<Record>({} as Record);
  const [mergeSelectionData, setMergeSelectionData] = useState<any>({});
  const [mergePreferencesData, setMergePreferencesData] = useState<any>({});
  const [overwriteRecord, setOverwriteRecord] = useState<Record>({ id: OverwriteValue } as Record);
  const [isLoading, setIsLoading] = useState(false);
  const sourceRecordIds = selectedRowDetails?.map((record) => record?.id);
  const [selectedRecordId, setSelectedRecordId] = useState<string>(sourceRecordIds?.[0]);
  const { wsId, id } = useParams();
  const [inputErrors, setInputErrors] = useState<{
    [key: string]: string | undefined;
  }>({});

  const convertToObject = (recordValues) => {
    return recordValues.reduce((acc, obj) => {
      const [key, value] = Object.entries(obj)[0];
      acc[key] = value;
      return acc;
    }, {});
  };
  const initOverwriteRecord = useCallback(() => {
    setOverwriteRecord({
      id: OverwriteValue,
      ...convertToObject(columns.map((col) => ({ [col.id]: undefined }))),
    });
  }, [columns]);

  useEffect(() => {
    initOverwriteRecord();
  }, [initOverwriteRecord]);

  useEffect(() => {
    if (selectedRowDetails?.length) {
      setMergePreferencesData(
        Preferences.reduce((acc, key) => {
          acc[key] = {
            type: SelectType,
            sourceRecordId: selectedRowDetails[0].id,
          };
          return acc;
        }, {})
      );
    }
  }, [selectedRowDetails]);

  const handleOverwriteChange = (fieldId: string, value: string | number) => {
    setOverwriteRecord({
      ...overwriteRecord,
      [fieldId]: value,
    });
  };

  useEffect(() => {
    const _selectedMergeRecord = convertToObject(
      columns.map((col) => ({
        [col.id]: {
          id: selectedRowDetails[0].id,
          type: col.type,
        },
      }))
    );
    setMergeSelectionData(_selectedMergeRecord);
  }, [selectedRowDetails]);

  const handelSelectRecord = (recordId: string) => {
    setSelectedRecordId(recordId);
    handleSelectAll(recordId);
    setMergePreferencesData(
      Preferences.reduce((acc, key) => {
        acc[key] = {
          ...mergePreferencesData[key],
          sourceRecordId: recordId,
        };
        return acc;
      }, {})
    );
  };

  const handleSelectAll = (recordId: string) => {
    const isOverwrite = recordId === OverwriteValue;
    const _selectedMergeRecord = convertToObject(
      Object.keys(mergeSelectionData).map((key) => {
        const currentData = mergeSelectionData[key];
        const allowOverwrite = ALLOWED_OVERWRITE_TYPES.includes(currentData.type);
        return {
          [key]: {
            ...currentData,
            id: isOverwrite ? (allowOverwrite ? overwriteRecord.id : currentData.id) : recordId,
          },
        };
      })
    );
    setMergeSelectionData(_selectedMergeRecord);
  };

  const handleSelect = (fieldId: string, fieldType: string, recordId: string) => {
    if (recordId === OverwriteValue) {
      setMergeSelectionData({
        ...mergeSelectionData,
        [fieldId]: {
          ...mergeSelectionData[fieldId],
          id: overwriteRecord.id,
        },
      });
      return;
    }
    if (fieldType === FieldTypes.MULTI_SELECT) {
      const currentValueIds = mergeSelectionData[fieldId].id ?? '';
      if (currentValueIds.includes(recordId)) {
        setMergeSelectionData({
          ...mergeSelectionData,
          [fieldId]: {
            ...mergeSelectionData[fieldId],
            id: currentValueIds.replace(recordId, ''),
          },
        });
      } else {
        setMergeSelectionData({
          ...mergeSelectionData,
          [fieldId]: {
            ...mergeSelectionData[fieldId],
            id: `${currentValueIds}, ${recordId}`,
          },
        });
      }
    } else {
      setMergeSelectionData({
        ...mergeSelectionData,
        [fieldId]: {
          ...mergeSelectionData[fieldId],
          id: recordId,
        },
      });
    }
  };
  const handlePreferenceSelect = (
    preferenceId: string,
    type: string,
    sourceRecordId?: string | undefined
  ) => {
    setMergePreferencesData({
      ...mergePreferencesData,
      [preferenceId]: {
        ...mergePreferencesData[preferenceId],
        type: type === KeepBothType ? KeepBothType : SelectType,
        sourceRecordId,
      },
    });
  };

  const handleMerge = async () => {
    const objectSelectedRowDetails = [...selectedRowDetails, overwriteRecord].reduce((acc, obj) => {
      acc[obj.id] = obj;
      return acc;
    }, {});

    const result = Object.keys(mergeSelectionData).reduce((acc, key) => {
      const id = mergeSelectionData[key].id;
      if (mergeSelectionData[key].type === FieldTypes.MULTI_SELECT) {
        const ids = id.split(',').filter((id) => id.trim());
        acc[key] = [...new Set(ids.flatMap((id) => objectSelectedRowDetails[id.trim()][key]))];
      } else {
        acc[key] = objectSelectedRowDetails[id][key];
      }
      return acc;
    }, {});
    const payload = {
      data: result,
      removeRecordIds: sourceRecordIds.filter((id) => id !== selectedRecordId),
      options: mergePreferencesData,
    };
    await RecordAPI.mergeRecord(wsId || '', id || '', selectedRecordId, payload);
    setRowSelection({});
    mutateRecord();
  };

  const onCloseModalMerge = useCallback(() => {
    setInputErrors({});
    handelSelectRecord(sourceRecordIds?.[0]);
    initOverwriteRecord();
  }, [sourceRecordIds]);

  const profileNames = useMemo(() => {
    const primaryFieldId = columns.find(
      (col) => col.options?.isPrimary && col.type === FieldTypes.SINGLE_LINE_TEXT
    )?.id;
    return selectedRowDetails.map((record) => {
      return {
        id: record?.id,
        name:
          primaryFieldId && record[primaryFieldId]
            ? record[primaryFieldId]
            : `${record?.recordIndex + 1}`,
      };
    });
  }, [selectedRowDetails, columns]);

  return {
    mergeRecord,
    mergeSelectionData,
    setMergeRecord,
    handleSelectAll,
    handleSelect,
    overwriteRecord,
    handleOverwriteChange,
    handleMerge,
    isLoading,
    setIsLoading,
    mergePreferencesData,
    handlePreferenceSelect,
    sourceRecordIds,
    profileNames,
    selectedRecordId,
    handelSelectRecord,
    inputErrors,
    setInputErrors,
    onCloseModalMerge,
  };
};

export type ProfileContextType = ReturnType<typeof useMergeProfile>;

const context = createContext<ProfileContextType | null>(null);

export const MergeProfileContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useMergeProfile();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useMergeProfileContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useMergeProfileContext must be used inside MergeProfileContextProvider');
  }

  return value;
};
