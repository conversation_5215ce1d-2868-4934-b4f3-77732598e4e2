import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { Box, Table } from '@mantine/core';
import { FieldTypes } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import { OverwriteValue, useMergeProfileContext } from './MergeProfileContext';
import { MergeTableRow } from './MergeTableRow';
import { useMergeStyles } from './useMergeStyles';

export const FieldsMergeTable = () => {
  const { classes } = useMergeStyles();
  const { columns } = useWorkspaceContext();
  const { handleSelectAll, profileNames } = useMergeProfileContext();
  const { t } = useTranslate('workspace');

  const allowMergeColumns = columns.filter(
    (col) =>
      ![
        FieldTypes.CREATED_BY,
        FieldTypes.MODIFIED_BY,
        FieldTypes.CREATED_TIME,
        FieldTypes.MODIFIED_TIME,
      ].includes(col.type as any)
  );

  return (
    <Box className={classes.tableWrapper} data-testid='fields-merge-table'>
      <Table className={classes.table} data-testid='fields-merge-table-table'>
        <thead>
          <tr>
            <th />
            {profileNames.map(({ id, name }) => (
              <th key={id}>
                {name}
                <Box
                  data-testid={`merge-table-row-${id}`}
                  className={classes.link}
                  onClick={() => handleSelectAll(id)}
                >
                  {t('selectAll')}
                </Box>
              </th>
            ))}
            <th>
              {t('overwrite')}
              <Box
                data-testid='merge-table-row-overwrite'
                className={classes.link}
                onClick={() => handleSelectAll(OverwriteValue)}
              >
                {t('selectAll')}
              </Box>
            </th>
          </tr>
        </thead>
        <tbody>
          {allowMergeColumns.map((col) => (
            <MergeTableRow key={col.id} col={col} />
          ))}
        </tbody>
      </Table>
    </Box>
  );
};
