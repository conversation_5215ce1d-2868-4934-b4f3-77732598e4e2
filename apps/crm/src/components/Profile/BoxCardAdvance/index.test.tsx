import type { BoxCard } from '@/models';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { DndContext } from '@dnd-kit/core';
import { screen } from '@testing-library/react';
import { vi } from 'vitest';
import BoxCardAdvance from './index';

const mockBoxCard: BoxCard = {
  id: 'test-box-1',
  height: '300',
};

const mockOnChangeSize = vi.fn();

const renderWithDnd = (ui: React.ReactNode) => {
  return renderWithMantine(<DndContext>{ui}</DndContext>);
};

describe('BoxCardAdvance', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with minimum required props', () => {
    renderWithDnd(
      <BoxCardAdvance recordId='record-1' boxCard={mockBoxCard} onChangeSize={mockOnChangeSize}>
        Test Content
      </BoxCardAdvance>
    );

    expect(screen.getByTestId(`box-card-${mockBoxCard.id}`)).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders with drag handle when allowDrag is true', () => {
    renderWithDnd(
      <BoxCardAdvance
        recordId='record-1'
        boxCard={mockBoxCard}
        onChangeSize={mockOnChangeSize}
        allowDrag={true}
      >
        Test Content
      </BoxCardAdvance>
    );

    expect(screen.getByTestId(`box-card-${mockBoxCard.id}`)).toBeInTheDocument();
    expect(document.querySelector('svg')).toBeInTheDocument(); // Grip icon
  });

  it('does not render drag handle when allowDrag is false', () => {
    renderWithDnd(
      <BoxCardAdvance
        recordId='record-1'
        boxCard={mockBoxCard}
        onChangeSize={mockOnChangeSize}
        allowDrag={false}
      >
        Test Content
      </BoxCardAdvance>
    );

    expect(screen.getByTestId(`box-card-${mockBoxCard.id}`)).toBeInTheDocument();
    expect(document.querySelector('svg')).not.toBeInTheDocument();
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-class';
    renderWithDnd(
      <BoxCardAdvance
        recordId='record-1'
        boxCard={mockBoxCard}
        onChangeSize={mockOnChangeSize}
        className={customClass}
      >
        Test Content
      </BoxCardAdvance>
    );

    expect(screen.getByTestId(`box-card-${mockBoxCard.id}`)).toHaveClass(customClass);
  });
  it('respects API height when provided', () => {
    const smallHeightBoxCard: BoxCard = {
      id: 'test-box-2',
      height: '100', // Should respect this height from API
    };

    renderWithDnd(
      <BoxCardAdvance
        recordId='record-1'
        boxCard={smallHeightBoxCard}
        onChangeSize={mockOnChangeSize}
      >
        Test Content
      </BoxCardAdvance>
    );

    const boxCard = screen.getByTestId(`box-card-${smallHeightBoxCard.id}`);
    expect(boxCard).toHaveStyle({ height: '100px' }); // Should respect API height
  });
});
