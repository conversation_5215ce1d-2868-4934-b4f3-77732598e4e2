import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { IHistory } from '@/models';
import { HistoryAPI } from '@/services/api';
import { Avatar, Flex, List, Stack, Text, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import dayjs from 'dayjs';
import React, { useMemo, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import useSWR from 'swr';
import { type ProfileData, RenderProfileTypes } from '../ObjectFields/RenderProfileTypes';

const isFileAction = (action: string) => {
  return ['ATTACHMENT_CREATED', 'ATTACHMENT_DELETED'].includes(action);
};

const isImageUrl = (url: string) => {
  return url.match(/\.(jpeg|jpg|gif|png)$/) != null;
};

// Create a memoized sub-component for rendering single history item
const HistoryItem = React.memo(({ data }: { data: IHistory }) => {
  const { t } = useTranslate('workspace');
  const { object } = useWorkspaceContext();

  // Memoize action name to avoid recalculation
  const actionName = useMemo(() => {
    switch (data.action) {
      case 'UPDATE_FIELD':
        return t('updatedField');
      case 'CREATE_RECORD':
        return t('createdProfile');
      case 'ATTACHMENT_CREATED':
        return t('createdAttachment');
      case 'ATTACHMENT_DELETED':
        return t('deletedAttachment');
      case 'DELETE_RECORD':
        return t('deletedProfile');
      case 'UPDATE_RECORD':
        return t('updatedProfile');
      default:
        return '';
    }
  }, [data.action, t]);

  // Memoize date display
  const dateDisplay = useMemo(() => {
    if (!data.sentAt) return '';
    return dayjs().isSame(data.sentAt, 'day')
      ? `${t('today')} - ${dayjs(data.sentAt).format('hh:mm:ss A')}`
      : dayjs(data.sentAt).format('DD/MM/YYYY - hh:mm:ss A');
  }, [data.sentAt, t]);

  // Memoize field name lookup
  const getFieldName = useCallback(
    (fieldId: string) => {
      const field = object?.fields?.find((field) => field.id === fieldId);
      return field?.name || fieldId;
    },
    [object?.fields]
  );

  // Memoize value change renderer
  const getValueChange = useCallback(
    (fieldId: string, value: any) => {
      const field = object?.fields?.find((field) => field.id === fieldId);
      return <RenderProfileTypes field={{ ...field, mapValue: value } as ProfileData} />;
    },
    [object?.fields]
  );

  // Memoize value rendering
  const renderedValues = useMemo(() => {
    if (isFileAction(data.action)) {
      return data.options?.title || '';
    }

    const newValues = data.changes?.newValue;
    if (typeof newValues === 'object' && !Array.isArray(newValues)) {
      return Object.entries(newValues).map(([fieldId, value]) => {
        if (object?.fields?.find((field) => field.id === fieldId)?.name === undefined) return null;
        return (
          <List.Item key={fieldId}>
            <Flex gap='xs' align='center'>
              <Text>{getFieldName(fieldId)}:</Text>
              {isImageUrl(String(value)) ? (
                <Avatar src={String(value)} alt='avatar' size={'md'} />
              ) : (
                <Text lineClamp={10}>{getValueChange(fieldId, value)}</Text>
              )}
            </Flex>
          </List.Item>
        );
      });
    }
    if (typeof newValues === 'string') {
      return (
        <List.Item key={data.fieldId}>
          <Flex gap='xs' align='center'>
            <Text>{getFieldName(data.fieldId || '')}:</Text>
            <Text lineClamp={10}>{newValues}</Text>
          </Flex>
        </List.Item>
      );
    }

    return null;
  }, [data, object?.fields, getFieldName, getValueChange]);

  return (
    <Stack key={data.recordId} gap='xs'>
      <Text fz='lg' fw={500}>
        {dateDisplay}
      </Text>
      <Flex align={'center'} gap={'xs'}>
        <Avatar src={data.user?.picture} alt='avatar' size={'sm'} radius='xl' />
        <Text fz='md' fw={400}>
          <b>{data.user?.name}:</b> {actionName}
        </Text>
      </Flex>
      <List listStyleType='disc' withPadding>
        {renderedValues}
      </List>
    </Stack>
  );
});

// Set display name for the memoized component
HistoryItem.displayName = 'HistoryItem';

const History = () => {
  const { t } = useTranslate('workspace');
  const { wsId, id: objectId, recordId } = useParams();

  // Memoize the SWR key to prevent unnecessary refetches
  const swrKey = useMemo(() => {
    return wsId && objectId && recordId
      ? `data/workspaces/${wsId}/${objectId}/${recordId}/history`
      : null;
  }, [wsId, objectId, recordId]);

  // Fetch history data with SWR
  const { data: histories } = useSWR(
    swrKey,
    () =>
      HistoryAPI.getList(wsId || '', objectId || '', recordId || '').then((res) =>
        res?.sort((a, b) => new Date(b.sentAt || '').getTime() - new Date(a.sentAt || '').getTime())
      ),
    {
      revalidateOnMount: true,
    }
  );

  // Memoize the rendered history items
  const historyItems = useMemo(() => {
    return histories?.map((data, index) => (
      <HistoryItem key={`${data.recordId}-${index}`} data={data} />
    ));
  }, [histories]);

  return (
    <Stack gap='md'>
      <Text fz='xl' fw={500}>
        {t('history')}
      </Text>

      <Stack gap='md' mb={rem(50)}>
        {historyItems}
      </Stack>
    </Stack>
  );
};

export default React.memo(History);
