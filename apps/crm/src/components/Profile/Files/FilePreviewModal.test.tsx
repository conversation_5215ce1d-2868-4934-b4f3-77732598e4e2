import { useAttachmentId } from '@/hooks';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useParams } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ImagePreviewModal, PDFPreviewModal } from './FilePreviewModal';

// Mock hooks
vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

vi.mock('@/hooks', () => ({
  useAttachmentId: vi.fn(),
}));

// Mock react-pdf
vi.mock('react-pdf', () => ({
  Document: vi.fn(({ children, onLoadSuccess }) => (
    <div data-testid='pdf-document' onClick={() => onLoadSuccess?.({ numPages: 5 })}>
      {children}
    </div>
  )),
  Page: vi.fn(() => <div data-testid='pdf-page'>PDF Page</div>),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: '',
    },
    version: '3.0.0',
  },
}));

// Mock tolgee translate
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

describe('ImagePreviewModal Component', () => {
  const mockOnClose = vi.fn();
  const mockFileTitle = 'test-image.jpg';
  const mockFileId = 'image-123';
  const mockUrl = 'http://example.com/test-image.jpg';

  beforeEach(() => {
    vi.clearAllMocks();
    (useParams as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-123',
      id: 'object-123',
      recordId: 'record-123',
    });
    (useAttachmentId as ReturnType<typeof vi.fn>).mockReturnValue({
      attachment: undefined,
      url: mockUrl,
      isLoading: false,
      error: null,
    });
  });

  it('renders the modal when opened is true', () => {
    renderWithMantine(
      <ImagePreviewModal
        opened={true}
        onClose={mockOnClose}
        fileId={mockFileId}
        fileTitle={mockFileTitle}
      />
    );

    expect(screen.getByText(mockFileTitle)).toBeInTheDocument();
    expect(screen.getByRole('img')).toBeInTheDocument();
    expect(screen.getByRole('img')).toHaveAttribute('src', mockUrl);
    expect(screen.getByRole('img')).toHaveAttribute('alt', mockFileTitle);
  });

  it('does not render when opened is false', () => {
    renderWithMantine(
      <ImagePreviewModal
        opened={false}
        onClose={mockOnClose}
        fileId={mockFileId}
        fileTitle={mockFileTitle}
      />
    );

    expect(screen.queryByText(mockFileTitle)).not.toBeInTheDocument();
    expect(screen.queryByRole('img')).not.toBeInTheDocument();
  });

  it('shows loader when image is loading', () => {
    (useAttachmentId as ReturnType<typeof vi.fn>).mockReturnValue({
      attachment: undefined,
      url: '',
      isLoading: true,
      error: null,
    });

    renderWithMantine(
      <ImagePreviewModal
        opened={true}
        onClose={mockOnClose}
        fileId={mockFileId}
        fileTitle={mockFileTitle}
      />
    );

    expect(screen.getByText(mockFileTitle)).toBeInTheDocument();
    expect(screen.getByTestId('image-preview-modal-loader')).toBeInTheDocument();
    expect(screen.queryByRole('img')).not.toBeInTheDocument();
  });

  it('calls onClose when modal is closed', async () => {
    const user = userEvent.setup();
    renderWithMantine(
      <ImagePreviewModal
        opened={true}
        onClose={mockOnClose}
        fileId={mockFileId}
        fileTitle={mockFileTitle}
      />
    );

    const closeButton = screen.getByRole('button');
    await user.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });
});

describe('PDFPreviewModal Component', () => {
  const mockOnClose = vi.fn();
  const mockFileTitle = 'test-document.pdf';
  const mockFileId = 'pdf-123';
  const mockUrl = 'http://example.com/test-document.pdf';

  beforeEach(() => {
    vi.clearAllMocks();
    (useParams as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-123',
      id: 'object-123',
      recordId: 'record-123',
    });
    (useAttachmentId as ReturnType<typeof vi.fn>).mockReturnValue({
      attachment: undefined,
      url: mockUrl,
      isLoading: false,
      error: null,
    });
  });

  it('renders the modal when opened is true', () => {
    renderWithMantine(
      <PDFPreviewModal
        opened={true}
        onClose={mockOnClose}
        fileId={mockFileId}
        fileTitle={mockFileTitle}
      />
    );

    expect(screen.getByText(mockFileTitle)).toBeInTheDocument();
    expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
    expect(screen.getByTestId('pdf-page')).toBeInTheDocument();
  });

  it('does not render when opened is false', () => {
    renderWithMantine(
      <PDFPreviewModal
        opened={false}
        onClose={mockOnClose}
        fileId={mockFileId}
        fileTitle={mockFileTitle}
      />
    );

    expect(screen.queryByText(mockFileTitle)).not.toBeInTheDocument();
    expect(screen.queryByTestId('pdf-document')).not.toBeInTheDocument();
  });

  it('shows loader when PDF is loading', () => {
    (useAttachmentId as ReturnType<typeof vi.fn>).mockReturnValue({
      attachment: undefined,
      url: '',
      isLoading: true,
      error: null,
    });

    renderWithMantine(
      <PDFPreviewModal
        opened={true}
        onClose={mockOnClose}
        fileId={mockFileId}
        fileTitle={mockFileTitle}
      />
    );

    expect(screen.getByText(mockFileTitle)).toBeInTheDocument();
  });

  it('calls onClose when modal is closed', async () => {
    const user = userEvent.setup();
    renderWithMantine(
      <PDFPreviewModal
        opened={true}
        onClose={mockOnClose}
        fileId={mockFileId}
        fileTitle={mockFileTitle}
      />
    );

    // Find the close button (typically an X in the corner)
    const closeButton = screen.getByRole('button');
    await user.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('displays pagination when PDF has multiple pages', async () => {
    const user = userEvent.setup();
    renderWithMantine(
      <PDFPreviewModal
        opened={true}
        onClose={mockOnClose}
        fileId={mockFileId}
        fileTitle={mockFileTitle}
      />
    );

    // Simulate the PDF document loading successfully with multiple pages
    const documentElement = screen.getByTestId('pdf-document');
    await user.click(documentElement); // This will trigger the onLoadSuccess mock

    // Check for pagination
    await waitFor(() => {
      expect(screen.getByText('page: 1/5')).toBeInTheDocument();
    });
  });
});
