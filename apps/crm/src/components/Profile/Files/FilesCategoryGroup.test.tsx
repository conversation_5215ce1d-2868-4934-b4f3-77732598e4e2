import type { Attachment } from '@/models/attachment';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { FilesCategoryGroup } from './FilesCategoryGroup';
import type { Category } from './index';

// Mock the Tolgee translation hook
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: (key: string) => key,
  })),
}));

// Mock the FileItem component
vi.mock('./FileItem', () => ({
  FileItem: ({ file, onDelete, onDownload, onChangeCategory, categories }) => (
    <div data-testid={`file-item-${file.id}`} data-file-id={file.id} data-file-title={file.title}>
      <button data-testid={`delete-btn-${file.id}`} onClick={() => onDelete(file.id, file.title)}>
        Delete
      </button>
      <button data-testid={`download-btn-${file.id}`} onClick={() => onDownload(file.id)}>
        Download
      </button>
      <select
        data-testid={`category-select-${file.id}`}
        onChange={(e) => onChangeCategory(file.id, e.target.value)}
      >
        {onChangeCategory && (
          <>
            <option value=''>Select Category</option>
            {categories?.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </>
        )}
      </select>
    </div>
  ),
}));

// Mock the EmptyState component
vi.mock('../../EmptyState/EmptyState', () => ({
  EmptyState: ({ message, className }) => (
    <div data-testid='empty-state' className={className}>
      {message}
    </div>
  ),
}));

describe('FilesCategoryGroup Component', () => {
  // Mock data
  const mockCategory: Category = {
    id: 'category-1',
    name: 'Documents',
  };

  const mockFiles: Attachment[] = [
    {
      id: 'file-1',
      path: '/path/to/file1.pdf',
      mimeType: 'application/pdf',
      size: 1024,
      title: 'document1.pdf',
      url: 'http://example.com/document1.pdf',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    },
    {
      id: 'file-2',
      path: '/path/to/file2.jpg',
      mimeType: 'image/jpeg',
      size: 2048,
      title: 'image1.jpg',
      url: 'http://example.com/image1.jpg',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    },
  ];

  const mockCategories: Category[] = [
    mockCategory,
    {
      id: 'category-2',
      name: 'Images',
    },
  ];

  // Mock functions
  const onDeleteFileMock = vi.fn();
  const onDownloadFileMock = vi.fn();
  const onChangeCategoryMock = vi.fn().mockResolvedValue(undefined);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the category title', () => {
    renderWithMantine(
      <FilesCategoryGroup
        category={mockCategory}
        files={mockFiles}
        onDeleteFile={onDeleteFileMock}
        onDownloadFile={onDownloadFileMock}
        categories={mockCategories}
        onChangeCategory={onChangeCategoryMock}
      />
    );

    expect(screen.getAllByText('Documents')[0]).toBeInTheDocument();
  });

  it('should render all files in the category', () => {
    renderWithMantine(
      <FilesCategoryGroup
        category={mockCategory}
        files={mockFiles}
        onDeleteFile={onDeleteFileMock}
        onDownloadFile={onDownloadFileMock}
        categories={mockCategories}
        onChangeCategory={onChangeCategoryMock}
      />
    );

    expect(screen.getByTestId('file-item-file-1')).toBeInTheDocument();
    expect(screen.getByTestId('file-item-file-2')).toBeInTheDocument();
  });

  it('should show empty state when no files are present', () => {
    renderWithMantine(
      <FilesCategoryGroup
        category={mockCategory}
        files={[]}
        onDeleteFile={onDeleteFileMock}
        onDownloadFile={onDownloadFileMock}
        categories={mockCategories}
        onChangeCategory={onChangeCategoryMock}
      />
    );

    const emptyState = screen.getByTestId('empty-state');
    expect(emptyState).toBeInTheDocument();
    expect(emptyState.textContent).toBe('noFilesInCategory');
  });

  it('should call onDeleteFile when delete button is clicked', async () => {
    renderWithMantine(
      <FilesCategoryGroup
        category={mockCategory}
        files={mockFiles}
        onDeleteFile={onDeleteFileMock}
        onDownloadFile={onDownloadFileMock}
        categories={mockCategories}
        onChangeCategory={onChangeCategoryMock}
      />
    );

    const deleteButton = screen.getByTestId('delete-btn-file-1');
    await userEvent.click(deleteButton);

    expect(onDeleteFileMock).toHaveBeenCalledWith('file-1', 'document1.pdf');
  });

  it('should call onDownloadFile when download button is clicked', async () => {
    renderWithMantine(
      <FilesCategoryGroup
        category={mockCategory}
        files={mockFiles}
        onDeleteFile={onDeleteFileMock}
        onDownloadFile={onDownloadFileMock}
        categories={mockCategories}
        onChangeCategory={onChangeCategoryMock}
      />
    );

    const downloadButton = screen.getByTestId('download-btn-file-2');
    await userEvent.click(downloadButton);

    expect(onDownloadFileMock).toHaveBeenCalledWith('file-2');
  });

  it('should call onChangeCategory when category is changed', async () => {
    renderWithMantine(
      <FilesCategoryGroup
        category={mockCategory}
        files={mockFiles}
        onDeleteFile={onDeleteFileMock}
        onDownloadFile={onDownloadFileMock}
        categories={mockCategories}
        onChangeCategory={onChangeCategoryMock}
      />
    );

    const categorySelect = screen.getByTestId('category-select-file-1');
    await userEvent.selectOptions(categorySelect, 'category-2');

    expect(onChangeCategoryMock).toHaveBeenCalledWith('file-1', 'category-2');
  });

  it('should apply custom className when provided', () => {
    const customClassName = 'custom-class';

    renderWithMantine(
      <FilesCategoryGroup
        category={mockCategory}
        files={[]}
        onDeleteFile={onDeleteFileMock}
        onDownloadFile={onDownloadFileMock}
        categories={mockCategories}
        onChangeCategory={onChangeCategoryMock}
        className={customClassName}
      />
    );

    const emptyState = screen.getByTestId('empty-state');
    expect(emptyState).toHaveClass(customClassName);
  });
});
