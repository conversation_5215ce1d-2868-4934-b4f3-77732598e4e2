import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { FileRemovalModal } from './FileRemovalModal';

// Mock tolgee translate
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

describe('FileRemovalModal Component', () => {
  const mockOnClose = vi.fn();
  const mockOnDelete = vi.fn().mockImplementation(() => Promise.resolve());
  const mockFileName = 'test-document.pdf';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the modal when opened is true', () => {
    renderWithMantine(
      <FileRemovalModal
        opened={true}
        onClose={mockOnClose}
        fileName={mockFileName}
        onDelete={mockOnDelete}
        isDeleting={false}
      />
    );

    // Check if title is rendered
    expect(screen.getByText('deleteFile')).toBeInTheDocument();

    // Check if buttons are rendered
    expect(screen.getByText('cancel')).toBeInTheDocument();
    expect(screen.getByText('delete')).toBeInTheDocument();
  });

  it('does not render when opened is false', () => {
    renderWithMantine(
      <FileRemovalModal
        opened={false}
        onClose={mockOnClose}
        fileName={mockFileName}
        onDelete={mockOnDelete}
        isDeleting={false}
      />
    );

    // Modal should not be in the document
    expect(screen.queryByText('deleteFile')).not.toBeInTheDocument();
    expect(screen.queryByText(/test-document.pdf/)).not.toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', async () => {
    const user = userEvent.setup();
    renderWithMantine(
      <FileRemovalModal
        opened={true}
        onClose={mockOnClose}
        fileName={mockFileName}
        onDelete={mockOnDelete}
        isDeleting={false}
      />
    );

    const cancelButton = screen.getByTestId('deca-modal-cancel-button');
    await user.click(cancelButton);

    expect(mockOnDelete).not.toHaveBeenCalled();
  });

  it('calls onDelete when delete button is clicked', async () => {
    const user = userEvent.setup();
    renderWithMantine(
      <FileRemovalModal
        opened={true}
        onClose={mockOnClose}
        fileName={mockFileName}
        onDelete={mockOnDelete}
        isDeleting={false}
      />
    );

    const deleteButton = screen.getByText('delete');
    await user.click(deleteButton);

    expect(mockOnDelete).toHaveBeenCalledTimes(1);
    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('displays loading state when isDeleting is true', () => {
    renderWithMantine(
      <FileRemovalModal
        opened={true}
        onClose={mockOnClose}
        fileName={mockFileName}
        onDelete={mockOnDelete}
        isDeleting={true}
      />
    );

    // Delete button should be in loading state
    const deleteButton = screen.getByText('delete');
    expect(deleteButton.closest('button')).toHaveAttribute('data-loading', 'true');
  });

  it('has the correct data-testid on the modal', () => {
    renderWithMantine(
      <FileRemovalModal
        opened={true}
        onClose={mockOnClose}
        fileName={mockFileName}
        onDelete={mockOnDelete}
        isDeleting={false}
      />
    );

    const modal = screen.getByTestId('file-removal-modal');
    expect(modal).toBeInTheDocument();
  });
});
