import isEqual from 'lodash/isEqual';
import pick from 'lodash/pick';

export const downloadFile = (url: string) => {
  const a = document.createElement('a');

  a.href = url;
  a.target = '_blank';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

export const fileEquals = (a: File, b: File) => {
  const fields: (keyof File)[] = ['lastModified', 'name', 'size', 'type'];

  return isEqual(pick(a, fields), pick(b, fields));
};

export const getFileKey = (file: Pick<File, 'lastModified' | 'name' | 'size' | 'type'>) =>
  `${file.name}_${file.size}_${file.type}_${file.lastModified}`;
