import { Dnd<PERSON>ontext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, arrayMove } from '@dnd-kit/sortable';
import { Button, Flex, rem } from '@mantine/core';
import { Modal } from '@resola-ai/ui';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useState } from 'react';
import type { Category } from '.';
import { SortableCategory } from './SortableCategory';

type CategoryManagerProps = {
  opened: boolean;
  onClose: () => void;
  categories: Category[];
  onSave: (categories: Category[]) => Promise<void>;
  isSaving: boolean;
};

export const CategoryManager = ({
  opened,
  onClose,
  categories: initialCategories,
  onSave,
  isSaving,
}: CategoryManagerProps) => {
  const { t: tCommon } = useTranslate('common');
  const { t: tWorkspace } = useTranslate('workspace');
  const [categories, setCategories] = useState<Category[]>([]);

  // Set up dnd sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  // Initialize categories when modal opens
  useEffect(() => {
    if (opened) {
      setCategories([...initialCategories]);
    }
  }, [opened, initialCategories]);

  // Add a new category
  const handleAddCategory = () => {
    const newId = Date.now().toString();
    // Add an empty category at the top of the list
    setCategories([{ id: newId, name: '' }, ...categories]);
  };

  // Remove a category
  const handleRemoveCategory = (id: string) => {
    setCategories(categories.filter((category) => category.id !== id));
  };

  // Update category name
  const handleCategoryNameChange = (id: string, newName: string) => {
    setCategories(
      categories.map((category) => (category.id === id ? { ...category, name: newName } : category))
    );
  };

  // Handle drag end
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setCategories((categories) => {
        const oldIndex = categories.findIndex((category) => category.id === active.id);
        const newIndex = categories.findIndex((category) => category.id === over.id);

        return arrayMove(categories, oldIndex, newIndex);
      });
    }
  };

  // Handle save
  const handleSave = async () => {
    await onSave(categories);
  };

  return (
    <Modal
      data-testid='category-manager'
      radius={rem(8)}
      opened={opened}
      onClose={onClose}
      title={tWorkspace('manageCategoryGroups')}
      centered
      size={rem(720)}
      onOk={handleSave}
      okText={tCommon('save')}
      onCancel={onClose}
      cancelText={tCommon('cancel')}
      okButtonProps={{
        disabled: categories.some((category) => !category.name.trim()),
        loading: isSaving,
      }}
    >
      <Flex direction='column'>
        {/* Add new category button */}
        <Button variant='subtle' w='fit-content' color='decaBlue.5' onClick={handleAddCategory}>
          <IconPlus size={16} style={{ marginRight: '8px' }} />
          {tWorkspace('addCategory')}
        </Button>

        {/* Drag and drop list */}
        <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
          <SortableContext items={categories.map((c) => c.id)}>
            {categories.map((category) => (
              <SortableCategory
                key={category.id}
                id={category.id}
                category={category}
                onRemove={handleRemoveCategory}
                onNameChange={handleCategoryNameChange}
              />
            ))}
          </SortableContext>
        </DndContext>
      </Flex>
    </Modal>
  );
};
