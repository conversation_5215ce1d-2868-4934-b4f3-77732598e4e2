import type { Attachment } from '@/models/attachment';
import { Flex, Title, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { EmptyState } from '../../EmptyState/EmptyState';
import { FileItem } from './FileItem';
import type { Category } from './index';

// Add props interface
interface FilesCategoryGroupProps {
  category: Category;
  files: Attachment[];
  onDeleteFile: (id: string, name: string) => void;
  onDownloadFile: (id: string) => void;
  categories: Category[];
  onChangeCategory: (attachmentId: string, categoryId: string) => Promise<void>;
  className?: string;
}

export const FilesCategoryGroup = ({
  category,
  files,
  onDeleteFile,
  onDownloadFile,
  categories,
  onChangeCategory,
  className,
}: FilesCategoryGroupProps) => {
  const { t: tWorkspace } = useTranslate('workspace');

  return (
    <Flex direction='column' gap={rem(18)} data-testid='files-category-group'>
      <Title order={6} fw={500}>
        {category.name}
      </Title>
      {files.length > 0 ? (
        <Flex direction='column' gap={rem(16)}>
          {files.map((file) => (
            <FileItem
              key={file.id}
              file={file}
              onDelete={onDeleteFile}
              categories={categories}
              onChangeCategory={onChangeCategory}
              onDownload={onDownloadFile}
            />
          ))}
        </Flex>
      ) : (
        <EmptyState
          className={className}
          imageUrl='images/empty_image.png'
          message={tWorkspace('noFilesInCategory')}
        />
      )}
    </Flex>
  );
};
