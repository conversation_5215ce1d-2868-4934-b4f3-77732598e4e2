import { useSortable } from '@dnd-kit/sortable';
import { ActionIcon, Box, Flex, TextInput, rem } from '@mantine/core';
import { IconGripVertical, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type { Category } from '.';

type SortableCategoryProps = {
  category: Category;
  id: string;
  onRemove: (id: string) => void;
  onNameChange: (id: string, newName: string) => void;
};

export const SortableCategory = ({
  category,
  id,
  onRemove,
  onNameChange,
}: SortableCategoryProps) => {
  const { t: tWorkspace } = useTranslate('workspace');
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });

  const style = {
    transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
    transition,
    opacity: transform ? 0.7 : 1,
  };

  return (
    <Flex
      my={rem(8)}
      ref={setNodeRef}
      style={style}
      align='center'
      gap={rem(8)}
      data-testid='sortable-category'
    >
      <Box {...attributes} {...listeners} style={{ cursor: 'grab' }}>
        <ActionIcon variant='transparent' color='decaGrey.4'>
          <IconGripVertical size={16} />
        </ActionIcon>
      </Box>

      <TextInput
        value={category.name}
        onChange={(e) => onNameChange(id, e.currentTarget.value)}
        style={{ flex: 1 }}
        w='100%'
        placeholder={tWorkspace('enterCategoryName')}
        data-testid='category-name-input'
      />

      <ActionIcon
        color='red'
        variant='subtle'
        onClick={() => onRemove(id)}
        data-testid='remove-category-button'
      >
        <IconTrash size={16} />
      </ActionIcon>
    </Flex>
  );
};
