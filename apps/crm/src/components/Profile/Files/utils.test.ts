import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { downloadFile, fileEquals, getFileKey } from './utils';

// Mock DOM methods
Object.defineProperty(document, 'createElement', {
  writable: true,
  value: vi.fn(),
});

Object.defineProperty(document, 'body', {
  writable: true,
  value: {
    appendChild: vi.fn(),
    removeChild: vi.fn(),
  },
});

describe('Profile Files Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('downloadFile', () => {
    it('should create an anchor element and trigger download', () => {
      const mockAnchor = {
        href: '',
        target: '',
        click: vi.fn(),
      };

      document.createElement = vi.fn().mockReturnValue(mockAnchor);
      
      const testUrl = 'https://example.com/file.pdf';
      
      downloadFile(testUrl);

      expect(document.createElement).toHaveBeenCalledWith('a');
      expect(mockAnchor.href).toBe(testUrl);
      expect(mockAnchor.target).toBe('_blank');
      expect(document.body.appendChild).toHaveBeenCalledWith(mockAnchor);
      expect(mockAnchor.click).toHaveBeenCalled();
      expect(document.body.removeChild).toHaveBeenCalledWith(mockAnchor);
    });

    it('should handle empty URL', () => {
      const mockAnchor = {
        href: '',
        target: '',
        click: vi.fn(),
      };

      document.createElement = vi.fn().mockReturnValue(mockAnchor);
      
      downloadFile('');

      expect(mockAnchor.href).toBe('');
      expect(mockAnchor.click).toHaveBeenCalled();
    });

    it('should handle special characters in URL', () => {
      const mockAnchor = {
        href: '',
        target: '',
        click: vi.fn(),
      };

      document.createElement = vi.fn().mockReturnValue(mockAnchor);
      
      const urlWithSpecialChars = 'https://example.com/file with spaces & symbols.pdf';
      
      downloadFile(urlWithSpecialChars);

      expect(mockAnchor.href).toBe(urlWithSpecialChars);
      expect(mockAnchor.click).toHaveBeenCalled();
    });

    it('should handle very long URLs', () => {
      const mockAnchor = {
        href: '',
        target: '',
        click: vi.fn(),
      };

      document.createElement = vi.fn().mockReturnValue(mockAnchor);
      
      const longUrl = 'https://example.com/' + 'a'.repeat(1000) + '.pdf';
      
      downloadFile(longUrl);

      expect(mockAnchor.href).toBe(longUrl);
      expect(mockAnchor.click).toHaveBeenCalled();
    });
  });

  describe('fileEquals', () => {
    it('should return true for identical files', () => {
      const file1 = new File(['content'], 'test.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });
      
      const file2 = new File(['content'], 'test.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });

      expect(fileEquals(file1, file2)).toBe(true);
    });

    it('should return false for files with different names', () => {
      const file1 = new File(['content'], 'test1.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });
      
      const file2 = new File(['content'], 'test2.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });

      expect(fileEquals(file1, file2)).toBe(false);
    });

    it('should return false for files with different sizes', () => {
      const file1 = new File(['content'], 'test.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });
      
      const file2 = new File(['different content'], 'test.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });

      expect(fileEquals(file1, file2)).toBe(false);
    });

    it('should return false for files with different types', () => {
      const file1 = new File(['content'], 'test.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });
      
      const file2 = new File(['content'], 'test.txt', {
        type: 'application/json',
        lastModified: 123456789,
      });

      expect(fileEquals(file1, file2)).toBe(false);
    });

    it('should return false for files with different lastModified timestamps', () => {
      const file1 = new File(['content'], 'test.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });
      
      const file2 = new File(['content'], 'test.txt', {
        type: 'text/plain',
        lastModified: 987654321,
      });

      expect(fileEquals(file1, file2)).toBe(false);
    });

    it('should handle files with empty names', () => {
      const file1 = new File(['content'], '', {
        type: 'text/plain',
        lastModified: 123456789,
      });
      
      const file2 = new File(['content'], '', {
        type: 'text/plain',
        lastModified: 123456789,
      });

      expect(fileEquals(file1, file2)).toBe(true);
    });

    it('should handle files with zero size', () => {
      const file1 = new File([], 'empty.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });
      
      const file2 = new File([], 'empty.txt', {
        type: 'text/plain',
        lastModified: 123456789,
      });

      expect(fileEquals(file1, file2)).toBe(true);
    });

    it('should handle files with special characters in names', () => {
      const fileName = 'file with spaces & symbols!@#$.txt';
      const file1 = new File(['content'], fileName, {
        type: 'text/plain',
        lastModified: 123456789,
      });
      
      const file2 = new File(['content'], fileName, {
        type: 'text/plain',
        lastModified: 123456789,
      });

      expect(fileEquals(file1, file2)).toBe(true);
    });

    it('should handle files with very long names', () => {
      const longFileName = 'a'.repeat(255) + '.txt';
      const file1 = new File(['content'], longFileName, {
        type: 'text/plain',
        lastModified: 123456789,
      });
      
      const file2 = new File(['content'], longFileName, {
        type: 'text/plain',
        lastModified: 123456789,
      });

      expect(fileEquals(file1, file2)).toBe(true);
    });
  });

  describe('getFileKey', () => {
    it('should generate a unique key for a file', () => {
      const file = {
        name: 'test.txt',
        size: 1024,
        type: 'text/plain',
        lastModified: 123456789,
      };

      const key = getFileKey(file);
      expect(key).toBe('test.txt_1024_text/plain_123456789');
    });

    it('should generate different keys for different files', () => {
      const file1 = {
        name: 'test1.txt',
        size: 1024,
        type: 'text/plain',
        lastModified: 123456789,
      };

      const file2 = {
        name: 'test2.txt',
        size: 1024,
        type: 'text/plain',
        lastModified: 123456789,
      };

      const key1 = getFileKey(file1);
      const key2 = getFileKey(file2);
      
      expect(key1).not.toBe(key2);
      expect(key1).toBe('test1.txt_1024_text/plain_123456789');
      expect(key2).toBe('test2.txt_1024_text/plain_123456789');
    });

    it('should handle files with empty names', () => {
      const file = {
        name: '',
        size: 0,
        type: '',
        lastModified: 0,
      };

      const key = getFileKey(file);
      expect(key).toBe('_0__0');
    });

    it('should handle files with special characters in names', () => {
      const file = {
        name: 'file with spaces & symbols!@#$.txt',
        size: 2048,
        type: 'application/pdf',
        lastModified: 987654321,
      };

      const key = getFileKey(file);
      expect(key).toBe('file with spaces & symbols!@#$.txt_2048_application/pdf_987654321');
    });

    it('should handle very large file sizes', () => {
      const file = {
        name: 'large-file.bin',
        size: Number.MAX_SAFE_INTEGER,
        type: 'application/octet-stream',
        lastModified: 123456789,
      };

      const key = getFileKey(file);
      expect(key).toBe(`large-file.bin_${Number.MAX_SAFE_INTEGER}_application/octet-stream_123456789`);
    });

    it('should handle zero timestamps', () => {
      const file = {
        name: 'test.txt',
        size: 1024,
        type: 'text/plain',
        lastModified: 0,
      };

      const key = getFileKey(file);
      expect(key).toBe('test.txt_1024_text/plain_0');
    });

    it('should handle file types with slashes', () => {
      const file = {
        name: 'image.png',
        size: 4096,
        type: 'image/png',
        lastModified: 123456789,
      };

      const key = getFileKey(file);
      expect(key).toBe('image.png_4096_image/png_123456789');
    });

    it('should generate consistent keys for the same file data', () => {
      const file = {
        name: 'consistent.txt',
        size: 512,
        type: 'text/plain',
        lastModified: 555666777,
      };

      const key1 = getFileKey(file);
      const key2 = getFileKey(file);
      
      expect(key1).toBe(key2);
      expect(key1).toBe('consistent.txt_512_text/plain_555666777');
    });

    it('should handle unicode characters in file names', () => {
      const file = {
        name: '测试文件.txt',
        size: 1024,
        type: 'text/plain',
        lastModified: 123456789,
      };

      const key = getFileKey(file);
      expect(key).toBe('测试文件.txt_1024_text/plain_123456789');
    });
  });
}); 