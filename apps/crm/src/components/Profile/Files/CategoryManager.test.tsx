import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { CategoryManager } from './CategoryManager';

// Mock the tolgee translation hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: vi.fn((key) => {
      const translations = {
        'common.cancel': 'Cancel',
        'common.save': 'Save',
        'workspace.manageCategoryGroups': 'Manage Category Groups',
        'workspace.addCategory': 'Add Category',
        'workspace.enterCategoryName': 'Enter category name',
      };
      return translations[`${key}`] || key;
    }),
  }),
}));

// Mock SortableCategory
vi.mock('./SortableCategory', () => ({
  SortableCategory: ({ id, category, onRemove, onNameChange }) => (
    <div data-testid={`sortable-category-${id}`}>
      <input
        data-testid={`category-name-${id}`}
        value={category.name}
        onChange={(e) => onNameChange(id, e.target.value)}
      />
      <button data-testid={`remove-category-${id}`} onClick={() => onRemove(id)}>
        Remove
      </button>
    </div>
  ),
}));

describe('CategoryManager', () => {
  const defaultProps = {
    opened: true,
    onClose: vi.fn(),
    categories: [
      { id: '1', name: 'Category 1' },
      { id: '2', name: 'Category 2' },
    ],
    onSave: vi.fn().mockResolvedValue(undefined),
    isSaving: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly when opened', () => {
    renderWithMantine(<CategoryManager {...defaultProps} />);

    expect(screen.getByTestId('category-manager')).toBeInTheDocument();
    expect(screen.getByText('manageCategoryGroups')).toBeInTheDocument();
    expect(screen.getByText('addCategory')).toBeInTheDocument();
    expect(screen.getByTestId('sortable-category-1')).toBeInTheDocument();
    expect(screen.getByTestId('sortable-category-2')).toBeInTheDocument();
  });

  // it('does not render when not opened', () => {
  //   renderWithMantine(<CategoryManager {...defaultProps} opened={false} />);
  //   expect(screen.queryByTestId('category-manager')).not.toBeInTheDocument();
  // });

  it('adds a new category when add button is clicked', async () => {
    const user = userEvent.setup();
    renderWithMantine(<CategoryManager {...defaultProps} />);

    await user.click(screen.getByText('addCategory'));

    // The new category should be added at the beginning
    const sortableCategories = screen.getAllByTestId(/sortable-category/);
    expect(sortableCategories.length).toBe(3);
  });

  it('removes a category when remove button is clicked', async () => {
    const user = userEvent.setup();
    renderWithMantine(<CategoryManager {...defaultProps} />);

    await user.click(screen.getByTestId('remove-category-1'));

    // One category should be removed
    const sortableCategories = screen.getAllByTestId(/sortable-category/);
    expect(sortableCategories.length).toBe(1);
    expect(screen.queryByTestId('sortable-category-1')).not.toBeInTheDocument();
    expect(screen.getByTestId('sortable-category-2')).toBeInTheDocument();
  });

  it('updates category name when changed', async () => {
    const user = userEvent.setup();
    renderWithMantine(<CategoryManager {...defaultProps} />);

    const categoryInput = screen.getByTestId('category-name-1');
    await user.clear(categoryInput);
    await user.type(categoryInput, 'Updated Category');

    // Save button should be enabled since all categories have names
    const saveButton = screen.getByText('save');
    expect(saveButton).not.toBeDisabled();
  });

  // it('disables save button if any category has an empty name', async () => {
  //   const user = userEvent.setup();
  //   renderWithMantine(<CategoryManager {...defaultProps} />);

  //   // First add a new category (which will have an empty name)
  //   await user.click(screen.getByText('addCategory'));

  //   // Save button should be disabled
  //   const saveButton = screen.getByText('save');
  //   expect(saveButton).toBeDisabled();
  // });

  it('calls onSave with updated categories when save button is clicked', async () => {
    const onSaveMock = vi.fn().mockResolvedValue(undefined);
    const user = userEvent.setup();

    renderWithMantine(<CategoryManager {...defaultProps} onSave={onSaveMock} />);

    // First update a category name
    const categoryInput = screen.getByTestId('category-name-1');
    await user.clear(categoryInput);
    await user.type(categoryInput, 'Updated Category');

    // Then save
    await user.click(screen.getByText('save'));

    // Check if onSave was called with the correct categories
    expect(onSaveMock).toHaveBeenCalledTimes(1);
    const savedCategories = onSaveMock.mock.calls[0][0];
    expect(savedCategories).toHaveLength(2);
    expect(savedCategories[0].name).toBe('Updated Category');
    expect(savedCategories[1].name).toBe('Category 2');
  });

  it('shows loading state when saving', () => {
    renderWithMantine(<CategoryManager {...defaultProps} isSaving={true} />);

    // The save button should show loading state
    const saveButton = screen.getByText('save');
    expect(saveButton).toHaveAttribute('data-loading', 'true');
  });

  it('calls onClose when cancel button is clicked', async () => {
    const onCloseMock = vi.fn();
    const user = userEvent.setup();

    renderWithMantine(<CategoryManager {...defaultProps} onClose={onCloseMock} />);

    await user.click(screen.getByText('cancel'));
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });
});
