import { Box, Text, rem } from '@mantine/core';
import { Modal } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';

type FileRemovalModalProps = {
  opened: boolean;
  onClose: () => void;
  fileName: string;
  isDeleting: boolean;
  onDelete: () => Promise<void>;
};

export const FileRemovalModal = ({
  opened,
  onClose,
  fileName,
  onDelete,
  isDeleting,
}: FileRemovalModalProps) => {
  const { t: tCommon } = useTranslate('common');
  const { t: tWorkspace } = useTranslate('workspace');

  return (
    <Modal
      data-testid='file-removal-modal'
      centered
      opened={opened}
      title={tWorkspace('deleteFile')}
      onClose={onClose}
      onOk={onDelete}
      okText={tCommon('delete')}
      cancelText={tCommon('cancel')}
      onCancel={onClose}
      okButtonProps={{ loading: isDeleting, variant: 'negative' }}
      footerDivider={false}
    >
      <Box fz={rem(16)} mb={rem(20)}>
        <Text mb={rem(20)}>{tCommon('deleteConfirmMsg', { name: `"${fileName}"` })}</Text>
        <Text>{tCommon('clearDesc')}</Text>
      </Box>
    </Modal>
  );
};
