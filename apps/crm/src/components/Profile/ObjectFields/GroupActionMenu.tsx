import { ActionIcon, Menu } from '@mantine/core';
import { IconDotsVertical } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import type { ProfileData } from './RenderProfileTypes';

export interface InternalFieldGroup {
  id: string;
  title: string;
  fields: string[];
}

export interface GroupActionMenuProps {
  field: ProfileData;
  availableGroups: InternalFieldGroup[];
  onMoveToGroup: (fieldId: string, groupId: string) => void;
  groupId: string;
}

const GroupActionMenu = React.memo<GroupActionMenuProps>(
  ({ field, availableGroups, onMoveToGroup, groupId }) => {
    const { t } = useTranslate('workspace');
    return (
      <Menu withinPortal position='bottom-end' shadow='md'>
        <Menu.Target>
          <ActionIcon size='md' variant='subtle'>
            <IconDotsVertical size={18} />
          </ActionIcon>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Label>{t('moveTo')}</Menu.Label>
          {availableGroups
            .filter((g) => g.id !== groupId)
            .map((group) => (
              <Menu.Item key={group.id} onClick={() => onMoveToGroup(field.id!, group.id)}>
                {group.title}
              </Menu.Item>
            ))}
        </Menu.Dropdown>
      </Menu>
    );
  }
);

GroupActionMenu.displayName = 'GroupActionMenu';

export default GroupActionMenu;
