import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import ViewAllModal from './ViewAllModal';

// Setup test environment
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock dependencies
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(() => ({
    onSaveData: vi.fn().mockResolvedValue({}),
    currRecordIndex: 0,
  })),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: vi.fn((key) => {
      const translations: Record<string, string> = {
        edit: 'Edit',
        save: 'Save',
        cancel: 'Cancel',
        copyToClipboard: 'Copy to Clipboard',
      };
      return translations[key] || key;
    }),
  })),
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn(() => ({
    classes: {
      contentWrapper: 'content-wrapper-class',
    },
  })),
}));

vi.mock('@tabler/icons-react', () => ({
  IconEdit: () => <div data-testid="icon-edit">Edit Icon</div>,
  IconCopy: () => <div data-testid="icon-copy">Copy Icon</div>,
  IconCheck: () => <div data-testid="icon-check">Check Icon</div>,
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(void 0),
  },
});

describe('ViewAllModal', () => {
  const mockOnSaveData = vi.fn();

  beforeEach(async () => {
    vi.clearAllMocks();
    
    const { useWorkspaceContext } = await import('@/contexts/WorkspaceContext');
    vi.mocked(useWorkspaceContext).mockReturnValue({
      onSaveData: mockOnSaveData,
      currRecordIndex: 0,
    } as any);
  });

  const mockData = {
    id: 'field1',
    name: 'Description',
    mapValue: 'This is a long description that can be edited.',
    type: 'text',
    icon: 'text-icon',
    header: 'Description Field',
  } as any;

  describe('Basic Rendering', () => {
    it('should render the component with initial data', () => {
      const { getByText } = renderWithMantine(
        <ViewAllModal data={mockData} />
      );

      expect(getByText('Edit')).toBeInTheDocument();
      expect(getByText('Copy to Clipboard')).toBeInTheDocument();
      expect(getByText('This is a long description that can be edited.')).toBeInTheDocument();
    });

    it('should render without data', () => {
      const { getByText } = renderWithMantine(<ViewAllModal />);

      expect(getByText('Edit')).toBeInTheDocument();
      expect(getByText('Copy to Clipboard')).toBeInTheDocument();
    });

    it('should render empty state when data is empty', () => {
      const emptyData = { ...mockData, mapValue: '' };
      const { getByText } = renderWithMantine(<ViewAllModal data={emptyData} />);

      expect(getByText('Edit')).toBeInTheDocument();
      expect(getByText('Copy to Clipboard')).toBeInTheDocument();
    });
  });

  describe('Edit Mode Functionality', () => {
    it('should switch to edit mode when edit button is clicked', () => {
      const { getByText, getByDisplayValue } = renderWithMantine(
        <ViewAllModal data={mockData} />
      );

      fireEvent.click(getByText('Edit'));

      expect(getByText('Save')).toBeInTheDocument();
      expect(getByText('Cancel')).toBeInTheDocument();
      expect(getByDisplayValue('This is a long description that can be edited.')).toBeInTheDocument();
    });

    it('should update text value when typing in edit mode', () => {
      const { getByText, getByDisplayValue } = renderWithMantine(
        <ViewAllModal data={mockData} />
      );

      fireEvent.click(getByText('Edit'));
      const textarea = getByDisplayValue('This is a long description that can be edited.');

      fireEvent.change(textarea, { target: { value: 'Updated description text' } });

      expect(getByDisplayValue('Updated description text')).toBeInTheDocument();
    });

    it('should exit edit mode and reset value when cancel is clicked', () => {
      const { getByText, getByDisplayValue } = renderWithMantine(
        <ViewAllModal data={mockData} />
      );

      // Enter edit mode
      fireEvent.click(getByText('Edit'));
      const textarea = getByDisplayValue('This is a long description that can be edited.');

      // Make changes
      fireEvent.change(textarea, { target: { value: 'Changed text' } });
      expect(getByDisplayValue('Changed text')).toBeInTheDocument();

      // Cancel changes
      fireEvent.click(getByText('Cancel'));

      // Should be back to view mode with original text
      expect(getByText('Edit')).toBeInTheDocument();
      expect(getByText('This is a long description that can be edited.')).toBeInTheDocument();
    });
  });

  describe('Save Functionality', () => {
    it('should call onSaveData when save button is clicked', async () => {
      const { getByText, getByDisplayValue } = renderWithMantine(
        <ViewAllModal data={mockData} />
      );

      // Enter edit mode
      fireEvent.click(getByText('Edit'));
      const textarea = getByDisplayValue('This is a long description that can be edited.');

      // Make changes
      fireEvent.change(textarea, { target: { value: 'Updated description' } });

      // Save changes
      fireEvent.click(getByText('Save'));

      await waitFor(() => {
        expect(mockOnSaveData).toHaveBeenCalledWith(
          'Updated description',
          0,
          'field1',
          false
        );
      });
    });

    it('should trim whitespace when saving', async () => {
      const { getByText, getByDisplayValue } = renderWithMantine(
        <ViewAllModal data={mockData} />
      );

      fireEvent.click(getByText('Edit'));
      const textarea = getByDisplayValue('This is a long description that can be edited.');

      fireEvent.change(textarea, { target: { value: '  Trimmed text  ' } });
      fireEvent.click(getByText('Save'));

      await waitFor(() => {
        expect(mockOnSaveData).toHaveBeenCalledWith(
          '  Trimmed text  ',
          0,
          'field1',
          false
        );
      });

      // After save, should show trimmed value
      expect(getByText('Trimmed text')).toBeInTheDocument();
    });

    it('should exit edit mode after successful save', async () => {
      const { getByText, getByDisplayValue } = renderWithMantine(
        <ViewAllModal data={mockData} />
      );

      fireEvent.click(getByText('Edit'));
      const textarea = getByDisplayValue('This is a long description that can be edited.');

      fireEvent.change(textarea, { target: { value: 'New content' } });
      fireEvent.click(getByText('Save'));

      await waitFor(() => {
        expect(getByText('Edit')).toBeInTheDocument();
        expect(getByText('Copy to Clipboard')).toBeInTheDocument();
      });
    });
  });

  describe('Copy Functionality', () => {
    it('should render copy button with correct content', () => {
      const { getByText } = renderWithMantine(<ViewAllModal data={mockData} />);

      const copyButton = getByText('Copy to Clipboard');
      expect(copyButton).toBeInTheDocument();
    });

    it('should show different copy states', () => {
      const { getByText } = renderWithMantine(<ViewAllModal data={mockData} />);

      // Copy button should be present
      expect(getByText('Copy to Clipboard')).toBeInTheDocument();
    });

    it('should handle empty content copy', () => {
      const emptyData = { ...mockData, mapValue: '' };
      const { getByText } = renderWithMantine(<ViewAllModal data={emptyData} />);

      expect(getByText('Copy to Clipboard')).toBeInTheDocument();
    });
  });

  describe('Component State Management', () => {
    it('should initialize with data prop correctly', () => {
      const customData = { 
        ...mockData, 
        mapValue: 'Custom initial content' 
      };
      
      const { getByText } = renderWithMantine(<ViewAllModal data={customData} />);

      expect(getByText('Custom initial content')).toBeInTheDocument();
      expect(getByText('Edit')).toBeInTheDocument();
      expect(getByText('Copy to Clipboard')).toBeInTheDocument();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle undefined mapValue gracefully', () => {
      const dataWithUndefinedValue = { ...mockData, mapValue: undefined };
      const { getByText } = renderWithMantine(
        <ViewAllModal data={dataWithUndefinedValue} />
      );

      expect(getByText('Edit')).toBeInTheDocument();
      
      // Should not crash when entering edit mode
      fireEvent.click(getByText('Edit'));
      expect(getByText('Save')).toBeInTheDocument();
    });

    it('should handle null data gracefully', () => {
      const { getByText } = renderWithMantine(<ViewAllModal data={null as any} />);

      expect(getByText('Edit')).toBeInTheDocument();
      expect(getByText('Copy to Clipboard')).toBeInTheDocument();
    });
  });

  describe('Integration with WorkspaceContext', () => {
    it('should use correct record index from context', async () => {
      const { useWorkspaceContext } = await import('@/contexts/WorkspaceContext');
      vi.mocked(useWorkspaceContext).mockReturnValue({
        onSaveData: mockOnSaveData,
        currRecordIndex: 5,
      } as any);

      const { getByText, getByDisplayValue } = renderWithMantine(
        <ViewAllModal data={mockData} />
      );

      fireEvent.click(getByText('Edit'));
      const textarea = getByDisplayValue('This is a long description that can be edited.');

      fireEvent.change(textarea, { target: { value: 'Test' } });
      fireEvent.click(getByText('Save'));

      await waitFor(() => {
        expect(mockOnSaveData).toHaveBeenCalledWith('Test', 5, 'field1', false);
      });
    });
  });
}); 