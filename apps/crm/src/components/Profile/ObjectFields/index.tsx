import { ColumnIcon } from '@/constants/workspace';
import { UNGROUPED_ID } from '@/constants/workspace';
import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { ObjectColumn } from '@/models';
import { Accordion, ActionIcon, Box, Flex, Stack, Text, rem, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { DecaSwitch, Modal } from '@resola-ai/ui';
import type { FieldType } from '@resola-ai/ui/components';
import { IconEdit } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { useEffect, useMemo, useState } from 'react';
import CustomDetailView from './CustomDetailView';
import FieldRender from './FieldRender';
import type { ProfileData } from './RenderProfileTypes';
import ViewAllModal from './ViewAllModal';

const useStyles = createStyles((theme) => ({
  accordion: {
    '.mantine-Accordion-item': {
      borderRadius: rem(8),
      border: `1px solid ${theme.colors.decaLight[2]}`,

      '&[data-active]': {
        '& .mantine-Accordion-control': {
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
          borderBottom: `1px dashed ${theme.colors.decaLight[4]}`,
        },
      },
      '& .mantine-Accordion-control': {
        borderRadius: rem(8),
        '& .mantine-Accordion-label': {
          padding: 0,
        },
      },
    },
    '.mantine-Accordion-content': {
      padding: rem(10),
    },
  },
}));

const HIDDEN_FIELDS_STORAGE_KEY = 'showHiddenFields';

const ObjectFields = () => {
  const { t } = useTranslate('workspace');
  const { object, activeView } = useWorkspaceContext();
  const [openedModal, { open: openModal, close: closeModal }] = useDisclosure(false);
  const [modalData, setModalData] = useState<ProfileData>();
  const [customObjectFieldsOpened, setCustomObjectFieldsOpened] = useState(false);
  const { customObjectFields, setCustomObjectFields, resetEditFields } = useProfileContext();
  const { profile: profileRecord } = useProfileContext();
  const { classes } = useStyles();
  const theme = useMantineTheme();

  // State for show hidden fields toggle with localStorage persistence
  const [showHiddenFields, setShowHiddenFields] = useState(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(`${HIDDEN_FIELDS_STORAGE_KEY}_${object?.id}`);
      return stored ? JSON.parse(stored) : false;
    }
    return false;
  });

  const fieldsByDetail = useMemo(() => {
    return object?.fields;
  }, [object?.fields]);

  const profileData: ProfileData[] = useMemo(() => {
    if (!fieldsByDetail || !profileRecord) return [];
    return (fieldsByDetail as ObjectColumn[])?.map((field) => {
      const viewField = activeView?.fieldGroups?.find((f) =>
        f.fields.find((f) => f.fieldMetaId === field.id)
      );

      return {
        ...field,
        mapValue: profileRecord[field.id!] as string,
        icon: ColumnIcon[field.type as FieldType],
        isDetailVisible:
          viewField?.fields.find((f) => f.fieldMetaId === field.id)?.isDetailVisible ?? true,
      };
    });
  }, [profileRecord, activeView]);

  useEffect(() => {
    setCustomObjectFields(profileData);
  }, [profileData]);

  const displayFieldsData = useMemo(() => {
    if (!activeView?.fieldGroups || activeView.fieldGroups.length === 0) {
      // Filter fields based on showHiddenFields toggle
      const filteredFields = showHiddenFields
        ? customObjectFields
        : customObjectFields.filter((field) => field.isDetailVisible);

      return {
        fields: { [UNGROUPED_ID]: filteredFields },
        groupOrder: [UNGROUPED_ID],
      };
    }

    const fieldMap = new Map(customObjectFields.map((field) => [field.id, field]));
    const fields: Record<string, ProfileData[]> = {};
    const groupOrder: string[] = [];

    // Process each group in API order
    activeView.fieldGroups.forEach((group) => {
      const groupId = group.title;

      // Only add to order once
      if (!groupOrder.includes(groupId)) {
        groupOrder.push(groupId);
      }

      fields[groupId] = [];

      // Add fields to the group in their original order
      group.fields.forEach((field) => {
        // Show field if showHiddenFields is true OR if field is visible
        const shouldShowField = showHiddenFields || field.isDetailVisible;

        if (shouldShowField) {
          const fieldData = fieldMap.get(field.fieldMetaId);
          if (fieldData) {
            fields[groupId].push({
              ...fieldData,
            });
          }
        }
      });
    });

    // Add ungrouped section at the end if not already present
    if (!fields[UNGROUPED_ID]) {
      fields[UNGROUPED_ID] = [];
      if (!groupOrder.includes(UNGROUPED_ID)) {
        groupOrder.push(UNGROUPED_ID);
      }
    }

    return { fields, groupOrder };
  }, [customObjectFields, activeView, showHiddenFields]);

  // Get data from the memo
  const displayFields = displayFieldsData.fields;
  const regularGroupIds = displayFieldsData.groupOrder.filter((id) => id !== UNGROUPED_ID);
  const hasUngroupedFields = displayFields[UNGROUPED_ID]?.length > 0;

  const groupTitles = useMemo(() => {
    const titles: Record<string, string> = {};

    Object.keys(displayFields).forEach((key) => {
      titles[key] = key === UNGROUPED_ID ? '' : key;
    });

    return titles;
  }, [displayFields, t]);

  // Handle field visibility toggling
  const handleToggleVisibility = (isVisible: boolean, fieldId?: string) => {
    if (!fieldId) return;
    setCustomObjectFields((prevFields) =>
      prevFields.map((field) =>
        field.id === fieldId ? { ...field, isDetailVisible: isVisible } : field
      )
    );
  };

  // Handle toggle change
  const handleToggleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.currentTarget.checked;
    if (typeof window !== 'undefined' && object?.id) {
      setShowHiddenFields(checked);
      localStorage.setItem(`${HIDDEN_FIELDS_STORAGE_KEY}_${object.id}`, JSON.stringify(checked));
    }
  };

  // Calculate total visible fields
  const totalVisibleFields = Object.values(displayFields).flat().length;
  const totalFields = customObjectFields?.length || 0;

  return (
    <Box h={'100%'} py={0} data-testid='object-fields-test-id'>
      <Stack h='100%'>
        <Flex
          align={'center'}
          justify={'space-between'}
          gap='md'
          data-testid='object-fields-header-test-id'
        >
          <Flex align={'center'} gap={rem(12)}>
            <Text fz='xl' fw={500}>
              {object?.name?.singular || object?.name?.plural}
            </Text>
            <ActionIcon
              size={24}
              radius='xl'
              variant='subtle'
              c='decaGrey.4'
              onClick={() => {
                resetEditFields();
                setCustomObjectFieldsOpened(true);
              }}
              disabled={activeView?.locked}
            >
              <IconEdit size={16} />
            </ActionIcon>
          </Flex>
          <Flex align={'center'} gap='md'>
            <Text fz='sm' fw={500} c='decaGrey.4'>
              {t('totalFields', {
                num: totalVisibleFields,
                total: totalFields,
              })}
            </Text>
            <DecaSwitch
              checked={showHiddenFields}
              onChange={handleToggleChange}
              size='sm'
              data-testid='show-hidden-fields-toggle'
            />
            <Text fz='sm' fw={500} c='decaGrey.6'>
              {t('showHiddenFields')}
            </Text>
          </Flex>
        </Flex>

        {/* Show Hidden Fields Toggle */}
        <Box h='100%' pos='relative' data-testid='object-fields-accordion-test-id'>
          <Flex direction='column' gap={rem(16)}>
            {/* First render accordion for regular groups */}
            {regularGroupIds.length > 0 &&
              regularGroupIds.map((groupId) => (
                <Accordion
                  data-testid={`accordion-${groupId}-test-id`}
                  key={groupId}
                  defaultValue={regularGroupIds}
                  multiple
                  className={classes.accordion}
                >
                  <Accordion.Item value={groupId}>
                    <Accordion.Control bg={theme.colors.decaLight[1]} px={rem(12)} py={rem(8)}>
                      <Text fz='md' fw={500}>
                        {groupTitles[groupId] || groupId}
                      </Text>
                    </Accordion.Control>
                    <Accordion.Panel>
                      <FieldRender
                        fields={displayFields[groupId]}
                        hideShowFieldToggle={false}
                        setModalData={setModalData}
                        openViewAllModal={openModal}
                        handleToggleVisibility={handleToggleVisibility}
                      />
                    </Accordion.Panel>
                  </Accordion.Item>
                </Accordion>
              ))}

            {/* Then render ungrouped fields at the bottom */}
            {hasUngroupedFields && (
              <Box mt={rem(4)} data-testid='object-fields-ungrouped-test-id'>
                <FieldRender
                  fields={displayFields[UNGROUPED_ID]}
                  hideShowFieldToggle={false}
                  setModalData={setModalData}
                  openViewAllModal={openModal}
                  handleToggleVisibility={handleToggleVisibility}
                />
              </Box>
            )}
          </Flex>
        </Box>
      </Stack>

      <Modal
        data-testid='object-fields-modal-test-id'
        size={'70%'}
        opened={openedModal}
        title={modalData?.name}
        centered
        onClose={closeModal}
        okText={t('close')}
        onOk={closeModal}
        closeOnClickOutside
      >
        <ViewAllModal data={modalData} />
      </Modal>

      <CustomDetailView
        opened={customObjectFieldsOpened}
        onClose={() => {
          setCustomObjectFieldsOpened(false);
          resetEditFields();
        }}
        customObjectFields={customObjectFields}
      />
    </Box>
  );
};

export default React.memo(ObjectFields);
