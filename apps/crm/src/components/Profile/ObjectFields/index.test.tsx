import { AppContextProvider } from '@/contexts/AppContext';
import { useProfileContext } from '@/contexts/ProfileContext';
import { WorkspaceContextProvider, useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { describe, expect, it, vi } from 'vitest';
import ObjectFields from './index';

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  useParams: () => ({ wsId: 'workspace-1' }),
  useNavigate: () => vi.fn(),
  BrowserRouter: ({ children }: any) => <div>{children}</div>,
  Link: ({ children, to, className, target }: any) => (
    <a href={to} className={className} target={target}>
      {children}
    </a>
  ),
}));

// Mock BreadcrumbContext
vi.mock('@/contexts/BreadcrumbContext', () => ({
  useBreadcrumbContext: () => ({
    breadcrumbs: [],
    addBreadcrumb: vi.fn(),
    navigateToBreadcrumb: vi.fn(),
    clearBreadcrumbs: vi.fn(),
  }),
}));

// Mock useBreadcrumbNavigation
vi.mock('@/hooks/useBreadcrumbNavigation', () => ({
  useBreadcrumbNavigation: () => ({
    navigateToLinkedRecord: vi.fn(),
  }),
}));

// Mock @resola-ai/ui/hooks
vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({
    createPathWithLngParam: (path: string) => path,
  }),
}));

// Mock the contexts
vi.mock('@/contexts/ProfileContext', () => ({
  useProfileContext: vi.fn(),
  ProfileContextProvider: ({ children }) => children,
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
  WorkspaceContextProvider: ({ children }) => children,
}));

// Mock AppContext
vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(() => ({
    isManager: true,
  })),
  AppContextProvider: ({ children }) => children,
}));

// Mock tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: (key, options) => key + (options ? JSON.stringify(options) : ''),
  })),
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
    run: vi.fn(),
  })),
  FormatSimple: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
  })),
  InContextTools: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
  })),
  T: vi.fn(({ children }) => children),
}));

vi.mock('@tolgee/web/tools');

// Mock AppConfig
vi.mock('@/configs', () => ({
  default: {
    TOLGEE_TOOLS_ENABLED: false,
    TOLGEE_URL: 'https://example.com',
    TOLGEE_KEY: 'test-key',
  },
}));

// Mock shared constants
vi.mock('@resola-ai/shared-constants', () => ({
  DEFAULT_LANGUAGE: 'en',
  PREFERENCE_LANGUAGE: 'en',
}));

// Mock local tolgee module
vi.mock('@/tolgee/index', () => ({
  tolgee: {
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
    run: vi.fn(),
  },
}));

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(() => null),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  },
  writable: true,
});

// Wrapper component with context providers
const renderWithContext = (ui) => {
  return renderWithMantine(
    <BrowserRouter>
      <AppContextProvider>
        <WorkspaceContextProvider>{ui}</WorkspaceContextProvider>
      </AppContextProvider>
    </BrowserRouter>
  );
};

describe('ObjectFields', () => {
  const mockSetCustomObjectFields = vi.fn();
  const mockResetEditFields = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    (useProfileContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      profile: { id: 'test-record' },
      customObjectFields: [],
      setCustomObjectFields: mockSetCustomObjectFields,
      resetEditFields: mockResetEditFields,
    });

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: {
        id: 'test-object',
        name: { singular: 'Test Object', plural: 'Test Objects' },
        fields: [],
      },
      activeView: {
        fieldGroups: [],
        locked: false,
      },
    });
  });

  it('renders the ObjectFields component', () => {
    renderWithContext(<ObjectFields />);
    expect(screen.getByTestId('object-fields-test-id')).toBeInTheDocument();
  });

  it('displays correct total fields count', () => {
    const mockFields = [
      { id: 'field1', name: 'Field 1', type: 'text', isDetailVisible: true },
      { id: 'field2', name: 'Field 2', type: 'text', isDetailVisible: false },
      { id: 'field3', name: 'Field 3', type: 'text', isDetailVisible: true },
    ];

    (useProfileContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      profile: { id: 'test-record' },
      customObjectFields: mockFields,
      setCustomObjectFields: mockSetCustomObjectFields,
      resetEditFields: mockResetEditFields,
    });

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: {
        id: 'test-object',
        name: { singular: 'Test Object', plural: 'Test Objects' },
        fields: mockFields,
      },
      activeView: {
        fieldGroups: [],
        locked: false,
      },
    });

    renderWithContext(<ObjectFields />);

    // The translation key will be 'totalFields' with the parameters showing 2 visible out of 3 total fields
    expect(screen.getByText(/totalFields/)).toHaveTextContent('totalFields{"num":2,"total":3}');
  });

  it('renders the show hidden fields toggle', () => {
    renderWithContext(<ObjectFields />);

    expect(screen.getByTestId('show-hidden-fields-toggle')).toBeInTheDocument();
    expect(screen.getByText('showHiddenFields')).toBeInTheDocument();
  });

  it('toggles show hidden fields functionality', () => {
    const mockFields = [
      { id: 'field1', name: 'Field 1', type: 'text', isDetailVisible: true },
      { id: 'field2', name: 'Field 2', type: 'text', isDetailVisible: false },
      { id: 'field3', name: 'Field 3', type: 'text', isDetailVisible: true },
    ];

    (useProfileContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      profile: { id: 'test-record' },
      customObjectFields: mockFields,
      setCustomObjectFields: mockSetCustomObjectFields,
      resetEditFields: mockResetEditFields,
    });

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: {
        id: 'test-object',
        name: { singular: 'Test Object', plural: 'Test Objects' },
        fields: mockFields,
      },
      activeView: {
        fieldGroups: [],
        locked: false,
      },
    });

    renderWithContext(<ObjectFields />);

    const toggle = screen.getByTestId('show-hidden-fields-toggle');

    // Initially shows only visible fields (2 out of 3)
    expect(screen.getByText(/totalFields/)).toHaveTextContent('totalFields{"num":2,"total":3}');

    // Click the toggle to show hidden fields
    fireEvent.click(toggle);

    // Now should show all fields (3 out of 3)
    expect(screen.getByText(/totalFields/)).toHaveTextContent('totalFields{"num":3,"total":3}');
  });

  it('persists toggle state to localStorage', () => {
    const mockFields = [
      { id: 'field1', name: 'Field 1', type: 'text', isDetailVisible: true },
      { id: 'field2', name: 'Field 2', type: 'text', isDetailVisible: false },
    ];

    (useProfileContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      profile: { id: 'test-record' },
      customObjectFields: mockFields,
      setCustomObjectFields: mockSetCustomObjectFields,
      resetEditFields: mockResetEditFields,
    });

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: {
        id: 'test-object',
        name: { singular: 'Test Object', plural: 'Test Objects' },
        fields: mockFields,
      },
      activeView: {
        fieldGroups: [],
        locked: false,
      },
    });

    renderWithContext(<ObjectFields />);

    const toggle = screen.getByTestId('show-hidden-fields-toggle');

    // Click the toggle
    fireEvent.click(toggle);

    // Check that localStorage.setItem was called
    expect(window.localStorage.setItem).toHaveBeenCalledWith(
      'showHiddenFields_test-object',
      'true'
    );
  });
});
