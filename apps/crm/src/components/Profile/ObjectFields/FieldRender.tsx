import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import {
  type Active,
  DndContext,
  type Over,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ActionIcon, Box, Flex, Grid, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { FieldTypes } from '@resola-ai/ui/components';
import { IconEye, IconEyeOff, IconGripVertical, IconLetterCase } from '@tabler/icons-react';
import startCase from 'lodash/startCase';
import React, { type CSSProperties, useCallback } from 'react';
import { Field } from './Field';
import GroupActionMenu from './GroupActionMenu';
import type { ProfileData } from './RenderProfileTypes';

const useStyle = createStyles((theme) => ({
  row: {
    display: 'flex',
    alignItems: 'center',
    gap: rem(10),

    '.tabler-icon-edit, .tabler-icon-dots-vertical': {
      color: theme.colors.decaGray[4],
      visibility: 'hidden',
    },

    '&:hover': {
      '.tabler-icon-edit, .tabler-icon-dots-vertical': {
        visibility: 'visible',
      },
    },
    '.hyperlink': {
      color: theme.colors.decaBlue[4],
    },
  },
  gripVertical: {
    color: theme.colors.decaGrey[4],
  },
}));

const UN_EDIT_TYPES = [
  FieldTypes.AUTONUMBER,
  FieldTypes.CREATED_TIME,
  FieldTypes.MODIFIED_TIME,
  FieldTypes.CREATED_BY,
  FieldTypes.MODIFIED_BY,
] as string[];

interface Props {
  fields: ProfileData[];
  hideShowFieldToggle?: boolean;
  setModalData?: (data: ProfileData) => void;
  openViewAllModal?: () => void;
  handleToggleVisibility?: (isVisible: boolean, fieldId?: string) => void;
  onFieldsReordered?: (reorderedFields: ProfileData[]) => void;
  menuProps?: {
    availableGroups: any[];
    onMoveToGroup: (fieldId: string, groupId: string) => void;
    groupId: string;
  };
  allowEdit?: boolean;
}

export const RowField = ({
  data,
  hideShowFieldToggle,
  setModalData,
  openViewAllModal,
  handleToggleVisibility,
  menuProps,
  allowEdit: allowEditProp = true,
}: Omit<Props, 'fields' | 'onFieldsReordered'> & { data: ProfileData } & {
  handleToggleVisibility?: (isVisible: boolean, fieldId?: string) => void;
}) => {
  const { attributes, isDragging, listeners, setNodeRef, transform, transition } = useSortable({
    id: data.id || '',
    disabled: !hideShowFieldToggle,
  });

  const onToggleVisibility = useCallback(
    (newVisible: boolean) => {
      if (handleToggleVisibility) {
        handleToggleVisibility(newVisible, data.id);
      }
    },
    [data.id, handleToggleVisibility]
  );

  const style: CSSProperties = {
    opacity: isDragging ? 0.4 : undefined,
    transform: CSS.Translate.toString(transform),
    transition,
  };
  const { activeView } = useWorkspaceContext();
  const { classes } = useStyle();

  const allowEdit =
    !hideShowFieldToggle &&
    !UN_EDIT_TYPES.includes(data.type) &&
    !activeView?.locked &&
    !data.isProtected &&
    allowEditProp;

  return (
    <Grid {...attributes} {...listeners} sx={{ wordBreak: 'break-all' }}>
      <Grid.Col span={5} ref={setNodeRef} style={style}>
        <Flex align={'center'} gap={rem(6)}>
          {hideShowFieldToggle && (
            <>
              <IconGripVertical size={18} className={classes.gripVertical} />
              {data.isDetailVisible ? (
                <ActionIcon
                  variant='subtle'
                  c='decaBlue.4'
                  mr={rem(6)}
                  radius='xl'
                  onClick={() => onToggleVisibility(false)}
                >
                  <IconEye size={16} />
                </ActionIcon>
              ) : (
                <ActionIcon
                  variant='subtle'
                  c='decaGrey.4'
                  mr={rem(6)}
                  radius='xl'
                  onClick={() => onToggleVisibility(true)}
                >
                  <IconEyeOff size={16} />
                </ActionIcon>
              )}
            </>
          )}

          {data.icon ? <data.icon size={16} /> : <IconLetterCase size={16} />}
          <Text fz='md' fw={400} opacity={data.isDetailVisible ? 1 : 0.5}>
            {startCase(data.name?.toLowerCase())}
          </Text>
        </Flex>
      </Grid.Col>
      <Grid.Col span={7} className={classes.row} ref={setNodeRef} style={style}>
        <Flex align='center' justify='space-between' w='100%' gap={rem(4)}>
          <Box style={{ flex: 1 }}>
            <Field
              data={data}
              allowEdit={allowEdit}
              setModalData={setModalData}
              openViewAllModal={openViewAllModal}
            />
          </Box>

          {menuProps && (
            <Flex miw={rem(40)} justify='flex-end' align='center'>
              <GroupActionMenu
                field={data}
                availableGroups={menuProps.availableGroups}
                onMoveToGroup={menuProps.onMoveToGroup}
                groupId={menuProps.groupId}
              />
            </Flex>
          )}
        </Flex>
      </Grid.Col>
    </Grid>
  );
};

const FieldRender = ({
  fields,
  hideShowFieldToggle = false,
  setModalData,
  openViewAllModal,
  handleToggleVisibility,
  onFieldsReordered,
  menuProps,
  allowEdit = true,
}: Props) => {
  const { setCustomObjectFields } = useProfileContext();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const onDragEnd = useCallback(
    (active: Active, over: Over) => {
      if (!active || !over || !active.id || !over.id) return;

      const activeIndex = fields.findIndex(({ id }) => id === active.id);
      const overIndex = fields.findIndex(({ id }) => id === over.id);

      if (activeIndex === -1 || overIndex === -1) return;

      // Create a new array with the reordered fields
      const arrayMoved = arrayMove([...fields], activeIndex, overIndex);

      // Notify parent component if callback is provided
      if (onFieldsReordered) {
        onFieldsReordered(arrayMoved);
      } else {
        setCustomObjectFields((prevFields) => {
          const updatedFields = [...prevFields];

          // Create a map for new ordering positions
          const newOrderMap = new Map();
          arrayMoved.forEach((field, index) => {
            if (field.id) {
              newOrderMap.set(field.id, index);
            }
          });

          // Sort the fields based on the new ordering
          updatedFields.sort((a, b) => {
            const aIndex = newOrderMap.get(a.id) ?? Number.MAX_SAFE_INTEGER;
            const bIndex = newOrderMap.get(b.id) ?? Number.MAX_SAFE_INTEGER;
            return aIndex - bIndex;
          });

          return updatedFields;
        });
      }

      setCustomObjectFields((prevFields) => {
        const fieldMap = new Map(prevFields.map((field) => [field.id, field]));

        arrayMoved.forEach((field) => {
          if (field.id && fieldMap.has(field.id)) {
            fieldMap.set(field.id, {
              ...fieldMap.get(field.id)!,
              isDetailVisible: field.isDetailVisible,
            });
          }
        });

        return Array.from(fieldMap.values());
      });
    },
    [fields, setCustomObjectFields, onFieldsReordered]
  );

  const handleLocalToggleVisibility = useCallback(
    (isVisible: boolean, fieldId?: string) => {
      if (!fieldId) return;

      const field = fields.find((f) => f.id === fieldId);
      if (!field) return;

      if (handleToggleVisibility) {
        handleToggleVisibility(isVisible, fieldId);
      }

      const updatedFields = fields.map((field) =>
        field.id === fieldId ? { ...field, isDetailVisible: isVisible } : field
      );

      if (onFieldsReordered) {
        onFieldsReordered(updatedFields);
      }

      setCustomObjectFields((prevFields) => {
        return prevFields.map((field) =>
          field.id === fieldId ? { ...field, isDetailVisible: isVisible } : field
        );
      });
    },
    [fields, handleToggleVisibility, onFieldsReordered, setCustomObjectFields]
  );

  return (
    <DndContext
      sensors={sensors}
      onDragStart={() => {}}
      onDragEnd={({ active, over }) => {
        if (over && active.id !== over?.id) {
          onDragEnd(active, over);
        }
      }}
      onDragCancel={() => {}}
    >
      <SortableContext
        items={fields.map((f) => f.id || `field-${f.name}-${Math.random()}`)}
        strategy={verticalListSortingStrategy}
        disabled={false}
      >
        <Flex className='SortableList' gap={rem(10)} direction={'column'}>
          {fields?.map((data, index) => (
            <RowField
              data={data}
              key={data.id || `field-${index}`}
              hideShowFieldToggle={hideShowFieldToggle}
              setModalData={setModalData}
              openViewAllModal={openViewAllModal}
              handleToggleVisibility={handleLocalToggleVisibility}
              menuProps={menuProps}
              allowEdit={allowEdit}
            />
          ))}
        </Flex>
      </SortableContext>
    </DndContext>
  );
};

export default React.memo(FieldRender);
