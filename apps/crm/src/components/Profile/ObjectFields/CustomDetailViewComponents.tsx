import { UNGROUPED_ID } from '@/constants/workspace';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Accordion,
  ActionIcon,
  Box,
  Flex,
  Text,
  TextInput,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useForm } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import { Modal } from '@resola-ai/ui';
import { IconGripVertical, IconX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { useEffect, type CSSProperties, useCallback } from 'react';
import FieldRender from './FieldRender';
import type { InternalFieldGroup } from './GroupActionMenu';
import type { ProfileData } from './RenderProfileTypes';

const useStyle = createStyles((theme) => ({
  description: {
    borderRadius: theme.radius.md,
  },
  groupHeader: {
    borderRadius: theme.radius.sm,
  },
  groupDragHandle: {
    cursor: 'grab',
  },
  groupContainer: {
    marginBottom: rem(16),
  },
  accordion: {
    backgroundColor: theme.colors.decaLight[1],
    border: `1px solid ${theme.colors.decaLight[1]}`,
    borderRadius: theme.radius.md,
  },
}));

export interface FieldRenderWithMenuProps {
  fields: ProfileData[];
  groupId: string;
  availableGroups: InternalFieldGroup[];
  onMoveToGroup: (fieldId: string, groupId: string) => void;
  hideShowFieldToggle?: boolean;
  setModalData?: (data: any) => void;
  openViewAllModal?: () => void;
  onFieldsReordered?: (reorderedFields: ProfileData[]) => void;
}

export interface SortableGroupComponentProps {
  group: InternalFieldGroup;
  fields: ProfileData[];
  onEditGroup: (groupId: string, newTitle: string) => void;
  onDeleteGroup: (groupId: string) => void;
  availableGroups?: InternalFieldGroup[];
  onMoveToGroup?: (fieldId: string, groupId: string) => void;
  onFieldsReordered?: (groupId: string, updatedFields: ProfileData[]) => void;
  onValidationChange: (groupId: string, isValid: boolean) => void;
  isUngrouped?: boolean;
  allGroups: InternalFieldGroup[];
}

export interface UngroupedFieldsComponentProps {
  fields: ProfileData[];
  availableGroups: InternalFieldGroup[];
  onMoveToGroup: (fieldId: string, groupId: string) => void;
  onFieldsReordered: (updatedFields: ProfileData[]) => void;
}

export const FieldRenderWithMenu = React.memo<FieldRenderWithMenuProps>(
  ({ fields, groupId, availableGroups, onMoveToGroup, ...props }) => {
    // Create a wrapper for visibility toggle handling
    const handleToggleVisibility = useCallback(
      (isVisible: boolean, fieldId?: string) => {
        if (!fieldId) return;

        // Create a new array with updated visibility state
        const updatedFields = fields.map((field) =>
          field.id === fieldId ? { ...field, isDetailVisible: isVisible } : field
        );

        if (props.onFieldsReordered) {
          props.onFieldsReordered(updatedFields);
        }
      },
      [fields, props.onFieldsReordered]
    );

    // Enhanced props to pass to FieldRender that include menu-related props
    const fieldRenderProps = {
      ...props,
      handleToggleVisibility,
      // Add menu props if we have groups and a move handler
      menuProps:
        availableGroups && availableGroups.length > 0 && typeof onMoveToGroup === 'function'
          ? {
              availableGroups,
              onMoveToGroup,
              groupId,
            }
          : undefined,
    };

    return (
      <Box pos='relative' w='100%'>
        <FieldRender fields={fields} {...fieldRenderProps} />
      </Box>
    );
  }
);
FieldRenderWithMenu.displayName = 'FieldRenderWithMenu';

export const SortableGroupComponent = React.memo<SortableGroupComponentProps>(
  ({
    group,
    fields,
    onEditGroup,
    onDeleteGroup,
    availableGroups = [],
    onMoveToGroup,
    onFieldsReordered,
    onValidationChange,
    isUngrouped = false,
    allGroups,
  }) => {
    const { classes } = useStyle();
    const theme = useMantineTheme();
    const { t } = useTranslate('workspace');
    const [deleteConfirmOpened, { open: openDeleteConfirm, close: closeDeleteConfirm }] =
      useDisclosure(false);

    const form = useForm({
      initialValues: {
        title: group.title,
      },
      validate: {
        title: (value) => {
          if (!value.trim()) {
            return t('groupNameRequired');
          }

          const isDuplicate = allGroups.some(
            (g) => g.id !== group.id && g.title.toLowerCase() === value.trim().toLowerCase()
          );

          if (isDuplicate) {
            return t('duplicateGroupName');
          }

          return null;
        },
      },
    });

    useEffect(() => {
      const isValid = !form.validate().hasErrors;
      onValidationChange(group.id, isValid);
    }, []);

    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
      id: group.id,
      data: { type: 'group', group },
      disabled: isUngrouped,
    });

    const style: CSSProperties = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.4 : undefined,
      position: 'relative',
    };

    const validateAndReport = useCallback(() => {
      const isValid = !form.validate().hasErrors;
      onValidationChange(group.id, isValid);
      return isValid;
    }, [form, group.id, onValidationChange]);

    const handleSaveTitle = useCallback(() => {
      const isValid = validateAndReport();
      if (!isValid) return;

      onEditGroup(group.id, form.values.title);
    }, [form, onEditGroup, group.id, validateAndReport]);

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        e.stopPropagation();

        if (e.key === 'Enter') {
          handleSaveTitle();
        } else if (e.key === 'Escape') {
          form.setFieldValue('title', group.title);
          validateAndReport();
        }
      },
      [handleSaveTitle, group.title, form, validateAndReport]
    );

    const handleInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        form.setFieldValue('title', e.target.value);
        validateAndReport();
      },
      [form, validateAndReport]
    );

    const handleDeleteConfirm = useCallback(() => {
      onDeleteGroup(group.id);
      closeDeleteConfirm();
    }, [onDeleteGroup, group.id, closeDeleteConfirm]);

    const handleFieldsReordered = useCallback(
      (reorderedFields: ProfileData[]) => {
        if (onFieldsReordered) {
          onFieldsReordered(group.id, reorderedFields);
        }
      },
      [group.id, onFieldsReordered]
    );

    useEffect(() => {
      if (form.values.title !== group.title) {
        form.setFieldValue('title', group.title);
      }
    }, [group.title]);

    if (isUngrouped) {
      return (
        <Flex
          direction='column'
          ref={setNodeRef}
          style={style}
          className={classes.groupContainer}
          w={'100%'}
        >
          <FieldRenderWithMenu
            fields={fields}
            hideShowFieldToggle={true}
            onFieldsReordered={handleFieldsReordered}
            groupId={group.id}
            availableGroups={availableGroups}
            onMoveToGroup={onMoveToGroup!}
          />
        </Flex>
      );
    }

    return (
      <Flex direction='row' ref={setNodeRef} style={style} className={classes.groupContainer}>
        <Box
          {...attributes}
          {...listeners}
          className={classes.groupDragHandle}
          mr={rem(8)}
          c={theme.colors.decaGrey[4]}
        >
          <IconGripVertical size={16} />
        </Box>
        <Accordion
          w={'100%'}
          defaultValue={[group.id]}
          multiple
          variant='filled'
          chevronPosition='left'
          className={classes.accordion}
        >
          <Accordion.Item value={group.id}>
            <Accordion.Control bg={theme.colors.decaLight[1]}>
              <Flex className={classes.groupHeader} direction={'column'} gap={rem(4)} mb={rem(8)}>
                <Flex align='flex-start' justify='space-between' gap={rem(12)}>
                  <TextInput
                    w={'100%'}
                    {...form.getInputProps('title')}
                    onChange={handleInputChange}
                    onBlur={handleSaveTitle}
                    onKeyDown={handleKeyDown}
                    autoFocus={!form.values.title.trim()}
                    onClick={(e) => e.stopPropagation()}
                    onFocus={(e) => e.stopPropagation()}
                  />
                  <ActionIcon
                    mt={rem(4)}
                    variant='subtle'
                    color='red'
                    onClick={(e) => {
                      e.stopPropagation();
                      openDeleteConfirm();
                    }}
                  >
                    <IconX size={16} />
                  </ActionIcon>
                </Flex>
                <Text size='xs' c='dimmed' ml={rem(4)} mt={rem(4)}>
                  {fields.filter((field) => field.isDetailVisible).length}/{fields.length}{' '}
                  {t('fieldsShownInGroup')}
                </Text>
              </Flex>
            </Accordion.Control>
            <Accordion.Panel bg='white'>
              <Box pos='relative'>
                <FieldRenderWithMenu
                  fields={fields}
                  hideShowFieldToggle={true}
                  onFieldsReordered={handleFieldsReordered}
                  groupId={group.id}
                  availableGroups={availableGroups}
                  onMoveToGroup={onMoveToGroup!}
                />
              </Box>
            </Accordion.Panel>
          </Accordion.Item>
        </Accordion>

        <Modal
          opened={deleteConfirmOpened}
          onClose={closeDeleteConfirm}
          title={t('deleteConfirmation')}
          centered
          cancelText={t('cancel')}
          deleteText={t('delete')}
          onDelete={handleDeleteConfirm}
          onCancel={closeDeleteConfirm}
          size='md'
        >
          <Text mb={20}>{t('deleteGroupConfirmation', { group: group.title })}</Text>
        </Modal>
      </Flex>
    );
  }
);
SortableGroupComponent.displayName = 'SortableGroupComponent';

export const UngroupedFieldsComponent = React.memo<UngroupedFieldsComponentProps>(
  ({ fields, availableGroups, onMoveToGroup, onFieldsReordered }) => {
    const { classes } = useStyle();

    // Create a dedicated handler for field reordering
    const handleFieldsReordered = useCallback(
      (reorderedFields: ProfileData[]) => {
        if (onFieldsReordered) {
          onFieldsReordered(reorderedFields);
        }
      },
      [onFieldsReordered]
    );

    return (
      <Box mt={rem(20)}>
        <Flex direction='column' className={classes.groupContainer} w={'100%'}>
          <FieldRenderWithMenu
            fields={fields}
            hideShowFieldToggle={true}
            onFieldsReordered={handleFieldsReordered}
            groupId={UNGROUPED_ID}
            availableGroups={availableGroups}
            onMoveToGroup={onMoveToGroup}
          />
        </Flex>
      </Box>
    );
  }
);
UngroupedFieldsComponent.displayName = 'UngroupedFieldsComponent';
