import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { DateTimeConfig, RelationshipConfig } from '@/models/workspace';
import { validateInput } from '@/utils';
import { ActionIcon, Box, Flex, Menu, Text, TextInput, rem } from '@mantine/core';
import { DecaCheckbox, ErrorMessage } from '@resola-ai/ui';
import { FieldTypes } from '@resola-ai/ui/components';
import { IconDotsVertical, IconEdit } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { get } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  DateTimeCell,
  ImageCell,
  LinkToRecordCell,
  MultiSelectCell,
  SingleSelectCell,
} from '../../TableCellRendering/EditCell';
import { type ProfileData, RenderProfileTypes } from './RenderProfileTypes';

export const Field = ({
  data,
  allowEdit,
  setModalData,
  openViewAllModal,
}: {
  data: ProfileData;
  allowEdit: boolean;
  setModalData?: (data: ProfileData) => void;
  openViewAllModal?: () => void;
}) => {
  const ref = useRef<HTMLFormElement>(null);
  const defaultValue = get(data, 'mapValue');
  const { t } = useTranslate('workspace');
  const [value, setValue] = useState(defaultValue);
  const { onSaveData, currRecordIndex, handleViewChange, activeView } = useWorkspaceContext();
  const { editFields, onEditField, resetEditFields, mutateProfile, handleSaveRecord } =
    useProfileContext();
  const config = data.options;
  const [error, setError] = useState('');
  const format = config?.numberFormat;
  const type = data.type;
  const isEdit = editFields?.[data.id!];

  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);

  const handleSaveData = async (
    val: number | string | string[] | boolean | RelationshipConfig,
    closed = true
  ) => {
    if (currRecordIndex === -1) {
      await handleSaveRecord(val, data.id);
    } else {
      await onSaveData(val, currRecordIndex, data.id, false);
    }
    currRecordIndex === -1 && mutateProfile();
    closed && resetEditFields();
  };

  const handleBlur = () => {
    resetEditFields();
    if (!error && defaultValue !== value) {
      if (
        [FieldTypes.CURRENCY, FieldTypes.PERCENT, FieldTypes.NUMBER].includes(type as any) &&
        value !== ''
      ) {
        handleSaveData(+value);
      } else {
        handleSaveData(value || '');
      }
    } else {
      setValue(defaultValue);
    }
    setError('');
  };

  const handleChange = useCallback((e) => {
    const value = e.target.value;
    setValue(value);
    setError(validateInput(value, type, t, format));
  }, []);

  const onClickEdit = useCallback(
    (e) => {
      e.stopPropagation();
      onEditField(data.id!);
      setTimeout(() => {
        if (ref.current) {
          const input = ref.current.querySelector('input');
          if (input) {
            input.focus();
          }
        }
      });
    },
    [data.id]
  );

  const handlePinToTabbar = () => {
    const longTextList = activeView?.displayLongText || [];
    if (activeView) {
      handleViewChange(activeView.id, {
        ...activeView,
        displayLongText: [...longTextList, data.id!],
      });
    }
  };

  const renderTypeEdit = () => {
    switch (type) {
      case FieldTypes.DATETIME:
        return (
          <DateTimeCell
            value={value}
            config={config as DateTimeConfig}
            autoOpen={false}
            onChange={(val) => {
              val && handleSaveData(val);
            }}
          />
        );
      case FieldTypes.CHECKBOX:
        return (
          <DecaCheckbox
            checked={value}
            onChange={(e) => {
              setValue(e.target.checked);
              handleSaveData(e.target.checked);
            }}
          />
        );
      case FieldTypes.SINGLE_SELECT:
        return (
          <SingleSelectCell
            cell={{ getValue: () => value, column: { id: data.id } } as any}
            config={config}
            onChange={(val) => {
              val !== undefined && handleSaveData(val);
              val === undefined && resetEditFields();
            }}
          />
        );
      case FieldTypes.MULTI_SELECT:
        return (
          <Box>
            <MultiSelectCell
              cell={{ getValue: () => value, column: { id: data.id } } as any}
              config={config}
              onChange={(val, closed) => {
                val && handleSaveData(val, closed);
              }}
            />
          </Box>
        );
      case FieldTypes.IMAGE:
        return <ImageCell value={value} onChange={handleSaveData} isEditable={true} />;
      case FieldTypes.RELATIONSHIP:
        return (
          <LinkToRecordCell
            value={value}
            config={config}
            onChange={(val) => {
              val && handleSaveData(val);
            }}
          />
        );
      default:
        return (
          <>
            <TextInput
              autoFocus
              value={value}
              onBlur={handleBlur}
              onChange={(e) => handleChange(e)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleBlur();
                }
              }}
            />
            {error && <ErrorMessage mb={rem(10)} message={error || ''} />}
          </>
        );
    }
  };

  const isDisabled = !data.isDetailVisible;

  return (
    <Flex w={'100%'} align={'center'} gap={rem(12)} data-testid={`field-${data.id}-test-id`}>
      {isEdit ? (
        <Box sx={{ cursor: 'pointer' }}>{renderTypeEdit()}</Box>
      ) : (
        <Text
          component='div'
          fz='md'
          fw={500}
          lineClamp={3}
          sx={{
            whiteSpace: 'pre-wrap',
            opacity: isDisabled ? 0.5 : 1,
            pointerEvents: isDisabled ? 'none' : 'auto',
          }}
        >
          <RenderProfileTypes field={data} />
        </Text>
      )}

      {allowEdit &&
        !isEdit &&
        (type !== FieldTypes.LONG_TEXT ? (
          <ActionIcon
            data-testid={`field-${data.id}-edit-test-id`}
            size={24}
            radius='xl'
            variant='subtle'
            c='decaGrey.4'
            onClick={(e) => onClickEdit(e)}
          >
            <IconEdit size={16} />
          </ActionIcon>
        ) : (
          <Menu>
            <Menu.Target>
              <ActionIcon
                size={rem(18)}
                variant='subtle'
                c='decaGrey.4'
                data-testid={`field-${data.id}-menu-test-id`}
              >
                <IconDotsVertical />
              </ActionIcon>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item
                onClick={() => {
                  setModalData?.(data);
                  openViewAllModal?.();
                }}
              >
                {t('viewAll')}
              </Menu.Item>
              <Menu.Item
                disabled={activeView?.displayLongText?.includes(data.id!)}
                onClick={handlePinToTabbar}
              >
                {t('pinToTabbar')}
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        ))}
    </Flex>
  );
};
