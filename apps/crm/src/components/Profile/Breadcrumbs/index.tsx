import { useBreadcrumbContext } from '@/contexts/BreadcrumbContext';
import {
  Flex,
  Breadcrumbs as MantineBreadcrumbs,
  Menu,
  Text,
  UnstyledButton,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaTooltip } from '@resola-ai/ui/components';
import { IconChevronRight, IconDots } from '@tabler/icons-react';
import React, { useState, useMemo, useCallback, memo } from 'react';

const useStyles = createStyles((theme) => ({
  breadcrumbText: {
    fontSize: rem(14),
    fontWeight: 500,
    cursor: 'pointer',
    textDecoration: 'none',
    verticalAlign: 'middle',
  },
  ellipsisButton: {
    padding: 0,
    borderRadius: rem(4),
    cursor: 'pointer',
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    '&:hover': {
      backgroundColor: theme.colors.decaLight[2],
    },
  },
  menuItem: {
    fontSize: rem(14),
    fontWeight: 500,
  },
}));

const Breadcrumbs: React.FC = () => {
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const { breadcrumbs, navigateToBreadcrumb } = useBreadcrumbContext();
  const [ellipsisMenuOpened, setEllipsisMenuOpened] = useState(false);

  // All hooks must be called before any early returns
  const handleEllipsisToggle = useCallback(() => {
    setEllipsisMenuOpened((prev) => !prev);
  }, []);

  const handleMenuClose = useCallback(() => {
    setEllipsisMenuOpened(false);
  }, []);

  // Calculate visibility state with useMemo for performance
  const visibilityState = useMemo(() => {
    if (!breadcrumbs || breadcrumbs.length === 0) {
      return {
        isEmpty: true,
        showEllipsis: false,
        firstItems: [],
        hiddenItems: [],
        lastItems: [],
      };
    }

    const showEllipsis = breadcrumbs.length > 5;
    return {
      isEmpty: false,
      showEllipsis,
      firstItems: showEllipsis ? breadcrumbs.slice(0, 1) : breadcrumbs,
      hiddenItems: showEllipsis ? breadcrumbs.slice(1, -4) : [],
      lastItems: showEllipsis ? breadcrumbs.slice(-4) : [],
    };
  }, [breadcrumbs]);

  const { isEmpty, showEllipsis, firstItems, hiddenItems, lastItems } = visibilityState;

  // Memoized helper function to render breadcrumb item
  const renderBreadcrumbItem = useCallback(
    (item: any, originalIndex: number, isFirst = false) => (
      <React.Fragment key={item.id}>
        {isFirst && (
          <Text component='span' mx={rem(8)} c='decaGrey.1' lh={1} fz={14} display='inline-block'>
            |
          </Text>
        )}

        <DecaTooltip
          label={`${item.objectName}: ${item.recordName}`}
          position='top'
          withArrow
          offset={10}
        >
          <Text
            component='a'
            className={classes.breadcrumbText}
            onClick={() => navigateToBreadcrumb(originalIndex)}
            truncate='end'
            c={originalIndex === breadcrumbs?.length - 1 ? 'decaGrey.9' : 'decaGrey.4'}
            maw={rem(170)}
            display='inline-block'
          >
            {item.recordName}
          </Text>
        </DecaTooltip>
      </React.Fragment>
    ),
    [navigateToBreadcrumb, breadcrumbs?.length]
  );

  // Memoized helper function to render ellipsis dropdown
  const renderEllipsis = useCallback(
    () => (
      <Menu
        opened={ellipsisMenuOpened}
        onChange={setEllipsisMenuOpened}
        position='bottom-start'
        withArrow
      >
        <Menu.Target>
          <UnstyledButton className={classes.ellipsisButton} onClick={handleEllipsisToggle}>
            <IconDots size={14} color={theme.colors.decaGrey[4]} />
          </UnstyledButton>
        </Menu.Target>

        <Menu.Dropdown>
          {hiddenItems.map((item, hiddenIndex) => {
            const originalIndex = 1 + hiddenIndex; // Calculate original index
            return (
              <Menu.Item
                key={item.id}
                className={classes.menuItem}
                onClick={() => {
                  navigateToBreadcrumb(originalIndex);
                  handleMenuClose();
                }}
              >
                <DecaTooltip
                  label={`${item.objectName}: ${item.recordName}`}
                  position='right'
                  withArrow
                  offset={10}
                >
                  <Text truncate='end' maw={rem(200)}>
                    {item.recordName}
                  </Text>
                </DecaTooltip>
              </Menu.Item>
            );
          })}
        </Menu.Dropdown>
      </Menu>
    ),
    [
      ellipsisMenuOpened,
      setEllipsisMenuOpened,
      handleEllipsisToggle,
      hiddenItems,
      navigateToBreadcrumb,
      handleMenuClose,
    ]
  );

  // Memoize breadcrumb items construction
  const breadcrumbItems = useMemo(() => {
    if (isEmpty) return [];

    const items: React.ReactNode[] = [];

    if (!showEllipsis) {
      // Show all items if 5 or fewer
      breadcrumbs?.forEach((item, index) => {
        items.push(renderBreadcrumbItem(item, index, index === 0));
      });
    } else {
      // Show first items
      firstItems.forEach((item, index) => {
        items.push(renderBreadcrumbItem(item, index, index === 0));
      });

      // Add ellipsis
      items.push(<React.Fragment key='ellipsis'>{renderEllipsis()}</React.Fragment>);

      // Show last items
      lastItems.forEach((item, index) => {
        const originalIndex = (breadcrumbs?.length || 0) - lastItems.length + index;
        items.push(renderBreadcrumbItem(item, originalIndex));
      });
    }

    return items;
  }, [
    isEmpty,
    showEllipsis,
    breadcrumbs,
    firstItems,
    lastItems,
    renderBreadcrumbItem,
    renderEllipsis,
  ]);

  // Early return after all hooks
  if (isEmpty) {
    return null;
  }

  return (
    <Flex align='center' flex={1}>
      <MantineBreadcrumbs
        separator={<IconChevronRight size={14} />}
        styles={{
          separator: {
            marginLeft: rem(8),
            marginRight: rem(8),
          },
        }}
      >
        {breadcrumbItems}
      </MantineBreadcrumbs>
    </Flex>
  );
};

export default memo(Breadcrumbs);
