import { ColumnIcon } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import type { Record as RecordType, WSObject } from '@/models';
import { ObjectAPI } from '@/services/api';
import { Accordion, Box, Button, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Modal } from '@resola-ai/ui';
import type { FieldType } from '@resola-ai/ui/components';
import { FieldTypes } from '@resola-ai/ui/components';
import { IconPinnedOff } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { useCallback, useState } from 'react';
import { useParams } from 'react-router-dom';
import FieldRender from '../ObjectFields/FieldRender';
import type { ProfileData } from '../ObjectFields/RenderProfileTypes';

const useStyles = createStyles((theme) => ({
  accordion: {
    borderRadius: rem(16),
    border: `1px solid ${theme.colors.decaLight[2]}`,
    padding: 0,
    paddingTop: rem(16),
    gap: rem(4),
    flexDirection: 'column',
    '.mantine-Accordion-item': {
      marginBottom: rem(16),
      borderBottom: 'none',
      '&[data-active]': {
        '& .mantine-Accordion-control': {
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
        },
      },
      '& .mantine-Accordion-control': {
        '& .mantine-Accordion-label': {
          padding: `${rem(4)} ${rem(8)}`,
          backgroundColor: theme.colors.decaViolet[0],
          borderRadius: rem(4),
        },
        '&:hover': {
          backgroundColor: 'transparent',
        },
      },
    },
    '.mantine-Accordion-content': {
      padding: rem(16),
    },
  },
}));

interface ChildObjectFieldsProps {
  form?: {
    linkedObj?: WSObject;
    records?: RecordType[];
    template?: string;
  };
}

// Helper to create profile data for a single record
const createProfileDataForRecord = (
  record: RecordType,
  recordIndex: number,
  linkedObj: WSObject
): ProfileData[] => {
  const fields: ProfileData[] = [];

  // Use the field schema from linkedObj.fields instead of record keys
  // This ensures we show all defined fields, even if they don't have values
  if (linkedObj.fields) {
    linkedObj.fields.forEach((fieldDef) => {
      // Skip system fields if they exist in the schema
      if (
        fieldDef.id === 'id' ||
        fieldDef.id === 'objectId' ||
        fieldDef.id === 'createdAt' ||
        fieldDef.id === 'updatedAt'
      ) {
        return;
      }

      // Create a unique field ID that includes the record index
      const uniqueFieldId = `${linkedObj.id}-${fieldDef.id}-${recordIndex}`;

      // Get the value from the record data
      const fieldValue = record[fieldDef.id!] || record[fieldDef.name!] || '';

      fields.push({
        id: uniqueFieldId,
        name: fieldDef.name,
        type: fieldDef.type,
        options: fieldDef.options,
        mapValue: fieldValue,
        icon: ColumnIcon[fieldDef.type as FieldType] || ColumnIcon[FieldTypes.SINGLE_LINE_TEXT],
        isDetailVisible: true,
      } as ProfileData);
    });
  }

  return fields;
};

const ChildObjectFields: React.FC<ChildObjectFieldsProps> = ({ form }) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();
  const { object, refetchObject } = useWorkspaceContext();
  const { wsId } = useParams<{ wsId: string }>();

  if (!form?.linkedObj || !form?.records || form.records.length === 0) {
    return (
      <Box p={rem(16)}>
        <Text c='dimmed'>{t('noDataAvailable')}</Text>
      </Box>
    );
  }

  const [isUnpinModalOpen, setIsUnpinModalOpen] = useState(false);

  // Handle field visibility toggling (placeholder - always visible for child objects)
  const handleToggleVisibility = (isVisible: boolean, fieldId?: string) => {
    // For child objects, we don't need to implement this as fields are always visible
    console.log('Toggle visibility:', isVisible, fieldId);
  };

  // Handle unpinning of custom object
  const handleTogglePin = useCallback(async () => {
    if (!object || !form?.linkedObj?.id || !wsId) return;

    try {
      // Update the childObjects array
      const updatedChildObjects =
        object.childObjects?.map((childObj) => {
          if (childObj.id === form.linkedObj?.id) {
            return { ...childObj, pinned: false };
          }
          return childObj;
        }) || [];

      // Update the object with the modified childObjects
      await ObjectAPI.update(wsId, {
        ...object,
        childObjects: updatedChildObjects,
      });

      // Refetch the object to get the updated data
      refetchObject();
    } catch (error) {
      console.error('Failed to update pin status:', error);
    }
  }, [object, form?.linkedObj?.id, refetchObject, wsId]);

  const objectName = form.linkedObj.name?.singular || form.linkedObj.name?.plural || 'Record';

  return (
    <Box h={'100%'} py={0} data-testid='child-object-fields-test-id'>
      <Flex justify='space-between' align='center' mb={rem(16)}>
        <Text fz={rem(18)} fw={500}>
          {objectName}
        </Text>
        <Button
          leftSection={<IconPinnedOff size={16} />}
          variant='subtle'
          size='sm'
          fw={500}
          fz={rem(14)}
          c='decaGrey.6'
          miw={rem(100)}
          onClick={() => setIsUnpinModalOpen(true)}
        >
          {t('unpinTabbar')}
        </Button>
      </Flex>
      <Accordion
        chevronPosition='left'
        multiple
        className={classes.accordion}
        bg='white'
        display='flex'
      >
        {form.records.map((record, recordIndex) => {
          // Create profile data for this specific record
          const profileData = createProfileDataForRecord(record, recordIndex, form.linkedObj!);

          return (
            <Accordion.Item
              key={record.id}
              value={`record-${record.id}`}
              data-testid={`record-${record.id}-test-id`}
            >
              <Accordion.Control>
                <Flex justify='space-between' align='center' w='100%'>
                  <Text fz={rem(16)} fw={500}>
                    {objectName} - {record.id}
                  </Text>
                  <Text fz='sm' fw={500} c='decaGrey.4'>
                    {t('totalFields', {
                      num: profileData.length,
                      total: profileData.length,
                    })}
                  </Text>
                </Flex>
              </Accordion.Control>
              <Accordion.Panel>
                <Box
                  pl={rem(32)}
                  data-testid={`child-object-fields-content-${recordIndex}-test-id`}
                >
                  <FieldRender
                    fields={profileData}
                    hideShowFieldToggle={false}
                    handleToggleVisibility={handleToggleVisibility}
                    allowEdit={false}
                  />
                </Box>
              </Accordion.Panel>
            </Accordion.Item>
          );
        })}
      </Accordion>

      <Modal
        centered
        title={t('unpinTabbar')}
        onOk={handleTogglePin}
        okText={t('confirm')}
        cancelText={t('cancel')}
        opened={isUnpinModalOpen}
        onClose={() => setIsUnpinModalOpen(false)}
        onCancel={() => setIsUnpinModalOpen(false)}
        okButtonProps={{
          bg: 'decaRed.4',
        }}
      >
        <Text>{t('unpinTabbarDescription')}</Text>
      </Modal>
    </Box>
  );
};

export default React.memo(ChildObjectFields);
