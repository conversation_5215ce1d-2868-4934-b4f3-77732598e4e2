import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import type React from 'react';
import { vi } from 'vitest';
import { type ITab, TabItem } from './TabItem';

// Mock the DecaTooltip component
vi.mock('@resola-ai/ui/components', () => ({
  DecaTooltip: ({ children, label, position }: any) => (
    <div data-testid='tooltip-wrapper' data-label={label} data-position={position}>
      {children}
    </div>
  ),
}));

// Mock the translation hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key, // Return the key itself for testing
  }),
}));

// Mock Tabler icons
vi.mock('@tabler/icons-react', () => ({
  IconDirections: () => <div data-testid='icon-directions'>DirectionsIcon</div>,
  IconFileAnalytics: () => <div data-testid='icon-file-analytics'>FileAnalyticsIcon</div>,
  IconKeyframes: () => <div data-testid='icon-keyframes'>KeyframesIcon</div>,
}));

const mockTab: ITab = {
  value: 'activities',
  component: 'activities',
  icon: <div data-testid='mock-icon'>MockIcon</div>,
};

const mockOnClick = vi.fn();

const renderComponent = (props: Partial<React.ComponentProps<typeof TabItem>> = {}) => {
  const defaultProps = {
    tab: mockTab,
    isMinimized: false,
    isActive: false,
    onClick: mockOnClick,
    ...props,
  };

  return renderWithMantine(<TabItem {...defaultProps} />);
};

describe('TabItem', () => {
  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      renderComponent();
      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    });

    it('renders with correct test id based on tab value', () => {
      const customTab = { ...mockTab, value: 'custom-tab' };
      renderComponent({ tab: customTab });
      expect(screen.getByTestId('tab-custom-tab')).toBeInTheDocument();
    });

    it('renders tab icon', () => {
      renderComponent();
      expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
    });

    it('renders tab text when not minimized', () => {
      renderComponent({ isMinimized: false });
      expect(screen.getByText('activities')).toBeInTheDocument();
    });

    it('does not render tab text when minimized and not active', () => {
      renderComponent({ isMinimized: true, isActive: false });
      expect(screen.queryByText('activities')).not.toBeInTheDocument();
    });

    it('renders tab text when minimized but active', () => {
      renderComponent({ isMinimized: true, isActive: true });
      expect(screen.getByText('activities')).toBeInTheDocument();
    });
  });

  describe('Active State', () => {
    it('applies active styling when isActive is true', () => {
      renderComponent({ isActive: true });
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toHaveStyle('border-bottom: 2px solid rgb(53, 57, 188)');
    });

    it('applies inactive styling when isActive is false', () => {
      renderComponent({ isActive: false });
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toHaveStyle('border-bottom: 2px solid transparent');
    });

    it('applies correct color for active tab', () => {
      renderComponent({ isActive: true });
      const tabElement = screen.getByTestId('tab-activities');
      // Note: We can't easily test the exact color values due to Mantine's color system
      // but we can verify the element exists and has styling
      expect(tabElement).toBeInTheDocument();
    });

    it('applies correct color for inactive tab', () => {
      renderComponent({ isActive: false });
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toBeInTheDocument();
    });
  });

  describe('Minimized State', () => {
    it('applies minimized styling when isMinimized is true and not active', () => {
      renderComponent({ isMinimized: true, isActive: false });
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toBeInTheDocument();
      // The component should have different width styling
    });

    it('shows tooltip when minimized', () => {
      renderComponent({ isMinimized: true });
      expect(screen.getByTestId('tooltip-wrapper')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-wrapper')).toHaveAttribute('data-label', 'activities');
      expect(screen.getByTestId('tooltip-wrapper')).toHaveAttribute('data-position', 'bottom');
    });

    it('does not show tooltip when not minimized', () => {
      renderComponent({ isMinimized: false });
      expect(screen.queryByTestId('tooltip-wrapper')).not.toBeInTheDocument();
    });

    it('applies correct width constraints when minimized and not active', () => {
      renderComponent({ isMinimized: true, isActive: false });
      const tabElement = screen.getByTestId('tab-activities');
      // The element should have specific width styling, though we can't test exact values
      expect(tabElement).toBeInTheDocument();
    });

    it('applies correct width constraints when minimized and active', () => {
      renderComponent({ isMinimized: true, isActive: true });
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toBeInTheDocument();
    });
  });

  describe('Click Handling', () => {
    it('calls onClick when tab is clicked', () => {
      const mockOnClick = vi.fn();
      renderComponent({ onClick: mockOnClick });

      const tabElement = screen.getByTestId('tab-activities');
      fireEvent.click(tabElement);

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('does not throw error when onClick is not provided', () => {
      renderComponent({ onClick: undefined });

      const tabElement = screen.getByTestId('tab-activities');
      expect(() => fireEvent.click(tabElement)).not.toThrow();
    });

    it('has cursor pointer style for clickable appearance', () => {
      renderComponent();
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toHaveStyle('cursor: pointer');
    });
  });

  describe('Text Truncation', () => {
    it('truncates long text properly', () => {
      const longTextTab = {
        ...mockTab,
        component: 'very-long-tab-name-that-should-be-truncated',
      };
      renderComponent({ tab: longTextTab });

      const textElement = screen.getByText('very-long-tab-name-that-should-be-truncated');
      expect(textElement).toBeInTheDocument();
      // The text element should have truncate class/styling
    });

    it('applies correct max-width for active tabs', () => {
      renderComponent({ isActive: true });
      const textElement = screen.getByText('activities');
      expect(textElement).toBeInTheDocument();
    });

    it('applies correct max-width for inactive tabs', () => {
      renderComponent({ isActive: false });
      const textElement = screen.getByText('activities');
      expect(textElement).toBeInTheDocument();
    });
  });

  describe('Font Weight', () => {
    it('applies correct font weight for active tab', () => {
      renderComponent({ isActive: true });
      const textElement = screen.getByText('activities');
      expect(textElement).toHaveStyle('font-weight: 500');
    });

    it('applies correct font weight for inactive tab', () => {
      renderComponent({ isActive: false });
      const textElement = screen.getByText('activities');
      expect(textElement).toHaveStyle('font-weight: 400');
    });
  });

  describe('Accessibility', () => {
    it('has proper data-testid for testing', () => {
      renderComponent();
      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    });

    it('is properly focusable', () => {
      renderComponent();
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toBeInTheDocument();
      // The element should be clickable and therefore focusable
    });
  });

  describe('Icon Rendering', () => {
    it('renders icon in correct container', () => {
      renderComponent();
      const iconContainer = screen.getByTestId('mock-icon').parentElement;
      expect(iconContainer).toBeInTheDocument();
    });

    it('icon container has correct styling', () => {
      renderComponent();
      const iconContainer = screen.getByTestId('mock-icon').parentElement;
      expect(iconContainer).toHaveStyle('flex-shrink: 0');
    });
  });

  describe('Component Variants', () => {
    it('renders different tab types correctly', () => {
      const variants = [
        { value: 'activities', component: 'activities' },
        { value: 'identities', component: 'identities' },
        { value: 'files', component: 'files' },
        { value: 'history', component: 'history' },
        { value: 'longText', component: 'longText' },
        { value: 'customObject-123', component: 'CustomObject' },
      ];

      variants.forEach((variant) => {
        const variantTab = { ...mockTab, ...variant };
        const { unmount } = renderComponent({ tab: variantTab });

        expect(screen.getByTestId(`tab-${variant.value}`)).toBeInTheDocument();
        expect(screen.getByText(variant.component)).toBeInTheDocument();

        unmount();
      });
    });
  });

  describe('Styling Transitions', () => {
    it('has transition styling for smooth hover effects', () => {
      renderComponent();
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toHaveStyle('transition: all 0.2s ease');
    });
  });

  describe('Layout Properties', () => {
    it('has correct display properties', () => {
      renderComponent();
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toHaveStyle('display: inline-flex');
    });

    it('has correct white-space handling', () => {
      renderComponent();
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toHaveStyle('white-space: nowrap');
    });

    it('has correct alignment properties', () => {
      renderComponent();
      const tabElement = screen.getByTestId('tab-activities');
      expect(tabElement).toHaveStyle('align-items: center');
    });
  });

  describe('Edge Cases', () => {
    it('handles empty component name gracefully', () => {
      const emptyTab = { ...mockTab, component: '' };
      renderComponent({ tab: emptyTab });
      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    });

    it('handles missing icon gracefully', () => {
      const noIconTab = { ...mockTab, icon: null };
      renderComponent({ tab: noIconTab });
      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    });

    it('handles special characters in tab value', () => {
      const specialTab = { ...mockTab, value: 'tab-with-special-chars-123_ABC' };
      renderComponent({ tab: specialTab });
      expect(screen.getByTestId('tab-tab-with-special-chars-123_ABC')).toBeInTheDocument();
    });

    it('handles both minimized and active states simultaneously', () => {
      renderComponent({ isMinimized: true, isActive: true });
      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
      expect(screen.getByText('activities')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-wrapper')).toBeInTheDocument();
    });
  });
});
