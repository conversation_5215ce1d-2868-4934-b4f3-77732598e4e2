import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import ProfileTabs from './index';

// Mock the contexts
vi.mock('@/contexts/ProfileContext');
vi.mock('@/contexts/WorkspaceContext');

// Mock the lazy-loaded components
vi.mock('../Activities', () => ({
  default: () => <div data-testid='activities-component'>Activities Component</div>,
}));

vi.mock('../Identities', () => ({
  default: () => <div data-testid='identities-component'>Identities Component</div>,
}));

vi.mock('../Files', () => ({
  default: () => <div data-testid='files-component'>Files Component</div>,
}));

vi.mock('../History', () => ({
  default: () => <div data-testid='history-component'>History Component</div>,
}));

vi.mock('../LongText', () => ({
  default: () => <div data-testid='longtext-component'>LongText Component</div>,
}));

vi.mock('../Forms', () => ({
  default: ({ form }: { form: any }) => (
    <div data-testid='forms-component'>Forms Component - {form?.linkedObj?.name?.singular}</div>
  ),
}));

vi.mock('./ChildObjectFields', () => ({
  default: ({ form }: { form: any }) => (
    <div data-testid='child-object-fields-test-id'>
      ChildObjectFields Component - {form?.linkedObj?.name?.singular}
    </div>
  ),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

const mockUseProfileContext = useProfileContext as any;
const mockUseWorkspaceContext = useWorkspaceContext as any;

const defaultProfileContext = {
  getForm: vi.fn(),
};

const defaultWorkspaceContext = {
  object: {
    profileSettings: [
      { type: 'activities', enabled: true },
      { type: 'identities', enabled: true },
      { type: 'files', enabled: false },
      { type: 'tasks', enabled: true },
    ],
    childObjects: [],
  },
  activeView: {
    displayLongText: [],
  },
};

const renderComponent = (props = {}) => {
  const defaultProps = {
    isFullview: false,
    ...props,
  };

  return renderWithMantine(<ProfileTabs {...defaultProps} />);
};

describe('ProfileTabs', () => {
  beforeEach(() => {
    mockUseProfileContext.mockReturnValue(defaultProfileContext);
    mockUseWorkspaceContext.mockReturnValue(defaultWorkspaceContext);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      renderComponent();
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    it('renders tabs based on enabled profile settings', () => {
      renderComponent();

      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
      expect(screen.getByTestId('tab-identities')).toBeInTheDocument();
      expect(screen.getByTestId('tab-history')).toBeInTheDocument();
      expect(screen.queryByTestId('tab-files')).not.toBeInTheDocument(); // disabled
    });

    it('renders with correct accessibility attributes', () => {
      renderComponent();

      const tablist = screen.getByRole('tablist');
      expect(tablist).toBeInTheDocument();
    });

    it('returns null when no tabs are available', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [],
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();
      expect(screen.queryByRole('tablist')).not.toBeInTheDocument();
    });
  });

  describe('Tab Creation and Types', () => {
    it('creates activities tab when activities setting is enabled', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [{ type: 'activities', enabled: true }],
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();
      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    });

    it('creates identities tab when identities setting is enabled', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [{ type: 'identities', enabled: true }],
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();
      expect(screen.getByTestId('tab-identities')).toBeInTheDocument();
    });

    it('creates files tab when attachments setting is enabled', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [{ type: 'attachments', enabled: true }],
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();
      expect(screen.getByTestId('tab-files')).toBeInTheDocument();
    });

    it('creates history tab when tasks setting is enabled', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [{ type: 'tasks', enabled: true }],
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();
      expect(screen.getByTestId('tab-history')).toBeInTheDocument();
    });

    it('creates longText tab when pinLongText setting is enabled and displayLongText exists', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [{ type: 'pinLongText', enabled: true }],
          childObjects: [],
        },
        activeView: { displayLongText: ['some text'] },
      });

      renderComponent();
      expect(screen.getByTestId('tab-longText')).toBeInTheDocument();
    });

    it('does not create longText tab when displayLongText is empty', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [{ type: 'pinLongText', enabled: true }],
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();
      expect(screen.queryByTestId('tab-longText')).not.toBeInTheDocument();
    });

    it('creates custom object tabs when pinCustomObject setting is enabled', () => {
      const mockForm = {
        linkedObj: {
          name: { singular: 'CustomObject' },
        },
      };

      mockUseProfileContext.mockReturnValue({
        getForm: vi.fn().mockReturnValue(mockForm),
      });

      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [{ type: 'pinCustomObject', enabled: true }],
          childObjects: [
            { id: 'custom1', pinned: true },
            { id: 'custom2', pinned: false },
          ],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();
      expect(screen.getByTestId('tab-customObject-custom1')).toBeInTheDocument();
      expect(screen.queryByTestId('tab-customObject-custom2')).not.toBeInTheDocument();
    });
  });

  describe('Tab Switching', () => {
    it('sets the first tab as active by default', async () => {
      renderComponent();

      // Wait for the component to load and set active tab
      await waitFor(() => {
        const activitiesTab = screen.getByTestId('tab-activities');
        expect(activitiesTab).toHaveStyle('border-bottom: 2px solid rgb(53, 57, 188)');
      });
    });

    it('switches active tab when clicked', async () => {
      renderComponent();

      const identitiesTab = screen.getByTestId('tab-identities');
      fireEvent.click(identitiesTab);

      await waitFor(() => {
        expect(identitiesTab).toHaveStyle('border-bottom: 2px solid rgb(53, 57, 188)');
      });
    });

    it('renders correct component for active tab', async () => {
      renderComponent();

      const activitiesTab = screen.getByTestId('tab-activities');
      fireEvent.click(activitiesTab);

      await waitFor(() => {
        expect(screen.getByTestId('activities-component')).toBeInTheDocument();
      });
    });

    it('renders child object fields component for custom object tabs', async () => {
      const mockForm = {
        linkedObj: {
          name: { singular: 'CustomObject' },
        },
      };

      mockUseProfileContext.mockReturnValue({
        getForm: vi.fn().mockReturnValue(mockForm),
      });

      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [{ type: 'pinCustomObject', enabled: true }],
          childObjects: [{ id: 'custom1', pinned: true }],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();

      const customTab = screen.getByTestId('tab-customObject-custom1');
      fireEvent.click(customTab);

      await waitFor(() => {
        expect(screen.getByTestId('child-object-fields-test-id')).toBeInTheDocument();
      });
    });
  });

  describe('Scroll Functionality', () => {
    const createManyTabs = () => {
      const profileSettings: Array<{ type: string; enabled: boolean }> = [];
      for (let i = 0; i < 10; i++) {
        profileSettings.push({ type: 'activities', enabled: true });
      }
      return profileSettings;
    };

    it('shows navigation buttons when there are many tabs', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: createManyTabs(),
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      // Mock scrollWidth to be greater than clientWidth
      Object.defineProperty(HTMLElement.prototype, 'scrollWidth', {
        configurable: true,
        value: 1000,
      });
      Object.defineProperty(HTMLElement.prototype, 'clientWidth', {
        configurable: true,
        value: 500,
      });

      renderComponent();

      // Navigation buttons should be present when there are scroll conditions
      const scrollContainer = screen.getByRole('tablist').parentElement;
      expect(scrollContainer).toBeInTheDocument();
    });

    it('handles scroll left action', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: createManyTabs(),
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      const mockScrollBy = vi.fn();
      Object.defineProperty(HTMLElement.prototype, 'scrollBy', {
        configurable: true,
        value: mockScrollBy,
      });

      renderComponent();

      // Since we can't easily test the scroll buttons visibility without mocking scroll state,
      // we'll just verify the component renders with many tabs
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });
  });

  describe('Minimized Mode', () => {
    it('enters minimized mode when there are more than 8 tabs', () => {
      const profileSettings: Array<{ type: string; enabled: boolean }> = [];
      for (let i = 0; i < 10; i++) {
        profileSettings.push({ type: 'activities', enabled: true });
      }

      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings,
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();

      // Should render tabs in minimized mode
      const tabs = screen.getAllByTestId(/^tab-/);
      expect(tabs.length).toBe(10);
    });
  });

  describe('Lazy Loading', () => {
    it('shows loading state before components load', async () => {
      renderComponent();

      // The Suspense fallback should be shown initially
      // Since we're mocking the components, they load immediately
      // but we can verify the tab content area exists
      await waitFor(() => {
        expect(screen.getByTestId('activities-component')).toBeInTheDocument();
      });
    });
  });

  describe('Props', () => {
    it('handles isFullview prop correctly', () => {
      renderComponent({ isFullview: true });

      // Component should render normally with isFullview prop
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    it('handles isFullview false correctly', () => {
      renderComponent({ isFullview: false });

      // Component should render normally with isFullview prop
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles missing object gracefully', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: null,
        activeView: { displayLongText: [] },
      });

      renderComponent();
      expect(screen.queryByRole('tablist')).not.toBeInTheDocument();
    });

    it('handles missing profileSettings gracefully', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: null,
          childObjects: [],
        },
        activeView: { displayLongText: [] },
      });

      renderComponent();
      expect(screen.queryByRole('tablist')).not.toBeInTheDocument();
    });

    it('handles missing activeView gracefully', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          profileSettings: [{ type: 'activities', enabled: true }],
          childObjects: [],
        },
        activeView: null,
      });

      renderComponent();
      expect(screen.getByTestId('tab-activities')).toBeInTheDocument();
    });
  });
});
