import { Box, Flex, Text, rem, useMantineTheme } from '@mantine/core';
import { DecaTooltip } from '@resola-ai/ui/components';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

const MAX_TAB_WIDTH = 150;
const MIN_TAB_WIDTH = 40;

export interface ITab {
  value: string;
  component: string;
  icon: React.ReactNode;
}

interface TabItemProps {
  tab: ITab;
  isMinimized: boolean;
  isActive?: boolean;
  onClick?: () => void;
}

export const TabItem: React.FC<TabItemProps> = ({
  tab,
  isMinimized,
  isActive = false,
  onClick,
}) => {
  const { t } = useTranslate('workspace');
  const { colors } = useMantineTheme();
  const tabContent = (
    <Box
      data-testid={`tab-${tab.value}`}
      onClick={onClick}
      w={isMinimized && !isActive ? rem(MIN_TAB_WIDTH) : 'auto'}
      maw={isMinimized && !isActive ? rem(MIN_TAB_WIDTH) : isActive ? rem(200) : rem(MAX_TAB_WIDTH)}
      miw={isMinimized && !isActive ? rem(MIN_TAB_WIDTH) : rem(100)}
      px={isMinimized && !isActive ? rem(8) : rem(12)}
      py={rem(8)}
      display='inline-flex'
      c={isActive ? colors.decaNavy[4] : colors.decaGrey[6]}
      style={{
        whiteSpace: 'nowrap',
        alignItems: 'center',
        cursor: 'pointer',
        borderBottom: isActive ? `2px solid ${colors.decaNavy[4]}` : `2px solid transparent`,
        transition: 'all 0.2s ease',
      }}
    >
      <Flex align='center' style={{ flexShrink: 0 }}>
        {tab.icon}
      </Flex>
      {(!isMinimized || isActive) && (
        <Text
          size='sm'
          maw={isActive ? rem(160) : rem(MAX_TAB_WIDTH - 60)}
          ml={rem(8)}
          fw={isActive ? 500 : 400}
          truncate
        >
          {t(tab.component)}
        </Text>
      )}
    </Box>
  );

  // Show tooltip for minimized tabs
  if (isMinimized) {
    return (
      <DecaTooltip label={t(tab.component)} position='bottom'>
        {tabContent}
      </DecaTooltip>
    );
  }

  return tabContent;
};
