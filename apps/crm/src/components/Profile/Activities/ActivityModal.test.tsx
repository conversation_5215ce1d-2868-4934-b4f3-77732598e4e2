import { useActivities } from '@/hooks';
import { ActivityAPI } from '@/services/api/activity';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { showNotification } from '@mantine/notifications';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useParams } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ActivityModal } from './ActivityModal';

// Mock hooks
vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
  useTranslation: () => ({ i18n: { language: 'en' } }),
}));

vi.mock('@/hooks', () => ({
  useActivities: vi.fn(),
}));

// Mock ActivityAPI
vi.mock('@/services/api/activity', () => ({
  ActivityAPI: {
    create: vi.fn(),
  },
}));

// Mock notification
vi.mock('@mantine/notifications', () => ({
  showNotification: vi.fn(),
}));

// Mock InfoAlert using the importOriginal pattern
vi.mock('@resola-ai/ui/components', async (importOriginal) => {
  const actual = (await importOriginal()) as any;
  return {
    ...actual,
    InfoAlert: vi.fn(() => <div data-testid='info-alert'>Mocked InfoAlert</div>),
  };
});

// Mock tolgee translate
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

// Mock uuid
vi.mock('uuid', () => ({
  v4: () => 'mocked-uuid',
}));

describe('ActivityModal Component', () => {
  const mockSetOpened = vi.fn();
  const mockMutate = vi.fn();
  const mockActivities = [{ id: 'activity1' }];

  beforeEach(() => {
    vi.clearAllMocks();
    (useParams as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-123',
      id: 'object-123',
      recordId: 'record-123',
    });
    (useActivities as ReturnType<typeof vi.fn>).mockReturnValue({
      activities: mockActivities,
      mutate: mockMutate,
    });
  });

  it('renders the modal when opened is true', () => {
    renderWithMantine(<ActivityModal opened={true} setOpened={mockSetOpened} />);

    expect(screen.getByText('createActivity')).toBeInTheDocument();
    expect(screen.getByText('offlineEvent')).toBeInTheDocument();
    expect(screen.getByTestId('info-alert')).toBeInTheDocument();
  });

  it('does not render when opened is false', () => {
    renderWithMantine(<ActivityModal opened={false} setOpened={mockSetOpened} />);

    expect(screen.queryByText('createActivity')).not.toBeInTheDocument();
  });

  it('closes the modal when cancel button is clicked', async () => {
    const user = userEvent.setup();
    renderWithMantine(<ActivityModal opened={true} setOpened={mockSetOpened} />);

    const cancelButton = screen.getByText('cancel');
    await user.click(cancelButton);

    expect(mockSetOpened).toHaveBeenCalledWith(false);
  });

  it('creates an activity when form is filled and submitted', async () => {
    const user = userEvent.setup();
    (ActivityAPI.create as ReturnType<typeof vi.fn>).mockResolvedValue({ id: 'new-activity' });

    renderWithMantine(<ActivityModal opened={true} setOpened={mockSetOpened} />);

    // Fill in the title
    const titleInput = screen.getByTestId('activity-title-input');
    await user.type(titleInput, 'Test Activity');

    // Fill in the description
    const descriptionInput = screen.getByTestId('activity-description-input');
    await user.type(descriptionInput, 'Test Description');

    // Submit the form
    const saveButton = screen.getByText('save');
    await user.click(saveButton);

    // Verify API call was made with correct params
    await waitFor(() => {
      expect(ActivityAPI.create).toHaveBeenCalledWith(
        'workspace-123',
        'object-123',
        'record-123',
        expect.objectContaining({
          action: 'manualRecord',
          source: expect.objectContaining({
            id: 'mocked-uuid',
          }),
          object: expect.objectContaining({
            properties: expect.objectContaining({
              eventName: 'Test Activity',
              eventDescription: 'Test Description',
            }),
          }),
        })
      );
    });

    // Verify notification was shown
    expect(showNotification).toHaveBeenCalled();

    // Verify modal was closed
    expect(mockSetOpened).toHaveBeenCalledWith(false);

    // Verify activities were mutated
    expect(mockMutate).toHaveBeenCalledWith([...mockActivities, { id: 'new-activity' }], {
      revalidate: false,
    });
  });
});
