import { IgnoreActivityActorType, type MessageType } from '@/constants/workspace';
import { useActivities } from '@/hooks/useActivity';
import ActivityEmailSms from '@/templates/activitiesEmail';
import { syncContextData } from '@/utils';
import { Box, Center, Flex, Loader, Text, Timeline, rem } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui/components/DecaButton';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import DOMPurify from 'dompurify';
import React, { useState } from 'react';
import { ActivityModal } from './ActivityModal';

const Activities = () => {
  const { t } = useTranslate('workspace');
  const { activities } = useActivities();
  const [opened, setOpened] = useState(false);

  if (!activities)
    return (
      <Center h='100%' w='100%' data-testid='activities-loading'>
        <Loader />
      </Center>
    );
  return (
    <>
      <Flex justify='space-between' mb='md'>
        <Text fz='xl' fw={500}>
          {t('activities')}
        </Text>
        <DecaButton
          onClick={() => setOpened(true)}
          leftSection={<IconPlus size={14} />}
          variant='neutral'
          color='decaLight.5'
          size='sm'
        >
          {t('create')}
        </DecaButton>
      </Flex>
      {activities?.map((activity, i) => (
        <Timeline key={i} active={1} bulletSize={6} lineWidth={1} py={rem(18)} color='decaGrey.2'>
          <Timeline.Item
            pl={rem(16)}
            pt={rem(20)}
            lineVariant='dashed'
            bullet={<Box w={6} h={6} bg='decaGrey.2' sx={{ borderRadius: '50%' }} mr={rem(10)} />}
            sx={{
              '.mantine-Timeline-itemContent': {
                marginTop: rem(-40),
              },
            }}
          >
            {IgnoreActivityActorType.includes(activity.actor?.type || '') ? (
              <ActivityEmailSms
                type={
                  activity.actor?.type === 'mail' ? 'email' : (activity.actor?.type as MessageType)
                }
                payload={activity}
              />
            ) : (
              <div
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(syncContextData(activity.template, { ...activity })),
                }}
              />
            )}
          </Timeline.Item>
          <Timeline.Item display='none' m={0} />
        </Timeline>
      ))}
      <ActivityModal opened={opened} setOpened={setOpened} />
    </>
  );
};

export default React.memo(Activities);
