import { IconCalendarMinus } from '@/components/Icons';
import { useActivities } from '@/hooks';
import { ActivityAPI } from '@/services/api/activity';
import { customNotificationStyles } from '@/utils/workspace';
import { Group, Text, TextInput, Textarea, rem } from '@mantine/core';
import { DateTimePicker } from '@mantine/dates';
import { createStyles } from '@mantine/emotion';
import { showNotification } from '@mantine/notifications';
import { Modal } from '@resola-ai/ui';
import { InfoAlert } from '@resola-ai/ui/components';
import { IconCircleCheck } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import dayjs from 'dayjs';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { v4 as uuidV4 } from 'uuid';

const useStyles = createStyles((theme) => ({
  datetimePicker: {
    ['.mantine-DateTimePicker-icon']: {
      left: 'unset',
      right: '0',
    },
    ['.mantine-DateTimePicker-input[data-with-icon]']: {
      paddingLeft: rem(14),
    },
  },
  button: {
    marginLeft: rem(-24),
    marginRight: rem(-24),
    borderTop: `1px solid ${theme.colors.gray[3]}`,
    paddingTop: rem(16),
    paddingRight: rem(24),
  },
}));
interface ActivityModalProps {
  opened: boolean;
  setOpened: (value: boolean) => void;
}

export const ActivityModal = memo(({ opened, setOpened }: ActivityModalProps) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();
  const { i18n } = useTranslation();
  const { wsId, id: objectId, recordId } = useParams();
  const { activities, mutate } = useActivities();

  const [newActivity, setNewActivity] = useState({
    title: '',
    datetime: dayjs().toDate(),
    description: '',
  });
  const [titleError, setTitleError] = useState('');

  const handleClose = () => {
    setNewActivity({ title: '', datetime: dayjs().toDate(), description: '' });
    setOpened(false);
  };

  const handleSubmit = async () => {
    if (!wsId || !objectId || !recordId) return;
    try {
      setTitleError('');
      if (!newActivity.title.trim()) {
        setTitleError(t('activityTitleRequired'));
        return;
      }

      const activity = await ActivityAPI.create(wsId, objectId, recordId, {
        action: 'manualRecord',
        id: '',
        sentAt: dayjs(newActivity.datetime).toISOString(),
        source: {
          type: 'deca.crm',
          id: uuidV4(),
          properties: {
            displayName: 'Manual Record',
          },
        },
        actor: {
          id: recordId,
          type: 'admin',
        },
        object: {
          id: recordId,
          type: 'deca.crm',
          properties: {
            eventName: newActivity.title,
            eventDatetime: dayjs(newActivity.datetime).toISOString(),
            eventDescription: newActivity.description,
          },
        },
      });

      if (activity) {
        showNotification({
          message: t('activityCreated')!,
          color: 'green',
          icon: <IconCircleCheck size={24} />,
          autoClose: 3000,
          styles: (theme: any) => customNotificationStyles(theme) as any,
        });
        await mutate(activities ? [...activities, activity] : ([activity] as any), {
          revalidate: false,
        });
        handleClose();
      }
    } catch (error) {
      console.error('Failed to create activity:', error);
    }
  };

  return (
    <Modal
      size={rem(480)}
      opened={opened}
      onClose={handleClose}
      title={t('createActivity')}
      centered
      top='-10%'
      radius={rem(8)}
      onOk={handleSubmit}
      okText={t('save')}
      onCancel={handleClose}
      cancelText={t('cancel')}
      okButtonProps={{ disabled: !newActivity.title.trim() }}
    >
      <Group mb={rem(24)} gap={rem(4)}>
        <span>{t('eventCategory')}</span>
        <Text fz='md' fw={500}>
          {t('offlineEvent')}
        </Text>
      </Group>
      <InfoAlert description={t('activityGuideline')} />
      <TextInput
        data-testid='activity-title-input'
        mt={rem(24)}
        withAsterisk
        label={t('activityTitle')}
        value={newActivity.title}
        onChange={(e) => {
          setTitleError('');
          setNewActivity({ ...newActivity, title: e.target.value });
        }}
        mb='md'
        error={titleError}
        placeholder={t('activityEditText')}
        labelProps={{ style: { marginBottom: rem(8) } }}
      />
      <DateTimePicker
        className={classes.datetimePicker}
        withAsterisk
        placeholder='yyyy/mm/dd hh:mm'
        label={t('activityDatetime')}
        value={newActivity.datetime}
        onChange={(date) => setNewActivity({ ...newActivity, datetime: date || new Date() })}
        mb='md'
        rightSection={<IconCalendarMinus width={16} height={16} fill='#A3A3A3' />}
        popoverProps={{ withinPortal: true }}
        locale={i18n.language === 'en' ? 'en' : 'ja'}
        valueFormat='YYYY/MM/DD HH:mm'
        labelProps={{ style: { marginBottom: rem(8) } }}
      />
      <Textarea
        data-testid='activity-description-input'
        label={t('activityDescription')}
        value={newActivity.description}
        onChange={(e) => setNewActivity({ ...newActivity, description: e.target.value })}
        mb={rem(16)}
        autosize
        minRows={5.6}
        maxRows={10}
        placeholder={t('activityEditText')}
        labelProps={{ style: { marginBottom: rem(8) } }}
      />
    </Modal>
  );
});

ActivityModal.displayName = 'ActivityModal';
