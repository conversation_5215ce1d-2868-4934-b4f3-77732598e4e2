import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { Box, Menu, ScrollArea, Stack, TextInput, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { DecaTag } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import React, { useRef, useState, useMemo } from 'react';

const useStyles = createStyles((theme) => ({
  tag: {
    textTransform: 'initial',
    marginRight: rem(8),
    marginBottom: rem(8),
  },
  menuItem: {
    fontSize: rem(12),
    padding: rem(5),
    borderRadius: rem(4),
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: theme.colors.decaLight[1],
    },
  },
  highlighted: {
    backgroundColor: theme.colors.decaLight[1],
  },
}));

const Tags = () => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('workspace');
  const [newTag, setNewTag] = useState<string>('');
  const [opened, { open, close }] = useDisclosure(false);
  const [openedMenu, { open: openMenu }] = useDisclosure(false);
  const [isFocused, setIsFocused] = useState(true);
  const { tags: tagsListInitial = [] } = useWorkspaceContext();
  const { tags, handleAddTag, handleRemoveTag } = useProfileContext();
  const tagsList = useMemo(() => {
    return tagsListInitial.filter((tag) => !tags.some((existingTag) => existingTag.id === tag.id));
  }, [tagsListInitial, tags]);

  const ref = useRef<HTMLInputElement>(null);
  const [searchValue, setSearchValue] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const previousFilteredTagsRef = useRef<typeof tagsList>([]);

  const filteredTags = useMemo(() => {
    if (selectedIndex !== -1) return previousFilteredTagsRef.current;
    const newFilteredTags = tagsList.filter((tag) =>
      tag.name.toLowerCase().includes(searchValue.toLowerCase())
    );
    previousFilteredTagsRef.current = newFilteredTags;
    return newFilteredTags;
  }, [tagsList, searchValue, selectedIndex]);

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    setNewTag(value);

    if (!isFocused) {
      setIsFocused(true);
    }
  };

  const handleAddingTag = () => {
    open();
    openMenu();
    setTimeout(() => {
      ref.current?.focus();
    }, 100);
  };

  const handleChangeNewTag = async (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && !tags.some((tag) => tag.name === newTag)) {
      await handleAddTag(newTag);
      handleSearchChange('');
      close();
      return;
    }

    setNewTag(event.currentTarget.value);
  };

  const handleCancelAddingTag = () => {
    setSearchValue('');
    setNewTag('');
    setSelectedIndex(-1);
    setTimeout(() => {
      setIsFocused(true);
      ref.current?.focus();
    }, 200);
  };

  const hideDropdownStyle = filteredTags.length && isFocused ? {} : { padding: 0, border: 0 };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (!filteredTags.length) return;

    switch (event.key) {
      case 'ArrowDown': {
        event.preventDefault();
        const nextIndex = selectedIndex === filteredTags.length - 1 ? 0 : selectedIndex + 1;
        setSelectedIndex(nextIndex);
        handleSearchChange(filteredTags[nextIndex].name);
        break;
      }
      case 'ArrowUp': {
        event.preventDefault();
        const prevIndex = selectedIndex <= 0 ? filteredTags.length - 1 : selectedIndex - 1;
        setSelectedIndex(prevIndex);
        handleSearchChange(filteredTags[prevIndex].name);
        break;
      }
      case 'Enter':
        if (selectedIndex >= 0) {
          handleSearchChange(filteredTags[selectedIndex].name);
          setSelectedIndex(-1);
          return;
        }
        if (!tags.some((tag) => tag.name === newTag)) {
          handleAddTag(newTag);
          handleSearchChange('');
          close();
        }
        break;
    }
  };

  return (
    <Box h={'100%'} py={0} data-testid='tags-test-id'>
      <Stack h='100%'>
        <Box data-testid='tags-input-test-id'>
          {opened ? (
            <Menu
              shadow='md'
              width={200}
              opened={openedMenu}
              position='bottom-start'
              withinPortal={false}
            >
              <Menu.Target>
                <DecaTag
                  className={classes.tag}
                  isNew
                  rightIcon
                  onClickRightIcon={() => {
                    handleCancelAddingTag();
                  }}
                >
                  <TextInput
                    data-testid='new-tag-input'
                    variant='unstyled'
                    value={newTag}
                    onChange={(e) => {
                      setSelectedIndex(-1);
                      handleSearchChange(e.currentTarget.value);
                    }}
                    onKeyDown={handleKeyDown}
                    onKeyUp={handleChangeNewTag}
                    onBlur={() => {
                      setTimeout(() => {
                        setIsFocused(false);
                      }, 170);
                    }}
                    onFocus={() => {
                      setIsFocused(true);
                    }}
                    ref={ref}
                    placeholder={t('newTag')}
                  />
                </DecaTag>
              </Menu.Target>

              <Menu.Dropdown sx={{ marginTop: rem(40), marginLeft: rem(15), ...hideDropdownStyle }}>
                {filteredTags.length > 0 && isFocused && (
                  <ScrollArea h={filteredTags.length > 7 ? rem(200) : 'auto'}>
                    {filteredTags.map((tag, index) => (
                      <Box
                        data-testid={`tag-${tag.id}`}
                        className={cx(classes.menuItem, {
                          [classes.highlighted]: index === selectedIndex,
                        })}
                        key={tag.id}
                        onClick={() => {
                          ref.current?.focus();
                          handleSearchChange(tag.name);
                        }}
                      >
                        {tag.name}
                      </Box>
                    ))}
                  </ScrollArea>
                )}
              </Menu.Dropdown>
            </Menu>
          ) : (
            <DecaTag className={classes.tag} text={t('newTag')} isNew onClick={handleAddingTag} />
          )}
        </Box>
        <ScrollArea data-testid='tags-list-test-id'>
          {tags?.map((tag) => (
            <DecaTag
              className={classes.tag}
              text={tag.name}
              key={tag.id}
              rightIcon
              onClickRightIcon={() => handleRemoveTag(tag.id)}
            />
          ))}
        </ScrollArea>
      </Stack>
    </Box>
  );
};

export default React.memo(Tags);
