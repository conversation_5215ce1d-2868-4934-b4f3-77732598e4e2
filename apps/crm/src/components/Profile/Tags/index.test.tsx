import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';
import Tags from '.';

vi.mock('@/contexts/WorkspaceContext');
vi.mock('@/contexts/ProfileContext');
vi.mock('@mantine/hooks', () => ({
  useDisclosure: () => [true, { open: vi.fn(), close: vi.fn() }],
}));

describe('Tags Component', () => {
  const mockTags = [
    { id: '1', name: 'Tag 1' },
    { id: '2', name: 'Tag 2' },
  ];

  const mockWorkspaceTags = [
    { id: '1', name: 'Tag 1' },
    { id: '2', name: 'Tag 2' },
    { id: '3', name: 'Tag 3' },
  ];

  const mockHandleAddTag = vi.fn();
  const mockHandleRemoveTag = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(useWorkspaceContext).mockReturnValue({
      tags: mockWorkspaceTags,
    } as any);

    vi.mocked(useProfileContext).mockReturnValue({
      tags: mockTags,
      handleAddTag: mockHandleAddTag,
      handleRemoveTag: mockHandleRemoveTag,
    } as any);
  });

  it('renders tags correctly', () => {
    renderWithMantine(<Tags />);

    expect(screen.getByTestId('tags-test-id')).toBeInTheDocument();
    expect(screen.getByText('Tag 1')).toBeInTheDocument();
    expect(screen.getByText('Tag 2')).toBeInTheDocument();
    expect(screen.getByTestId('tags-input-test-id')).toBeInTheDocument();
    expect(screen.getByTestId('tags-list-test-id')).toBeInTheDocument();
    expect(screen.getAllByTestId('tag-test-id')).toHaveLength(mockTags.length);
  });

  it('opens new tag input when clicking new tag button', () => {
    renderWithMantine(<Tags />);

    const newTagButton = screen.getByTestId('new-tag-test-id');
    fireEvent.click(newTagButton);

    expect(screen.getByPlaceholderText('newTag')).toBeInTheDocument();
  });

  it('adds a new tag when pressing Enter', async () => {
    renderWithMantine(<Tags />);

    // Open new tag input
    const newTagButton = screen.getByTestId('new-tag-test-id');
    fireEvent.click(newTagButton);

    // Type new tag name
    const input = screen.getByPlaceholderText('newTag');
    fireEvent.change(input, { target: { value: 'New Tag' } });
    fireEvent.keyUp(input, { key: 'Enter' });

    expect(mockHandleAddTag).toHaveBeenCalledWith('New Tag');
  });

  it('shows filtered suggestions when typing', () => {
    renderWithMantine(<Tags />);

    // Open new tag input
    const newTagButton = screen.getByTestId('new-tag-test-id');
    fireEvent.click(newTagButton);

    // Type to filter
    const input = screen.getByPlaceholderText('newTag');
    fireEvent.change(input, { target: { value: 'Tag' } });

    // Should show Tag 3 as suggestion (since Tag 1 and 2 are already added)
    expect(screen.getByText('Tag 3')).toBeInTheDocument();
  });

  it('removes a tag when clicking remove button', () => {
    renderWithMantine(<Tags />);

    const removeButtons = screen.getAllByTestId('tag-remove-button');
    fireEvent.click(removeButtons[0]); // Click first tag's remove button

    expect(mockHandleRemoveTag).toHaveBeenCalledWith(mockTags[0].id);
  });

  it('cancels adding new tag when clicking cancel button', () => {
    renderWithMantine(<Tags />);

    // Open new tag input
    const newTagButton = screen.getByTestId('new-tag-test-id');
    fireEvent.click(newTagButton);

    // Type something
    const input = screen.getByPlaceholderText('newTag');
    fireEvent.change(input, { target: { value: 'New Tag input' } });

    // Click cancel
    const cancelButton = screen.getByTestId('clear-adding-tag-button');
    fireEvent.click(cancelButton);

    // Input should be cleared and closed
    expect(screen.queryByText('New Tag input')).not.toBeInTheDocument();
  });

  // can filter tags
  it('can filter tags', () => {
    renderWithMantine(<Tags />);

    // Open new tag input
    const newTagButton = screen.getByTestId('new-tag-test-id');
    fireEvent.click(newTagButton);

    // Type to filter
    const input = screen.getByPlaceholderText('newTag');
    fireEvent.change(input, { target: { value: 'Tag' } });

    // Should show Tag 3 as suggestion (since Tag 1 and 2 are already added)
    expect(screen.getByText('Tag 3')).toBeInTheDocument();
  });

  it('should update search value and new tag when typing', () => {
    renderWithMantine(<Tags />);

    // Open new tag input
    const newTagButton = screen.getByTestId('new-tag-test-id');
    fireEvent.click(newTagButton);

    // Type in the input
    const input = screen.getByPlaceholderText('newTag');
    fireEvent.change(input, { target: { value: 'Test Tag' } });

    // Check if the input value is updated
    expect(input).toHaveValue('Test Tag');
  });

  it('should update input value and focus when clicking a suggestion', () => {
    renderWithMantine(<Tags />);

    // Open new tag input
    const newTagButton = screen.getByTestId('new-tag-test-id');
    fireEvent.click(newTagButton);

    // Type to show suggestions
    const input = screen.getByPlaceholderText('newTag');
    fireEvent.change(input, { target: { value: 'Tag' } });

    // Click on Tag 3 suggestion
    const suggestion = screen.getByText('Tag 3');
    fireEvent.click(suggestion);

    // Check if input is focused and value is updated
    expect(input).toHaveFocus();
    expect(input).toHaveValue('Tag 3');
  });

  describe('keyboard navigation', () => {
    beforeEach(() => {
      renderWithMantine(<Tags />);

      // Open new tag input
      const newTagButton = screen.getByTestId('new-tag-test-id');
      fireEvent.click(newTagButton);

      // Type to show suggestions
      const input = screen.getByPlaceholderText('newTag');
      fireEvent.change(input, { target: { value: 'Tag' } });
    });

    it('should navigate down through suggestions with ArrowDown', () => {
      const input = screen.getByPlaceholderText('newTag');

      // Press arrow down
      fireEvent.keyDown(input, { key: 'ArrowDown' });
      // Verify input value is updated to the selected suggestion
      expect(input).toHaveValue('Tag 3');

      // Press arrow down again should cycle back to first item
      fireEvent.keyDown(input, { key: 'ArrowDown' });
      expect(input).toHaveValue('Tag 3'); // Since we only have one suggestion
    });
    it('should not add duplicate tag on Enter', () => {
      const input = screen.getByPlaceholderText('newTag');

      // Try to add existing tag
      fireEvent.change(input, { target: { value: 'Tag 1' } });
      fireEvent.keyDown(input, { key: 'Enter' });

      expect(mockHandleAddTag).not.toHaveBeenCalled();
    });

    it('should do nothing when pressing keys with no suggestions', () => {
      const input = screen.getByPlaceholderText('newTag');

      // Type something that won't match any suggestions
      fireEvent.change(input, { target: { value: 'xyz' } });

      // Try navigation keys
      fireEvent.keyDown(input, { key: 'ArrowDown' });
      fireEvent.keyDown(input, { key: 'ArrowUp' });

      // Input value should remain unchanged
      expect(input).toHaveValue('xyz');
    });
  });
});
