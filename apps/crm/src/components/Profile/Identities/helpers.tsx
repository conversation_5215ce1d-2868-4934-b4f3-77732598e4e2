import type { IdentityType } from '@/models/identity';
import { DecaStatus } from '@resola-ai/ui';
import type { TFnType } from '@tolgee/react';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

export const getIdentityBadge = (type: IdentityType, t: TFnType) => {
  switch (type) {
    case 'email':
      return <DecaStatus variant='yellow'>{t(`identity.${type}`)}</DecaStatus>;
    case 'lineId':
      return (
        <DecaStatus tt='uppercase' variant='green'>
          {t(`identity.${type}`)}
        </DecaStatus>
      );
    case 'phone':
      return <DecaStatus variant='blue'>{t(`identity.${type}`)}</DecaStatus>;
    case 'webuserId':
      return <DecaStatus variant='violet'>{t(`identity.${type}`)}</DecaStatus>;
    default:
      return null;
  }
};

export const groupDataByDate = <T, K extends keyof T>(data: T[], fieldName: K) => {
  const now = dayjs();
  const weekStart = now.startOf('week');
  const weekEnd = now.endOf('week');

  return data.reduce<Map<'thisWeek' | (string & NonNullable<unknown>), T[]>>(
    (previous, current) => {
      const date = current[fieldName] as string;
      const key = dayjs(date).format('DD/MM/YYYY');

      if (dayjs(date).isSameOrAfter(weekStart) && dayjs(date).isSameOrBefore(weekEnd)) {
        previous.set('thisWeek', (previous.get('thisWeek') || []).concat(current));
      } else {
        previous.set(key, (previous.get(key) || []).concat(current));
      }

      return previous;
    },
    new Map([['thisWeek', []]])
  );
};
