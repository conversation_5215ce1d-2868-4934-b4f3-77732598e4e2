import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { createRef } from 'react';
import { describe, expect, it } from 'vitest';
import TruncatedText from './TruncatedText';

describe('TruncatedText Component', () => {
  describe('Basic Functionality', () => {
    it('should render truncated text with default start and end values', () => {
      const testText = 'this-is-a-very-long-text-string-that-should-be-truncated';

      renderWithMantine(<TruncatedText>{testText}</TruncatedText>);

      // Default start=4, end=4: "this...ated"
      // Use text matcher function to handle fragmented text
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'this...ated';
        })
      ).toBeInTheDocument();
    });

    it('should handle short text that does not need truncation', () => {
      const shortText = 'short';

      renderWithMantine(<TruncatedText>{shortText}</TruncatedText>);

      // With start=4, end=4 on "short": "shor...hort"
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'shor...hort';
        })
      ).toBeInTheDocument();
    });

    it('should handle very short text (shorter than start + end)', () => {
      const veryShortText = 'hi';

      renderWithMantine(<TruncatedText>{veryShortText}</TruncatedText>);

      // For "hi" with start=4, end=4: should show "hi...hi" (taking available chars)
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'hi...hi';
        })
      ).toBeInTheDocument();
    });
  });

  describe('Custom Start and End Values', () => {
    it('should respect custom start value', () => {
      const testText = 'abcdefghijklmnopqrstuvwxyz';

      renderWithMantine(<TruncatedText start={6}>{testText}</TruncatedText>);

      // start=6, end=4 (default): "abcdef...wxyz"
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'abcdef...wxyz';
        })
      ).toBeInTheDocument();
    });

    it('should respect custom end value', () => {
      const testText = 'abcdefghijklmnopqrstuvwxyz';

      renderWithMantine(<TruncatedText end={6}>{testText}</TruncatedText>);

      // start=4 (default), end=6: "abcd...uvwxyz"
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'abcd...uvwxyz';
        })
      ).toBeInTheDocument();
    });

    it('should handle both custom start and end values', () => {
      const testText = 'abcdefghijklmnopqrstuvwxyz';

      renderWithMantine(
        <TruncatedText start={3} end={3}>
          {testText}
        </TruncatedText>
      );

      // start=3, end=3: "abc...xyz"
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'abc...xyz';
        })
      ).toBeInTheDocument();
    });

    it('should handle zero start value', () => {
      const testText = 'abcdefghijklmnopqrstuvwxyz';

      renderWithMantine(
        <TruncatedText start={0} end={4}>
          {testText}
        </TruncatedText>
      );

      // start=0, end=4: "...wxyz"
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === '...wxyz';
        })
      ).toBeInTheDocument();
    });

    it('should handle zero end value', () => {
      const testText = 'abcdefghijklmnopqrstuvwxyz';

      renderWithMantine(
        <TruncatedText start={4} end={0}>
          {testText}
        </TruncatedText>
      );

      // start=4, end=0: "abcd..."
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'abcd...';
        })
      ).toBeInTheDocument();
    });

    it('should handle both start and end as zero', () => {
      const testText = 'abcdefghijklmnopqrstuvwxyz';

      renderWithMantine(
        <TruncatedText start={0} end={0}>
          {testText}
        </TruncatedText>
      );

      // start=0, end=0: "..."
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === '...';
        })
      ).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty string', () => {
      renderWithMantine(<TruncatedText>{''}</TruncatedText>);

      // Empty string with start=4, end=4: "..." (since substring of empty string is empty)
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === '...';
        })
      ).toBeInTheDocument();
    });

    it('should handle single character', () => {
      renderWithMantine(<TruncatedText>{'a'}</TruncatedText>);

      // Single char "a" with start=4, end=4: "a...a" (since start goes beyond length)
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'a...a';
        })
      ).toBeInTheDocument();
    });

    it('should handle start value larger than string length', () => {
      const testText = 'abc';

      renderWithMantine(<TruncatedText start={10}>{testText}</TruncatedText>);

      // start=10 on "abc": "abc...abc"
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'abc...abc';
        })
      ).toBeInTheDocument();
    });

    it('should handle end value larger than string length', () => {
      const testText = 'abc';

      renderWithMantine(<TruncatedText end={10}>{testText}</TruncatedText>);

      // end=10 on "abc": "abc...abc"
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'abc...abc';
        })
      ).toBeInTheDocument();
    });

    it('should handle very large start and end values', () => {
      const testText = 'hello-world';

      renderWithMantine(
        <TruncatedText start={100} end={100}>
          {testText}
        </TruncatedText>
      );

      // Both values larger than string: "hello-world...hello-world"
      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'hello-world...hello-world';
        })
      ).toBeInTheDocument();
    });
  });

  describe('Forwarded Ref', () => {
    it('should forward ref to the underlying Text element', () => {
      const ref = createRef<HTMLParagraphElement>();
      const testText = 'test-ref-forwarding';

      renderWithMantine(<TruncatedText ref={ref}>{testText}</TruncatedText>);

      // Mantine Text renders as <p> element
      expect(ref.current).toBeInstanceOf(HTMLParagraphElement);
      expect(ref.current).toHaveTextContent('test...ding');
    });

    it('should maintain ref functionality with custom props', () => {
      const ref = createRef<HTMLParagraphElement>();
      const testText = 'test-ref-with-custom-props';

      renderWithMantine(
        <TruncatedText ref={ref} start={2} end={2}>
          {testText}
        </TruncatedText>
      );

      expect(ref.current).toBeInstanceOf(HTMLParagraphElement);
      expect(ref.current).toHaveTextContent('te...ps');
    });
  });

  describe('Props Forwarding', () => {
    it('should forward Text component props correctly', () => {
      const testText = 'test-props-forwarding';

      renderWithMantine(
        <TruncatedText className='custom-class' data-testid='custom-truncated-text' size='xl'>
          {testText}
        </TruncatedText>
      );

      const element = screen.getByTestId('custom-truncated-text');
      expect(element).toBeInTheDocument();
      expect(element).toHaveClass('custom-class');
      expect(element).toHaveTextContent('test...ding');
    });

    it('should handle style props', () => {
      const testText = 'styled-text';

      renderWithMantine(
        <TruncatedText style={{ color: 'red', fontSize: '16px' }} data-testid='styled-text'>
          {testText}
        </TruncatedText>
      );

      const element = screen.getByTestId('styled-text');
      expect(element).toBeInTheDocument();
      // Color is rendered as rgb value in computed styles
      expect(element).toHaveStyle({ color: 'rgb(255, 0, 0)', fontSize: '16px' });
    });

    it('should not pass truncation-specific props to underlying component', () => {
      const testText = 'test-prop-filtering';

      const { container } = renderWithMantine(
        <TruncatedText start={2} end={2}>
          {testText}
        </TruncatedText>
      );

      // The start and end props should not appear as HTML attributes
      const textElement = container.querySelector('[data-start]');
      expect(textElement).not.toBeInTheDocument();

      const endElement = container.querySelector('[data-end]');
      expect(endElement).not.toBeInTheDocument();
    });
  });

  describe('Text Content Scenarios', () => {
    it('should handle text with special characters', () => {
      const specialText = 'hello@#$%^&*()world!';

      renderWithMantine(
        <TruncatedText start={3} end={3}>
          {specialText}
        </TruncatedText>
      );

      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'hel...ld!';
        })
      ).toBeInTheDocument();
    });

    it('should handle text with spaces', () => {
      const textWithSpaces = 'hello world this is a test';

      renderWithMantine(
        <TruncatedText start={5} end={4}>
          {textWithSpaces}
        </TruncatedText>
      );

      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'hello...test';
        })
      ).toBeInTheDocument();
    });

    it('should handle text with unicode characters', () => {
      const unicodeText = 'こんにちは世界🌍';

      renderWithMantine(
        <TruncatedText start={3} end={2}>
          {unicodeText}
        </TruncatedText>
      );

      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === 'こんに...🌍';
        })
      ).toBeInTheDocument();
    });

    it('should handle numeric strings', () => {
      const numericText = '1234567890123456';

      renderWithMantine(
        <TruncatedText start={4} end={4}>
          {numericText}
        </TruncatedText>
      );

      expect(
        screen.getByText((_content, element) => {
          return element?.textContent === '1234...3456';
        })
      ).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('should work as expected when used in larger component trees', () => {
      const longId = 'user-id-123456789-abcdef-ghijkl-mnopqr';

      renderWithMantine(
        <div data-testid='wrapper'>
          <span>User ID: </span>
          <TruncatedText data-testid='user-id'>{longId}</TruncatedText>
        </div>
      );

      expect(screen.getByTestId('wrapper')).toBeInTheDocument();
      // "user-id-123456789-abcdef-ghijkl-mnopqr" has 38 chars
      // start=4: "user", end=4: substring from (38-4)=34, so "opqr"
      expect(screen.getByTestId('user-id')).toHaveTextContent('user...opqr');
      expect(screen.getByText('User ID:')).toBeInTheDocument();
    });

    it('should maintain proper text flow in flex layouts', () => {
      const testText = 'flexible-layout-text-content';

      renderWithMantine(
        <div style={{ display: 'flex', alignItems: 'center' }} data-testid='flex-container'>
          <span>Label:</span>
          <TruncatedText data-testid='flex-text'>{testText}</TruncatedText>
        </div>
      );

      const container = screen.getByTestId('flex-container');
      const truncatedElement = screen.getByTestId('flex-text');

      expect(container).toBeInTheDocument();
      expect(truncatedElement).toBeInTheDocument();
      expect(truncatedElement).toHaveTextContent('flex...tent');
    });
  });
});
