import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import type { Identity } from '@/models/identity';
import IdentityRow from './IdentityRow';

// Setup test environment
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock navigator.clipboard
const mockWriteText = vi.fn();
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: mockWriteText,
  },
  writable: true,
});

// Hoisted mocks
const mockTranslate = vi.hoisted(() => vi.fn((key) => key));
const mockGetLanguage = vi.hoisted(() => vi.fn(() => 'en'));
const mockGetIdentityBadge = vi.hoisted(() => vi.fn((type, t) => (
  <div data-testid="identity-badge" data-type={type}>
    {t(`identity.${type}`)}
  </div>
)));

// Mock dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: mockTranslate }),
}));

vi.mock('@/tolgee', () => ({
  tolgee: {
    getLanguage: mockGetLanguage,
  },
}));

vi.mock('@resola-ai/ui', () => ({
  DecaButton: vi.fn(({ children, onClick, ...props }) => (
    <button 
      data-testid="deca-button" 
      onClick={onClick}
      data-radius={props.radius}
      data-size={props.size}
      data-variant={props.variant}
    >
      {children}
    </button>
  )),
  DecaDataValue: vi.fn(({ title, value }) => (
    <div data-testid="deca-data-value">
      <div data-testid="data-value-title">{title}</div>
      <div data-testid="data-value-content">{value}</div>
    </div>
  )),
}));

vi.mock('@tabler/icons-react', () => ({
  IconFiles: vi.fn(() => <span data-testid="icon-files">📁</span>),
}));

vi.mock('../helpers', () => ({
  getIdentityBadge: mockGetIdentityBadge,
}));

vi.mock('./TruncatedText', () => ({
  default: vi.fn(({ children }) => (
    <span data-testid="truncated-text">{children}</span>
  )),
}));

describe('IdentityRow', () => {
  const mockIdentity: Identity = {
    id: 'test-identity-id-12345',
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    type: 'email',
    value: '<EMAIL>',
    provider: 'Test Provider',
    displayName: 'Test Email',
    relatedObjectId: 'object-123',
    relatedRecordId: 'record-123',
    properties: {
      verified: true,
      primary: false,
    },
    settingId: 'setting-123',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render identity row with all components', () => {
      const { getByTestId, getAllByTestId } = renderWithMantine(
        <IdentityRow {...mockIdentity} />
      );

      // Check identity badge
      expect(getByTestId('identity-badge')).toBeInTheDocument();
      expect(getByTestId('identity-badge')).toHaveAttribute('data-type', 'email');

      // Check data values (should have 2: belongsTo and lastUpdated)
      const dataValues = getAllByTestId('deca-data-value');
      expect(dataValues).toHaveLength(2);

      // Check truncated text for ID
      expect(getByTestId('truncated-text')).toBeInTheDocument();
      expect(getByTestId('truncated-text')).toHaveTextContent('test-identity-id-12345');

      // Check copy button
      expect(getByTestId('deca-button')).toBeInTheDocument();
      
      // Check for the actual SVG icon (IconFiles renders as SVG)
      const button = getByTestId('deca-button');
      const svgIcon = button.querySelector('svg');
      expect(svgIcon).toBeInTheDocument();
      expect(svgIcon).toHaveClass('tabler-icon-files');
    });

    it('should pass correct props to DecaButton', () => {
      const { getByTestId } = renderWithMantine(
        <IdentityRow {...mockIdentity} />
      );

      const button = getByTestId('deca-button');
      expect(button).toHaveAttribute('data-radius', 'xl');
      expect(button).toHaveAttribute('data-size', 'sm');
      expect(button).toHaveAttribute('data-variant', 'neutral_text');
    });

    it('should display provider information correctly', () => {
      const { getAllByTestId } = renderWithMantine(
        <IdentityRow {...mockIdentity} />
      );

      const dataValues = getAllByTestId('deca-data-value');
      const providerValue = dataValues[0];
      
      expect(providerValue.querySelector('[data-testid="data-value-content"]'))
        .toHaveTextContent('Test Provider');
    });

    it('should call translation functions with correct keys', () => {
      renderWithMantine(<IdentityRow {...mockIdentity} />);

      expect(mockTranslate).toHaveBeenCalledWith('belongsTo');
      expect(mockTranslate).toHaveBeenCalledWith('lastUpdated');
      expect(mockTranslate).toHaveBeenCalledWith('copy');
    });
  });

  describe('Identity Badge Integration', () => {
    it('should call getIdentityBadge with correct parameters', () => {
      renderWithMantine(<IdentityRow {...mockIdentity} />);

      expect(mockGetIdentityBadge).toHaveBeenCalledWith('email', expect.any(Function));
    });

    it('should render different identity types correctly', () => {
      const identityTypes = ['email', 'lineId', 'phone', 'webuserId'] as const;
      
      identityTypes.forEach((type) => {
        const identity = { ...mockIdentity, type };
        const { getByTestId, unmount } = renderWithMantine(
          <IdentityRow {...identity} />
        );

        expect(getByTestId('identity-badge')).toHaveAttribute('data-type', type);
        unmount();
      });
    });
  });

  describe('Copy Functionality', () => {
    it('should copy identity ID to clipboard when copy button is clicked', async () => {
      const { getByTestId } = renderWithMantine(
        <IdentityRow {...mockIdentity} />
      );

      const copyButton = getByTestId('deca-button');
      fireEvent.click(copyButton);

      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith('test-identity-id-12345');
      });
    });

    it('should handle copy functionality with different ID formats', async () => {
      const identityIds = [
        'short-id',
        'very-long-identity-id-with-many-characters-12345',
        'id_with_underscores_123',
        'ID.WITH.DOTS.456',
      ];

      for (const id of identityIds) {
        const identity = { ...mockIdentity, id };
        const { getByTestId, unmount } = renderWithMantine(
          <IdentityRow {...identity} />
        );

        const copyButton = getByTestId('deca-button');
        fireEvent.click(copyButton);

        await waitFor(() => {
          expect(mockWriteText).toHaveBeenCalledWith(id);
        });

        unmount();
        vi.clearAllMocks();
      }
    });

    it('should handle clipboard errors gracefully', async () => {
      mockWriteText.mockRejectedValueOnce(new Error('Clipboard access denied'));
      
      const { getByTestId } = renderWithMantine(
        <IdentityRow {...mockIdentity} />
      );

      const copyButton = getByTestId('deca-button');
      
      // Should not throw error even if clipboard fails
      expect(() => {
        fireEvent.click(copyButton);
      }).not.toThrow();

      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith('test-identity-id-12345');
      });
    });
  });

  describe('Provider Information', () => {
    it('should display different providers correctly', () => {
      const providers = [
        'Google',
        'Facebook',
        'Line',
        'Custom Provider with Spaces',
        'provider_with_underscores',
      ];

      providers.forEach((provider) => {
        const identity = { ...mockIdentity, provider };
        const { getAllByTestId, unmount } = renderWithMantine(
          <IdentityRow {...identity} />
        );

        const dataValues = getAllByTestId('deca-data-value');
        const providerValue = dataValues[0];
        
        expect(providerValue.querySelector('[data-testid="data-value-content"]'))
          .toHaveTextContent(provider);
        
        unmount();
      });
    });

    it('should handle empty or null provider values', () => {
      const emptyProviders = ['', null, undefined];

      emptyProviders.forEach((provider) => {
        const identity = { ...mockIdentity, provider: provider as any };
        const { getAllByTestId, unmount } = renderWithMantine(
          <IdentityRow {...identity} />
        );

        const dataValues = getAllByTestId('deca-data-value');
        expect(dataValues[0]).toBeInTheDocument();
        
        unmount();
      });
    });
  });

  describe('Date Formatting', () => {
    it('should format dates correctly using dayjs', () => {
      const { getAllByTestId } = renderWithMantine(
        <IdentityRow {...mockIdentity} />
      );

      const dataValues = getAllByTestId('deca-data-value');
      const dateValue = dataValues[1];
      
      // The date should be formatted as relative time
      expect(dateValue.querySelector('[data-testid="data-value-content"]'))
        .toBeInTheDocument();
    });

    it('should handle different date formats', () => {
      const differentDates = [
        '2024-01-15T10:00:00Z',
        '2023-12-25T15:30:00Z',
        '2024-02-01T08:45:00Z',
      ];

      differentDates.forEach((updatedAt) => {
        const identity = { ...mockIdentity, updatedAt };
        const { getAllByTestId, unmount } = renderWithMantine(
          <IdentityRow {...identity} />
        );

        const dataValues = getAllByTestId('deca-data-value');
        expect(dataValues[1]).toBeInTheDocument();
        unmount();
      });
    });

    it('should use correct language for date formatting', () => {
      const languages = ['en', 'ja', 'fr'];
      
      languages.forEach((lang) => {
        mockGetLanguage.mockReturnValue(lang);
        
        const { unmount } = renderWithMantine(
          <IdentityRow {...mockIdentity} />
        );
        
        expect(mockGetLanguage).toHaveBeenCalled();
        unmount();
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing updatedAt gracefully', () => {
      const identity = { ...mockIdentity, updatedAt: null as any };
      
      expect(() => {
        renderWithMantine(<IdentityRow {...identity} />);
      }).not.toThrow();
    });

    it('should handle invalid date formats', () => {
      const invalidDates = ['invalid-date', '', 'not-a-date'];

      invalidDates.forEach((updatedAt) => {
        const identity = { ...mockIdentity, updatedAt };
        
        expect(() => {
          const { unmount } = renderWithMantine(<IdentityRow {...identity} />);
          unmount();
        }).not.toThrow();
      });
    });

    it('should handle missing identity type', () => {
      const identity = { ...mockIdentity, type: undefined as any };
      
      expect(() => {
        renderWithMantine(<IdentityRow {...identity} />);
      }).not.toThrow();
    });

    it('should render with minimal props', () => {
      const minimalIdentity = {
        id: 'min-id',
        type: 'email' as const,
        provider: 'Min Provider',
        updatedAt: '2024-01-15T10:00:00Z',
      };

      expect(() => {
        renderWithMantine(<IdentityRow {...minimalIdentity as any} />);
      }).not.toThrow();
    });
  });

  describe('Layout Structure', () => {
    it('should apply correct Grid layout structure', () => {
      const { container } = renderWithMantine(
        <IdentityRow {...mockIdentity} />
      );

      // Check for Grid and Col components
      const gridElement = container.querySelector('.mantine-Grid-root');
      expect(gridElement).toBeInTheDocument();

      const colElements = container.querySelectorAll('.mantine-Grid-col');
      expect(colElements.length).toBeGreaterThan(0);
    });

    it('should maintain responsive layout structure', () => {
      const { container } = renderWithMantine(
        <IdentityRow {...mockIdentity} />
      );

      // Should have columns with the Grid structure
      const colElements = container.querySelectorAll('.mantine-Grid-col');
      expect(colElements.length).toBeGreaterThanOrEqual(4);
    });
  });

  describe('Component Integration', () => {
    it('should work correctly when rendered multiple times', () => {
      const identities = [
        { ...mockIdentity, id: 'id-1', type: 'email' as const },
        { ...mockIdentity, id: 'id-2', type: 'phone' as const },
        { ...mockIdentity, id: 'id-3', type: 'lineId' as const },
      ];

      const { getAllByTestId } = renderWithMantine(
        <div>
          {identities.map((identity) => (
            <IdentityRow key={identity.id} {...identity} />
          ))}
        </div>
      );

      const identityBadges = getAllByTestId('identity-badge');
      const truncatedTexts = getAllByTestId('truncated-text');
      const copyButtons = getAllByTestId('deca-button');

      expect(identityBadges).toHaveLength(3);
      expect(truncatedTexts).toHaveLength(3);
      expect(copyButtons).toHaveLength(3);
    });

    it('should maintain state independence between instances', async () => {
      const identity1 = { ...mockIdentity, id: 'first-id' };
      const identity2 = { ...mockIdentity, id: 'second-id' };

      const { getAllByTestId } = renderWithMantine(
        <div>
          <IdentityRow {...identity1} />
          <IdentityRow {...identity2} />
        </div>
      );

      const copyButtons = getAllByTestId('deca-button');
      
      // Click first button
      fireEvent.click(copyButtons[0]);
      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith('first-id');
      });

      vi.clearAllMocks();

      // Click second button
      fireEvent.click(copyButtons[1]);
      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith('second-id');
      });
    });
  });
}); 