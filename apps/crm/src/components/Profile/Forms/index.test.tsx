import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useBreadcrumbNavigation } from '@/hooks/useBreadcrumbNavigation';
import { ObjectAPI } from '@/services/api';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { syncContextData } from '@/utils';
import { FieldTypes } from '@resola-ai/ui/components';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Forms from './index';

// Mock dependencies
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

vi.mock('@/hooks/useBreadcrumbNavigation', () => ({
  useBreadcrumbNavigation: vi.fn(),
}));

vi.mock('@/services/api', () => ({
  ObjectAPI: {
    update: vi.fn(),
  },
}));

vi.mock('@/utils', () => ({
  syncContextData: vi.fn(),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ wsId: 'test-workspace' }),
  };
});

describe('Forms Component', () => {
  const mockNavigateToLinkedRecord = vi.fn();
  const mockRefetchObject = vi.fn();
  const mockObject = {
    id: 'test-object',
    childObjects: [
      { id: 'linked-obj-1', pinned: false }, // This should match the linkedObj.id
      { id: 'child-2', pinned: true },
    ],
    profileSettings: [{ type: 'pinCustomObject', enabled: true }],
  };

  const mockLinkedObj = {
    id: 'linked-obj-1',
    name: { singular: 'Contact', plural: 'Contacts' },
    fields: [
      { id: 'field-1', name: 'firstName', type: FieldTypes.SINGLE_LINE_TEXT },
      { id: 'field-2', name: 'email', type: FieldTypes.EMAIL },
      { id: 'field-3', name: 'createdAt', type: FieldTypes.CREATED_TIME },
      {
        id: 'field-4',
        name: 'status',
        type: FieldTypes.SINGLE_SELECT,
        options: {
          choices: [
            { id: 'active', label: 'Active' },
            { id: 'inactive', label: 'Inactive' },
          ],
        },
      },
    ],
  };

  const mockForm = {
    linkedObj: mockLinkedObj,
    records: [
      {
        id: 'record-1',
        objectId: 'linked-obj-1',
        firstName: 'John',
        email: '<EMAIL>',
        status: 'active',
        createdAt: '2023-01-01T10:00:00Z',
      },
    ],
    template: '',
  };

  beforeEach(() => {
    vi.clearAllMocks();

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: mockObject,
      refetchObject: mockRefetchObject,
    });

    (useBreadcrumbNavigation as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      navigateToLinkedRecord: mockNavigateToLinkedRecord,
    });

    (ObjectAPI.update as ReturnType<typeof vi.fn>).mockResolvedValue({});
    (syncContextData as ReturnType<typeof vi.fn>).mockReturnValue('<div>Synced template</div>');
  });

  it('should be defined', () => {
    expect(Forms).toBeDefined();
    // Forms component is wrapped with React.memo so it's an object
    expect(typeof Forms).toBe('object');
  });

  it('should render loader when no form is provided', () => {
    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={null} />
      </MemoryRouter>
    );

    // Check for the Loader component by class name since it doesn't have progressbar role
    const loader = container.querySelector('.mantine-Loader-root');
    expect(loader).toBeInTheDocument();
  });

  it('should render nothing when form has no records', () => {
    const formWithoutRecords = { ...mockForm, records: [] };

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={formWithoutRecords} />
      </MemoryRouter>
    );

    // The component renders a fragment with conditional content
    expect(container.firstChild).toBeInTheDocument();
  });

  it('should render form title correctly', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    expect(screen.getByText('Contact')).toBeInTheDocument();
  });

  it('should render pin button when pin is enabled and not pinned', () => {
    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    // Look for pin button by the icon class
    const pinIcon = container.querySelector('.tabler-icon-pin');
    expect(pinIcon).toBeInTheDocument();
  });

  it('should not render pin button when already pinned', () => {
    const pinnedForm = {
      ...mockForm,
      linkedObj: { ...mockLinkedObj, id: 'child-2' },
    };

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={pinnedForm} />
      </MemoryRouter>
    );

    // Should not have pin button since this object is already pinned
    const pinIcon = container.querySelector('.tabler-icon-pin');
    expect(pinIcon).not.toBeInTheDocument();
  });

  it('should handle pin toggle correctly', async () => {
    const user = userEvent.setup();

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    const pinIcon = container.querySelector('.tabler-icon-pin');
    expect(pinIcon).toBeInTheDocument();

    const pinButton = pinIcon?.closest('button');
    expect(pinButton).toBeInTheDocument();

    await user.click(pinButton!);

    // The component should update the matching child object to be pinned
    expect(ObjectAPI.update).toHaveBeenCalledWith('test-workspace', {
      ...mockObject,
      childObjects: [
        { id: 'linked-obj-1', pinned: true }, // This should be updated to pinned: true
        { id: 'child-2', pinned: true },
      ],
    });
    expect(mockRefetchObject).toHaveBeenCalled();
  });

  it('should render template when template is provided', () => {
    const formWithTemplate = {
      ...mockForm,
      template: '<div>Custom template</div>',
    };

    renderWithMantine(
      <MemoryRouter>
        <Forms form={formWithTemplate} />
      </MemoryRouter>
    );

    expect(syncContextData).toHaveBeenCalledWith('<div>Custom template</div>', {
      forms: mockForm.records,
    });
  });

  it('should render default accordion when no template', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    expect(screen.getByText('Contact - record-1')).toBeInTheDocument();
    expect(screen.getByText('editObject')).toBeInTheDocument();
  });

  it('should render field values correctly', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    expect(screen.getByText('John')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('should format datetime fields correctly', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    // The date formatting might be different based on the actual implementation
    // Let's check if the date is displayed in some format
    expect(screen.getByText(/2023/)).toBeInTheDocument();
  });

  it('should handle single select field values', () => {
    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('should handle multi select field values', () => {
    const formWithMultiSelect = {
      ...mockForm,
      linkedObj: {
        ...mockLinkedObj,
        fields: [
          ...mockLinkedObj.fields,
          {
            id: 'field-5',
            name: 'tags',
            type: FieldTypes.MULTI_SELECT,
            options: {
              choices: [
                { id: 'tag1', label: 'Tag 1' },
                { id: 'tag2', label: 'Tag 2' },
              ],
            },
          },
        ],
      },
      records: [
        {
          ...mockForm.records[0],
          tags: ['tag1', 'tag2'],
        },
      ],
    };

    renderWithMantine(
      <MemoryRouter>
        <Forms form={formWithMultiSelect} />
      </MemoryRouter>
    );

    expect(screen.getByText('Tag 1, Tag 2')).toBeInTheDocument();
  });

  it('should handle edit object click', async () => {
    const user = userEvent.setup();

    renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    const editButton = screen.getByText('editObject');
    await user.click(editButton);

    expect(mockNavigateToLinkedRecord).toHaveBeenCalledWith(
      'linked-obj-1',
      'record-1',
      mockForm.records[0]
    );
  });

  it('should handle relationship field values', () => {
    const formWithRelationship = {
      ...mockForm,
      linkedObj: {
        ...mockLinkedObj,
        fields: [
          ...mockLinkedObj.fields,
          { id: 'field-6', name: 'company', type: FieldTypes.RELATIONSHIP },
        ],
      },
      records: [
        {
          ...mockForm.records[0],
          company: { name: 'Test Company' },
        },
      ],
    };

    renderWithMantine(
      <MemoryRouter>
        <Forms form={formWithRelationship} />
      </MemoryRouter>
    );

    expect(screen.getByText('Test Company')).toBeInTheDocument();
  });

  it('should handle object field values', () => {
    const formWithObject = {
      ...mockForm,
      records: [
        {
          ...mockForm.records[0],
          customObject: { name: 'Custom Object Name' },
        },
      ],
    };

    renderWithMantine(
      <MemoryRouter>
        <Forms form={formWithObject} />
      </MemoryRouter>
    );

    expect(screen.getByText('Custom Object Name')).toBeInTheDocument();
  });

  it('should handle pin functionality when pin is disabled', () => {
    const mockObjectWithoutPin = {
      ...mockObject,
      profileSettings: [{ type: 'pinCustomObject', enabled: false }],
    };

    (useWorkspaceContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: mockObjectWithoutPin,
      refetchObject: mockRefetchObject,
    });

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    const pinIcon = container.querySelector('.tabler-icon-pin');
    expect(pinIcon).not.toBeInTheDocument();
  });

  it('should handle error in pin toggle', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    (ObjectAPI.update as ReturnType<typeof vi.fn>).mockRejectedValue(new Error('Update failed'));

    const { container } = renderWithMantine(
      <MemoryRouter>
        <Forms form={mockForm} />
      </MemoryRouter>
    );

    const pinIcon = container.querySelector('.tabler-icon-pin');
    const pinButton = pinIcon?.closest('button');

    await user.click(pinButton!);

    expect(consoleSpy).toHaveBeenCalledWith('Failed to update pin status:', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
