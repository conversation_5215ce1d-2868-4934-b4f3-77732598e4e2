import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import LongText from './index';

// Mock window.matchMedia to fix media query issues
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock contexts
const mockProfileContext = {
  profile: {
    'field-1': 'This is a long text content',
    'field-2': 'Another long text\nwith multiple lines',
    'field-3': '',
  },
};

const mockWorkspaceContext = {
  activeView: {
    id: 'view-1',
    displayLongText: ['field-1', 'field-2', 'field-3'],
  },
  object: {
    fields: [
      { id: 'field-1', name: 'Description' },
      { id: 'field-2', name: 'Notes' },
      { id: 'field-3', name: 'Comments' },
    ],
  },
  onSaveData: vi.fn(),
  currRecordIndex: 0,
  handleViewChange: vi.fn(),
};

const mockUseProfileContext = vi.fn();
const mockUseWorkspaceContext = vi.fn();

vi.mock('@/contexts/ProfileContext', () => ({
  useProfileContext: () => mockUseProfileContext(),
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => mockUseWorkspaceContext(),
}));

// Mock useTranslate
const mockTranslate = vi.fn();
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: mockTranslate,
  }),
}));

// Mock the Modal component to avoid complex dependencies
vi.mock('@resola-ai/ui', () => ({
  Modal: ({ children, opened, title, onOk, onClose, onCancel }: any) =>
    opened ? (
      <div data-testid='modal'>
        <div data-testid='modal-title'>{title}</div>
        <div data-testid='modal-content'>{children}</div>
        <button data-testid='modal-ok' onClick={onOk}>
          OK
        </button>
        <button data-testid='modal-cancel' onClick={onCancel}>
          Cancel
        </button>
        <button data-testid='modal-close' onClick={onClose}>
          Close
        </button>
      </div>
    ) : null,
}));

describe('LongText Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseProfileContext.mockReturnValue(mockProfileContext);
    mockUseWorkspaceContext.mockReturnValue(mockWorkspaceContext);

    mockTranslate.mockImplementation((key: string) => {
      const translations: { [key: string]: string } = {
        longText: 'Long Text',
        unpinTabbar: 'Unpin from Tabbar',
        edit: 'Edit',
        save: 'Save',
        cancel: 'Cancel',
        copyToClipboard: 'Copy to Clipboard',
        confirm: 'Confirm',
        unpinTabbarDescription: 'Are you sure you want to unpin this field from the tabbar?',
      };
      return translations[key] || key;
    });
  });

  describe('Basic Rendering', () => {
    it('should render the component title', () => {
      renderWithMantine(<LongText />);

      expect(screen.getByText('Long Text')).toBeInTheDocument();
    });

    it('should render the unpin button', () => {
      renderWithMantine(<LongText />);

      expect(screen.getByText('Unpin from Tabbar')).toBeInTheDocument();
    });

    it('should call useTranslate with workspace domain', () => {
      renderWithMantine(<LongText />);

      expect(mockTranslate).toHaveBeenCalledWith('longText');
      expect(mockTranslate).toHaveBeenCalledWith('unpinTabbar');
    });
  });

  describe('Context Integration', () => {
    it('should handle missing activeView gracefully', () => {
      mockUseWorkspaceContext.mockReturnValue({
        ...mockWorkspaceContext,
        activeView: null,
      });

      expect(() => renderWithMantine(<LongText />)).not.toThrow();
    });

    it('should handle missing displayLongText array', () => {
      mockUseWorkspaceContext.mockReturnValue({
        ...mockWorkspaceContext,
        activeView: {
          id: 'view-1',
          displayLongText: undefined,
        },
      });

      expect(() => renderWithMantine(<LongText />)).not.toThrow();
    });

    it('should handle missing profile data', () => {
      mockUseProfileContext.mockReturnValue({
        profile: null,
      });

      expect(() => renderWithMantine(<LongText />)).not.toThrow();
    });
  });

  describe('Button States', () => {
    it('should initially have unpin button disabled', () => {
      renderWithMantine(<LongText />);

      // Find the actual button element, not just the text span
      const unpinButton = screen.getByRole('button', { name: /unpin from tabbar/i });
      expect(unpinButton).toBeDisabled();
    });

    it('should show edit and copy buttons for each field', () => {
      renderWithMantine(<LongText />);

      // Should have edit buttons for each field
      expect(screen.getAllByText('Edit')).toHaveLength(3);
      expect(screen.getAllByText('Copy to Clipboard')).toHaveLength(3);
    });
  });

  describe('Modal Functionality', () => {
    it('should not show modal initially', () => {
      renderWithMantine(<LongText />);

      expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
    });

    it('should show modal when unpin button is clicked and activeAccordion is set', () => {
      renderWithMantine(<LongText />);

      // First expand an accordion item to set activeAccordion
      const firstAccordionButton = screen.getAllByRole('button')[1]; // Skip the main unpin button
      fireEvent.click(firstAccordionButton);

      // Now click the unpin button (it should be enabled)
      const unpinButton = screen.getByRole('button', { name: /unpin from tabbar/i });
      fireEvent.click(unpinButton);

      expect(screen.getByTestId('modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-title')).toHaveTextContent('Unpin from Tabbar');
    });

    it('should close modal when cancel is clicked', () => {
      renderWithMantine(<LongText />);

      // First expand an accordion item
      const firstAccordionButton = screen.getAllByRole('button')[1];
      fireEvent.click(firstAccordionButton);

      // Click unpin button to open modal
      const unpinButton = screen.getByRole('button', { name: /unpin from tabbar/i });
      fireEvent.click(unpinButton);

      // Click cancel to close modal
      const cancelButton = screen.getByTestId('modal-cancel');
      fireEvent.click(cancelButton);

      expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
    });
  });

  describe('Field Content', () => {
    it('should display field names from object configuration', () => {
      renderWithMantine(<LongText />);

      expect(screen.getByText('Description')).toBeInTheDocument();
      expect(screen.getByText('Notes')).toBeInTheDocument();
      expect(screen.getByText('Comments')).toBeInTheDocument();
    });

    it('should handle missing field names gracefully', () => {
      mockUseWorkspaceContext.mockReturnValue({
        ...mockWorkspaceContext,
        object: {
          fields: [],
        },
      });

      expect(() => renderWithMantine(<LongText />)).not.toThrow();
    });
  });

  describe('Edit Mode', () => {
    it('should show cancel and save buttons when editing', async () => {
      renderWithMantine(<LongText />);

      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);

      await waitFor(() => {
        expect(screen.getByText('Cancel')).toBeInTheDocument();
        expect(screen.getByText('Save')).toBeInTheDocument();
      });
    });

    it('should call onSaveData when save is clicked', async () => {
      renderWithMantine(<LongText />);

      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);

      await waitFor(() => {
        const saveButton = screen.getByText('Save');
        fireEvent.click(saveButton);
      });

      expect(mockWorkspaceContext.onSaveData).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty displayLongText array', () => {
      mockUseWorkspaceContext.mockReturnValue({
        ...mockWorkspaceContext,
        activeView: {
          id: 'view-1',
          displayLongText: [],
        },
      });

      expect(() => renderWithMantine(<LongText />)).not.toThrow();
    });

    it('should handle undefined profile values', () => {
      mockUseProfileContext.mockReturnValue({
        profile: {
          'field-1': undefined,
          'field-2': null,
        },
      });

      expect(() => renderWithMantine(<LongText />)).not.toThrow();
    });
  });
});
