import BoxCard from '@/components/Common/BoxCard';
import Editor from '@/components/Editor';
import { ColumnIcon, type MessageType } from '@/constants/workspace';
import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useRecords } from '@/hooks';
import { ActionIcon, Avatar, Box, Flex, Text, Title, Tooltip, rem } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { Modal } from '@resola-ai/ui';
import { type FieldType, FieldTypes } from '@resola-ai/ui/components';
import { IconLine } from '@resola-ai/ui/components/Icons';
import { IconMail, IconQuote, IconUser } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { startCase } from 'lodash';
import React, { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import type { ProfileData } from '../ObjectFields/RenderProfileTypes';

// Define message button configuration
type MessageButtonConfig = {
  type: MessageType;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  condition: (object: any, findLineField?: any) => boolean;
  disabled?: boolean;
};

const overlayProps = {
  opacity: 0.3,
  blur: 0,
};

const ProfileContact = () => {
  const { t } = useTranslate('workspace');
  const { wsId } = useParams();
  const { object } = useWorkspaceContext();
  const [fetchObjRecords] = useState<string>();
  const { records: linkedRecords } = useRecords(wsId, fetchObjRecords, false);

  // Combined editor state with type parameter
  const [messageType, setMessageType] = useState<MessageType | null>(null);
  const [openedEditor, { open: openEditor, close: closeEditor }] = useDisclosure(false);

  const [mounted, setMounted] = useState(false);
  const { profile: profileRecord } = useProfileContext();

  // Helper function to open editor with specified type
  const handleOpenEditor = (type: MessageType) => {
    setMessageType(type);
    openEditor();
  };

  // Helper function to close editor and reset type
  const handleCloseEditor = () => {
    closeEditor();
  };

  // const [openedAddAction, { close: closeAddAction }] = useDisclosure(false);
  const profileData: ProfileData[] = useMemo(() => {
    if (!object?.fields || !profileRecord) return [];
    return object.fields.map((field) => {
      return {
        ...field,
        mapValue: profileRecord[field.id!] as string,
        icon: ColumnIcon[field.type as FieldType],
      };
    });
  }, [object?.fields, profileRecord, linkedRecords]);

  const primaryField = useMemo(
    () =>
      profileData.find(
        (field) => field.type === FieldTypes.SINGLE_LINE_TEXT && field.options?.isPrimary === true
      ),
    [profileData]
  );

  const avatarField = useMemo(
    () =>
      profileData.find(
        (field) => field.type === FieldTypes.IMAGE && field.options?.isAvatar === true
      ),
    [profileData]
  );

  const findLineChannelId = useMemo(() => {
    return profileData.find((field) => field.type === FieldTypes.LINE)?.options?.channelId;
  }, [profileData]);

  // Message button configurations
  const messageButtons: MessageButtonConfig[] = useMemo(
    () => [
      {
        type: 'email',
        icon: <IconMail />,
        color: 'decaBlue.5',
        bgColor: 'decaBlue.0',
        condition: (obj) => !!obj?.messaging?.email,
      },
      {
        type: 'sms',
        icon: <IconQuote />,
        color: 'decaRed.5',
        bgColor: 'decaRed.0',
        condition: (obj) => !!obj?.messaging?.sms,
      },
      {
        type: 'line',
        icon: <IconLine />,
        color: 'decaGreen.5',
        bgColor: 'decaGreen.0',
        condition: (obj) => !!obj?.messaging?.line,
        disabled: !findLineChannelId,
      },
    ],
    []
  );

  // Helper function to get the modal title based on message type
  const getModalTitle = () => {
    switch (messageType) {
      case 'email':
        return t('newEmail');
      case 'sms':
        return t('newSms');
      case 'line':
        return t('newLine');
      default:
        return '';
    }
  };

  return (
    <BoxCard>
      <Box py={0} data-testid='profile-contact-test-id'>
        <Flex align={'center'} gap='md'>
          {object?.hasAvatar && (
            <Avatar color='cyan' radius='xl' size={60}>
              {avatarField?.mapValue ? (
                <Avatar size={60} src={avatarField?.mapValue} alt='avatar' />
              ) : (
                <IconUser size={60} />
              )}
            </Avatar>
          )}
          <Box>
            <Title order={3}>{startCase(primaryField?.mapValue?.toLowerCase())}</Title>
            <Text fz='lg' fw={400} c='decaGrey.4'>
              ID: {profileRecord?.id}
            </Text>
          </Box>
        </Flex>

        <Flex gap={rem(10)} data-testid='profile-contact-message-buttons'>
          {messageButtons.map(
            (button) =>
              button.condition(object) &&
              (button.type === 'line' && button.disabled ? (
                <Tooltip key={`tooltip-${button.type}`} label={t('noLineChannel')} withArrow>
                  <ActionIcon
                    key={`action-${button.type}`}
                    w={rem(30)}
                    h={rem(30)}
                    p={rem(7)}
                    mt={rem(15)}
                    radius={'lg'}
                    disabled={button.disabled}
                    data-testid={`action-${button.type}`}
                  >
                    {button.icon}
                  </ActionIcon>
                </Tooltip>
              ) : (
                <ActionIcon
                  key={`action-${button.type}`}
                  c={button.color}
                  w={rem(30)}
                  h={rem(30)}
                  bg={button.bgColor}
                  p={rem(7)}
                  mt={rem(15)}
                  radius={'lg'}
                  onClick={() => handleOpenEditor(button.type)}
                  disabled={button.disabled}
                  data-testid={`action-${button.type}`}
                >
                  {button.icon}
                </ActionIcon>
              ))
          )}
          {/* TODO: comment out since the feature is not ready */}
          {/* <DecaButton
            variant='neutral'
            size='sm'
            leftSection={<IconPlus size={16} />}
            mt={rem(15)}
            onClick={openAddAction}>
            {t('addAction')}
          </DecaButton> */}
        </Flex>
      </Box>
      <Modal
        data-testid='message-editor-modal'
        color='decaGrey.9'
        fz={rem(14)}
        opened={openedEditor}
        onClose={handleCloseEditor}
        title={getModalTitle()}
        overlayProps={overlayProps}
        centered
        size='70%'
        keepMounted={mounted}
      >
        {messageType && (
          <Editor
            editorType={messageType}
            closeModal={handleCloseEditor}
            setMounted={setMounted}
            openModal={openEditor}
          />
        )}
      </Modal>

      {/* <Modal
        centered
        opened={openedAddAction}
        onClose={closeAddAction}
        title={t('customAction')}
        size='60%'>
        <CustomAction closeAddAction={closeAddAction} />
      </Modal> */}
    </BoxCard>
  );
};

export default React.memo(ProfileContact);
