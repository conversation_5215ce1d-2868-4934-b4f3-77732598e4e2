import { CUSTOM_ACTION_COLORS } from '@/constants/workspace';
import { Box, Group, Radio } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { useCustomActionStyles } from './useCustomActionStyles';

const ColorConfig = ({ name }: { name: string }) => {
  const { t } = useTranslate('workspace');
  const { classes, cx } = useCustomActionStyles();
  const { watch, setValue } = useFormContext();

  return (
    <Radio.Group label={t('color')}>
      <Group mt='xs'>
        {CUSTOM_ACTION_COLORS.map((color) => (
          <Box
            key={color}
            className={cx(
              classes.color,
              classes[color],
              watch(name) === color && classes[`selected_${color}`]
            )}
            onClick={() => setValue(name, color)}
          />
        ))}
      </Group>
    </Radio.Group>
  );
};

export default React.memo(ColorConfig);
