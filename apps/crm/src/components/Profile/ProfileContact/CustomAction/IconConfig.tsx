import { Box, Group, Radio, rem } from '@mantine/core';
import {
  IconActivity,
  IconBell,
  IconCalendarTime,
  IconCode,
  IconFileText,
  IconLink,
  IconListCheck,
  IconMail,
  IconMessages,
  IconPhone,
  IconUser,
  IconUsers,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { useCustomActionStyles } from './useCustomActionStyles';

const ICONS = [
  {
    icon: <IconPhone size={20} />,
    name: 'phone',
  },
  {
    icon: <IconMail size={20} />,
    name: 'email',
  },
  {
    icon: <IconLink size={24} />,
    name: 'link',
  },
  {
    icon: <IconFileText size={20} />,
    name: 'file',
  },
  {
    icon: <IconListCheck size={20} />,
    name: 'list',
  },
  {
    icon: <IconCalendarTime size={20} />,
    name: 'calendar',
  },
  {
    icon: <IconUser size={20} />,
    name: 'user',
  },
  {
    icon: <IconUsers size={20} />,
    name: 'users',
  },
  {
    icon: <IconBell size={20} />,
    name: 'notification',
  },
  {
    icon: <IconMessages size={20} />,
    name: 'messages',
  },
  {
    icon: <IconCode size={20} />,
    name: 'code',
  },
  {
    icon: <IconActivity size={20} />,
    name: 'activity',
  },
];

const IconConfig = ({ name }: { name: string }) => {
  const { t } = useTranslate('workspace');
  const { classes, cx } = useCustomActionStyles();

  const { watch, setValue } = useFormContext();

  return (
    <Radio.Group label={t('icon')}>
      <Group mt={rem(10)}>
        {ICONS.map(({ icon, name: _name }) => (
          <Box
            key={_name}
            className={cx(classes.icon, watch(name) === _name && classes.selectedIcon)}
            onClick={() => setValue(name, _name)}
          >
            {icon}
          </Box>
        ))}
      </Group>
    </Radio.Group>
  );
};

export default React.memo(IconConfig);
