import type { ICustomAction } from '@/models';
import { customActionSchemas } from '@/utils/schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Flex, Group, Radio, Text, rem } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { IconActivity, IconLink } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import OpenLinkConfig from './OpenLinkConfig';
import WebhookConfig from './WebhookConfig';
import { useCustomActionStyles } from './useCustomActionStyles';

const CustomAction = ({
  closeAddAction,
  data: initialData,
}: {
  data?: ICustomAction;
  closeAddAction: () => void;
}) => {
  const { t } = useTranslate('workspace');
  const { classes } = useCustomActionStyles();

  const schema = customActionSchemas(t);

  const methods = useForm({
    defaultValues: {
      type: '',
      color: 'red',
      openLink: {
        color: 'red',
      },
      webhook: {
        color: 'red',
        icon: 'phone',
      },
    },
    resolver: zodResolver(schema),
  });

  const { handleSubmit, watch, setValue } = methods;

  const onSubmit = (data) => {
    // TODO: implement onSubmit
    console.log('data', data);
  };

  const onDelete = () => {
    // TODO: implement onDelete
    console.log('delete');
  };

  const type = watch('type');

  return (
    <FormProvider {...methods}>
      <form onSubmit={(e) => e.preventDefault()}>
        <Box w='100%' pr={rem(60)}>
          <Radio.Group mb={rem(20)} onChange={(value) => setValue('type', value)}>
            <Group
              mt='xs'
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: rem(10),
                alignItems: 'flex-start',
              }}
            >
              <Radio label={t('openLink')} value='openLink' />
              <Flex className={classes.desc}>
                <IconLink size={24} />
                <Text>{t('openLinkDesc')}</Text>
              </Flex>

              {type === 'openLink' && <OpenLinkConfig />}
              <Radio label={t('webhook')} value='webhook' />
              <Flex className={classes.desc}>
                <IconActivity size={24} />
                <Text>{t('webhookDesc')}</Text>
              </Flex>

              {type === 'webhook' && <WebhookConfig />}
            </Group>
          </Radio.Group>
        </Box>
        <Flex justify='space-between'>
          <Box>
            {initialData && (
              <DecaButton variant='negative_text' onClick={onDelete}>
                {t('delete')}
              </DecaButton>
            )}
          </Box>
          <Group gap={rem(10)}>
            <DecaButton variant='neutral' onClick={closeAddAction}>
              {t('cancel')}
            </DecaButton>
            <DecaButton type='submit' disabled={!type} onClick={handleSubmit(onSubmit)}>
              {t('save')}
            </DecaButton>
          </Group>
        </Flex>
      </form>
    </FormProvider>
  );
};

export default React.memo(CustomAction);
