import { Box, Flex, rem } from '@mantine/core';
import { IconChevronDown } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Select, Switch, TextInput } from 'react-hook-form-mantine';
import ColorConfig from './ColorConfig';
import IconConfig from './IconConfig';
import Parameters from './Parameters';
const WebhookConfig = () => {
  const { t } = useTranslate('workspace');

  const { control, watch } = useFormContext();

  const sendHeaders = watch('webhook.sendHeaders.enabled');
  const sendBody = watch('webhook.sendBody.enabled');

  return (
    <Box ml={rem(30)} w={'100%'}>
      <Flex align='center' gap={rem(20)} mt={rem(-10)}>
        <Select
          allowDeselect={false}
          control={control}
          withAsterisk
          name='webhook.method'
          labelProps={{ mb: rem(10) }}
          label={t('method')}
          placeholder={t('selectMethod')}
          data={['Get', 'Post', 'Patch', 'Put', 'Delete']}
          rightSection={<IconChevronDown size={16} />}
        />
        <TextInput
          control={control}
          w={'100%'}
          my={rem(20)}
          label={'URL'}
          withAsterisk
          labelProps={{ mb: rem(10) }}
          name='webhook.url'
          placeholder={t('inputUrl')}
        />
      </Flex>
      <Box pr={rem(20)}>
        <Switch
          control={control}
          mb={rem(20)}
          label={`${t('sendHeaders')} (${t('optional')})`}
          name='webhook.sendHeaders.enabled'
          color='decaGreen'
        />
        {sendHeaders && <Parameters prefixName='webhook.sendHeaders' />}
        <Switch
          control={control}
          mb={rem(20)}
          label={`${t('sendBody')} (${t('optional')})`}
          name='webhook.sendBody.enabled'
          color='decaGreen'
        />
        {sendBody && <Parameters prefixName='webhook.sendBody' />}
      </Box>
      <ColorConfig name='webhook.color' />
      <Box mt={rem(20)}>
        <IconConfig name='webhook.icon' />
      </Box>
    </Box>
  );
};

export default React.memo(WebhookConfig);
