import { screen } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { renderWithMantine } from '@/tests/utils/testUtils';
import OpenLinkConfig from './OpenLinkConfig';

// Mock dependencies
vi.mock('@tolgee/react', () => ({
    useTranslate: () => ({
        t: (key: string) => `translated_${key}`,
    }),
}));

vi.mock('react-hook-form-mantine', () => ({
    TextInput: vi.fn(({ control, name, label, placeholder, withAsterisk, labelProps, ...props }) => (
        <div data-testid={`text-input-${name?.split('.').pop()}`}>
            <label data-testid={`label-${name?.split('.').pop()}`}>
                {label}
                {withAsterisk && <span data-testid="asterisk">*</span>}
            </label>
            <input
                data-testid={`input-${name?.split('.').pop()}`}
                placeholder={placeholder}
                {...props}
            />
        </div>
    )),
}));

vi.mock('./ColorConfig', () => ({
    default: vi.fn(({ name }) => (
        <div data-testid="color-config" data-name={name}>
            Color Config Component
        </div>
    )),
}));

// Test wrapper component with form provider
const TestWrapper = ({
    children,
    defaultValues = {}
}: {
    children: React.ReactNode;
    defaultValues?: any;
}) => {
    const methods = useForm({
        defaultValues: {
            openLink: {
                label: '',
                url: '',
                color: 'red',
            },
            ...defaultValues
        }
    });
    return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('OpenLinkConfig Component', () => {

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('Rendering', () => {
        it('should render all form fields correctly', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            // Check for label input
            expect(screen.getByTestId('text-input-label')).toBeInTheDocument();
            expect(screen.getByTestId('label-label')).toHaveTextContent('translated_displayLabel');
            expect(screen.getByTestId('input-label')).toHaveAttribute('placeholder', 'translated_inputLabel');
            // Check that asterisks are present (there are multiple)
            const asterisks = screen.getAllByTestId('asterisk');
            expect(asterisks.length).toBeGreaterThan(0);

            // Check for URL input
            expect(screen.getByTestId('text-input-url')).toBeInTheDocument();
            expect(screen.getByTestId('label-url')).toHaveTextContent('URL');
            expect(screen.getByTestId('input-url')).toHaveAttribute('placeholder', 'translated_inputUrl');

            // Check for color config
            expect(screen.getByTestId('color-config')).toBeInTheDocument();
            expect(screen.getByTestId('color-config')).toHaveAttribute('data-name', 'openLink.color');
        });

        it('should apply correct spacing and layout styles', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            const container = screen.getByTestId('text-input-label').closest('div');
            expect(container).toBeInTheDocument();
        });

        it('should display translated labels', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            expect(screen.getByText('translated_displayLabel')).toBeInTheDocument();
            expect(screen.getByText('URL')).toBeInTheDocument();
            expect(screen.getByText('Color Config Component')).toBeInTheDocument();
        });
    });

    describe('Form Integration', () => {
        it('should integrate with react-hook-form context', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            // Verify that form fields are connected to form context
            const labelInput = screen.getByTestId('input-label');
            const urlInput = screen.getByTestId('input-url');

            expect(labelInput).toBeInTheDocument();
            expect(urlInput).toBeInTheDocument();
        });

        it('should pass correct props to TextInput components', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            // Check label input props
            const labelInputContainer = screen.getByTestId('text-input-label');
            expect(labelInputContainer).toBeInTheDocument();

            // Check that asterisks are present (there are multiple)
            const asterisks = screen.getAllByTestId('asterisk');
            expect(asterisks.length).toBeGreaterThan(0); // withAsterisk

            // Check URL input props
            const urlInputContainer = screen.getByTestId('text-input-url');
            expect(urlInputContainer).toBeInTheDocument();
        });

        it('should pass correct name prop to ColorConfig', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            const colorConfig = screen.getByTestId('color-config');
            expect(colorConfig).toHaveAttribute('data-name', 'openLink.color');
        });
    });

    describe('Form Field Names', () => {
        it('should use correct field names for form binding', () => {
            const TestComponent = () => {
                const methods = useForm({
                    defaultValues: {
                        openLink: {
                            label: 'Test Label',
                            url: 'https://example.com',
                            color: 'blue'
                        }
                    }
                });

                return (
                    <FormProvider {...methods}>
                        <OpenLinkConfig />
                        <div data-testid="form-values">
                            {JSON.stringify(methods.getValues())}
                        </div>
                    </FormProvider>
                );
            };

            renderWithMantine(<TestComponent />);

            const formValues = screen.getByTestId('form-values');
            expect(formValues).toHaveTextContent('Test Label');
            expect(formValues).toHaveTextContent('https://example.com');
            expect(formValues).toHaveTextContent('blue');
        });
    });

    describe('Accessibility', () => {
        it('should have proper labeling for form fields', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            // Check label associations
            expect(screen.getByTestId('label-label')).toBeInTheDocument();
            expect(screen.getByTestId('label-url')).toBeInTheDocument();
        });

        it('should indicate required fields with asterisk', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            const asterisks = screen.getAllByTestId('asterisk');
            expect(asterisks).toHaveLength(2); // Both label and URL are required
        });
    });

    describe('Component Structure', () => {
        it('should render within proper container structure', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            // The component should render all expected elements
            expect(screen.getByTestId('text-input-label')).toBeInTheDocument();
            expect(screen.getByTestId('text-input-url')).toBeInTheDocument();
            expect(screen.getByTestId('color-config')).toBeInTheDocument();
        });

        it('should maintain proper field ordering', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            const container = document.body;
            const labelInput = screen.getByTestId('text-input-label');
            const urlInput = screen.getByTestId('text-input-url');
            const colorConfig = screen.getByTestId('color-config');

            // Check that elements appear in expected order
            const allElements = [labelInput, urlInput, colorConfig];
            let previousPosition = -1;

            allElements.forEach(element => {
                const position = Array.from(container.querySelectorAll('*')).indexOf(element);
                expect(position).toBeGreaterThan(previousPosition);
                previousPosition = position;
            });
        });
    });

    describe('Props and Configuration', () => {
        it('should render without props (using default form context)', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            // Should render without errors
            expect(screen.getByTestId('text-input-label')).toBeInTheDocument();
            expect(screen.getByTestId('text-input-url')).toBeInTheDocument();
        });

        it('should work with different form contexts', () => {
            const TestComponent = () => {
                const methods = useForm({
                    defaultValues: {
                        openLink: {
                            label: 'Custom Label',
                            url: 'https://custom.com',
                            color: 'green'
                        }
                    }
                });

                return (
                    <FormProvider {...methods}>
                        <OpenLinkConfig />
                    </FormProvider>
                );
            };

            renderWithMantine(<TestComponent />);

            // Should render correctly with custom form context
            expect(screen.getByTestId('text-input-label')).toBeInTheDocument();
            expect(screen.getByTestId('color-config')).toBeInTheDocument();
        });
    });

    describe('Edge Cases', () => {
        it('should handle missing form context gracefully', () => {
            // This would normally throw an error, but we're testing the structure
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });

            try {
                renderWithMantine(<OpenLinkConfig />);
            } catch (error) {
                // Expected to throw due to missing FormProvider
            }

            consoleSpy.mockRestore();
        });

        it('should handle empty default values', () => {
            renderWithMantine(
                <TestWrapper defaultValues={{ openLink: {} }}>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            expect(screen.getByTestId('text-input-label')).toBeInTheDocument();
            expect(screen.getByTestId('text-input-url')).toBeInTheDocument();
            expect(screen.getByTestId('color-config')).toBeInTheDocument();
        });

        it('should handle null/undefined values in form', () => {
            renderWithMantine(
                <TestWrapper defaultValues={{ openLink: { label: null, url: undefined, color: null } }}>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            expect(screen.getByTestId('text-input-label')).toBeInTheDocument();
            expect(screen.getByTestId('text-input-url')).toBeInTheDocument();
            expect(screen.getByTestId('color-config')).toBeInTheDocument();
        });
    });

    describe('Component Memoization', () => {
        it('should be memoized with React.memo', () => {
            // Simple test to verify the component is memoized
            // Test that it renders without errors when properly wrapped
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );
            
            expect(screen.getByText('translated_displayLabel')).toBeInTheDocument();
        });
    });

    describe('Translation Integration', () => {
        it('should use translation keys correctly', () => {
            renderWithMantine(
                <TestWrapper>
                    <OpenLinkConfig />
                </TestWrapper>
            );

            // Verify translation keys are being used
            expect(screen.getByText('translated_displayLabel')).toBeInTheDocument();
            expect(screen.getByText('URL')).toBeInTheDocument(); // URL is not translated
            expect(screen.getByTestId('input-label')).toHaveAttribute('placeholder', 'translated_inputLabel');
            expect(screen.getByTestId('input-url')).toHaveAttribute('placeholder', 'translated_inputUrl');
        });
    });
}); 