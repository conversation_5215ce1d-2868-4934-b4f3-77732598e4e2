import { fireEvent, screen, waitFor } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { renderWithMantine } from '@/tests/utils/testUtils';
import Parameters from './Parameters';

// Mock dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => `translated_${key}`,
  }),
}));

vi.mock('./useCustomActionStyles', () => ({
  useCustomActionStyles: () => ({
    classes: {
      headerParameters: 'mock-header-parameters-class',
    },
  }),
}));

vi.mock('@resola-ai/ui', () => ({
  DecaButton: vi.fn(({ variant, size, children, onClick, ...props }) => (
    <button 
      data-testid="deca-button"
      data-variant={variant}
      data-size={size}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  )),
}));

vi.mock('react-hook-form-mantine', () => ({
  Select: vi.fn(({ control, name, label, placeholder, data, rightSection, ...props }) => (
    <div data-testid={`select-${name?.split('.').pop()}`}>
      <label data-testid={`select-label-${name?.split('.').pop()}`}>{label}</label>
      <select 
        data-testid={`select-input-${name?.split('.').pop()}`}
        placeholder={placeholder}
        {...props}
      >
        {data?.map((item: any) => (
          <option key={item.value} value={item.value}>
            {item.label}
          </option>
        ))}
      </select>
      {rightSection && <div data-testid="right-section">{rightSection}</div>}
    </div>
  )),
  TextInput: vi.fn(({ control, name, label, placeholder, w, labelProps, ...props }) => (
    <div data-testid={`text-input-${name?.split('.').pop()}`} style={{ width: w }}>
      <label data-testid={`text-input-label-${name?.split('.').pop()}`}>{label}</label>
      <input
        data-testid={`text-input-field-${name?.split('.').pop()}`}
        placeholder={placeholder}
        {...props}
      />
    </div>
  )),
  Textarea: vi.fn(({ control, name, label, rows, ...props }) => (
    <div data-testid={`textarea-${name?.split('.').pop()}`}>
      <label data-testid={`textarea-label-${name?.split('.').pop()}`}>{label}</label>
      <textarea
        data-testid={`textarea-field-${name?.split('.').pop()}`}
        rows={rows}
        {...props}
      />
    </div>
  )),
}));

vi.mock('@tabler/icons-react', () => ({
  IconChevronDown: vi.fn(({ size }) => (
    <div data-testid="icon-chevron-down" data-size={size}>ChevronDown</div>
  )),
  IconTrash: vi.fn(({ size }) => (
    <div data-testid="icon-trash" data-size={size}>Trash</div>
  )),
}));

// Test wrapper component with form provider
const TestWrapper = ({ 
  children, 
  defaultValues = {} 
}: { 
  children: React.ReactNode;
  defaultValues?: any;
}) => {
  const methods = useForm({
    defaultValues: {
      webhook: {
        sendHeaders: {
          type: 'manually',
          parameters: [{ name: '', value: '' }]
        }
      },
      ...defaultValues
    }
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('Parameters Component', () => {
  const mockPrefixName = 'webhook.sendHeaders';
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render type selection dropdown', () => {
      renderWithMantine(
        <TestWrapper>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.getByTestId('select-type')).toBeInTheDocument();
      expect(screen.getByTestId('select-label-type')).toHaveTextContent('translated_specifyHeaders');
      expect(screen.getByTestId('select-input-type')).toHaveAttribute('placeholder', 'translated_selectHeader');
    });

    it('should render dropdown options correctly', () => {
      renderWithMantine(
        <TestWrapper>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      const selectInput = screen.getByTestId('select-input-type');
      expect(selectInput).toBeInTheDocument();
      
      // Check if options are rendered
      expect(screen.getByText('translated_usingJson')).toBeInTheDocument();
      expect(screen.getByText('translated_usingFieldsBelow')).toBeInTheDocument();
    });

    it('should render dropdown with chevron icon', () => {
      renderWithMantine(
        <TestWrapper>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      // Find the chevron icon by its CSS class instead of test ID
      const chevronIcon = document.querySelector('.tabler-icon-chevron-down');
      expect(chevronIcon).toBeInTheDocument();
      expect(chevronIcon).toHaveAttribute('height', '16');
    });
  });

  describe('JSON Mode', () => {
    it('should render textarea when type is json', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { type: 'json', json: 'test json' }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.getByTestId('textarea-json')).toBeInTheDocument();
      expect(screen.getByTestId('textarea-label-json')).toHaveTextContent('translated_JSON');
      expect(screen.getByTestId('textarea-field-json')).toHaveAttribute('rows', '4');
    });

    it('should not render textarea when type is not json', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { type: 'manually' }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.queryByTestId('textarea-json')).not.toBeInTheDocument();
    });
  });

  describe('Manual Mode', () => {
    it('should render manual parameter fields when type is manually', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { 
              type: 'manually',
              parameters: [{ name: 'Content-Type', value: 'application/json' }]
            }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.getByText('translated_headerParameters')).toBeInTheDocument();
      expect(screen.getByTestId('deca-button')).toBeInTheDocument();
      expect(screen.getByTestId('deca-button')).toHaveTextContent('translated_addParameter');
    });

    it('should render parameter fields for existing parameters', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { 
              type: 'manually',
              parameters: [{ name: 'Content-Type', value: 'application/json' }]
            }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.getByTestId('text-input-name')).toBeInTheDocument();
      expect(screen.getByTestId('text-input-label-name')).toHaveTextContent('translated_headerName');
      expect(screen.getByTestId('text-input-field-name')).toHaveAttribute('placeholder', 'translated_inputHeaderName');

      expect(screen.getByTestId('text-input-value')).toBeInTheDocument();
      expect(screen.getByTestId('text-input-label-value')).toHaveTextContent('translated_headerValue');
      expect(screen.getByTestId('text-input-field-value')).toHaveAttribute('placeholder', 'translated_inputHeaderValue');
    });

    it('should render delete button for each parameter', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { 
              type: 'manually',
              parameters: [{ name: 'test', value: 'test' }]
            }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      // Find the trash icon by its CSS class instead of test ID
      const trashIcon = document.querySelector('.tabler-icon-trash');
      expect(trashIcon).toBeInTheDocument();
      expect(trashIcon).toHaveAttribute('height', '18');
    });

    it('should not render manual fields when type is not manually', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { type: 'json' }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.queryByText('translated_headerParameters')).not.toBeInTheDocument();
      expect(screen.queryByTestId('deca-button')).not.toBeInTheDocument();
    });
  });

  describe('Field Array Operations', () => {
    it('should handle add parameter button click', () => {
      const TestComponent = () => {
        const methods = useForm({
          defaultValues: {
            webhook: {
              sendHeaders: { 
                type: 'manually',
                parameters: []
              }
            }
          }
        });

        return (
          <FormProvider {...methods}>
            <Parameters prefixName={mockPrefixName} />
            <div data-testid="parameters-count">
              {methods.watch('webhook.sendHeaders.parameters')?.length || 0}
            </div>
          </FormProvider>
        );
      };

      renderWithMantine(<TestComponent />);

      const addButton = screen.getByTestId('deca-button');
      const parametersCount = screen.getByTestId('parameters-count');

      expect(parametersCount).toHaveTextContent('0');

      fireEvent.click(addButton);

      // After clicking, parameters array should have one item
      waitFor(() => {
        expect(parametersCount).toHaveTextContent('1');
      });
    });

    it('should handle remove parameter button click', () => {
      const TestComponent = () => {
        const methods = useForm({
          defaultValues: {
            webhook: {
              sendHeaders: { 
                type: 'manually',
                parameters: [{ name: 'test', value: 'test' }]
              }
            }
          }
        });

        return (
          <FormProvider {...methods}>
            <Parameters prefixName={mockPrefixName} />
            <div data-testid="parameters-count">
              {methods.watch('webhook.sendHeaders.parameters')?.length || 0}
            </div>
          </FormProvider>
        );
      };

      renderWithMantine(<TestComponent />);

      const parametersCount = screen.getByTestId('parameters-count');
      expect(parametersCount).toHaveTextContent('1');

      // Find the remove button by the trash icon
      const trashIcon = document.querySelector('.tabler-icon-trash');
      const removeButton = trashIcon?.closest('button');
      
      if (removeButton) {
        fireEvent.click(removeButton);

        waitFor(() => {
          expect(parametersCount).toHaveTextContent('0');
        });
      }
    });
  });

  describe('Different Prefix Names', () => {
    it('should work with different prefix names', () => {
      const altPrefixName = 'webhook.sendBody';
      
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendBody: { type: 'manually', parameters: [] }
          }
        }}>
          <Parameters prefixName={altPrefixName} />
        </TestWrapper>
      );

      expect(screen.getByTestId('select-type')).toBeInTheDocument();
    });

    it('should handle nested prefix names correctly', () => {
      renderWithMantine(
        <TestWrapper>
          <Parameters prefixName="deeply.nested.prefix" />
        </TestWrapper>
      );

      expect(screen.getByTestId('select-type')).toBeInTheDocument();
    });
  });

  describe('Styling and Layout', () => {
    it('should apply correct CSS classes to parameter containers', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { 
              type: 'manually',
              parameters: [{ name: 'test', value: 'test' }]
            }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      // The styling classes should be applied through the component
      expect(screen.getByText('translated_headerParameters')).toBeInTheDocument();
    });

    it('should apply correct spacing and margins', () => {
      renderWithMantine(
        <TestWrapper>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      // Test that elements are rendered with proper structure
      expect(screen.getByTestId('select-type')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty parameters array', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { 
              type: 'manually',
              parameters: []
            }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.getByText('translated_headerParameters')).toBeInTheDocument();
      expect(screen.getByTestId('deca-button')).toBeInTheDocument();
      expect(screen.queryByTestId('text-input-name')).not.toBeInTheDocument();
    });

    it('should handle undefined type gracefully', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { 
              type: undefined,
              parameters: []
            }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.getByTestId('select-type')).toBeInTheDocument();
      expect(screen.queryByTestId('textarea-json')).not.toBeInTheDocument();
      expect(screen.queryByText('translated_headerParameters')).not.toBeInTheDocument();
    });

    it('should handle missing prefix name gracefully', () => {
      renderWithMantine(
        <TestWrapper>
          <Parameters prefixName="" />
        </TestWrapper>
      );

      expect(screen.getByTestId('select-type')).toBeInTheDocument();
    });

    it('should handle null parameters array', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { 
              type: 'manually',
              parameters: null
            }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.getByText('translated_headerParameters')).toBeInTheDocument();
      expect(screen.getByTestId('deca-button')).toBeInTheDocument();
    });
  });

  describe('Form Integration', () => {
    it('should integrate properly with react-hook-form', () => {
      const TestComponent = () => {
        const methods = useForm({
          defaultValues: {
            webhook: {
              sendHeaders: { 
                type: 'json',
                json: 'test json content',
                parameters: []
              }
            }
          }
        });

        return (
          <FormProvider {...methods}>
            <Parameters prefixName={mockPrefixName} />
            <div data-testid="form-state">
              {JSON.stringify(methods.watch('webhook.sendHeaders'))}
            </div>
          </FormProvider>
        );
      };

      renderWithMantine(<TestComponent />);

      const formState = screen.getByTestId('form-state');
      expect(formState).toHaveTextContent('test json content');
      expect(formState).toHaveTextContent('"type":"json"');
    });

    it('should update form state when switching between modes', () => {
      const TestComponent = () => {
        const methods = useForm({
          defaultValues: {
            webhook: {
              sendHeaders: { 
                type: 'manually',
                parameters: []
              }
            }
          }
        });

        return (
          <FormProvider {...methods}>
            <Parameters prefixName={mockPrefixName} />
            <button 
              type="button"
              onClick={() => methods.setValue('webhook.sendHeaders.type', 'json')}
              data-testid="switch-to-json"
            >
              Switch to JSON
            </button>
            <div data-testid="current-type">
              {methods.watch('webhook.sendHeaders.type')}
            </div>
          </FormProvider>
        );
      };

      renderWithMantine(<TestComponent />);

      const currentType = screen.getByTestId('current-type');
      expect(currentType).toHaveTextContent('manually');

      const switchButton = screen.getByTestId('switch-to-json');
      fireEvent.click(switchButton);

      expect(currentType).toHaveTextContent('json');
    });
  });

  describe('Accessibility', () => {
    it('should have proper labels and structure', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { 
              type: 'manually',
              parameters: [{ name: 'test', value: 'test' }]
            }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      expect(screen.getByTestId('select-label-type')).toBeInTheDocument();
      expect(screen.getByTestId('text-input-label-name')).toBeInTheDocument();
      expect(screen.getByTestId('text-input-label-value')).toBeInTheDocument();
    });

    it('should have accessible button text', () => {
      renderWithMantine(
        <TestWrapper defaultValues={{
          webhook: {
            sendHeaders: { type: 'manually', parameters: [] }
          }
        }}>
          <Parameters prefixName={mockPrefixName} />
        </TestWrapper>
      );

      const addButton = screen.getByTestId('deca-button');
      expect(addButton).toHaveTextContent('translated_addParameter');
    });
  });
}); 