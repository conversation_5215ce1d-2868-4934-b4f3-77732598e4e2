import { renderHook } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { useCustomActionStyles } from './useCustomActionStyles';

// Mock the constants
vi.mock('@/constants/workspace', () => ({
  CUSTOM_ACTION_COLORS: ['red', 'violet', 'green', 'yellow', 'purple', 'grey'],
}));

// Mock the rem function
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    rem: (value: number) => `${value}px`,
  };
});

// Mock the createStyles function - it returns CSS class name strings, not objects
vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn(() => () => {
    const mockClasses = {
      desc: 'mocked-desc-class',
      color: 'mocked-color-class',
      icon: 'mocked-icon-class',
      selectedIcon: 'mocked-selected-icon-class',
      headerParameters: 'mocked-header-parameters-class',
      // Dynamic color classes
      red: 'mocked-red-class',
      violet: 'mocked-violet-class',
      green: 'mocked-green-class',
      yellow: 'mocked-yellow-class',
      purple: 'mocked-purple-class',
      grey: 'mocked-grey-class',
      // Selected color classes
      selected_red: 'mocked-selected-red-class',
      selected_violet: 'mocked-selected-violet-class',
      selected_green: 'mocked-selected-green-class',
      selected_yellow: 'mocked-selected-yellow-class',
      selected_purple: 'mocked-selected-purple-class',
      selected_grey: 'mocked-selected-grey-class',
    };
    
    return {
      classes: mockClasses,
      cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
    };
  }),
  MantineEmotionProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Test wrapper with Mantine providers
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider>
    <MantineEmotionProvider>
      {children}
    </MantineEmotionProvider>
  </MantineProvider>
);

describe('useCustomActionStyles Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Hook Functionality', () => {
    it('should return classes and cx function', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      expect(result.current.classes).toBeDefined();
      expect(result.current.cx).toBeDefined();
      expect(typeof result.current.cx).toBe('function');
    });

    it('should return consistent results across multiple calls', () => {
      const { result: result1 } = renderHook(() => useCustomActionStyles(), { wrapper });
      const { result: result2 } = renderHook(() => useCustomActionStyles(), { wrapper });

      expect(result1.current.classes).toEqual(result2.current.classes);
    });
  });

  describe('Generated Style Classes', () => {
    it('should generate desc class as string', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const { desc } = result.current.classes;
      expect(desc).toBeDefined();
      expect(typeof desc).toBe('string');
      expect(desc).toBe('mocked-desc-class');
    });

    it('should generate color class as string', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const { color } = result.current.classes;
      expect(color).toBeDefined();
      expect(typeof color).toBe('string');
      expect(color).toBe('mocked-color-class');
    });

    it('should generate icon class as string', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const { icon } = result.current.classes;
      expect(icon).toBeDefined();
      expect(typeof icon).toBe('string');
      expect(icon).toBe('mocked-icon-class');
    });

    it('should generate selectedIcon class as string', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const { selectedIcon } = result.current.classes;
      expect(selectedIcon).toBeDefined();
      expect(typeof selectedIcon).toBe('string');
      expect(selectedIcon).toBe('mocked-selected-icon-class');
    });

    it('should generate headerParameters class as string', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const { headerParameters } = result.current.classes;
      expect(headerParameters).toBeDefined();
      expect(typeof headerParameters).toBe('string');
      expect(headerParameters).toBe('mocked-header-parameters-class');
    });
  });

  describe('Dynamic Color Classes', () => {
    it('should generate classes for all custom action colors', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const colors = ['red', 'violet', 'green', 'yellow', 'purple', 'grey'];
      
      colors.forEach(color => {
        expect((result.current.classes as any)[color]).toBeDefined();
        expect(typeof (result.current.classes as any)[color]).toBe('string');
      });
    });

    it('should generate selected classes for all custom action colors', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const colors = ['red', 'violet', 'green', 'yellow', 'purple', 'grey'];
      
      colors.forEach(color => {
        const selectedClass = `selected_${color}`;
        expect((result.current.classes as any)[selectedClass]).toBeDefined();
        expect(typeof (result.current.classes as any)[selectedClass]).toBe('string');
      });
    });

    it('should generate mocked color classes', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Test that mocked classes are returned
      expect((result.current.classes as any).red).toBe('mocked-red-class');
      expect((result.current.classes as any).green).toBe('mocked-green-class');
      expect((result.current.classes as any).violet).toBe('mocked-violet-class');
    });

    it('should generate mocked selected classes', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      expect((result.current.classes as any).selected_red).toBe('mocked-selected-red-class');
      expect((result.current.classes as any).selected_green).toBe('mocked-selected-green-class');
    });
  });

  describe('cx Function', () => {
    it('should combine class names correctly', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const combined = result.current.cx('class1', 'class2', 'class3');
      expect(combined).toBe('class1 class2 class3');
    });

    it('should filter out falsy values', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const combined = result.current.cx('class1', null, 'class2', undefined, false, 'class3');
      expect(combined).toBe('class1 class2 class3');
    });

    it('should handle empty inputs', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const combined = result.current.cx();
      expect(combined).toBe('');
    });

    it('should handle conditional classes', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const isSelected = true;
      const combined = result.current.cx(
        'base-class',
        isSelected && 'selected-class',
        !isSelected && 'unselected-class'
      );
      expect(combined).toBe('base-class selected-class');
    });
  });

  describe('Style Object Structure', () => {
    it('should include all required base classes', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const requiredClasses = ['desc', 'color', 'headerParameters', 'icon', 'selectedIcon'];
      
      requiredClasses.forEach(className => {
        expect(result.current.classes[className]).toBeDefined();
      });
    });

    it('should include color-specific classes', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const colors = ['red', 'violet', 'green', 'yellow', 'purple', 'grey'];
      
      colors.forEach(color => {
        expect(result.current.classes[color]).toBeDefined();
        expect(result.current.classes[`selected_${color}`]).toBeDefined();
      });
    });

    it('should generate style classes for responsive design', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Check that style classes are generated (actual CSS properties are handled by Mantine)
      expect(result.current.classes.desc).toBeDefined();
      expect(typeof result.current.classes.desc).toBe('string');
    });
  });

  describe('Theme Integration', () => {
    it('should generate theme-integrated classes', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Verify that classes are generated (actual theme integration is handled by Mantine)
      expect(result.current.classes.desc).toBe('mocked-desc-class');
      expect(result.current.classes.selectedIcon).toBe('mocked-selected-icon-class');
    });

    it('should generate classes for nested styling', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Classes should be generated for components that have nested svg styling
      expect(result.current.classes.desc).toBeDefined();
      expect(result.current.classes.icon).toBeDefined();
    });

    it('should generate classes for interactive states', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Classes should be generated for components with hover states
      expect(result.current.classes.icon).toBeDefined();
      expect(typeof result.current.classes.icon).toBe('string');
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing theme colors gracefully', () => {
      // Mock createStyles with minimal theme
      const mockCreateStyles = vi.fn((stylesFn) => () => {
        const minimalTheme = { colors: {} };
        try {
          const styles = stylesFn(minimalTheme);
          return {
            classes: styles,
            cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
          };
        } catch (error) {
          return {
            classes: {},
            cx: vi.fn((...args) => args.filter(Boolean).join(' ')),
          };
        }
      });

      vi.doMock('@mantine/emotion', () => ({
        createStyles: mockCreateStyles,
        MantineEmotionProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
      }));

      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Should not throw error even with missing theme colors
      expect(result.current.classes).toBeDefined();
      expect(result.current.cx).toBeDefined();
    });

    it('should handle empty CUSTOM_ACTION_COLORS array', () => {
      vi.doMock('@/constants/workspace', () => ({
        CUSTOM_ACTION_COLORS: [],
      }));

      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Should still return base classes
      expect(result.current.classes.desc).toBeDefined();
      expect(result.current.classes.icon).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should be stable across re-renders', () => {
      const { result, rerender } = renderHook(() => useCustomActionStyles(), { wrapper });

      const firstResult = result.current;
      rerender();
      const secondResult = result.current;

      // For mocked implementation, just verify structure consistency
      expect(firstResult.classes).toEqual(secondResult.classes);
      expect(typeof firstResult.cx).toBe(typeof secondResult.cx);
    });

    it('should not recreate styles unnecessarily', () => {
      // Since we're using a mocked implementation, just verify the hook works consistently
      const { result: result1 } = renderHook(() => useCustomActionStyles(), { wrapper });
      const { result: result2 } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Both instances should return the same structure
      expect(result1.current.classes).toEqual(result2.current.classes);
      expect(typeof result1.current.cx).toBe(typeof result2.current.cx);
    });
  });

  describe('CSS Class Generation', () => {
    it('should generate CSS class names as strings', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Check that classes are generated as strings (actual CSS handled by Mantine)
      expect(typeof result.current.classes.desc).toBe('string');
      expect(typeof result.current.classes.color).toBe('string');
      expect(typeof result.current.classes.icon).toBe('string');
    });

    it('should generate classes with consistent naming', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Classes should be generated with consistent mocked names
      expect(result.current.classes.desc).toBe('mocked-desc-class');
      expect(result.current.classes.color).toBe('mocked-color-class');
      expect(result.current.classes.icon).toBe('mocked-icon-class');
    });
  });

  describe('Class Name Generation', () => {
    it('should generate predictable class names for colors', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      const colors = ['red', 'violet', 'green', 'yellow', 'purple', 'grey'];
      
      colors.forEach(color => {
        // Should have both base and selected variants
        expect((result.current.classes as any)[color]).toBeDefined();
        expect((result.current.classes as any)[`selected_${color}`]).toBeDefined();
      });
    });

    it('should handle dynamic class generation', () => {
      const { result } = renderHook(() => useCustomActionStyles(), { wrapper });

      // Test that dynamic classes are generated
      expect((result.current.classes as any).red).toBeDefined();
      expect((result.current.classes as any).green).toBeDefined();
      expect(typeof (result.current.classes as any).red).toBe('string');
      expect(typeof (result.current.classes as any).green).toBe('string');
    });
  });
}); 