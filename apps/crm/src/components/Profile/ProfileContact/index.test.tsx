import * as ProfileContextModule from '@/contexts/ProfileContext';
import type { ProfileContextType } from '@/contexts/ProfileContext';
import type { TableContextType } from '@/contexts/WorkspaceContext';
import * as WorkspaceContextModule from '@/contexts/WorkspaceContext';
import type { BoxCard, IForm, ITag, ObjectColumn, WSObject } from '@/models';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { screen } from '@testing-library/react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import type { KeyedMutator } from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ProfileContact from './index';

// Mock the workspace context hook
const mockWorkspaceData: Partial<TableContextType> = {
  object: {
    id: 'test-object',
    name: {
      singular: 'Test Object',
      plural: 'Test Objects',
    },
    hasAvatar: true,
    fields: [
      {
        id: 'name',
        type: FieldTypes.SINGLE_LINE_TEXT,
        name: 'Name',
        header: 'Name',
        options: { isPrimary: true },
      },
      {
        id: 'avatar',
        type: FieldTypes.IMAGE,
        name: 'Avatar',
        header: 'Avatar',
        options: { isAvatar: true },
      },
    ] as unknown as ObjectColumn[],
    messaging: {
      email: true,
      sms: true,
      line: true,
    },
    userconfig: {
      viewId: 'default-view',
    },
  } as WSObject,
  profileRecord: {
    id: '12345',
    name: 'John Doe',
    avatar: 'https://example.com/avatar.jpg',
  },
};

const mockForm: IForm = {
  name: 'Test Form',
  phone: '1234567890',
  email: '<EMAIL>',
};

const mockProfileData = {
  profile: mockWorkspaceData.profileRecord,
  mutateProfile: vi.fn() as unknown as KeyedMutator<any>,
  tags: [] as ITag[],
  handleAddTag: vi.fn() as unknown as (name: string) => Promise<void>,
  handleRemoveTag: vi.fn() as unknown as (id: string) => Promise<void>,
  handleSaveRecord: vi.fn() as unknown as (value: any, colId: any) => Promise<void>,
  handleUpdateRecord: vi.fn() as unknown as (value: any) => Promise<void>,
  handleDeleteRecord: vi.fn() as unknown as () => Promise<void>,
  handleDuplicateRecord: vi.fn() as unknown as () => Promise<void>,
  handleMoveRecord: vi.fn() as unknown as (value: any) => Promise<void>,
  handleMergeRecord: vi.fn() as unknown as (value: any) => Promise<void>,
  handleImportRecord: vi.fn() as unknown as (value: any) => Promise<void>,
  handleExportRecord: vi.fn() as unknown as () => Promise<void>,
  handleBulkUpdateRecord: vi.fn() as unknown as (value: any) => Promise<void>,
  handleBulkDeleteRecord: vi.fn() as unknown as () => Promise<void>,
  handleBulkDuplicateRecord: vi.fn() as unknown as () => Promise<void>,
  dragging: false,
  onSetDragging: vi.fn() as unknown as (value: boolean) => void,
  sortItems: [] as BoxCard[],
  handleDragEnd: vi.fn() as unknown as () => void,
  handleDragStart: vi.fn() as unknown as () => void,
  handleDragOver: vi.fn() as unknown as (e: React.DragEvent) => void,
  handleDrop: vi.fn() as unknown as (e: React.DragEvent) => void,
  handleDragEnter: vi.fn() as unknown as (e: React.DragEvent) => void,
  handleDragLeave: vi.fn() as unknown as (e: React.DragEvent) => void,
  handleChangeSize: vi.fn() as unknown as (value: any) => void,
  forms: [mockForm],
  getForm: vi.fn() as unknown as (id: string) => Promise<IForm>,
  editFields: vi.fn() as unknown as (value: any) => void,
  handleSortFields: vi.fn() as unknown as (value: any) => void,
  handleDeleteField: vi.fn() as unknown as (id: string) => Promise<void>,
  handleAddField: vi.fn() as unknown as (value: any) => Promise<void>,
  handleEditField: vi.fn() as unknown as (value: any) => Promise<void>,
  onEditField: vi.fn() as unknown as (value: any) => void,
  resetEditFields: vi.fn() as unknown as () => void,
  customObjectFields: [],
  setCustomObjectFields: vi.fn() as unknown as (value: any) => void,
  closeProfile: vi.fn() as unknown as () => void,
};

const renderComponent = (
  workspaceData: Partial<TableContextType> = mockWorkspaceData,
  profileData = mockProfileData
) => {
  const defaultView = {
    id: 'default-view',
    name: 'Default View',
    type: 'table',
    icon: 'table',
    fields:
      workspaceData.object?.fields?.map((field) => ({
        fieldMetaId: field.id,
        isVisible: true,
      })) || [],
    createdBy: new Date(),
    rowHeight: 'md',
    fieldOrder: workspaceData.object?.fields?.map((f) => f.id) || [],
  };

  const mockContext = {
    ...mockWorkspaceData,
    ...workspaceData,
    loading: false,
    recordsLoading: false,
    viewLoading: false,
    data: [],
    columns: workspaceData.object?.fields || [],
    handleAddColumn: vi.fn(),
    validationErrors: {},
    setValidationErrors: vi.fn(),
    onSavingCell: vi.fn(),
    handleAddRow: vi.fn(),
    handleUpdateColumn: vi.fn(),
    opened: false,
    open: vi.fn(),
    close: vi.fn(),
    views: [defaultView],
    activeView: defaultView,
    handleViewChange: vi.fn(),
    handleActionRow: vi.fn(),
    openProfile: vi.fn(),
    currRecordIndex: -1,
    onSaveData: vi.fn(),
    refetchObject: vi.fn(),
    rowSelection: {},
    setRowSelection: vi.fn(),
    selectedRowDetails: [],
    mutateRecord: vi.fn() as unknown as KeyedMutator<any>,
    tags: [],
    mutateTags: vi.fn() as unknown as KeyedMutator<any>,
    size: 1,
    setSize: vi.fn(),
    totalRecords: 0,
    mutateObject: vi.fn() as unknown as KeyedMutator<any>,
    viewGroups: [],
    handleApplyManageView: vi.fn(),
  };

  vi.spyOn(WorkspaceContextModule, 'useWorkspaceContext').mockImplementation(
    () => mockContext as unknown as TableContextType
  );
  vi.spyOn(ProfileContextModule, 'useProfileContext').mockImplementation(
    () => profileData as unknown as ProfileContextType
  );

  return renderWithMantine(
    <MemoryRouter initialEntries={['/workspace/123']}>
      <Routes>
        <Route path='/workspace/:wsId' element={<ProfileContact />} />
      </Routes>
    </MemoryRouter>
  );
};

describe('ProfileContact', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component correctly', () => {
    renderComponent();
    expect(screen.getByTestId('profile-contact-test-id')).toBeInTheDocument();
  });

  it('displays the primary field value and ID correctly', () => {
    renderComponent();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('ID: 12345')).toBeInTheDocument();
  });

  it('displays avatar when hasAvatar is true and avatar URL exists', () => {
    renderComponent();
    const avatar = screen.getByAltText('avatar') as HTMLImageElement;
    expect(avatar).toBeInTheDocument();
    expect(avatar.src).toBe('https://example.com/avatar.jpg');
  });

  it('displays user icon when hasAvatar is true but no avatar URL exists', () => {
    const modifiedWorkspaceData = {
      ...mockWorkspaceData,
      profileRecord: {
        ...mockWorkspaceData.profileRecord!,
        avatar: '',
      },
    };
    renderComponent(modifiedWorkspaceData);
    expect(screen.getByTestId('profile-contact-test-id')).toBeInTheDocument();
  });

  it('renders message buttons correctly when messaging options are enabled', () => {
    renderComponent();
    expect(screen.getByTestId('action-email')).toBeInTheDocument();
    expect(screen.getByTestId('action-sms')).toBeInTheDocument();
    expect(screen.getByTestId('action-line')).toBeInTheDocument();
  });

  it('disables line button when channel ID is not available', () => {
    const modifiedWorkspaceData = {
      ...mockWorkspaceData,
      object: {
        ...mockWorkspaceData.object!,
        fields: mockWorkspaceData.object!.fields?.map((field) => ({
          ...field,
          options: { ...field.options },
        })) as unknown as ObjectColumn[],
      } as WSObject,
    };
    renderComponent(modifiedWorkspaceData);
    const lineButton = screen.getByTestId('action-line');
    expect(lineButton).toHaveAttribute('disabled');
  });

  it('does not render message buttons when messaging options are disabled', () => {
    const modifiedWorkspaceData = {
      ...mockWorkspaceData,
      object: {
        ...mockWorkspaceData.object!,
        messaging: {
          email: false,
          sms: false,
          line: false,
        },
      } as WSObject,
    };
    renderComponent(modifiedWorkspaceData);
    expect(screen.queryByTestId('action-email')).not.toBeInTheDocument();
    expect(screen.queryByTestId('action-sms')).not.toBeInTheDocument();
    expect(screen.queryByTestId('action-line')).not.toBeInTheDocument();
  });
});
