import { getFieldFormattedValue } from '@/utils/workspace';
import { Group, Paper, Stack, Text, UnstyledButton, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import Mention from '@tiptap/extension-mention';
import { ReactRenderer } from '@tiptap/react';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import tippy from 'tippy.js';
import { RenderProfileTypes } from '../Profile/ObjectFields/RenderProfileTypes';
import type { ProfileField } from './EditorConfig';

export interface MentionSuggestionProps {
  items: ProfileField[];
  command: (item: ProfileField) => void;
  isTemplate?: boolean;
}

// Function to create the mention configuration
export const createMentionConfiguration = (
  objectFields?: any[],
  profileFields?: ProfileField[],
  isTemplate?: boolean
) => {
  return Mention.configure({
    HTMLAttributes: {
      class: 'mention',
    },
    renderText({ node }) {
      const displayText = node.attrs.label || node.attrs.value || node.attrs.id;
      return `@${displayText}`;
    },
    renderHTML({ node }) {
      const displayText = node.attrs.label || node.attrs.value || node.attrs.id;
      const fieldId = node.attrs.id;
      const isDeleted = fieldId && !objectFields?.some((field) => field.id === fieldId);

      // Find the actual field to get its value for non-deleted fields
      let fieldValue = '';
      if (!isDeleted && fieldId) {
        const field = objectFields?.find((field) => field.id === fieldId);
        if (field) {
          // Use the actual field value for the tooltip
          fieldValue = field.value || '';
        }
      }

      const attributes = {
        'data-type': 'mention',
        'data-id': node.attrs.id || '',
        'data-field-id': node.attrs.id || '',
        'data-label': node.attrs.label || '',
        'data-value': fieldValue || node.attrs.value || displayText,
        'data-field-type': node.attrs.fieldType || '',
        'data-field-options': node.attrs.fieldOptions || '{}',
        'data-is-template': node.attrs.isTemplate || 'false',
        'data-is-deleted': isDeleted ? 'true' : 'false',
        class: `mention ${isDeleted ? 'mention-deleted' : ''}`,
      };

      if (isDeleted) {
        // For deleted fields, render with a warning structure
        return ['span', attributes, ['span', {}, `@${displayText}`]];
      }
      // For normal fields, render simple mention
      return ['span', attributes, `@${displayText}`];
    },
    suggestion: {
      char: '@',
      items: ({ query }) => {
        if (!profileFields || profileFields.length === 0) return [];

        // Map the profile fields to include explicit label and value properties
        const enhancedFields = profileFields
          .filter((field) => (!isTemplate ? field.mapValue : true))
          .map((field) => ({
            ...field,
            label: isTemplate ? field.name : field.mapValue,
            value: isTemplate ? field.name : field.mapValue,
          }));

        return enhancedFields.filter((field) => {
          if (!field.id) return false;

          const nameMatch = field.name.toLowerCase().includes(query.toLowerCase());

          const valueMatch = field.value
            ? field.value.toString().toLowerCase().includes(query.toLowerCase())
            : false;

          return nameMatch || valueMatch;
        });
      },
      render: () => {
        let component: any;
        let popup: any = null;

        return {
          onStart: (props) => {
            try {
              component = new ReactRenderer(MentionSuggestion, {
                props: {
                  ...props,
                  isTemplate,
                },
                editor: props.editor,
              });

              // Create a fixed client rect function that returns a DOMRect
              const getReferenceClientRect = () => {
                const rect = props.clientRect?.();
                return rect ? rect : new DOMRect(0, 0, 0, 0);
              };

              popup = tippy('body', {
                getReferenceClientRect,
                appendTo: document.body,
                content: component.element,
                showOnCreate: true,
                interactive: true,
                trigger: 'manual',
                placement: 'bottom-start',
                animation: 'scale',
                arrow: false,
                offset: [0, 10],
                zIndex: 9999,
                popperOptions: {
                  strategy: 'fixed',
                  modifiers: [
                    {
                      name: 'flip',
                      options: {
                        fallbackPlacements: ['top-start', 'bottom-end', 'top-end'],
                      },
                    },
                  ],
                },
              })[0];
            } catch (error) {
              console.error('Error creating tippy instance:', error);
            }
          },
          onUpdate(props) {
            try {
              if (component) {
                component.updateProps(props);
              }

              // Create a fixed client rect function that returns a DOMRect
              const getReferenceClientRect = () => {
                const rect = props.clientRect?.();
                return rect ? rect : new DOMRect(0, 0, 0, 0);
              };

              // Only update popup if it exists
              if (popup) {
                popup.setProps({
                  getReferenceClientRect,
                });
              }
            } catch (error) {
              console.error('Error updating tippy instance:', error);
            }
          },
          onKeyDown(props) {
            if (props.event.key === 'Escape') {
              // Hide the popup if it exists
              if (popup) {
                popup.hide();
              }
              return true;
            }

            return component?.ref?.onKeyDown(props) || false;
          },
          onExit() {
            try {
              // Clean up the tippy instance
              if (popup) {
                popup.destroy();
              }

              // Clean up the component
              if (component) {
                component.destroy();
              }
            } catch (error) {
              console.error('Error destroying tippy instance:', error);
            }
          },
          command: ({ editor, range, props }) => {
            // Use field name for templates, otherwise use formatted display value
            const displayValue = props.isTemplate
              ? props.name
              : props.displayValue || props.label || props.value;
            // Get the actual field value from the profile fields if available
            const profileField = profileFields?.find((field) => field.id === props.id);
            const actualMapValue = profileField?.mapValue || '';

            // Create the mention node with all attributes needed
            const mentionNode = {
              type: 'mention',
              attrs: {
                id: props.id,
                label: displayValue,
                value: displayValue,
                mapValue: actualMapValue,
                // Store the original field type and options for potential future use
                fieldType: props.type,
                fieldOptions: JSON.stringify(props.options || {}),
                isTemplate: props.isTemplate || false,
              },
            };

            // Insert the mention node
            editor.chain().focus().deleteRange(range).insertContent(mentionNode).run();
          },
        };
      },
    },
  });
};

const useStyles = createStyles((theme) => ({
  dropdown: {
    backgroundColor: 'white',
    borderRadius: 4,
    width: rem(280),
    maxHeight: rem(300),
    overflow: 'auto',
    zIndex: 9999,
    overflowX: 'hidden',
  },
  item: {
    padding: rem(10),
    borderRadius: 4,
    transition: 'background-color 150ms ease',
    '&:hover': {
      backgroundColor: theme.colors.gray[0],
    },
    cursor: 'pointer',
    width: '100%',
  },
  selectedItem: {
    backgroundColor: theme.colors.blue[0],
    '&:hover': {
      backgroundColor: theme.colors.blue[1],
    },
  },
  value: {
    fontSize: rem(14),
    color: theme.colors.blue[7],
    marginTop: 2,
    fontWeight: 500,
    wordBreak: 'break-word',
    overflowWrap: 'break-word',
  },
  nameText: {
    color: theme.colors.green[7],
    wordBreak: 'break-word',
    overflowWrap: 'break-word',
    maxWidth: rem(200),
  },
  mentionDeleted: {
    color: theme.colors.red[6],
    borderBottom: `1px dotted ${theme.colors.red[6]}`,
    backgroundColor: theme.colors.red[0],
    position: 'relative',
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: theme.colors.red[1],
    },
  },
  mentionErrorIcon: {
    color: theme.colors.red[6],
    marginLeft: '2px',
  },
  errorTooltip: {
    maxWidth: rem(250),
  },
}));

export const MentionSuggestion = forwardRef((props: MentionSuggestionProps, ref) => {
  const { classes, cx } = useStyles();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const isTemplate = props.isTemplate || false;

  // Setup item refs array when items change
  useEffect(() => {
    itemRefs.current = itemRefs.current.slice(0, props.items.length);
  }, [props.items]);

  // Scroll to selected item when selection changes
  useEffect(() => {
    const scrollToSelectedItem = () => {
      if (!dropdownRef.current) return;

      const selectedItemElement = itemRefs.current[selectedIndex];
      if (!selectedItemElement) return;

      const container = dropdownRef.current;
      const itemTop = selectedItemElement.offsetTop;
      const itemHeight = selectedItemElement.offsetHeight;
      // If item is above visible area
      if (itemTop < container.scrollTop) {
        container.scrollTop = itemTop;
      }
      // If item is below visible area
      else if (itemTop + itemHeight > container.scrollTop + container.clientHeight) {
        container.scrollTop = itemTop + itemHeight - container.clientHeight;
      }
    };
    scrollToSelectedItem();
  }, [selectedIndex]);

  const selectItem = (index: number) => {
    const item = props.items[index];
    if (item) {
      // For templates, use the field name. Otherwise use formatted value
      const displayValue = isTemplate
        ? item.name
        : getFieldFormattedValue({ type: item.type, value: item.mapValue, options: item.options });

      const commandItem = {
        id: item.id,
        name: item.name,
        label: displayValue,
        mapValue: item.mapValue,
        type: item.type,
        options: item.options,
        isTemplate: isTemplate,
      };
      props.command(commandItem);
    }
  };

  const upHandler = () => {
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length);
  };

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % props.items.length);
  };

  const enterHandler = () => {
    selectItem(selectedIndex);
  };

  useEffect(() => {
    setSelectedIndex(0);
  }, [props.items]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }: { event: KeyboardEvent }) => {
      if (event.key === 'ArrowUp') {
        upHandler();
        return true;
      }

      if (event.key === 'ArrowDown') {
        downHandler();
        return true;
      }

      if (event.key === 'Enter') {
        enterHandler();
        return true;
      }

      return false;
    },
  }));

  if (props.items.length === 0) {
    return null;
  }

  return (
    <Paper className={classes.dropdown} withBorder p={0} ref={dropdownRef}>
      <Stack gap={0}>
        {props.items.map((item, index) => (
          <UnstyledButton
            key={item.id}
            className={cx(classes.item, index === selectedIndex && classes.selectedItem)}
            onClick={() => selectItem(index)}
            ref={(el) => (itemRefs.current[index] = el)}
          >
            <Stack gap={4} w='100%'>
              <Group justify='space-between' wrap='nowrap'>
                <Text fw={500} className={classes.nameText}>
                  {item.name}
                </Text>
              </Group>
              {!isTemplate ? (
                <Text className={classes.value}>
                  <RenderProfileTypes field={{ ...item, icon: '', header: '' }} />
                </Text>
              ) : null}
            </Stack>
          </UnstyledButton>
        ))}
      </Stack>
    </Paper>
  );
});

MentionSuggestion.displayName = 'MentionSuggestion';

export default MentionSuggestion;
