import { describe, expect, it, vi } from 'vitest';

// Store the extension configuration passed to Extension.create
let extensionConfig: any;

// Mock the @tiptap/core Extension class
vi.mock('@tiptap/core', () => ({
  Extension: {
    create: vi.fn((config) => {
      extensionConfig = config; // Store the configuration
      return {
        name: config.name,
        ...config,
      };
    }),
  },
}));

// Mock TextStyle extension
vi.mock('@tiptap/extension-text-style', () => ({
  default: 'TextStyle',
}));

// Import after mocking
const { FontSize, TextStyleExtended } = await import('./FontSize');

describe('FontSize Extension', () => {
  it('should have correct extension name', () => {
    expect(FontSize.name).toBe('fontSize');
  });

  it('should provide default options', () => {
    const options = extensionConfig.addOptions();
    expect(options).toEqual({
      types: ['textStyle'],
    });
  });

  it('should define global attributes for fontSize', () => {
    const globalAttributes = extensionConfig.addGlobalAttributes.call({
      options: { types: ['textStyle'] },
    });

    expect(globalAttributes).toHaveLength(1);
    expect(globalAttributes[0].types).toEqual(['textStyle']);
    expect(globalAttributes[0].attributes).toHaveProperty('fontSize');
  });

  it('should configure fontSize attribute correctly', () => {
    const globalAttributes = extensionConfig.addGlobalAttributes.call({
      options: { types: ['textStyle'] },
    });
    const fontSizeAttribute = globalAttributes[0].attributes.fontSize;

    expect(fontSizeAttribute.default).toBeNull();
    expect(typeof fontSizeAttribute.parseHTML).toBe('function');
    expect(typeof fontSizeAttribute.renderHTML).toBe('function');
  });

  it('should parse fontSize from HTML correctly', () => {
    const globalAttributes = extensionConfig.addGlobalAttributes.call({
      options: { types: ['textStyle'] },
    });
    const parseHTML = globalAttributes[0].attributes.fontSize.parseHTML;

    // Mock element with font-size in pixels
    const elementWithPx = {
      style: { fontSize: '16px' },
    };
    expect(parseHTML(elementWithPx)).toBe('16');

    // Mock element with font-size without px
    const elementWithoutPx = {
      style: { fontSize: '24' },
    };
    expect(parseHTML(elementWithoutPx)).toBe('24');

    // Mock element with no font-size
    const elementWithoutFontSize = {
      style: {},
    };
    expect(parseHTML(elementWithoutFontSize)).toBeUndefined();
  });

  it('should render HTML with correct font-size style', () => {
    const globalAttributes = extensionConfig.addGlobalAttributes.call({
      options: { types: ['textStyle'] },
    });
    const renderHTML = globalAttributes[0].attributes.fontSize.renderHTML;

    // Test with fontSize value
    const attributesWithFontSize = { fontSize: '18' };
    expect(renderHTML(attributesWithFontSize)).toEqual({
      style: 'font-size: 18px',
    });

    // Test without fontSize value
    const attributesWithoutFontSize = { fontSize: null };
    expect(renderHTML(attributesWithoutFontSize)).toEqual({});

    // Test with undefined fontSize
    const attributesWithUndefinedFontSize = {};
    expect(renderHTML(attributesWithUndefinedFontSize)).toEqual({});
  });

  it('should define setFontSize and unsetFontSize commands', () => {
    const commands = extensionConfig.addCommands();

    expect(commands).toHaveProperty('setFontSize');
    expect(commands).toHaveProperty('unsetFontSize');
    expect(typeof commands.setFontSize).toBe('function');
    expect(typeof commands.unsetFontSize).toBe('function');
  });

  it('should execute setFontSize command correctly', () => {
    const commands = extensionConfig.addCommands();
    const mockChain = {
      setMark: vi.fn().mockReturnThis(),
      run: vi.fn().mockReturnValue(true),
    };
    const mockEditor = {
      chain: vi.fn().mockReturnValue(mockChain),
    };

    const setFontSizeCommand = commands.setFontSize('20');
    const result = setFontSizeCommand(mockEditor);

    expect(mockEditor.chain).toHaveBeenCalled();
    expect(mockChain.setMark).toHaveBeenCalledWith('textStyle', { fontSize: '20' });
    expect(mockChain.run).toHaveBeenCalled();
    expect(result).toBe(true);
  });

  it('should execute unsetFontSize command correctly', () => {
    const commands = extensionConfig.addCommands();
    const mockChain = {
      setMark: vi.fn().mockReturnThis(),
      removeEmptyTextStyle: vi.fn().mockReturnThis(),
      run: vi.fn().mockReturnValue(true),
    };
    const mockEditor = {
      chain: vi.fn().mockReturnValue(mockChain),
    };

    const unsetFontSizeCommand = commands.unsetFontSize();
    const result = unsetFontSizeCommand(mockEditor);

    expect(mockEditor.chain).toHaveBeenCalled();
    expect(mockChain.setMark).toHaveBeenCalledWith('textStyle', { fontSize: null });
    expect(mockChain.removeEmptyTextStyle).toHaveBeenCalled();
    expect(mockChain.run).toHaveBeenCalled();
    expect(result).toBe(true);
  });

  it('should handle various fontSize values in setFontSize', () => {
    const commands = extensionConfig.addCommands();
    const mockChain = {
      setMark: vi.fn().mockReturnThis(),
      run: vi.fn().mockReturnValue(true),
    };
    const mockEditor = {
      chain: vi.fn().mockReturnValue(mockChain),
    };

    // Test with string number
    commands.setFontSize('16')(mockEditor);
    expect(mockChain.setMark).toHaveBeenCalledWith('textStyle', { fontSize: '16' });

    // Reset mock
    mockChain.setMark.mockClear();

    // Test with larger size
    commands.setFontSize('32')(mockEditor);
    expect(mockChain.setMark).toHaveBeenCalledWith('textStyle', { fontSize: '32' });
  });

  it('should handle edge cases in parseHTML', () => {
    const globalAttributes = extensionConfig.addGlobalAttributes.call({
      options: { types: ['textStyle'] },
    });
    const parseHTML = globalAttributes[0].attributes.fontSize.parseHTML;

    // Test with empty fontSize
    const elementWithEmptyFontSize = {
      style: { fontSize: '' },
    };
    expect(parseHTML(elementWithEmptyFontSize)).toBe('');

    // Test with fontSize containing 'px' multiple times
    const elementWithComplexFontSize = {
      style: { fontSize: '16px' },
    };
    expect(parseHTML(elementWithComplexFontSize)).toBe('16');

    // Test with element having no style property
    const elementWithoutStyle = {};
    try {
      parseHTML(elementWithoutStyle);
    } catch (error) {
      expect(error).toBeInstanceOf(TypeError);
    }
  });

  it('should handle edge cases in renderHTML', () => {
    const globalAttributes = extensionConfig.addGlobalAttributes.call({
      options: { types: ['textStyle'] },
    });
    const renderHTML = globalAttributes[0].attributes.fontSize.renderHTML;

    // Test with zero fontSize
    const attributesWithZeroFontSize = { fontSize: '0' };
    expect(renderHTML(attributesWithZeroFontSize)).toEqual({
      style: 'font-size: 0px',
    });

    // Test with empty string fontSize
    const attributesWithEmptyFontSize = { fontSize: '' };
    expect(renderHTML(attributesWithEmptyFontSize)).toEqual({});

    // Test with undefined fontSize
    const attributesWithUndefinedFontSize = { fontSize: undefined };
    expect(renderHTML(attributesWithUndefinedFontSize)).toEqual({});

    // Test with false fontSize
    const attributesWithFalseFontSize = { fontSize: false };
    expect(renderHTML(attributesWithFalseFontSize)).toEqual({});
  });

  it('should work with different types configuration', () => {
    // Test that the extension respects the types option
    const globalAttributes = extensionConfig.addGlobalAttributes.call({
      options: { types: ['textStyle'] },
    });
    expect(globalAttributes[0].types).toEqual(['textStyle']);
  });

  it('should integrate properly with TextStyle extension', () => {
    // Test that TextStyleExtended is correctly exported
    expect(TextStyleExtended).toBe('TextStyle');
  });
});

describe('TextStyleExtended', () => {
  it('should export TextStyle extension', () => {
    expect(TextStyleExtended).toBe('TextStyle');
  });
});

describe('FontSize Integration', () => {
  it('should work with typical editor workflow', () => {
    const globalAttributes = extensionConfig.addGlobalAttributes.call({
      options: { types: ['textStyle'] },
    });

    // Test parsing and rendering HTML
    const parseHTML = globalAttributes[0].attributes.fontSize.parseHTML;
    const element = { style: { fontSize: '14px' } };
    expect(parseHTML(element)).toBe('14');

    // Test rendering HTML
    const renderHTML = globalAttributes[0].attributes.fontSize.renderHTML;
    const attributes = { fontSize: '14' };
    expect(renderHTML(attributes)).toEqual({ style: 'font-size: 14px' });
  });

  it('should handle HTML parsing and rendering correctly', () => {
    const globalAttributes = extensionConfig.addGlobalAttributes.call({
      options: { types: ['textStyle'] },
    });

    const parseHTML = globalAttributes[0].attributes.fontSize.parseHTML;
    const renderHTML = globalAttributes[0].attributes.fontSize.renderHTML;

    // Test round trip: parse then render
    const element = { style: { fontSize: '18px' } };
    const parsedValue = parseHTML(element);
    expect(parsedValue).toBe('18');

    const attributes = { fontSize: parsedValue };
    const renderedHTML = renderHTML(attributes);
    expect(renderedHTML).toEqual({ style: 'font-size: 18px' });
  });
});
