import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import '@testing-library/jest-dom';
import MailList from './MailList';
import type { ReceiverType } from './EditorConfig';
import type { UserProfile } from './MailList';
import type { MessageType } from '@/constants/workspace';

// Mock @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

// Mock scrollIntoView for JSDOM compatibility
Object.defineProperty(Element.prototype, 'scrollIntoView', {
  value: vi.fn(),
  writable: true,
});

describe('MailList Component', () => {
  const mockSetReceiver = vi.fn();
  const mockSetProfileList = vi.fn();
  const mockSetReceiverError = vi.fn();

  const defaultReceiver: ReceiverType = {
    to: [],
    cc: [],
    bcc: [],
  };

  const sampleProfileList: UserProfile[] = [
    {
      label: 'John Doe',
      value: '<EMAIL>',
      avatar: 'https://example.com/john.jpg',
      group: 'Team A',
    },
    {
      label: 'Jane Smith',
      value: '<EMAIL>',
      avatar: 'https://example.com/jane.jpg',
      group: 'Team A',
    },
    {
      label: 'Bob Wilson',
      value: '<EMAIL>',
      group: 'Team B',
    },
    {
      label: 'Alice Johnson',
      value: '<EMAIL>',
      group: 'Team B',
    },
  ];

  const defaultProps = {
    type: 'to' as const,
    receiver: defaultReceiver,
    setReceiver: mockSetReceiver,
    profileList: sampleProfileList,
    setProfileList: mockSetProfileList,
    editorType: 'email' as MessageType,
    receiverError: false,
    setReceiverError: mockSetReceiverError,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render MailList component', () => {
      renderWithMantine(<MailList {...defaultProps} />);
      
      // Should render the pills input
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should render with different recipient types', () => {
      const types = ['to', 'cc', 'bcc'] as const;
      
      types.forEach(type => {
        const { unmount } = renderWithMantine(
          <MailList {...defaultProps} type={type} />
        );
        expect(screen.getByRole('textbox')).toBeInTheDocument();
        unmount();
      });
    });

    it('should show error message for required "to" field', () => {
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          type="to" 
          receiverError={true}
        />
      );
      
      expect(screen.getByText('receiverRequired')).toBeInTheDocument();
    });

    it('should not show error for cc/bcc fields even with receiverError true', () => {
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          type="cc" 
          receiverError={true}
        />
      );
      
      expect(screen.queryByText('receiverRequired')).not.toBeInTheDocument();
    });
  });

  describe('Profile Selection', () => {
    it('should display profile options when combobox is focused', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MailList {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });
    });

    it('should group profiles correctly', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MailList {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      await waitFor(() => {
        // Should show profiles grouped by team
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        expect(screen.getByText('Bob Wilson')).toBeInTheDocument();
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      });
    });

    it('should filter profiles based on search input', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MailList {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'John');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      });
    });

    it('should filter profiles by email value', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MailList {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'john@');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      });
    });

    it('should select profile when clicked', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MailList {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });
      
      await user.click(screen.getByText('John Doe'));
      
      expect(mockSetReceiver).toHaveBeenCalledWith({
        ...defaultReceiver,
        to: ['<EMAIL>'],
      });
      expect(mockSetReceiverError).toHaveBeenCalledWith(false);
    });

    it('should add multiple profiles for email editor type', async () => {
      const user = userEvent.setup();
      const receiverWithExisting = {
        ...defaultReceiver,
        to: ['<EMAIL>'],
      };
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          receiver={receiverWithExisting}
          editorType="email"
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });
      
      await user.click(screen.getByText('John Doe'));
      
      expect(mockSetReceiver).toHaveBeenCalledWith({
        ...receiverWithExisting,
        to: ['<EMAIL>', '<EMAIL>'],
      });
    });

    it('should replace selection for line editor type', async () => {
      const user = userEvent.setup();
      const receiverWithExisting = {
        ...defaultReceiver,
        to: ['<EMAIL>'],
      };
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          receiver={receiverWithExisting}
          editorType="line"
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });
      
      await user.click(screen.getByText('John Doe'));
      
      expect(mockSetReceiver).toHaveBeenCalledWith({
        ...receiverWithExisting,
        to: ['<EMAIL>'],
      });
    });
  });

  describe('Selected Recipients Display', () => {
    it('should display selected recipients as pills', () => {
      const receiverWithSelected = {
        ...defaultReceiver,
        to: ['<EMAIL>', '<EMAIL>'],
      };
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          receiver={receiverWithSelected}
        />
      );
      
      // Should show selected recipients as text (not form values)
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('should remove recipient when remove button is clicked', async () => {
      const user = userEvent.setup();
      const receiverWithSelected = {
        ...defaultReceiver,
        to: ['<EMAIL>', '<EMAIL>'],
      };
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          receiver={receiverWithSelected}
        />
      );
      
      // Find and click remove button for john
      const removeButtons = screen.getAllByRole('button');
      const johnRemoveButton = removeButtons.find(btn => 
        btn.getAttribute('aria-label')?.includes('Remove') ||
        btn.textContent === '×'
      );
      
      if (johnRemoveButton) {
        await user.click(johnRemoveButton);
        
        expect(mockSetReceiver).toHaveBeenCalledWith({
          ...receiverWithSelected,
          to: ['<EMAIL>'],
        });
      }
    });

    it('should handle backspace to remove last recipient', async () => {
      const user = userEvent.setup();
      const receiverWithSelected = {
        ...defaultReceiver,
        to: ['<EMAIL>', '<EMAIL>'],
      };
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          receiver={receiverWithSelected}
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.keyboard('{Backspace}');
      
      expect(mockSetReceiver).toHaveBeenCalledWith({
        ...receiverWithSelected,
        to: ['<EMAIL>'],
      });
    });

    it('should not remove on backspace when there is text in input', async () => {
      const user = userEvent.setup();
      const receiverWithSelected = {
        ...defaultReceiver,
        to: ['<EMAIL>'],
      };
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          receiver={receiverWithSelected}
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'test');
      await user.keyboard('{Backspace}');
      
      // Should not remove recipient, just remove character
      expect(mockSetReceiver).not.toHaveBeenCalled();
    });
  });

  describe('Email Creation for Email Editor', () => {
    it('should show "add email" option for new email addresses in email editor', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          editorType="email"
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, '<EMAIL>');
      
      await waitFor(() => {
        expect(screen.getByText('+ add <EMAIL>')).toBeInTheDocument();
      });
    });

    it('should not show "add email" option for existing emails', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          editorType="email"
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, '<EMAIL>');
      
      await waitFor(() => {
        expect(screen.queryByText('+ add <EMAIL>')).not.toBeInTheDocument();
      });
    });

    it('should not show "add email" option for non-email editors', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          editorType="sms"
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, '<EMAIL>');
      
      await waitFor(() => {
        expect(screen.queryByText('+ add <EMAIL>')).not.toBeInTheDocument();
      });
    });

    it('should create new email when "add email" option is selected', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          editorType="email"
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, '<EMAIL>');
      
      await waitFor(() => {
        expect(screen.getByText('+ add <EMAIL>')).toBeInTheDocument();
      });
      
      await user.click(screen.getByText('+ add <EMAIL>'));
      
      expect(mockSetProfileList).toHaveBeenCalledWith([
        ...sampleProfileList,
        { label: '<EMAIL>', value: '<EMAIL>' },
      ]);
      
      expect(mockSetReceiver).toHaveBeenCalledWith({
        ...defaultReceiver,
        to: ['<EMAIL>'],
      });
    });
  });

  describe('Profile Filtering', () => {
    it('should exclude already selected profiles from options', async () => {
      const user = userEvent.setup();
      const receiverWithSelected = {
        ...defaultReceiver,
        to: ['<EMAIL>'],
      };
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          receiver={receiverWithSelected}
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      await waitFor(() => {
        // John Doe should be visible as a selected pill but not in the dropdown options
        const johnDoeElements = screen.getAllByText('John Doe');
        expect(johnDoeElements).toHaveLength(1); // Only in the selected pills, not in options
        
        // Jane Smith should be available in options
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });
    });

    it('should show all profiles when search is empty', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MailList {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        expect(screen.getByText('Bob Wilson')).toBeInTheDocument();
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      });
    });

    it('should handle case-insensitive search', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MailList {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'JOHN');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty profile list', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          profileList={[]}
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      // Should not show any options
      await waitFor(() => {
        expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
      });
    });

    it('should handle profiles without group', () => {
      const profilesWithoutGroup: UserProfile[] = [
        {
          label: 'Test User',
          value: '<EMAIL>',
        },
      ];
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          profileList={profilesWithoutGroup}
        />
      );
      
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should handle profiles with empty values', () => {
      const profilesWithEmptyValues: UserProfile[] = [
        {
          label: 'Empty User',
          value: '',
        },
      ];
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          profileList={profilesWithEmptyValues}
        />
      );
      
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should handle missing profiles in receiver', () => {
      const receiverWithUnknown = {
        ...defaultReceiver,
        to: ['<EMAIL>'],
      };
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          receiver={receiverWithUnknown}
        />
      );
      
      // Should not crash, may not display the unknown recipient
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should handle very long email addresses', async () => {
      const user = userEvent.setup();
      const longEmail = '<EMAIL>';
      
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          editorType="email"
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, longEmail);
      
      await waitFor(() => {
        expect(screen.getByText(`+ add ${longEmail}`)).toBeInTheDocument();
      });
    });

    it('should handle special characters in search', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MailList {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, '@#$%');
      
      // Should not crash
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('Different Editor Types', () => {
    it('should work correctly with SMS editor type', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          editorType="sms"
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });
      
      await user.click(screen.getByText('John Doe'));
      
      expect(mockSetReceiver).toHaveBeenCalledWith({
        ...defaultReceiver,
        to: ['<EMAIL>'],
      });
    });

    it('should work correctly with line editor type', async () => {
      const user = userEvent.setup();
      renderWithMantine(
        <MailList 
          {...defaultProps} 
          editorType="line"
        />
      );
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });
      
      await user.click(screen.getByText('John Doe'));
      
      expect(mockSetReceiver).toHaveBeenCalledWith({
        ...defaultReceiver,
        to: ['<EMAIL>'],
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      renderWithMantine(<MailList {...defaultProps} />);
      
      const textbox = screen.getByRole('textbox');
      expect(textbox).toBeInTheDocument();
      expect(textbox).toHaveAttribute('aria-haspopup', 'listbox');
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithMantine(<MailList {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      
      // Should be able to navigate with keyboard
      await user.keyboard('{ArrowDown}');
      await user.keyboard('{Enter}');
      
      // Should have selected the first option
      expect(mockSetReceiver).toHaveBeenCalled();
    });
  });
}); 