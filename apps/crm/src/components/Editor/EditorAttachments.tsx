import { useAttachments } from '@/hooks';
import { FileEnhanced } from '@/models/file';
import { AssetAPI, AttachmentAPI, UploadAPI } from '@/services/api';
import {
  ActionIcon,
  Box,
  type BoxProps,
  Collapse,
  Flex,
  Menu,
  Notification,
  Progress,
  Text,
  rem,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { RichTextEditor } from '@mantine/tiptap';
import { DecaProgressBar } from '@resola-ai/ui';
import { DecaFileUpload, type DecaFileUploadProps } from '@resola-ai/ui/components';
import {
  IconCheck,
  IconFileAnalytics,
  IconInfoCircle,
  IconPaperclip,
  IconX,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import omitBy from 'lodash/omitBy';
import {
  type ReactElement,
  type ReactNode,
  createContext,
  isValidElement,
  useCallback,
  useContext,
  useId,
  useMemo,
  useState,
} from 'react';
import { useParams } from 'react-router-dom';
import { fileEquals, getFileKey } from '../Profile/Files/utils';

// Validation constants
const FILE_UNIT = 1024 * 1024;
const MAX_FILE_SIZE = 10 * FILE_UNIT; // 10 MB
const MAX_ATTACHMENTS = 10;
const MAX_TOTAL_SIZE = 25; // 25 MB

// Utility functions to reduce duplication
const formatSizeInMB = (bytes: number): string => (bytes / (1024 * 1024)).toFixed(2);

const getProgressColor = (percentage: number, thresholds = { high: 80, medium: 60 }): string =>
  percentage > thresholds.high ? 'red' : percentage > thresholds.medium ? 'yellow' : 'blue';

const calculateAttachmentStats = (
  uploadingFiles: FileEnhanced[],
  selectedAttachmentObjects: any[]
) => {
  const uploadingFilesSize = uploadingFiles.reduce((total, file) => total + file.size, 0);
  const selectedFilesSize = selectedAttachmentObjects.reduce(
    (total, attachment) => total + attachment.size,
    0
  );

  const totalSize = uploadingFilesSize + selectedFilesSize;
  const fileCount = uploadingFiles.length + selectedAttachmentObjects.length;
  const sizePercentage = (totalSize / (MAX_TOTAL_SIZE * FILE_UNIT)) * 100;

  return {
    totalSize,
    fileCount,
    sizePercentage,
    formattedSize: formatSizeInMB(totalSize),
  };
};

// Standard container for attachment UI sections
const AttachmentContainer = ({
  children,
  ...props
}: BoxProps & { children: ReactElement | ReactElement[] }) => (
  <Box
    sx={(theme) => ({
      backgroundColor: theme.colors.gray[0],
      borderRadius: theme.radius.sm,
      border: `1px solid ${theme.colors.gray[3]}`,
    })}
    {...props}
  >
    {children}
  </Box>
);

// Check if adding an attachment would exceed limits
const wouldExceedLimits = (
  attachment: { size: number },
  uploadingFiles: FileEnhanced[],
  selectedAttachmentObjects: any[]
): { exceeds: boolean; reason?: 'size' | 'format' | 'count' | 'totalSize' } => {
  // Check file size limit
  if (attachment.size > MAX_FILE_SIZE) {
    return { exceeds: true, reason: 'size' };
  }

  // Check total count limit
  const potentialFileCount = uploadingFiles.length + selectedAttachmentObjects.length + 1;
  if (potentialFileCount > MAX_ATTACHMENTS) {
    return { exceeds: true, reason: 'count' };
  }

  // Check total size limit
  const uploadingFilesSize = uploadingFiles.reduce((total, file) => total + file.size, 0);
  const existingAttachmentsSize = selectedAttachmentObjects.reduce(
    (total, att) => total + att.size,
    0
  );
  const potentialTotalSize = uploadingFilesSize + existingAttachmentsSize + attachment.size;

  if (potentialTotalSize > MAX_TOTAL_SIZE * FILE_UNIT) {
    return { exceeds: true, reason: 'totalSize' };
  }

  return { exceeds: false };
};

// Reusable components
const ValidationErrorNotification = ({
  error,
  onClose,
}: {
  error: FileValidationError | null;
  onClose: () => void;
}) => {
  const { t } = useTranslate('workspace');

  if (!error) return null;

  return (
    <Notification
      icon={<IconInfoCircle size={20} />}
      color='red'
      title={t('attachmentErr')}
      onClose={onClose}
      withCloseButton
      mb={rem(10)}
    >
      {error.message}
    </Notification>
  );
};

const AttachmentStatsDisplay = ({
  stats,
}: {
  stats: ReturnType<typeof calculateAttachmentStats>;
}) => {
  const { t } = useTranslate('workspace');

  return (
    <Flex align='center' gap={rem(8)}>
      <Text size='xs' c='dimmed'>
        {`${stats.fileCount}/${MAX_ATTACHMENTS} ${t('files')}`}
      </Text>
      <Text size='xs' c='dimmed'>
        {stats.formattedSize}/{MAX_TOTAL_SIZE} MB
      </Text>
    </Flex>
  );
};

const AttachmentProgressBar = ({ percentage }: { percentage: number }) => (
  <Progress value={percentage} size='xs' color={getProgressColor(percentage)} />
);

// Component to display an uploading file with progress
const UploadingFileItem = ({
  file,
  progress,
  onRetry,
}: {
  file: FileEnhanced;
  progress: number;
  onDelete: () => void;
  onRetry?: () => void;
}) => (
  <Box key={getFileKey(file)}>
    <DecaFileUpload.Item
      mimeType={file.type}
      size={file.size}
      status={
        file.status || <DecaProgressBar value={progress} styles={{ root: { width: '100%' } }} />
      }
      title={file.name}
      onRetry={
        file.status === undefined ||
        (typeof file.status !== 'string' && isValidElement(file.status))
          ? undefined
          : onRetry
      }
    />
    {/* Add file size indicator under each uploading file */}
    {!file.status && (
      <Flex justify='flex-end' align='center' gap={8} mt={4} mb={8} pr={8}>
        <Text size='xs' c='dimmed'>
          {formatSizeInMB(file.size)} MB
        </Text>
        <Box w={100}>
          <Progress
            value={(file.size / MAX_FILE_SIZE) * 100}
            size='xs'
            color={getProgressColor((file.size / MAX_FILE_SIZE) * 100, {
              high: 0.8 * 100,
              medium: 0.6 * 100,
            })}
          />
        </Box>
      </Flex>
    )}
  </Box>
);

// Component to render a file item in menu
const AttachmentMenuItem = ({
  attachment,
  isSelected,
  exceedsLimits,
  onClick,
}: {
  attachment: any;
  isSelected: boolean;
  exceedsLimits: boolean;
  onClick: () => void;
}) => (
  <Menu.Item
    key={attachment.id}
    leftSection={
      isSelected ? (
        <IconCheck size={14} />
      ) : exceedsLimits ? (
        <IconInfoCircle size={14} color='red' />
      ) : (
        <Box w={14} />
      )
    }
    onClick={onClick}
    disabled={exceedsLimits}
    sx={
      exceedsLimits
        ? (theme) => ({
            backgroundColor: theme.colors.red[0],
            '&:hover': { backgroundColor: theme.colors.red[0] },
          })
        : undefined
    }
  >
    <DecaFileUpload.Item
      mimeType={attachment.mimeType}
      size={attachment.size}
      title={attachment.title}
    />
  </Menu.Item>
);

interface FileValidationError {
  message: string;
  file: File;
  type: 'size' | 'format' | 'count' | 'totalSize';
}

// Create a type for our attachment context
interface AttachmentContextType {
  showFileUpload: boolean;
  toggleFileUpload: () => void;
  uploadingFiles: FileEnhanced[];
  uploadProgress: Record<string, number>;
  selectedAttachments: string[];
  attachments: any[];
  isLoadingAttachments: boolean;
  selectedAttachmentObjects: any[];
  handleDrop: DecaFileUploadProps['onDrop'];
  handleRetryUploadingFile: (file: FileEnhanced) => void;
  handleDeleteUploadingFile: (file: FileEnhanced) => void;
  toggleAttachmentSelection: (attachmentId: string) => void;
  removeSelectedAttachment: (attachmentId: string) => void;
  contextId: string;
  validationError: FileValidationError | null;
  clearValidationError: () => void;
  attachmentStats: ReturnType<typeof calculateAttachmentStats>;
}

// Create the context
const AttachmentContext = createContext<AttachmentContextType | null>(null);

// Custom hook to use the attachment context
export const useAttachmentContext = () => {
  const context = useContext(AttachmentContext);
  if (!context) {
    throw new Error('useAttachmentContext must be used within an AttachmentProvider');
  }
  return context;
};

interface AttachmentProviderProps {
  children: ReactNode;
}

// Provider component to manage attachment state
export const AttachmentProvider = ({ children }: AttachmentProviderProps) => {
  const contextId = useId();
  const { wsId: workspaceId = '', id: objectId = '', recordId } = useParams();
  const { t } = useTranslate('workspace');

  // Attachment states
  const [showFileUpload, { toggle: toggleFileUpload }] = useDisclosure(false);
  const [uploadingFiles, setUploadingFiles] = useState<FileEnhanced[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [selectedAttachments, setSelectedAttachments] = useState<string[]>([]);
  const [validationError, setValidationError] = useState<FileValidationError | null>(null);

  // Fetch available attachments
  const {
    data: attachments = [],
    isLoading: isLoadingAttachments,
    mutate: refetchAttachments,
  } = useAttachments({
    objectId,
    recordId: recordId || '',
    workspaceId,
    sort: { createdAt: 'desc' },
  });

  // Get selected attachment objects
  const selectedAttachmentObjects =
    selectedAttachments.length > 0
      ? attachments.filter((att) => selectedAttachments.includes(att.id))
      : [];

  // Calculate attachment statistics
  const attachmentStats = useMemo(
    () => calculateAttachmentStats(uploadingFiles, selectedAttachmentObjects),
    [uploadingFiles, selectedAttachmentObjects]
  );

  // Clear validation error
  const clearValidationError = useCallback(() => {
    setValidationError(null);
  }, []);

  // Validate file
  const validateFile = useCallback(
    (file: File): FileValidationError | null => {
      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        return {
          message: t('maxFileSize10MB'),
          file,
          type: 'size',
        };
      }

      // Check number of attachments
      const totalAttachments = uploadingFiles.length + selectedAttachmentObjects.length;
      if (totalAttachments >= MAX_ATTACHMENTS) {
        return {
          message: t('maxAttachmentNumberReach'),
          file,
          type: 'count',
        };
      }

      // Use the utility function to check if adding this file would exceed limits
      const { exceeds, reason } = wouldExceedLimits(
        file,
        uploadingFiles,
        selectedAttachmentObjects
      );

      if (exceeds && reason === 'totalSize') {
        return {
          message: t('totalSizeReach'),
          file,
          type: 'totalSize',
        };
      }

      return null;
    },
    [uploadingFiles, selectedAttachmentObjects]
  );

  // Handle file upload cleanup
  const cleanupFileUpload = useCallback((file: FileEnhanced) => {
    setUploadingFiles((prevState) => prevState.filter((f) => !fileEquals(f, file)));
    setUploadProgress((prevState) => omitBy(prevState, (_, key) => key === getFileKey(file)));
  }, []);

  // File upload handler
  const handleDrop: DecaFileUploadProps['onDrop'] = ([file]) => {
    const fileExists = uploadingFiles.find((f) => fileEquals(f, file));

    if (fileExists && fileExists.status === (file as FileEnhanced).status) return;

    // Validate file first
    const error = validateFile(file);
    if (error) {
      setValidationError(error);
      return;
    }

    const controller = new AbortController();
    const uploadingFile = new FileEnhanced([file], file.name, {
      controller,
      lastModified: file.lastModified,
      type: file.type,
    });

    setUploadingFiles((prevState) => [uploadingFile, ...prevState]);

    // Handle the file upload asynchronously without returning the promise
    (async () => {
      try {
        const { file: asset, uploadUrl } = await AssetAPI.save(file, 'attachment');

        await UploadAPI.update({
          file,
          url: uploadUrl,
          config: {
            signal: controller.signal,
            onUploadProgress: (event) => {
              setUploadProgress((prevState) => ({
                ...prevState,
                [getFileKey(file)]: event.lengthComputable ? event.progress! * 100 : 0,
              }));
            },
          },
        });

        const attachment = await AttachmentAPI.save(
          { workspaceId, objectId, recordId: recordId || '' },
          {
            assetId: asset.externalId,
            mimeType: asset.mimeType,
            path: asset.path,
            size: asset.size,
            title: file.name,
            url: asset.url,
            assetType: 'attachment',
          }
        );

        cleanupFileUpload(uploadingFile);

        // Add the newly created attachment to selected attachments
        if (attachment?.id) {
          setSelectedAttachments((prev) => [...prev, attachment.id]);
          // Refresh the attachments list to include the newly uploaded file
          await refetchAttachments();
        }
      } catch (err) {
        setUploadingFiles((prevState) =>
          prevState.map((file) =>
            file === uploadingFile
              ? new FileEnhanced([uploadingFile], uploadingFile.name, {
                  controller: uploadingFile.controller,
                  lastModified: uploadingFile.lastModified,
                  status: 'fileStatus.uploadFailed',
                  type: uploadingFile.type,
                })
              : file
          )
        );
      } finally {
        setUploadProgress((prevState) => omitBy(prevState, (_, key) => key === getFileKey(file)));
      }
    })();
  };

  // Handle file upload retry
  const handleRetryUploadingFile = (file: FileEnhanced) => {
    cleanupFileUpload(file);
    handleDrop([
      new FileEnhanced([file], file.name, {
        controller: file.controller,
        lastModified: file.lastModified,
        type: file.type,
      }),
    ]);
  };

  // Handle deleting uploading file
  const handleDeleteUploadingFile = (file: FileEnhanced) => {
    file.controller?.abort();
    cleanupFileUpload(file);
  };

  // Handle attachment selection toggle
  const toggleAttachmentSelection = (attachmentId: string) => {
    // If the attachment is already selected, we're removing it, so no validation needed
    if (selectedAttachments.includes(attachmentId)) {
      setSelectedAttachments((prev) => prev.filter((id) => id !== attachmentId));
      return;
    }

    // If we're adding a new attachment, check if we've reached the maximum count
    if (selectedAttachments.length >= MAX_ATTACHMENTS) {
      setValidationError({
        message: t('maxAttachmentNumberReach'),
        file: new File([], ''), // Empty file for type compatibility
        type: 'count',
      });
      return;
    }

    // Check the total size
    const attachment = attachments.find((att) => att.id === attachmentId);
    if (attachment) {
      const { exceeds, reason } = wouldExceedLimits(
        attachment,
        uploadingFiles,
        selectedAttachmentObjects
      );

      if (exceeds) {
        let errorMessage = t('cannotAddThisAttachment');

        if (reason === 'size') {
          errorMessage = t('maxFileSize10MB');
        } else if (reason === 'count') {
          errorMessage = t('maxAttachmentNumberReach');
        } else if (reason === 'totalSize') {
          errorMessage = t('totalSizeReach');
        }
        setValidationError({
          message: errorMessage,
          file: new File([], ''), // Empty file for type compatibility
          type: reason || 'totalSize',
        });
        return;
      }
    }

    // All checks passed, add the attachment
    setSelectedAttachments((prev) => [...prev, attachmentId]);
  };

  // Remove a single attachment from selection
  const removeSelectedAttachment = (attachmentId: string) => {
    setSelectedAttachments((prev) => prev.filter((id) => id !== attachmentId));
  };

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo<AttachmentContextType>(
    () => ({
      showFileUpload,
      toggleFileUpload,
      uploadingFiles,
      uploadProgress,
      selectedAttachments,
      attachments,
      isLoadingAttachments,
      selectedAttachmentObjects,
      handleDrop,
      handleRetryUploadingFile,
      handleDeleteUploadingFile,
      toggleAttachmentSelection,
      removeSelectedAttachment,
      contextId,
      validationError,
      clearValidationError,
      attachmentStats,
    }),
    [
      showFileUpload,
      toggleFileUpload,
      uploadingFiles,
      uploadProgress,
      selectedAttachments,
      attachments,
      isLoadingAttachments,
      selectedAttachmentObjects,
      handleDrop,
      handleRetryUploadingFile,
      handleDeleteUploadingFile,
      toggleAttachmentSelection,
      removeSelectedAttachment,
      contextId,
      validationError,
      clearValidationError,
      attachmentStats,
    ]
  );

  return <AttachmentContext.Provider value={value}>{children}</AttachmentContext.Provider>;
};

// Main component for attachments
export const EditorAttachments = () => {
  const {
    showFileUpload,
    uploadingFiles,
    uploadProgress,
    selectedAttachmentObjects,
    handleDrop,
    handleRetryUploadingFile,
    handleDeleteUploadingFile,
    validationError,
    clearValidationError,
    attachmentStats,
  } = useAttachmentContext();
  const { t } = useTranslate('workspace');

  return (
    <>
      {/* Display Selected Attachments */}
      {selectedAttachmentObjects.length > 0 && (
        <AttachmentContainer mb={rem(12)} p={rem(8)}>
          <Flex justify='flex-end' align='center' mb={rem(8)}>
            <AttachmentStatsDisplay stats={attachmentStats} />
          </Flex>

          <Flex direction='column' gap={rem(8)}>
            {selectedAttachmentObjects.map((attachment) => (
              <DecaFileUpload.Item
                key={attachment.id}
                mimeType={attachment.mimeType}
                size={attachment.size}
                title={attachment.title}
              />
            ))}
          </Flex>
        </AttachmentContainer>
      )}

      {/* File Upload Area */}
      <Collapse in={showFileUpload}>
        <AttachmentContainer
          p={rem(16)}
          mb={rem(16)}
          mt={selectedAttachmentObjects.length > 0 ? 0 : rem(12)}
        >
          <Flex direction='column' gap={rem(16)}>
            <ValidationErrorNotification error={validationError} onClose={clearValidationError} />
            <Box>
              <Text size='xs' c='dimmed' mb={rem(8)}>
                {t('emailAttachmentRule')}
              </Text>
              <DecaFileUpload onDrop={handleDrop} />
            </Box>
            {uploadingFiles.length > 0 && (
              <Flex direction='column' gap={rem(16)}>
                {uploadingFiles.map((file) => (
                  <UploadingFileItem
                    key={getFileKey(file)}
                    file={file}
                    progress={uploadProgress[getFileKey(file)]}
                    onDelete={() => handleDeleteUploadingFile(file)}
                    onRetry={
                      file.status === undefined ||
                      (typeof file.status !== 'string' && isValidElement(file.status))
                        ? undefined
                        : () => handleRetryUploadingFile(file)
                    }
                  />
                ))}
              </Flex>
            )}
          </Flex>
        </AttachmentContainer>
      </Collapse>
    </>
  );
};

// Component for attachment toolbar controls
export const EditorAttachmentControls = () => {
  const {
    toggleFileUpload,
    attachments,
    isLoadingAttachments,
    selectedAttachments,
    toggleAttachmentSelection,
    uploadingFiles,
    selectedAttachmentObjects,
    validationError,
    clearValidationError,
    attachmentStats,
  } = useAttachmentContext();
  const { t } = useTranslate('workspace');

  const CompactValidationError = ({ error }: { error: FileValidationError | null }) => {
    if (!error) return null;
    return (
      <Box px='md' py='xs' bg='red.0'>
        <Flex align='center' gap={8}>
          <IconInfoCircle size={16} color='red' />
          <Text size='xs' c='red'>
            {error.message}
          </Text>
          <ActionIcon size='xs' color='red' onClick={clearValidationError} ml='auto'>
            <IconX size={14} />
          </ActionIcon>
        </Flex>
      </Box>
    );
  };

  return (
    <RichTextEditor.ControlsGroup>
      <Menu position='bottom-end' shadow='md' width={380}>
        <Menu.Target>
          <RichTextEditor.Control title={t('selectAttachment')}>
            <IconFileAnalytics size={16} />
          </RichTextEditor.Control>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Label>{t('attachments')}</Menu.Label>

          {/* Stats display */}
          <Box px='md' py='xs'>
            <Flex justify='space-between' align='center' mb={4}>
              <Text size='xs'>
                {attachmentStats.fileCount}/{MAX_ATTACHMENTS} {t('filesSelected')}
              </Text>
              <Text size='xs'>
                {attachmentStats.formattedSize}/{MAX_TOTAL_SIZE} MB
              </Text>
            </Flex>
            <AttachmentProgressBar percentage={attachmentStats.sizePercentage} />
          </Box>

          {/* Validation error display */}
          <CompactValidationError error={validationError} />

          {isLoadingAttachments ? (
            <Menu.Item disabled>{t('loadingAttachments')}</Menu.Item>
          ) : attachments.length === 0 ? (
            <Menu.Item disabled>{t('noAttachment')}</Menu.Item>
          ) : (
            <>
              {attachments.map((attachment) => {
                // Check if this attachment would exceed the limits if added
                const isSelected = selectedAttachments.includes(attachment.id);
                const { exceeds } = isSelected
                  ? { exceeds: false }
                  : wouldExceedLimits(attachment, uploadingFiles, selectedAttachmentObjects);

                return (
                  <AttachmentMenuItem
                    key={attachment.id}
                    attachment={attachment}
                    isSelected={isSelected}
                    exceedsLimits={exceeds}
                    onClick={() => toggleAttachmentSelection(attachment.id)}
                  />
                );
              })}
            </>
          )}
        </Menu.Dropdown>
      </Menu>
      <RichTextEditor.Control onClick={toggleFileUpload} title={t('uploadFile')}>
        <IconPaperclip size={16} />
      </RichTextEditor.Control>
    </RichTextEditor.ControlsGroup>
  );
};
