import { describe, expect, it } from 'vitest';
import { getLanguageFromQueryString } from './i18n';

describe('i18n utils', () => {
  describe('getLanguageFromQueryString', () => {
    it('should return the language from query string when present', () => {
      const queryString = '?lng=en';
      expect(getLanguageFromQueryString(queryString)).toBe('en');
    });

    it('should return the language from query string with multiple parameters', () => {
      const queryString = '?page=1&lng=ja&sort=asc';
      expect(getLanguageFromQueryString(queryString)).toBe('ja');
    });

    it('should return null when language is not present in query string', () => {
      const queryString = '?page=1&sort=asc';
      expect(getLanguageFromQueryString(queryString)).toBeNull();
    });

    it('should return null for empty query string', () => {
      const queryString = '';
      expect(getLanguageFromQueryString(queryString)).toBeNull();
    });

    it('should handle query string without question mark', () => {
      const queryString = 'lng=fr';
      expect(getLanguageFromQueryString(queryString)).toBe('fr');
    });

    // Additional edge cases for better coverage
    it('should handle language parameter with empty value', () => {
      const queryString = '?lng=';
      expect(getLanguageFromQueryString(queryString)).toBe('');
    });

    it('should handle language parameter at the end', () => {
      const queryString = '?page=1&sort=desc&lng=es';
      expect(getLanguageFromQueryString(queryString)).toBe('es');
    });

    it('should handle language parameter at the beginning', () => {
      const queryString = '?lng=de&page=1&sort=asc';
      expect(getLanguageFromQueryString(queryString)).toBe('de');
    });

    it('should handle special characters in language code', () => {
      const queryString = '?lng=zh-CN';
      expect(getLanguageFromQueryString(queryString)).toBe('zh-CN');
    });

    it('should handle underscores in language code', () => {
      const queryString = '?lng=en_US';
      expect(getLanguageFromQueryString(queryString)).toBe('en_US');
    });

    it('should handle numeric values in language parameter', () => {
      const queryString = '?lng=123';
      expect(getLanguageFromQueryString(queryString)).toBe('123');
    });

    it('should handle URL encoded query string', () => {
      const queryString = '?lng=fr%2DCA';
      expect(getLanguageFromQueryString(queryString)).toBe('fr-CA');
    });

    it('should handle multiple lng parameters and return the first one', () => {
      const queryString = '?lng=en&other=value&lng=fr';
      expect(getLanguageFromQueryString(queryString)).toBe('en');
    });

    it('should handle malformed query string with missing equals', () => {
      const queryString = '?lng&other=value';
      expect(getLanguageFromQueryString(queryString)).toBe('');
    });

    it('should handle case sensitivity - lng vs LNG', () => {
      const queryString = '?LNG=uppercase';
      expect(getLanguageFromQueryString(queryString)).toBeNull();
    });

    it('should handle very long language codes', () => {
      const longLang = 'a'.repeat(100);
      const queryString = `?lng=${longLang}`;
      expect(getLanguageFromQueryString(queryString)).toBe(longLang);
    });

    it('should handle query string with only question mark', () => {
      const queryString = '?';
      expect(getLanguageFromQueryString(queryString)).toBeNull();
    });

    it('should handle hash fragment in URL', () => {
      const queryString = '?lng=en#section';
      expect(getLanguageFromQueryString(queryString)).toBe('en#section');
    });

    it('should handle spaces in language code', () => {
      const queryString = '?lng=en US';
      expect(getLanguageFromQueryString(queryString)).toBe('en US');
    });

    it('should handle plus signs in language code', () => {
      const queryString = '?lng=en+US';
      expect(getLanguageFromQueryString(queryString)).toBe('en US'); // URLSearchParams converts + to space
    });
  });
});
