import { handleBarService } from '@/services/handleBars';
import { tolgee } from '@/tolgee';
import dayjs from 'dayjs';

const getDate = (date: string) => {
  return dayjs(new Date(date)).format('YYYY年MM月DD日');
};

const getTime = (date: string) => {
  return dayjs(new Date(date)).format('HH:mm');
};

const getDateEn = (date: string) => {
  return dayjs(new Date(date)).format('DD/MM/YYYY');
};

handleBarService.registerHelper('t', (key: string) => tolgee.t(key));

handleBarService.registerHelper('formatTime', (date) => {
  try {
    const params = new URLSearchParams(document.location?.search || '');
    const lang = params.get('lang');
    if (lang === 'en') {
      return `${getDateEn(date)} ${getTime(date)}`;
    }
    return `${getDate(date)} ${getTime(date)}`;
  } catch (error) {
    // Fallback to default format if location is not available
    return `${getDate(date)} ${getTime(date)}`;
  }
});

handleBarService.registerHelper('dotToString', (text: string) => text.split('.').join(' '));

handleBarService.registerHelper('parseHtml', (text: string) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(text, 'text/html');
  return text ? doc.body?.firstChild?.textContent || '' : '';
});

export const syncContextData = (
  data: string,
  customContext: {
    [key: string]: any;
  }
) => {
  try {
    const configTemplate = handleBarService.compile(data);
    const compiledData = configTemplate({
      ...customContext,
    });
    return compiledData;
  } catch (error) {
    return data;
  }
};
