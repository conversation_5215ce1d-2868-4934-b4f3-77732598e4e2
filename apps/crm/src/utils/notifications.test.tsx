import { notifications } from '@mantine/notifications';
import { renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  useShowMessageFailedNotification,
  useShowMessageSentNotification,
  useShowMessageUndoNotification,
  useShowNewRecordNotification,
  useShowRecordUpdateNotification,
  useShowTemplateSavedNotification,
} from './notifications';

// Mock notifications
vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
    hide: vi.fn(),
    update: vi.fn(),
  },
}));

// Mock the workspace utility
vi.mock('./workspace', () => ({
  customNotificationStyles: vi.fn((theme) => ({
    root: {
      backgroundColor: theme.colors.decaGreen[0],
    },
  })),
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: vi.fn((key: string) => `translated_${key}`),
  })),
}));

// <PERSON><PERSON> is now mocked globally in setupTest.js

describe('notifications utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useShowNewRecordNotification', () => {
    it('should return a function that shows notification with correct parameters', () => {
      const { result } = renderHook(() => useShowNewRecordNotification());
      const onUpdateView = vi.fn();

      result.current({ callBack: onUpdateView });

      expect(notifications.show).toHaveBeenCalledWith({
        id: 'newRecordAdded',
        message: expect.any(Object),
        autoClose: 5000,
        withCloseButton: true,
        radius: '16px',
        position: 'top-right',
        top: expect.any(String),
        right: expect.any(String),
        icon: expect.any(Object),
        bg: '#E7F5FF',
        c: '#228BE6',
        styles: {
          root: {
            '.mantine-Notification-icon': {
              backgroundColor: 'unset',
            },
          },
          closeButton: {
            color: '#228BE6',
          },
        },
      });
    });

    it('should call onUpdateView when the notification function is called', () => {
      const { result } = renderHook(() => useShowNewRecordNotification());
      const onUpdateView = vi.fn();

      result.current({ callBack: onUpdateView });

      expect(notifications.show).toHaveBeenCalled();
    });
  });

  describe('useShowRecordUpdateNotification', () => {
    it('should return a function that shows notification with correct parameters', () => {
      const { result } = renderHook(() => useShowRecordUpdateNotification());
      const onUpdateView = vi.fn();

      result.current({ callBack: onUpdateView });

      expect(notifications.show).toHaveBeenCalledWith({
        id: 'recordUpdated',
        message: expect.any(Object),
        autoClose: 5000,
        withCloseButton: true,
        radius: '16px',
        position: 'top-right',
        top: expect.any(String),
        right: expect.any(String),
        icon: expect.any(Object),
        bg: '#E7F5FF',
        c: '#228BE6',
        styles: {
          root: {
            '.mantine-Notification-icon': {
              backgroundColor: 'unset',
            },
          },
          closeButton: {
            color: '#228BE6',
          },
        },
      });
    });

    it('should call onUpdateView when anchor is clicked', () => {
      const { result } = renderHook(() => useShowRecordUpdateNotification());
      const onUpdateView = vi.fn();

      result.current({ callBack: onUpdateView });

      expect(notifications.show).toHaveBeenCalled();
    });
  });

  describe('useShowTemplateSavedNotification', () => {
    it('should return a function that shows notification for email template', () => {
      const { result } = renderHook(() => useShowTemplateSavedNotification());

      result.current({ editorType: 'email' });

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'translated_email translated_templateSaved!',
        icon: expect.any(Object),
        autoClose: 3000,
        styles: expect.any(Function),
      });
    });

    it('should return a function that shows notification for sms template', () => {
      const { result } = renderHook(() => useShowTemplateSavedNotification());

      result.current({ editorType: 'sms' });

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'translated_sms translated_templateSaved!',
        icon: expect.any(Object),
        autoClose: 3000,
        styles: expect.any(Function),
      });
    });

    it('should return a function that shows notification for line template', () => {
      const { result } = renderHook(() => useShowTemplateSavedNotification());

      result.current({ editorType: 'line' });

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'translated_line translated_templateSaved!',
        icon: expect.any(Object),
        autoClose: 3000,
        styles: expect.any(Function),
      });
    });
  });

  describe('useShowMessageSentNotification', () => {
    it('should return a function that shows sent notification for email', () => {
      const { result } = renderHook(() => useShowMessageSentNotification());

      result.current({ editorType: 'email' });

      expect(notifications.show).toHaveBeenCalledWith({
        message: expect.any(Object),
        icon: expect.any(Object),
        autoClose: 5000,
        styles: expect.any(Function),
      });
    });

    it('should return a function that shows sent notification for line with live chat option', () => {
      const { result } = renderHook(() => useShowMessageSentNotification());
      const mockOnOpenLiveChat = vi.fn();

      result.current({
        editorType: 'line',
        onOpenLiveChat: mockOnOpenLiveChat,
      });

      expect(notifications.show).toHaveBeenCalledWith({
        message: expect.any(Object),
        icon: expect.any(Object),
        autoClose: 5000,
        styles: expect.any(Function),
      });
    });
  });

  describe('useShowMessageFailedNotification', () => {
    it('should return a function that shows failed notification', () => {
      const { result } = renderHook(() => useShowMessageFailedNotification());

      result.current({ editorType: 'email' });

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'translated_emailSmsSendFailed',
        icon: expect.any(Object),
        autoClose: 3000,
        styles: expect.any(Function),
      });
    });
  });

  describe('useShowMessageUndoNotification', () => {
    it('should return a function that shows undo notification', () => {
      const { result } = renderHook(() => useShowMessageUndoNotification());
      const mockOnUndo = vi.fn();
      const mockOnSend = vi.fn();

      result.current({
        editorType: 'email',
        onUndo: mockOnUndo,
        onSend: mockOnSend,
      });

      expect(notifications.show).toHaveBeenCalledWith({
        id: 'sendingNotification',
        message: expect.any(Object),
        icon: expect.any(Object),
        autoClose: 5000,
        styles: expect.any(Function),
        onClose: mockOnSend,
      });
    });

    it('should return a function that calls onSend when notification auto-closes', () => {
      const { result } = renderHook(() => useShowMessageUndoNotification());
      const mockOnUndo = vi.fn();
      const mockOnSend = vi.fn();

      result.current({
        editorType: 'sms',
        onUndo: mockOnUndo,
        onSend: mockOnSend,
      });

      expect(notifications.show).toHaveBeenCalledWith(
        expect.objectContaining({
          onClose: mockOnSend,
        })
      );
    });
  });
});
