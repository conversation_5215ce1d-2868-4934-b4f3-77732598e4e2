import { Route, Routes } from 'react-router-dom';
import BaseLayout from './components/BaseLayout';
import Home from './components/Home';
import Workspace from './components/Workspace';

const App = () => {
  return (
    <BaseLayout>
      <Routes>
        <Route path='/crm'>
          <Route path='' element={<Home />} />
          <Route path='workspace/:wsId' element={<Home />} />
          <Route path='workspace/:wsId/objects/:id' element={<Workspace />}>
            <Route path='views/:viewId' element={<Workspace />} />
            <Route path=':recordId' element={<Workspace />} />
            <Route path='views/:viewId/:recordId' element={<Workspace />} />
          </Route>
        </Route>
      </Routes>
    </BaseLayout>
  );
};

export default App;
