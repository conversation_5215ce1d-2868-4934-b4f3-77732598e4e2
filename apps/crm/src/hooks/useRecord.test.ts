import { RecordAPI } from '@/services/api';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useRecord } from './useRecord';

// Mock the services
vi.mock('@/services/api', () => ({
  RecordAPI: {
    getRecordById: vi.fn(),
  },
}));

// Mock SWR
vi.mock('swr');

// Mock React
vi.mock('react', async (importOriginal) => {
  const actual = await importOriginal<typeof import('react')>();
  return {
    ...actual,
    useMemo: vi.fn((fn) => fn()),
  };
});

// Mock SWR
const mockUseSWR = useSWR as unknown as ReturnType<typeof vi.fn>;

describe('useRecord', () => {
  const mockRecord = {
    id: 'record-1',
    title: 'Test Record',
    content: 'Test content',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-02',
  };

  const mockMutate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return record data when all parameters are provided', async () => {
    mockUseSWR.mockReturnValue({
      data: mockRecord,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

    expect(result.current.record).toEqual(mockRecord);
    expect(result.current.recordLoading).toBe(false);
    expect(result.current.mutateRecord).toBe(mockMutate);
  });

  it('should call SWR with correct key when all parameters are provided', () => {
    mockUseSWR.mockReturnValue({
      data: mockRecord,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      'data/ws-1/obj-1/record-1',
      expect.any(Function),
      expect.objectContaining({
        revalidateIfStale: false,
      })
    );
  });

  it('should call SWR with null key when wsId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord(null, 'obj-1', 'record-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        revalidateIfStale: false,
      })
    );
  });

  it('should call SWR with null key when objectId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord('ws-1', null, 'record-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        revalidateIfStale: false,
      })
    );
  });

  it('should call SWR with null key when recordId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord('ws-1', 'obj-1', null));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        revalidateIfStale: false,
      })
    );
  });

  it('should call SWR with null key when all parameters are missing', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord(null, null, null));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        revalidateIfStale: false,
      })
    );
  });

  it('should set revalidateIfStale to true when reValidate is true', () => {
    mockUseSWR.mockReturnValue({
      data: mockRecord,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord('ws-1', 'obj-1', 'record-1', true));

    expect(mockUseSWR).toHaveBeenCalledWith(
      'data/ws-1/obj-1/record-1',
      expect.any(Function),
      expect.objectContaining({
        revalidateIfStale: true,
      })
    );
  });

  it('should set revalidateIfStale to false when reValidate is false', () => {
    mockUseSWR.mockReturnValue({
      data: mockRecord,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord('ws-1', 'obj-1', 'record-1', false));

    expect(mockUseSWR).toHaveBeenCalledWith(
      'data/ws-1/obj-1/record-1',
      expect.any(Function),
      expect.objectContaining({
        revalidateIfStale: false,
      })
    );
  });

  it('should set revalidateIfStale to false when reValidate is undefined', () => {
    mockUseSWR.mockReturnValue({
      data: mockRecord,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      'data/ws-1/obj-1/record-1',
      expect.any(Function),
      expect.objectContaining({
        revalidateIfStale: false,
      })
    );
  });

  it('should return loading state when SWR is loading', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: true,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

    expect(result.current.record).toBeUndefined();
    expect(result.current.recordLoading).toBe(true);
    expect(result.current.mutateRecord).toBe(mockMutate);
  });

  it('should call RecordAPI.getRecordById with correct parameters', async () => {
    const mockFetcher = vi.fn().mockResolvedValue(mockRecord);
    (RecordAPI.getRecordById as any).mockImplementation(mockFetcher);

    mockUseSWR.mockImplementation((key, fetcher) => {
      if (key && fetcher) {
        fetcher();
      }
      return {
        data: mockRecord,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

    expect(RecordAPI.getRecordById).toHaveBeenCalledWith('ws-1', 'obj-1', 'record-1');
  });

  it('should handle empty string parameters by creating null key', async () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord('', '', ''));

    // Empty strings are falsy, so the SWR key becomes null
    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        revalidateIfStale: false,
      })
    );

    // RecordAPI should not be called when key is null
    expect(RecordAPI.getRecordById).not.toHaveBeenCalled();
  });

  it('should handle null parameters by passing empty strings to API', async () => {
    const mockFetcher = vi.fn().mockResolvedValue(mockRecord);
    (RecordAPI.getRecordById as any).mockImplementation(mockFetcher);

    mockUseSWR.mockImplementation((_key, fetcher) => {
      if (fetcher) {
        fetcher();
      }
      return {
        data: null,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useRecord(null, null, null));

    expect(RecordAPI.getRecordById).toHaveBeenCalledWith('', '', '');
  });

  it('should use React.useMemo to memoize the return value', () => {
    const mockUseMemo = vi.fn((fn) => fn());
    const React = require('react');
    React.useMemo = mockUseMemo;

    mockUseSWR.mockReturnValue({
      data: mockRecord,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

    expect(mockUseMemo).toHaveBeenCalledWith(expect.any(Function), [mockRecord, false, mockMutate]);
  });

  it('should handle different record data types', () => {
    const customRecord = {
      id: 'custom-1',
      name: 'Custom Record',
      data: { nested: 'value' },
      tags: ['tag1', 'tag2'],
    };

    mockUseSWR.mockReturnValue({
      data: customRecord,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

    expect(result.current.record).toEqual(customRecord);
    expect(result.current.recordLoading).toBe(false);
    expect(result.current.mutateRecord).toBe(mockMutate);
  });

  it('should handle undefined data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

    expect(result.current.record).toBeUndefined();
    expect(result.current.recordLoading).toBe(false);
    expect(result.current.mutateRecord).toBe(mockMutate);
  });

  it('should handle null data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

    expect(result.current.record).toBeNull();
    expect(result.current.recordLoading).toBe(false);
    expect(result.current.mutateRecord).toBe(mockMutate);
  });

  it('should handle complex parameter combinations', () => {
    const testCases: [string, string, string, boolean | undefined][] = [
      ['ws-1', 'obj-1', 'record-1', true],
      ['workspace-123', 'object-456', 'record-789', false],
      ['ws', 'obj', 'rec', undefined],
    ];

    testCases.forEach(([wsId, objectId, recordId, reValidate]) => {
      mockUseSWR.mockReturnValue({
        data: mockRecord,
        isLoading: false,
        mutate: mockMutate,
      });

      const { result } = renderHook(() => useRecord(wsId, objectId, recordId, reValidate));

      expect(result.current.record).toEqual(mockRecord);
      expect(result.current.recordLoading).toBe(false);
      expect(result.current.mutateRecord).toBe(mockMutate);

      const expectedKey = `data/${wsId}/${objectId}/${recordId}`;
      expect(mockUseSWR).toHaveBeenCalledWith(
        expectedKey,
        expect.any(Function),
        expect.objectContaining({
          revalidateIfStale: reValidate || false,
        })
      );
    });
  });

  it('should return consistent structure regardless of SWR state', () => {
    const states = [
      { data: mockRecord, isLoading: false, mutate: mockMutate },
      { data: undefined, isLoading: true, mutate: mockMutate },
      { data: null, isLoading: false, mutate: mockMutate },
    ];

    states.forEach((state) => {
      mockUseSWR.mockReturnValue(state);

      const { result } = renderHook(() => useRecord('ws-1', 'obj-1', 'record-1'));

      expect(result.current).toHaveProperty('record');
      expect(result.current).toHaveProperty('recordLoading');
      expect(result.current).toHaveProperty('mutateRecord');
      expect(typeof result.current.recordLoading).toBe('boolean');
      expect(typeof result.current.mutateRecord).toBe('function');
    });
  });
});
