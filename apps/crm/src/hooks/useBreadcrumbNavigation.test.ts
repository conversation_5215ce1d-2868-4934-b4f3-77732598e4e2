import { usePathParams } from '@resola-ai/ui/hooks';
import { renderHook } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import { useNavigate, useParams } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { useAppContext } from '@/contexts/AppContext';
import { useBreadcrumbContext } from '@/contexts/BreadcrumbContext';
import { useProfileContext } from '@/contexts/ProfileContext';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { useBreadcrumbNavigation } from './useBreadcrumbNavigation';

// Mock dependencies
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
  useParams: vi.fn(),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
}));

vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: vi.fn(),
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(),
}));

vi.mock('@/contexts/BreadcrumbContext', () => ({
  useBreadcrumbContext: vi.fn(),
}));

vi.mock('@/contexts/ProfileContext', () => ({
  useProfileContext: vi.fn(),
}));

vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: vi.fn(),
}));

vi.mock('@/configs', () => ({
  default: {
    BASE_PATH: '/app/',
  },
}));

vi.mock('lodash/isEmpty', () => ({
  default: vi.fn((value) => {
    // Mock isEmpty behavior
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.length === 0;
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  }),
}));

// Mock functions
const mockNavigate = vi.fn();
const mockCreatePathWithLngParam = vi.fn();
const mockAddBreadcrumb = vi.fn();
const mockT = vi.fn();

// Mock context values
const mockUseParams = useParams as ReturnType<typeof vi.fn>;
const mockUseNavigate = useNavigate as ReturnType<typeof vi.fn>;
const mockUseTranslate = useTranslate as ReturnType<typeof vi.fn>;
const mockUsePathParams = usePathParams as ReturnType<typeof vi.fn>;
const mockUseAppContext = useAppContext as ReturnType<typeof vi.fn>;
const mockUseBreadcrumbContext = useBreadcrumbContext as ReturnType<typeof vi.fn>;
const mockUseProfileContext = useProfileContext as ReturnType<typeof vi.fn>;
const mockUseWorkspaceContext = useWorkspaceContext as ReturnType<typeof vi.fn>;

describe('useBreadcrumbNavigation', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default mocks
    mockUseParams.mockReturnValue({
      wsId: 'workspace-1',
      id: 'object-1',
      recordId: 'record-1',
    });

    mockUseNavigate.mockReturnValue(mockNavigate);
    mockUseTranslate.mockReturnValue({ t: mockT });
    mockUsePathParams.mockReturnValue({
      createPathWithLngParam: mockCreatePathWithLngParam,
    });

    mockUseAppContext.mockReturnValue({
      objects: [
        {
          id: 'object-1',
          name: { singular: 'Contact' },
          fields: [
            { id: 'name', type: 'singleLineText' },
            { id: 'email', type: 'email' },
          ],
          views: [{ id: 'view-1' }],
          userconfig: { viewId: 'view-1' },
        },
        {
          id: 'object-2',
          name: { singular: 'Company' },
          fields: [
            { id: 'company_name', type: 'singleLineText' },
            { id: 'website', type: 'url' },
          ],
          views: [{ id: 'view-2' }],
          userconfig: { viewId: 'view-2' },
        },
      ],
    });

    mockUseBreadcrumbContext.mockReturnValue({
      addBreadcrumb: mockAddBreadcrumb,
    });

    mockUseProfileContext.mockReturnValue({
      profile: {
        id: 'record-1',
        name: 'John Doe',
        email: '<EMAIL>',
      },
    });

    mockUseWorkspaceContext.mockReturnValue({
      object: {
        id: 'object-1',
        name: { singular: 'Contact' },
        fields: [
          { id: 'name', type: 'singleLineText' },
          { id: 'email', type: 'email' },
        ],
      },
      activeView: {
        id: 'view-1',
        name: 'All Contacts',
      },
    });

    mockT.mockImplementation((key) => key);
    mockCreatePathWithLngParam.mockImplementation((path) => path);
  });

  describe('useEffect - breadcrumb updates', () => {
    it('should add breadcrumb when all required data is available', () => {
      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).toHaveBeenCalledWith(
        {
          id: 'object-1-record-1',
          objectId: 'object-1',
          recordId: 'record-1',
          recordName: 'John Doe',
          objectName: 'Contact',
          path: '/app/workspace/workspace-1/objects/object-1/views/view-1/record-1',
        },
        true
      );
    });

    it('should not add breadcrumb when recordId is missing', () => {
      mockUseParams.mockReturnValue({
        wsId: 'workspace-1',
        id: 'object-1',
        recordId: undefined,
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).not.toHaveBeenCalled();
    });

    it('should not add breadcrumb when object is missing', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: null,
        activeView: { id: 'view-1', name: 'All Contacts' },
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).not.toHaveBeenCalled();
    });

    it('should not add breadcrumb when activeView is missing', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          id: 'object-1',
          name: { singular: 'Contact' },
          fields: [],
        },
        activeView: null,
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).not.toHaveBeenCalled();
    });

    it('should not add breadcrumb when objectId is missing', () => {
      mockUseParams.mockReturnValue({
        wsId: 'workspace-1',
        id: undefined,
        recordId: 'record-1',
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).not.toHaveBeenCalled();
    });

    it('should use recordId as fallback when profile is missing', () => {
      mockUseProfileContext.mockReturnValue({
        profile: null,
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).toHaveBeenCalledWith(
        {
          id: 'object-1-record-1',
          objectId: 'object-1',
          recordId: 'record-1',
          recordName: 'record-1',
          objectName: 'Contact',
          path: '/app/workspace/workspace-1/objects/object-1/views/view-1/record-1',
        },
        true
      );
    });

    it('should use default object name when object.name is missing', () => {
      mockUseWorkspaceContext.mockReturnValue({
        object: {
          id: 'object-1',
          name: null,
          fields: [],
        },
        activeView: { id: 'view-1', name: 'All Contacts' },
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).toHaveBeenCalledWith(
        expect.objectContaining({
          objectName: 'Record',
        }),
        true
      );
    });
  });

  describe('navigateToLinkedRecord', () => {
    it('should navigate to linked record successfully', () => {
      const { result } = renderHook(() => useBreadcrumbNavigation());

      const linkedRecord = {
        id: 'company-1',
        company_name: 'ACME Corp',
      };

      result.current.navigateToLinkedRecord('object-2', 'company-1', linkedRecord);

      expect(mockAddBreadcrumb).toHaveBeenCalledWith({
        id: 'object-1-record-1',
        objectId: 'object-1',
        recordId: 'record-1',
        recordName: 'John Doe',
        objectName: 'Contact',
        path: '/app/workspace/workspace-1/objects/object-1/views/view-1/record-1',
      });

      expect(mockNavigate).toHaveBeenCalledWith(
        '/app/workspace/workspace-1/objects/object-2/views/view-2/company-1'
      );
    });

    it('should handle missing linked record gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const { result } = renderHook(() => useBreadcrumbNavigation());

      result.current.navigateToLinkedRecord('object-2', 'company-1', null);

      expect(consoleSpy).toHaveBeenCalledWith('Missing required data for navigation');
      expect(mockNavigate).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle missing linkedObjectId gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const { result } = renderHook(() => useBreadcrumbNavigation());

      const linkedRecord = { id: 'company-1', company_name: 'ACME Corp' };

      result.current.navigateToLinkedRecord('', 'company-1', linkedRecord);

      expect(consoleSpy).toHaveBeenCalledWith('Missing required data for navigation');
      expect(mockNavigate).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle missing linkedRecordId gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const { result } = renderHook(() => useBreadcrumbNavigation());

      const linkedRecord = { id: 'company-1', company_name: 'ACME Corp' };

      result.current.navigateToLinkedRecord('object-2', '', linkedRecord);

      expect(consoleSpy).toHaveBeenCalledWith('Missing required data for navigation');
      expect(mockNavigate).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle missing objects gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      mockUseAppContext.mockReturnValue({
        objects: [],
      });

      const { result } = renderHook(() => useBreadcrumbNavigation());

      const linkedRecord = { id: 'company-1', company_name: 'ACME Corp' };

      result.current.navigateToLinkedRecord('object-2', 'company-1', linkedRecord);

      expect(consoleSpy).toHaveBeenCalledWith('Missing required data for navigation');
      expect(mockNavigate).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle target object not found', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const { result } = renderHook(() => useBreadcrumbNavigation());

      const linkedRecord = { id: 'company-1', company_name: 'ACME Corp' };

      result.current.navigateToLinkedRecord('nonexistent-object', 'company-1', linkedRecord);

      expect(consoleSpy).toHaveBeenCalledWith('Target object not found:', 'nonexistent-object');
      expect(mockNavigate).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle target object with no views', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      mockUseAppContext.mockReturnValue({
        objects: [
          {
            id: 'object-1',
            name: { singular: 'Contact' },
            views: [],
            userconfig: {},
          },
        ],
      });

      const { result } = renderHook(() => useBreadcrumbNavigation());

      const linkedRecord = { id: 'company-1', company_name: 'ACME Corp' };

      result.current.navigateToLinkedRecord('object-1', 'company-1', linkedRecord);

      expect(consoleSpy).toHaveBeenCalledWith('No view found for target object:', 'object-1');
      expect(mockNavigate).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should use first view when userconfig viewId is not available', () => {
      mockUseAppContext.mockReturnValue({
        objects: [
          {
            id: 'object-2',
            name: { singular: 'Company' },
            views: [{ id: 'view-2' }],
            userconfig: {}, // No viewId
          },
        ],
      });

      const { result } = renderHook(() => useBreadcrumbNavigation());

      const linkedRecord = { id: 'company-1', company_name: 'ACME Corp' };

      result.current.navigateToLinkedRecord('object-2', 'company-1', linkedRecord);

      expect(mockNavigate).toHaveBeenCalledWith(
        '/app/workspace/workspace-1/objects/object-2/views/view-2/company-1'
      );
    });

    it('should handle string view ID in views array', () => {
      mockUseAppContext.mockReturnValue({
        objects: [
          {
            id: 'object-2',
            name: { singular: 'Company' },
            views: ['view-2'], // String instead of object
            userconfig: {},
          },
        ],
      });

      const { result } = renderHook(() => useBreadcrumbNavigation());

      const linkedRecord = { id: 'company-1', company_name: 'ACME Corp' };

      result.current.navigateToLinkedRecord('object-2', 'company-1', linkedRecord);

      expect(mockNavigate).toHaveBeenCalledWith(
        '/app/workspace/workspace-1/objects/object-2/views/view-2/company-1'
      );
    });

    it('should not add current breadcrumb when current record data is missing', () => {
      mockUseProfileContext.mockReturnValue({
        profile: null,
      });
      mockUseWorkspaceContext.mockReturnValue({
        object: null,
        activeView: null,
      });

      const { result } = renderHook(() => useBreadcrumbNavigation());

      const linkedRecord = { id: 'company-1', company_name: 'ACME Corp' };

      result.current.navigateToLinkedRecord('object-2', 'company-1', linkedRecord);

      // Should only be called once during useEffect (which won't run due to missing data)
      // and not again for current breadcrumb in navigateToLinkedRecord
      expect(mockAddBreadcrumb).not.toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith(
        '/app/workspace/workspace-1/objects/object-2/views/view-2/company-1'
      );
    });
  });

  describe('getRecordDisplayName helper', () => {
    it('should return record name when available', () => {
      mockUseProfileContext.mockReturnValue({
        profile: {
          id: 'record-1',
          name: 'John Doe',
        },
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).toHaveBeenCalledWith(
        expect.objectContaining({
          recordName: 'John Doe',
        }),
        true
      );
    });

    it('should find display field by name field', () => {
      mockUseProfileContext.mockReturnValue({
        profile: {
          id: 'record-1',
          name: 'Jane Smith',
        },
      });

      mockUseWorkspaceContext.mockReturnValue({
        object: {
          id: 'object-1',
          name: { singular: 'Contact' },
          fields: [
            { id: 'name', type: 'singleLineText' },
            { id: 'email', type: 'email' },
          ],
        },
        activeView: { id: 'view-1', name: 'All Contacts' },
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).toHaveBeenCalledWith(
        expect.objectContaining({
          recordName: 'Jane Smith',
        }),
        true
      );
    });

    it('should find display field by title field', () => {
      mockUseProfileContext.mockReturnValue({
        profile: {
          id: 'record-1',
          title: 'CEO',
        },
      });

      mockUseWorkspaceContext.mockReturnValue({
        object: {
          id: 'object-1',
          name: { singular: 'Contact' },
          fields: [
            { id: 'title', type: 'singleLineText' },
            { id: 'email', type: 'email' },
          ],
        },
        activeView: { id: 'view-1', name: 'All Contacts' },
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).toHaveBeenCalledWith(
        expect.objectContaining({
          recordName: 'CEO',
        }),
        true
      );
    });

    it('should fallback to first text field when no display field found', () => {
      mockUseProfileContext.mockReturnValue({
        profile: {
          id: 'record-1',
          email: '<EMAIL>',
        },
      });

      mockUseWorkspaceContext.mockReturnValue({
        object: {
          id: 'object-1',
          name: { singular: 'Contact' },
          fields: [
            { id: 'email', type: 'singleLineText' },
            { id: 'phone', type: 'phone' },
          ],
        },
        activeView: { id: 'view-1', name: 'All Contacts' },
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).toHaveBeenCalledWith(
        expect.objectContaining({
          recordName: '<EMAIL>',
        }),
        true
      );
    });

    it('should fallback to recordId when no suitable field found', () => {
      mockUseProfileContext.mockReturnValue({
        profile: {
          id: 'record-1',
          someField: 'value',
        },
      });

      mockUseWorkspaceContext.mockReturnValue({
        object: {
          id: 'object-1',
          name: { singular: 'Contact' },
          fields: [{ id: 'someField', type: 'number' }],
        },
        activeView: { id: 'view-1', name: 'All Contacts' },
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).toHaveBeenCalledWith(
        expect.objectContaining({
          recordName: 'record-1',
        }),
        true
      );
    });

    it('should handle empty string values gracefully', () => {
      mockUseProfileContext.mockReturnValue({
        profile: {
          id: 'record-1',
          name: '   ', // whitespace only
        },
      });

      mockUseWorkspaceContext.mockReturnValue({
        object: {
          id: 'object-1',
          name: { singular: 'Contact' },
          fields: [{ id: 'name', type: 'singleLineText' }],
        },
        activeView: { id: 'view-1', name: 'All Contacts' },
      });

      renderHook(() => useBreadcrumbNavigation());

      expect(mockAddBreadcrumb).toHaveBeenCalledWith(
        expect.objectContaining({
          recordName: 'record-1',
        }),
        true
      );
    });
  });

  describe('return value', () => {
    it('should return navigateToLinkedRecord function', () => {
      const { result } = renderHook(() => useBreadcrumbNavigation());

      expect(result.current).toEqual({
        navigateToLinkedRecord: expect.any(Function),
      });
    });
  });

  describe('dependencies and memoization', () => {
    it('should not change navigateToLinkedRecord function when dependencies are stable', () => {
      const { result, rerender } = renderHook(() => useBreadcrumbNavigation());

      const firstNavigateFunction = result.current.navigateToLinkedRecord;

      rerender();

      expect(result.current.navigateToLinkedRecord).toBe(firstNavigateFunction);
    });

    it('should update navigateToLinkedRecord function when dependencies change', () => {
      const { result, rerender } = renderHook(() => useBreadcrumbNavigation());

      const firstNavigateFunction = result.current.navigateToLinkedRecord;

      // Change a dependency
      mockUseAppContext.mockReturnValue({
        objects: [
          {
            id: 'object-1',
            name: { singular: 'NewContact' },
            views: [{ id: 'view-1' }],
            userconfig: { viewId: 'view-1' },
          },
        ],
      });

      rerender();

      expect(result.current.navigateToLinkedRecord).not.toBe(firstNavigateFunction);
    });
  });
});
