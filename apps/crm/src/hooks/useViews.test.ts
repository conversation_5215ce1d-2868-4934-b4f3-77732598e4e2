import { ViewAPI } from '@/services/api';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useViews } from './useViews';

// Mock the services
vi.mock('@/services/api', () => ({
  ViewAPI: {
    getList: vi.fn(),
  },
}));

// Mock SWR
vi.mock('swr');

const mockUseSWR = vi.mocked(useSWR);
const mockViewAPI = vi.mocked(ViewAPI);

describe('useViews', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('should return views when all parameters are provided', () => {
    const mockViews = [
      { id: 'view1', name: 'Test View 1' },
      { id: 'view2', name: 'Test View 2' },
    ];

    mockUseSWR.mockReturnValue({
      data: mockViews,
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    const { result } = renderHook(() => useViews('workspace1', 'object1'));

    expect(result.current.views).toEqual(mockViews);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mutate).toEqual(expect.any(Function));
  });

  it('should call SWR with correct key when all parameters are provided', () => {
    mockUseSWR.mockReturnValue({
      data: [],
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    renderHook(() => useViews('workspace1', 'object1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      'workspaces/workspace1/objects/object1/views',
      expect.any(Function)
    );
  });

  it('should call SWR with null key when wsId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    renderHook(() => useViews('', 'object1'));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should call SWR with null key when objId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    renderHook(() => useViews('workspace1', ''));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should call SWR with null key when all parameters are missing', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    renderHook(() => useViews('', ''));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should return loading state when SWR is loading', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: true,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    const { result } = renderHook(() => useViews('workspace1', 'object1'));

    expect(result.current.isLoading).toBe(true);
  });

  it('should call ViewAPI.getList with correct parameters', () => {
    mockUseSWR.mockReturnValue({
      data: [],
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    renderHook(() => useViews('workspace1', 'object1'));

    // Get the fetcher function from the SWR call
    const swrCall = mockUseSWR.mock.calls[0];
    const fetcher = swrCall[1];

    // Call the fetcher function if it exists
    if (fetcher) {
      fetcher();
    }

    expect(mockViewAPI.getList).toHaveBeenCalledWith('workspace1', 'object1');
  });

  it('should handle empty views array', () => {
    mockUseSWR.mockReturnValue({
      data: [],
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    const { result } = renderHook(() => useViews('workspace1', 'object1'));

    expect(result.current.views).toEqual([]);
  });

  it('should handle undefined data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    const { result } = renderHook(() => useViews('workspace1', 'object1'));

    expect(result.current.views).toBeUndefined();
  });

  it('should handle null data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    const { result } = renderHook(() => useViews('workspace1', 'object1'));

    expect(result.current.views).toBeNull();
  });

  it('should return consistent structure regardless of SWR state', () => {
    mockUseSWR.mockReturnValue({
      data: [],
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    const { result } = renderHook(() => useViews('workspace1', 'object1'));

    expect(result.current).toHaveProperty('views');
    expect(result.current).toHaveProperty('isLoading');
    expect(result.current).toHaveProperty('mutate');
  });

  it('should handle complex parameter combinations', () => {
    mockUseSWR.mockReturnValue({
      data: [],
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    // Test various combinations
    const { rerender } = renderHook(({ wsId, objId }) => useViews(wsId, objId), {
      initialProps: { wsId: 'ws1', objId: 'obj1' },
    });

    expect(mockUseSWR).toHaveBeenCalledWith(
      'workspaces/ws1/objects/obj1/views',
      expect.any(Function)
    );

    rerender({ wsId: 'ws2', objId: 'obj2' });

    expect(mockUseSWR).toHaveBeenCalledWith(
      'workspaces/ws2/objects/obj2/views',
      expect.any(Function)
    );
  });

  it('should not make API call when parameters are missing', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    renderHook(() => useViews('', ''));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should handle rerendering with different parameters', () => {
    mockUseSWR.mockReturnValue({
      data: [],
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    const { rerender } = renderHook(({ wsId, objId }) => useViews(wsId, objId), {
      initialProps: { wsId: 'workspace1', objId: 'object1' },
    });

    expect(mockUseSWR).toHaveBeenCalledWith(
      'workspaces/workspace1/objects/object1/views',
      expect.any(Function)
    );

    rerender({ wsId: 'workspace2', objId: 'object2' });

    expect(mockUseSWR).toHaveBeenCalledWith(
      'workspaces/workspace2/objects/object2/views',
      expect.any(Function)
    );
  });

  it('should handle API errors gracefully', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: vi.fn(),
      error: new Error('API Error'),
      isValidating: false,
    });

    const { result } = renderHook(() => useViews('workspace1', 'object1'));

    expect(result.current.views).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
  });

  it('should handle null parameter values', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    renderHook(() => useViews(null as any, null as any));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should generate correct SWR key format', () => {
    mockUseSWR.mockReturnValue({
      data: [],
      isLoading: false,
      mutate: vi.fn(),
      error: undefined,
      isValidating: false,
    });

    const wsId = 'my-workspace';
    const objId = 'my-object';

    renderHook(() => useViews(wsId, objId));

    expect(mockUseSWR).toHaveBeenCalledWith(
      `workspaces/${wsId}/objects/${objId}/views`,
      expect.any(Function)
    );
  });
});
