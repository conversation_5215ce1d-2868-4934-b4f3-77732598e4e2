import { ViewAPI } from '@/services/api';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useView } from './useView';

// Mock the services
vi.mock('@/services/api', () => ({
  ViewAPI: {
    get: vi.fn(),
  },
}));

// Mock SWR
vi.mock('swr');

// Mock React
vi.mock('react', async (importOriginal) => {
  const actual = await importOriginal<typeof import('react')>();
  return {
    ...actual,
    useMemo: vi.fn((fn) => fn()),
  };
});

// Mock SWR
const mockUseSWR = useSWR as unknown as ReturnType<typeof vi.fn>;

describe('useView', () => {
  const mockView = {
    id: 'view-1',
    name: 'Test View',
    type: 'table',
    filters: [],
    sorts: [],
    fields: ['field1', 'field2'],
  };
  const mockMutate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return view when all parameters are provided', () => {
    mockUseSWR.mockReturnValue({
      data: mockView,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    expect(result.current.view).toEqual(mockView);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mutateView).toBe(mockMutate);
  });

  it('should call SWR with correct key when all parameters are provided', () => {
    mockUseSWR.mockReturnValue({
      data: mockView,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      'workspaces/ws-1/objects/obj-1/views/view-1',
      expect.any(Function),
      expect.objectContaining({
        errorRetryCount: 0,
      })
    );
  });

  it('should call SWR with null key when wsId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView('', 'obj-1', 'view-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        errorRetryCount: 0,
      })
    );
  });

  it('should call SWR with null key when objId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView('ws-1', '', 'view-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        errorRetryCount: 0,
      })
    );
  });

  it('should call SWR with null key when viewId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView('ws-1', 'obj-1', ''));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        errorRetryCount: 0,
      })
    );
  });

  it('should call SWR with null key when all parameters are missing', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView('', '', ''));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        errorRetryCount: 0,
      })
    );
  });

  it('should return loading state when SWR is loading', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: true,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    expect(result.current.view).toBeUndefined();
    expect(result.current.isLoading).toBe(true);
    expect(result.current.mutateView).toBe(mockMutate);
  });

  it('should call ViewAPI.get with correct parameters', () => {
    const mockFetcher = vi.fn().mockResolvedValue(mockView);
    (ViewAPI.get as any).mockImplementation(mockFetcher);

    mockUseSWR.mockImplementation((key, fetcher) => {
      if (key && fetcher) {
        fetcher();
      }
      return {
        data: mockView,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    expect(ViewAPI.get).toHaveBeenCalledWith('ws-1', 'obj-1', 'view-1');
  });

  it('should use React.useMemo to memoize the return value', () => {
    const mockUseMemo = vi.fn((fn) => fn());
    const React = require('react');
    React.useMemo = mockUseMemo;

    mockUseSWR.mockReturnValue({
      data: mockView,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    expect(mockUseMemo).toHaveBeenCalledWith(expect.any(Function), [mockView]);
  });

  it('should handle different view data types', () => {
    const testCases = [
      { id: 'simple', name: 'Simple View' },
      mockView,
      {
        id: 'complex',
        name: 'Complex View',
        metadata: { version: '2.0' },
        customProps: { theme: 'dark' },
      },
      { id: 'empty', name: 'Empty View', filters: [], sorts: [], fields: [] },
    ];

    testCases.forEach((view) => {
      mockUseSWR.mockReturnValue({
        data: view,
        isLoading: false,
        mutate: mockMutate,
      });

      const { result } = renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

      expect(result.current.view).toEqual(view);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.mutateView).toBe(mockMutate);
    });
  });

  it('should handle undefined data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    expect(result.current.view).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mutateView).toBe(mockMutate);
  });

  it('should handle null data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    expect(result.current.view).toBeNull();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mutateView).toBe(mockMutate);
  });

  it('should return consistent structure regardless of SWR state', () => {
    const states = [
      { data: mockView, isLoading: false, mutate: mockMutate },
      { data: undefined, isLoading: true, mutate: mockMutate },
      { data: null, isLoading: false, mutate: mockMutate },
      { data: {}, isLoading: false, mutate: mockMutate },
    ];

    states.forEach((state) => {
      mockUseSWR.mockReturnValue(state);

      const { result } = renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

      expect(result.current).toHaveProperty('view');
      expect(result.current).toHaveProperty('isLoading');
      expect(result.current).toHaveProperty('mutateView');
      expect(typeof result.current.isLoading).toBe('boolean');
      expect(typeof result.current.mutateView).toBe('function');
    });
  });

  it('should handle complex parameter combinations', () => {
    const testCases = [
      { wsId: 'ws-1', objId: 'obj-1', viewId: 'view-1' },
      { wsId: 'workspace-123', objId: 'object-456', viewId: 'view-789' },
      { wsId: 'ws', objId: 'obj', viewId: 'view' },
      { wsId: '', objId: 'obj-1', viewId: 'view-1' },
      { wsId: 'ws-1', objId: '', viewId: 'view-1' },
      { wsId: 'ws-1', objId: 'obj-1', viewId: '' },
    ];

    testCases.forEach(({ wsId, objId, viewId }) => {
      const shouldHaveData = wsId && objId && viewId;

      mockUseSWR.mockReturnValue({
        data: shouldHaveData ? mockView : undefined,
        isLoading: false,
        mutate: mockMutate,
      });

      const { result } = renderHook(() => useView(wsId, objId, viewId));

      expect(result.current.view).toEqual(shouldHaveData ? mockView : undefined);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.mutateView).toBe(mockMutate);

      const expectedKey = shouldHaveData
        ? `workspaces/${wsId}/objects/${objId}/views/${viewId}`
        : null;
      expect(mockUseSWR).toHaveBeenCalledWith(
        expectedKey,
        expect.any(Function),
        expect.objectContaining({
          errorRetryCount: 0,
        })
      );
    });
  });

  it('should not make API call when parameters are missing', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView('', 'obj-1', 'view-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        errorRetryCount: 0,
      })
    );
    expect(ViewAPI.get).not.toHaveBeenCalled();
  });

  it('should handle rerendering with different parameters', () => {
    mockUseSWR.mockReturnValue({
      data: mockView,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result, rerender } = renderHook(
      ({ wsId, objId, viewId }) => useView(wsId, objId, viewId),
      { initialProps: { wsId: 'ws-1', objId: 'obj-1', viewId: 'view-1' } }
    );

    expect(result.current.view).toEqual(mockView);

    rerender({ wsId: 'ws-2', objId: 'obj-2', viewId: 'view-2' });

    expect(result.current.view).toEqual(mockView);
  });

  it('should include errorRetryCount option in SWR configuration', () => {
    mockUseSWR.mockReturnValue({
      data: mockView,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(
      'workspaces/ws-1/objects/obj-1/views/view-1',
      expect.any(Function),
      expect.objectContaining({
        errorRetryCount: 0,
      })
    );
  });

  it('should properly memoize with only view dependency', () => {
    const mockUseMemo = vi.fn((fn) => fn());
    const React = require('react');
    React.useMemo = mockUseMemo;

    mockUseSWR.mockReturnValue({
      data: mockView,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    // Verify that useMemo is called with only view in the dependency array
    expect(mockUseMemo).toHaveBeenCalledWith(expect.any(Function), [mockView]);
  });

  it('should handle API errors gracefully', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
      error: new Error('API Error'),
    });

    const { result } = renderHook(() => useView('ws-1', 'obj-1', 'view-1'));

    expect(result.current.view).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mutateView).toBe(mockMutate);
  });

  it('should handle null parameter values', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useView(null as any, null as any, null as any));

    expect(mockUseSWR).toHaveBeenCalledWith(
      null,
      expect.any(Function),
      expect.objectContaining({
        errorRetryCount: 0,
      })
    );
  });

  it('should generate correct SWR key format', () => {
    const testCases = [
      {
        wsId: 'workspace-1',
        objId: 'object-1',
        viewId: 'view-1',
        expected: 'workspaces/workspace-1/objects/object-1/views/view-1',
      },
      {
        wsId: 'ws_test',
        objId: 'obj_test',
        viewId: 'view_test',
        expected: 'workspaces/ws_test/objects/obj_test/views/view_test',
      },
      {
        wsId: '123',
        objId: '456',
        viewId: '789',
        expected: 'workspaces/123/objects/456/views/789',
      },
    ];

    testCases.forEach(({ wsId, objId, viewId, expected }) => {
      mockUseSWR.mockReturnValue({
        data: mockView,
        isLoading: false,
        mutate: mockMutate,
      });

      renderHook(() => useView(wsId, objId, viewId));

      expect(mockUseSWR).toHaveBeenCalledWith(
        expected,
        expect.any(Function),
        expect.objectContaining({
          errorRetryCount: 0,
        })
      );
    });
  });
});
