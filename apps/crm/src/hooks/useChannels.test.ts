import { renderHook } from '@testing-library/react';
import useS<PERSON> from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { WorkspaceAPI } from '@/services/api';
import { useChannels } from './useChannels';

// Mock dependencies
vi.mock('swr');
vi.mock('@/services/api', () => ({
  WorkspaceAPI: {
    getChannels: vi.fn(),
  },
}));

// Mock SWR
const mockUseSWR = useSWR as unknown as ReturnType<typeof vi.fn>;

describe('useChannels', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('SWR integration', () => {
    it('should call useSWR with correct parameters', () => {
      mockUseSWR.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      renderHook(() => useChannels());

      expect(useSWR).toHaveBeenCalledWith('/channels', expect.any(Function));
    });

    it('should call useSWR with the same key regardless of type parameter', () => {
      mockUseSWR.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      renderHook(() => useChannels('sms'));

      expect(useSWR).toHaveBeenCalledWith('/channels', expect.any(Function));
    });

    it('should call useSWR without type parameter', () => {
      mockUseSWR.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      renderHook(() => useChannels(undefined));

      expect(useSWR).toHaveBeenCalledWith('/channels', expect.any(Function));
    });
  });

  describe('fetcher function', () => {
    it('should call WorkspaceAPI.getChannels with provided type', async () => {
      const mockChannels = [
        { id: '1', name: 'SMS Channel', type: 'sms' },
        { id: '2', name: 'Email Channel', type: 'email' },
      ];

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: mockChannels,
          isLoading: false,
          error: null,
        };
      });

      (WorkspaceAPI.getChannels as ReturnType<typeof vi.fn>).mockResolvedValue(mockChannels);

      renderHook(() => useChannels('sms'));

      const result = await swrFetcher();

      expect(WorkspaceAPI.getChannels).toHaveBeenCalledWith('sms');
      expect(result).toEqual(mockChannels);
    });

    it('should call WorkspaceAPI.getChannels with email type', async () => {
      const mockChannels = [{ id: '2', name: 'Email Channel', type: 'email' }];

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: mockChannels,
          isLoading: false,
          error: null,
        };
      });

      (WorkspaceAPI.getChannels as ReturnType<typeof vi.fn>).mockResolvedValue(mockChannels);

      renderHook(() => useChannels('email'));

      const result = await swrFetcher();

      expect(WorkspaceAPI.getChannels).toHaveBeenCalledWith('email');
      expect(result).toEqual(mockChannels);
    });

    it('should call WorkspaceAPI.getChannels with line type', async () => {
      const mockChannels = [{ id: '3', name: 'Line Channel', type: 'line' }];

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: mockChannels,
          isLoading: false,
          error: null,
        };
      });

      (WorkspaceAPI.getChannels as ReturnType<typeof vi.fn>).mockResolvedValue(mockChannels);

      renderHook(() => useChannels('line'));

      const result = await swrFetcher();

      expect(WorkspaceAPI.getChannels).toHaveBeenCalledWith('line');
      expect(result).toEqual(mockChannels);
    });

    it('should call WorkspaceAPI.getChannels without type when undefined', async () => {
      const mockChannels = [{ id: '1', name: 'Default Channel' }];

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: mockChannels,
          isLoading: false,
          error: null,
        };
      });

      (WorkspaceAPI.getChannels as ReturnType<typeof vi.fn>).mockResolvedValue(mockChannels);

      renderHook(() => useChannels());

      const result = await swrFetcher();

      expect(WorkspaceAPI.getChannels).toHaveBeenCalledWith(undefined);
      expect(result).toEqual(mockChannels);
    });

    it('should handle API errors gracefully', async () => {
      const apiError = new Error('API request failed');

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: null,
          isLoading: false,
          error: apiError,
        };
      });

      (WorkspaceAPI.getChannels as ReturnType<typeof vi.fn>).mockRejectedValue(apiError);

      renderHook(() => useChannels('sms'));

      await expect(swrFetcher()).rejects.toThrow('API request failed');
      expect(WorkspaceAPI.getChannels).toHaveBeenCalledWith('sms');
    });
  });

  describe('return value', () => {
    it('should return channels and isLoading from SWR', () => {
      const mockChannels = [
        { id: '1', name: 'SMS Channel', type: 'sms' },
        { id: '2', name: 'Email Channel', type: 'email' },
      ];

      mockUseSWR.mockReturnValue({
        data: mockChannels,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useChannels('sms'));

      expect(result.current).toEqual({
        channels: mockChannels,
        isLoading: false,
      });
    });

    it('should return loading state when data is being fetched', () => {
      mockUseSWR.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      });

      const { result } = renderHook(() => useChannels('email'));

      expect(result.current).toEqual({
        channels: undefined,
        isLoading: true,
      });
    });

    it('should return undefined channels when no data', () => {
      mockUseSWR.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useChannels());

      expect(result.current).toEqual({
        channels: undefined,
        isLoading: false,
      });
    });

    it('should return null channels when API returns null', () => {
      mockUseSWR.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useChannels('line'));

      expect(result.current).toEqual({
        channels: null,
        isLoading: false,
      });
    });

    it('should return empty array when API returns empty array', () => {
      mockUseSWR.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useChannels('sms'));

      expect(result.current).toEqual({
        channels: [],
        isLoading: false,
      });
    });
  });

  describe('caching and revalidation', () => {
    it('should use the same SWR key for different type parameters', () => {
      mockUseSWR.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      const { rerender } = renderHook(({ type }) => useChannels(type), {
        initialProps: { type: 'sms' as any },
      });

      expect(useSWR).toHaveBeenCalledWith('/channels', expect.any(Function));

      rerender({ type: 'email' as any });

      // Should still use the same key for caching
      expect(useSWR).toHaveBeenCalledWith('/channels', expect.any(Function));
    });

    it('should maintain consistent SWR key across rerenders', () => {
      mockUseSWR.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      const { rerender } = renderHook(() => useChannels('line'));

      expect(useSWR).toHaveBeenCalledWith('/channels', expect.any(Function));

      rerender();

      // Should use the same key
      expect(useSWR).toHaveBeenCalledWith('/channels', expect.any(Function));
    });
  });

  describe('edge cases', () => {
    it('should handle undefined type parameter', () => {
      mockUseSWR.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      expect(() => renderHook(() => useChannels(undefined))).not.toThrow();
      expect(useSWR).toHaveBeenCalledWith('/channels', expect.any(Function));
    });

    it('should handle all valid message types', () => {
      mockUseSWR.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      const messageTypes = ['sms', 'email', 'line'] as const;

      messageTypes.forEach((type) => {
        renderHook(() => useChannels(type));
        expect(useSWR).toHaveBeenCalledWith('/channels', expect.any(Function));
      });
    });

    it('should work when SWR returns complex channel data', () => {
      const complexChannels = [
        {
          id: '1',
          name: 'Complex SMS Channel',
          type: 'sms',
          config: {
            apiKey: 'test-key',
            sender: '+1234567890',
          },
          isActive: true,
          createdAt: '2023-01-01T00:00:00Z',
        },
        {
          id: '2',
          name: 'Complex Email Channel',
          type: 'email',
          config: {
            smtpHost: 'smtp.example.com',
            smtpPort: 587,
            from: '<EMAIL>',
          },
          isActive: false,
          createdAt: '2023-01-02T00:00:00Z',
        },
      ];

      mockUseSWR.mockReturnValue({
        data: complexChannels,
        isLoading: false,
        error: null,
      });

      const { result } = renderHook(() => useChannels('sms'));

      expect(result.current).toEqual({
        channels: complexChannels,
        isLoading: false,
      });
    });
  });
});
