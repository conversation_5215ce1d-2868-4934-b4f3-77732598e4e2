import { ActivityAPI } from '@/services/api';
import { renderHook } from '@testing-library/react';
import { useParams } from 'react-router-dom';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useActivities } from './useActivity';

// Mock dependencies
vi.mock('@/services/api', () => ({
  ActivityAPI: {
    getList: vi.fn(),
  },
}));

vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

vi.mock('swr', () => ({
  default: vi.fn(),
}));

vi.mock('@/configs', () => ({
  default: {
    CDN_BASE_URL: 'https://cdn.example.com',
  },
}));

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('useActivities', () => {
  const mockMutate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockClear();
  });

  it('should be defined', () => {
    expect(useActivities).toBeDefined();
    expect(typeof useActivities).toBe('function');
  });

  it('should return correct structure when all params are present', () => {
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
      id: 'object1',
      recordId: 'record1',
    });

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: [],
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useActivities());

    expect(result.current).toEqual({
      activities: [],
      isLoading: false,
      mutate: mockMutate,
    });
  });

  it('should call useSWR with correct key when params are present', () => {
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
      id: 'object1',
      recordId: 'record1',
    });

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: [],
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useActivities());

    expect(useSWR).toHaveBeenCalledWith(
      'data/workspaces/workspace1/object1/record1/activities',
      expect.any(Function)
    );
  });

  it('should call useSWR with null key when params are missing', () => {
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: null,
      id: null,
      recordId: null,
    });

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useActivities());

    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should handle ActivityAPI.getList returning null', async () => {
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
      id: 'object1',
      recordId: 'record1',
    });

    (ActivityAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(null);

    let swrFetcher: Function = () => {};
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation((_key, fetcher) => {
      swrFetcher = fetcher;
      return {
        data: undefined,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useActivities());

    const result = await swrFetcher();
    expect(result).toEqual([]);
  });

  it('should sort activities by sentAt date in descending order', async () => {
    const mockActivities = [
      {
        id: '1',
        sentAt: '2023-01-01T10:00:00Z',
        source: { type: 'email' },
        action: 'sent',
        actor: { type: 'user' },
      },
      {
        id: '2',
        sentAt: '2023-01-02T10:00:00Z',
        source: { type: 'email' },
        action: 'opened',
        actor: { type: 'user' },
      },
    ];

    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
      id: 'object1',
      recordId: 'record1',
    });

    (ActivityAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockActivities);

    mockFetch.mockResolvedValue({
      text: async () => '<div>Template content</div>',
    });

    let swrFetcher: Function = () => {};
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation((_key, fetcher) => {
      swrFetcher = fetcher;
      return {
        data: undefined,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useActivities());

    const result = await swrFetcher();

    // Should be sorted with newer dates first
    expect(result[0].id).toBe('2');
    expect(result[1].id).toBe('1');
  });

  it('should fetch unique templates and map them to activities', async () => {
    const mockActivities = [
      {
        id: '1',
        sentAt: '2023-01-01T10:00:00Z',
        source: { type: 'email' },
        action: 'sent',
        actor: { type: 'user' },
      },
      {
        id: '2',
        sentAt: '2023-01-02T10:00:00Z',
        source: { type: 'email' },
        action: 'sent', // Same template as above
        actor: { type: 'user' },
      },
    ];

    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
      id: 'object1',
      recordId: 'record1',
    });

    (ActivityAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockActivities);

    mockFetch.mockResolvedValue({
      text: async () => '<div>Email sent template</div>',
    });

    let swrFetcher: Function = () => {};
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation((_key, fetcher) => {
      swrFetcher = fetcher;
      return {
        data: undefined,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useActivities());

    const result = await swrFetcher();

    // Should fetch template only once for unique template
    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(mockFetch).toHaveBeenCalledWith(
      'https://cdn.example.com/assets/templates/activities/email.sent.html'
    );

    // Both activities should have the template
    expect(result[0].template).toBe('<div>Email sent template</div>');
    expect(result[1].template).toBe('<div>Email sent template</div>');
  });

  it('should handle template fetch errors gracefully', async () => {
    const mockActivities = [
      {
        id: '1',
        sentAt: '2023-01-01T10:00:00Z',
        source: { type: 'email' },
        action: 'sent',
        actor: { type: 'user' },
      },
    ];

    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
      id: 'object1',
      recordId: 'record1',
    });

    (ActivityAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockActivities);

    mockFetch.mockRejectedValue(new Error('Template not found'));

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    let swrFetcher: Function = () => {};
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation((_key, fetcher) => {
      swrFetcher = fetcher;
      return {
        data: undefined,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useActivities());

    const result = await swrFetcher();

    expect(consoleSpy).toHaveBeenCalledWith(
      'Error fetching template for https://cdn.example.com/assets/templates/activities/email.sent.html:',
      expect.any(Error)
    );
    expect(result[0].template).toBe('');

    consoleSpy.mockRestore();
  });

  it('should skip template fetching for ignored actor types', async () => {
    const mockActivities = [
      {
        id: '1',
        sentAt: '2023-01-01T10:00:00Z',
        source: { type: 'email' },
        action: 'sent',
        actor: { type: 'user' },
      },
      {
        id: '2',
        sentAt: '2023-01-02T10:00:00Z',
        source: { type: 'notification' },
        action: 'created',
        actor: { type: 'mail' }, // This type is in IgnoreActivityActorType ['mail', 'sms', 'line']
      },
    ];

    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
      id: 'object1',
      recordId: 'record1',
    });

    (ActivityAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockActivities);

    mockFetch.mockResolvedValue({
      text: async () => '<div>Template content</div>',
    });

    let swrFetcher: Function = () => {};
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation((_key, fetcher) => {
      swrFetcher = fetcher;
      return {
        data: undefined,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useActivities());

    const result = await swrFetcher();

    // All activities should be returned
    expect(result).toHaveLength(2);
    // Should only fetch template for the non-ignored activity type (user actor type)
    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(mockFetch).toHaveBeenCalledWith(
      'https://cdn.example.com/assets/templates/activities/email.sent.html'
    );
  });

  it('should handle ActivityAPI.getList errors', async () => {
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
      id: 'object1',
      recordId: 'record1',
    });

    (ActivityAPI.getList as ReturnType<typeof vi.fn>).mockRejectedValue(new Error('API Error'));

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    let swrFetcher: Function = () => {};
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation((_key, fetcher) => {
      swrFetcher = fetcher;
      return {
        data: undefined,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useActivities());

    const result = await swrFetcher();

    expect(consoleSpy).toHaveBeenCalledWith('Error while fetching activities', expect.any(Error));
    expect(result).toEqual([]);

    consoleSpy.mockRestore();
  });

  it('should handle activities with missing sentAt dates', async () => {
    const mockActivities = [
      {
        id: '1',
        sentAt: '2023-01-01T10:00:00Z',
        source: { type: 'email' },
        action: 'sent',
        actor: { type: 'user' },
      },
      {
        id: '2',
        sentAt: null, // Missing sentAt
        source: { type: 'email' },
        action: 'opened',
        actor: { type: 'user' },
      },
    ];

    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace1',
      id: 'object1',
      recordId: 'record1',
    });

    (ActivityAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockActivities);

    mockFetch.mockResolvedValue({
      text: async () => '<div>Template content</div>',
    });

    let swrFetcher: Function = () => {};
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation((_key, fetcher) => {
      swrFetcher = fetcher;
      return {
        data: undefined,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useActivities());

    const result = await swrFetcher();

    // Should handle missing sentAt gracefully and still sort
    expect(result).toHaveLength(2);
    expect(result[0].id).toBe('1'); // Activity with valid date should be first
  });
});
