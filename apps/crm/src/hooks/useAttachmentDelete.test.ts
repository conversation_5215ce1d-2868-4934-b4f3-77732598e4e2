import { AttachmentAPI } from '@/services/api';
import { renderHook } from '@testing-library/react';
import useSWRMutation from 'swr/mutation';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useAttachmentDelete } from './useAttachmentDelete';

// Mock the AttachmentAPI
vi.mock('@/services/api', () => ({
  AttachmentAPI: {
    delete: vi.fn(),
  },
}));

// Mock useSWRMutation
vi.mock('swr/mutation', () => ({
  default: vi.fn(),
}));

describe('useAttachmentDelete', () => {
  const mockTrigger = vi.fn();
  const mockReset = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock useSWRMutation implementation
    (useSWRMutation as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      trigger: mockTrigger,
      reset: mockReset,
      data: null,
      error: null,
      isMutating: false,
    });
  });

  it('should be defined', () => {
    expect(useAttachmentDelete).toBeDefined();
    expect(typeof useAttachmentDelete).toBe('function');
  });

  it('should call useSWRMutation with correct key when all params are provided', () => {
    const props = {
      attachmentId: 'attachment-123',
      objectId: 'object-123',
      recordId: 'record-123',
      workspaceId: 'workspace-123',
    };

    renderHook(() => useAttachmentDelete(props));

    expect(useSWRMutation).toHaveBeenCalledWith(
      [props, 'attachments/delete'],
      expect.any(Function)
    );
  });

  it('should call useSWRMutation with falsy key when attachmentId is missing', () => {
    const props = {
      attachmentId: '',
      objectId: 'object-123',
      recordId: 'record-123',
      workspaceId: 'workspace-123',
    };

    renderHook(() => useAttachmentDelete(props));

    expect(useSWRMutation).toHaveBeenCalledWith('', expect.any(Function));
  });

  it('should call useSWRMutation with falsy key when objectId is missing', () => {
    const props = {
      attachmentId: 'attachment-123',
      objectId: '',
      recordId: 'record-123',
      workspaceId: 'workspace-123',
    };

    renderHook(() => useAttachmentDelete(props));

    expect(useSWRMutation).toHaveBeenCalledWith('', expect.any(Function));
  });

  it('should call useSWRMutation with falsy key when recordId is missing', () => {
    const props = {
      attachmentId: 'attachment-123',
      objectId: 'object-123',
      recordId: '',
      workspaceId: 'workspace-123',
    };

    renderHook(() => useAttachmentDelete(props));

    expect(useSWRMutation).toHaveBeenCalledWith('', expect.any(Function));
  });

  it('should call useSWRMutation with falsy key when workspaceId is missing', () => {
    const props = {
      attachmentId: 'attachment-123',
      objectId: 'object-123',
      recordId: 'record-123',
      workspaceId: '',
    };

    renderHook(() => useAttachmentDelete(props));

    expect(useSWRMutation).toHaveBeenCalledWith('', expect.any(Function));
  });

  it('should call useSWRMutation with falsy key when multiple params are missing', () => {
    const props = {
      attachmentId: '',
      objectId: '',
      recordId: 'record-123',
      workspaceId: 'workspace-123',
    };

    renderHook(() => useAttachmentDelete(props));

    expect(useSWRMutation).toHaveBeenCalledWith('', expect.any(Function));
  });

  it('should return the result from useSWRMutation', () => {
    const props = {
      attachmentId: 'attachment-123',
      objectId: 'object-123',
      recordId: 'record-123',
      workspaceId: 'workspace-123',
    };

    const { result } = renderHook(() => useAttachmentDelete(props));

    expect(result.current).toEqual({
      trigger: mockTrigger,
      reset: mockReset,
      data: null,
      error: null,
      isMutating: false,
    });
  });

  it('should pass props to AttachmentAPI.delete when mutation function is called', async () => {
    const props = {
      attachmentId: 'attachment-123',
      objectId: 'object-123',
      recordId: 'record-123',
      workspaceId: 'workspace-123',
    };

    let mutationFunction: Function = () => {};

    (useSWRMutation as unknown as ReturnType<typeof vi.fn>).mockImplementation((_key, fn) => {
      mutationFunction = fn;
      return {
        trigger: mockTrigger,
        reset: mockReset,
        data: null,
        error: null,
        isMutating: false,
      };
    });

    renderHook(() => useAttachmentDelete(props));

    // Call the mutation function
    await mutationFunction();

    expect(AttachmentAPI.delete).toHaveBeenCalledWith(props);
  });

  it('should handle undefined or null props gracefully', () => {
    const props = {
      attachmentId: null as any,
      objectId: undefined as any,
      recordId: 'record-123',
      workspaceId: 'workspace-123',
    };

    renderHook(() => useAttachmentDelete(props));

    expect(useSWRMutation).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should handle props with only whitespace as valid (since whitespace strings are truthy)', () => {
    const props = {
      attachmentId: '  ',
      objectId: 'object-123',
      recordId: 'record-123',
      workspaceId: 'workspace-123',
    };

    renderHook(() => useAttachmentDelete(props));

    expect(useSWRMutation).toHaveBeenCalledWith(
      [props, 'attachments/delete'],
      expect.any(Function)
    );
  });
});
