import { renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { IdentityAPI } from '@/services/api/identity';
import { useIdentities } from './useIdentities';

// Mock dependencies
vi.mock('swr');
vi.mock('@/services/api/identity', () => ({
  IdentityAPI: {
    getList: vi.fn(),
  },
}));

// Mock SWR
const mockUseSWR = useSWR as unknown as ReturnType<typeof vi.fn>;

describe('useIdentities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('SWR integration', () => {
    it('should call useSWR with correct parameters when all required props are provided', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useIdentities(props));

      expect(useSWR).toHaveBeenCalledWith([props, 'identities'], expect.any(Function));
    });

    it('should call useSWR with correct parameters including sort when provided', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
        sort: { createdAt: 'desc' as const },
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useIdentities(props));

      expect(useSWR).toHaveBeenCalledWith([props, 'identities'], expect.any(Function));
    });

    it('should not call useSWR when workspaceId is missing', () => {
      const props = {
        workspaceId: '',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useIdentities(props));

      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });

    it('should not call useSWR when objectId is missing', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: '',
        recordId: 'record-789',
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useIdentities(props));

      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });

    it('should not call useSWR when recordId is missing', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: '',
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useIdentities(props));

      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });

    it('should not call useSWR when all required parameters are missing', () => {
      const props = {
        workspaceId: '',
        objectId: '',
        recordId: '',
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useIdentities(props));

      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });
  });

  describe('fetcher function', () => {
    it('should call IdentityAPI.getList with correct parameters', async () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const mockIdentities = [
        { id: '1', type: 'email', value: '<EMAIL>', verified: true },
        { id: '2', type: 'phone', value: '+1234567890', verified: false },
      ];

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: mockIdentities,
          error: null,
          isLoading: false,
          mutate: vi.fn(),
        };
      });

      (IdentityAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockIdentities);

      renderHook(() => useIdentities(props));

      const result = await swrFetcher();

      expect(IdentityAPI.getList).toHaveBeenCalledWith(props);
      expect(result).toEqual(mockIdentities);
    });

    it('should call IdentityAPI.getList with sort parameter when provided', async () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
        sort: { updatedAt: 'asc' as const },
      };

      const mockIdentities = [
        { id: '1', type: 'email', value: '<EMAIL>' },
        { id: '2', type: 'phone', value: '+1234567890' },
      ];

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: mockIdentities,
          error: null,
          isLoading: false,
          mutate: vi.fn(),
        };
      });

      (IdentityAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockIdentities);

      renderHook(() => useIdentities(props));

      const result = await swrFetcher();

      expect(IdentityAPI.getList).toHaveBeenCalledWith(props);
      expect(result).toEqual(mockIdentities);
    });

    it('should handle API errors gracefully', async () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const apiError = new Error('API request failed');

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: null,
          error: apiError,
          isLoading: false,
          mutate: vi.fn(),
        };
      });

      (IdentityAPI.getList as ReturnType<typeof vi.fn>).mockRejectedValue(apiError);

      renderHook(() => useIdentities(props));

      await expect(swrFetcher()).rejects.toThrow('API request failed');
      expect(IdentityAPI.getList).toHaveBeenCalledWith(props);
    });
  });

  describe('return value', () => {
    it('should return SWR result object', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const mockSWRResult = {
        data: [{ id: '1', type: 'email', value: '<EMAIL>' }],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      };

      mockUseSWR.mockReturnValue(mockSWRResult);

      const { result } = renderHook(() => useIdentities(props));

      expect(result.current).toEqual(mockSWRResult);
    });

    it('should return loading state when data is being fetched', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const mockSWRResult = {
        data: undefined,
        error: null,
        isLoading: true,
        mutate: vi.fn(),
      };

      mockUseSWR.mockReturnValue(mockSWRResult);

      const { result } = renderHook(() => useIdentities(props));

      expect(result.current).toEqual(mockSWRResult);
      expect(result.current.isLoading).toBe(true);
    });

    it('should return error state when API request fails', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const apiError = new Error('Network error');
      const mockSWRResult = {
        data: null,
        error: apiError,
        isLoading: false,
        mutate: vi.fn(),
      };

      mockUseSWR.mockReturnValue(mockSWRResult);

      const { result } = renderHook(() => useIdentities(props));

      expect(result.current).toEqual(mockSWRResult);
      expect(result.current.error).toBe(apiError);
    });
  });

  describe('caching and revalidation', () => {
    it('should use consistent SWR key for same parameters', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { rerender } = renderHook(() => useIdentities(props));

      expect(useSWR).toHaveBeenCalledWith([props, 'identities'], expect.any(Function));

      rerender();

      // Should still use the same key
      expect(useSWR).toHaveBeenCalledWith([props, 'identities'], expect.any(Function));
    });

    it('should generate different SWR keys for different parameters', () => {
      const props1 = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const props2 = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-999',
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { rerender } = renderHook(({ props }) => useIdentities(props), {
        initialProps: { props: props1 },
      });

      expect(useSWR).toHaveBeenCalledWith([props1, 'identities'], expect.any(Function));

      rerender({ props: props2 });

      expect(useSWR).toHaveBeenCalledWith([props2, 'identities'], expect.any(Function));
    });
  });

  describe('edge cases', () => {
    it('should handle null/undefined parameters gracefully', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: null as any,
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      expect(() => renderHook(() => useIdentities(props))).not.toThrow();
    });

    it('should handle empty string parameters correctly', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: '',
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useIdentities(props));

      // Should not make the API call due to falsy recordId
      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });

    it('should handle complex sort parameters', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
        sort: {
          createdAt: 'desc' as const,
          type: 'asc' as const,
        },
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useIdentities(props));

      expect(useSWR).toHaveBeenCalledWith([props, 'identities'], expect.any(Function));
    });

    it('should work with different identity types', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const complexIdentities = [
        {
          id: '1',
          type: 'email',
          value: '<EMAIL>',
          provider: 'custom',
          displayName: 'John Doe',
          verified: true,
          primary: true,
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-02T00:00:00Z',
        },
        {
          id: '2',
          type: 'phone',
          value: '******-123-4567',
          provider: 'twilio',
          displayName: 'John Mobile',
          verified: false,
          primary: false,
          createdAt: '2023-01-03T00:00:00Z',
          updatedAt: '2023-01-04T00:00:00Z',
        },
        {
          id: '3',
          type: 'social',
          value: '@johndoe',
          provider: 'twitter',
          displayName: 'John on Twitter',
          verified: true,
          primary: false,
          createdAt: '2023-01-05T00:00:00Z',
          updatedAt: '2023-01-06T00:00:00Z',
        },
      ];

      mockUseSWR.mockReturnValue({
        data: complexIdentities,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(() => useIdentities(props));

      expect(result.current.data).toEqual(complexIdentities);
    });

    it('should handle empty identity list', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { result } = renderHook(() => useIdentities(props));

      expect(result.current.data).toEqual([]);
    });
  });
});
