import { renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { AttachmentAPI } from '@/services/api';
import { useAttachments } from './useAttachments';

// Mock dependencies
vi.mock('swr');
vi.mock('@/services/api', () => ({
  AttachmentAPI: {
    getList: vi.fn(),
  },
}));

// Mock SWR
const mockUseSWR = useSWR as unknown as ReturnType<typeof vi.fn>;

describe('useAttachments', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('SWR integration', () => {
    it('should call useSWR with correct parameters when all required props are provided', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useAttachments(props));

      expect(useSWR).toHaveBeenCalledWith([props, 'attachments'], expect.any(Function));
    });

    it('should call useSWR with correct parameters including sort when provided', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
        sort: { title: 'asc' as const },
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useAttachments(props));

      expect(useSWR).toHaveBeenCalledWith([props, 'attachments'], expect.any(Function));
    });

    it('should not call useSWR when workspaceId is missing', () => {
      const props = {
        workspaceId: '',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useAttachments(props));

      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });

    it('should not call useSWR when objectId is missing', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: '',
        recordId: 'record-789',
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useAttachments(props));

      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });

    it('should not call useSWR when recordId is missing', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: '',
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useAttachments(props));

      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });

    it('should not call useSWR when all required parameters are missing', () => {
      const props = {
        workspaceId: '',
        objectId: '',
        recordId: '',
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useAttachments(props));

      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });
  });

  describe('fetcher function', () => {
    it('should call AttachmentAPI.getList with correct parameters', async () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const mockAttachments = [
        { id: '1', name: 'document1.pdf' },
        { id: '2', name: 'image1.jpg' },
      ];

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: mockAttachments,
          error: null,
          isLoading: false,
          mutate: vi.fn(),
        };
      });

      (AttachmentAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockAttachments);

      renderHook(() => useAttachments(props));

      const result = await swrFetcher();

      expect(AttachmentAPI.getList).toHaveBeenCalledWith(props);
      expect(result).toEqual(mockAttachments);
    });

    it('should call AttachmentAPI.getList with sort parameter when provided', async () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
        sort: { title: 'desc' as const },
      };

      const mockAttachments = [
        { id: '1', name: 'document1.pdf' },
        { id: '2', name: 'image1.jpg' },
      ];

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: mockAttachments,
          error: null,
          isLoading: false,
          mutate: vi.fn(),
        };
      });

      (AttachmentAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockAttachments);

      renderHook(() => useAttachments(props));

      const result = await swrFetcher();

      expect(AttachmentAPI.getList).toHaveBeenCalledWith(props);
      expect(result).toEqual(mockAttachments);
    });

    it('should handle API errors gracefully', async () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const apiError = new Error('API request failed');

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: null,
          error: apiError,
          isLoading: false,
          mutate: vi.fn(),
        };
      });

      (AttachmentAPI.getList as ReturnType<typeof vi.fn>).mockRejectedValue(apiError);

      renderHook(() => useAttachments(props));

      await expect(swrFetcher()).rejects.toThrow('API request failed');
      expect(AttachmentAPI.getList).toHaveBeenCalledWith(props);
    });
  });

  describe('return value', () => {
    it('should return SWR result object', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const mockSWRResult = {
        data: [{ id: '1', name: 'document1.pdf' }],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      };

      mockUseSWR.mockReturnValue(mockSWRResult);

      const { result } = renderHook(() => useAttachments(props));

      expect(result.current).toEqual(mockSWRResult);
    });

    it('should return loading state when data is being fetched', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const mockSWRResult = {
        data: undefined,
        error: null,
        isLoading: true,
        mutate: vi.fn(),
      };

      mockUseSWR.mockReturnValue(mockSWRResult);

      const { result } = renderHook(() => useAttachments(props));

      expect(result.current).toEqual(mockSWRResult);
      expect(result.current.isLoading).toBe(true);
    });

    it('should return error state when API request fails', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const apiError = new Error('Network error');
      const mockSWRResult = {
        data: null,
        error: apiError,
        isLoading: false,
        mutate: vi.fn(),
      };

      mockUseSWR.mockReturnValue(mockSWRResult);

      const { result } = renderHook(() => useAttachments(props));

      expect(result.current).toEqual(mockSWRResult);
      expect(result.current.error).toBe(apiError);
    });
  });

  describe('caching and revalidation', () => {
    it('should use consistent SWR key for same parameters', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { rerender } = renderHook(() => useAttachments(props));

      expect(useSWR).toHaveBeenCalledWith([props, 'attachments'], expect.any(Function));

      rerender();

      // Should still use the same key
      expect(useSWR).toHaveBeenCalledWith([props, 'attachments'], expect.any(Function));
    });

    it('should generate different SWR keys for different parameters', () => {
      const props1 = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
      };

      const props2 = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-999',
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      const { rerender } = renderHook(({ props }) => useAttachments(props), {
        initialProps: { props: props1 },
      });

      expect(useSWR).toHaveBeenCalledWith([props1, 'attachments'], expect.any(Function));

      rerender({ props: props2 });

      expect(useSWR).toHaveBeenCalledWith([props2, 'attachments'], expect.any(Function));
    });
  });

  describe('edge cases', () => {
    it('should handle null/undefined parameters gracefully', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: null as any,
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      expect(() => renderHook(() => useAttachments(props))).not.toThrow();
    });

    it('should handle empty string parameters correctly', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: '', // empty string
      };

      mockUseSWR.mockReturnValue({
        data: null,
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useAttachments(props));

      // Should not make the API call due to falsy recordId
      expect(useSWR).toHaveBeenCalledWith('', expect.any(Function));
    });

    it('should handle complex sort parameters', () => {
      const props = {
        workspaceId: 'workspace-123',
        objectId: 'object-456',
        recordId: 'record-789',
        sort: {
          createdAt: 'desc' as const,
          title: 'asc' as const,
        },
      };

      mockUseSWR.mockReturnValue({
        data: [],
        error: null,
        isLoading: false,
        mutate: vi.fn(),
      });

      renderHook(() => useAttachments(props));

      expect(useSWR).toHaveBeenCalledWith([props, 'attachments'], expect.any(Function));
    });
  });
});
