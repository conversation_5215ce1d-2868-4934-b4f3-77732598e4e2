import { WorkspaceAPI } from '@/services/api';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useTemplates } from './useTemplates';

// Mock the services
vi.mock('@/services/api', () => ({
  WorkspaceAPI: {
    getTemplates: vi.fn(),
  },
}));

// Mock SWR
vi.mock('swr');

// Mock React
vi.mock('react', async (importOriginal) => {
  const actual = await importOriginal<typeof import('react')>();
  return {
    ...actual,
    useMemo: vi.fn((fn) => fn()),
  };
});

// Mock SWR
const mockUseSWR = useSWR as unknown as ReturnType<typeof vi.fn>;

describe('useTemplates', () => {
  const mockTemplates = [
    { id: 'template-1', name: 'Template 1', content: 'Template content 1' },
    { id: 'template-2', name: 'Template 2', content: 'Template content 2' },
  ];
  const mockMutate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return templates when wsId is provided', () => {
    mockUseSWR.mockReturnValue({
      data: mockTemplates,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useTemplates('ws-1'));

    expect(result.current.templates).toEqual(mockTemplates);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mutateTemplates).toBe(mockMutate);
  });

  it('should call SWR with correct key when wsId is provided', () => {
    mockUseSWR.mockReturnValue({
      data: mockTemplates,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useTemplates('ws-1'));

    expect(mockUseSWR).toHaveBeenCalledWith('ws-1/templates', expect.any(Function));
  });

  it('should call SWR with null key when wsId is not provided', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useTemplates());

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should call SWR with null key when wsId is empty string', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useTemplates(''));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should call SWR with null key when wsId is undefined', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useTemplates(undefined));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should return loading state when SWR is loading', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: true,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useTemplates('ws-1'));

    expect(result.current.templates).toBeUndefined();
    expect(result.current.isLoading).toBe(true);
    expect(result.current.mutateTemplates).toBe(mockMutate);
  });

  it('should call WorkspaceAPI.getTemplates with correct parameters when no filters', () => {
    const mockFetcher = vi.fn().mockResolvedValue(mockTemplates);
    (WorkspaceAPI.getTemplates as any).mockImplementation(mockFetcher);

    mockUseSWR.mockImplementation((key, fetcher) => {
      if (key && fetcher) {
        fetcher();
      }
      return {
        data: mockTemplates,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useTemplates('ws-1'));

    expect(WorkspaceAPI.getTemplates).toHaveBeenCalledWith('ws-1', undefined);
  });

  it('should call WorkspaceAPI.getTemplates with filters parameter when provided', () => {
    const mockFetcher = vi.fn().mockResolvedValue(mockTemplates);
    (WorkspaceAPI.getTemplates as any).mockImplementation(mockFetcher);

    mockUseSWR.mockImplementation((key, fetcher) => {
      if (key && fetcher) {
        fetcher();
      }
      return {
        data: mockTemplates,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useTemplates('ws-1', 'status=active'));

    expect(WorkspaceAPI.getTemplates).toHaveBeenCalledWith('ws-1', 'status=active');
  });

  it('should use React.useMemo to memoize the return value', () => {
    const mockUseMemo = vi.fn((fn) => fn());
    const React = require('react');
    React.useMemo = mockUseMemo;

    mockUseSWR.mockReturnValue({
      data: mockTemplates,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useTemplates('ws-1'));

    expect(mockUseMemo).toHaveBeenCalledWith(expect.any(Function), [mockTemplates]);
  });

  it('should handle different template data types', () => {
    const testCases = [
      [],
      [{ id: 'single', name: 'Single Template' }],
      mockTemplates,
      [{ id: 'complex', name: 'Complex', metadata: { version: '1.0' } }],
    ];

    testCases.forEach((templates) => {
      mockUseSWR.mockReturnValue({
        data: templates,
        isLoading: false,
        mutate: mockMutate,
      });

      const { result } = renderHook(() => useTemplates('ws-1'));

      expect(result.current.templates).toEqual(templates);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.mutateTemplates).toBe(mockMutate);
    });
  });

  it('should handle undefined data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useTemplates('ws-1'));

    expect(result.current.templates).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mutateTemplates).toBe(mockMutate);
  });

  it('should handle null data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useTemplates('ws-1'));

    expect(result.current.templates).toBeNull();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mutateTemplates).toBe(mockMutate);
  });

  it('should return consistent structure regardless of SWR state', () => {
    const states = [
      { data: mockTemplates, isLoading: false, mutate: mockMutate },
      { data: undefined, isLoading: true, mutate: mockMutate },
      { data: null, isLoading: false, mutate: mockMutate },
      { data: [], isLoading: false, mutate: mockMutate },
    ];

    states.forEach((state) => {
      mockUseSWR.mockReturnValue(state);

      const { result } = renderHook(() => useTemplates('ws-1'));

      expect(result.current).toHaveProperty('templates');
      expect(result.current).toHaveProperty('isLoading');
      expect(result.current).toHaveProperty('mutateTemplates');
      expect(typeof result.current.isLoading).toBe('boolean');
      expect(typeof result.current.mutateTemplates).toBe('function');
    });
  });

  it('should handle complex parameter combinations', () => {
    const testCases = [
      { wsId: 'ws-1' as string | undefined, filters: undefined as string | undefined },
      {
        wsId: 'workspace-123' as string | undefined,
        filters: 'category=email' as string | undefined,
      },
      { wsId: 'ws' as string | undefined, filters: '' as string | undefined },
      { wsId: undefined as string | undefined, filters: 'filter=test' as string | undefined },
      { wsId: '' as string | undefined, filters: 'status=draft' as string | undefined },
    ];

    testCases.forEach(({ wsId, filters }) => {
      mockUseSWR.mockReturnValue({
        data: wsId ? mockTemplates : undefined,
        isLoading: false,
        mutate: mockMutate,
      });

      const { result } = renderHook(() => useTemplates(wsId, filters));

      expect(result.current.templates).toEqual(wsId ? mockTemplates : undefined);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.mutateTemplates).toBe(mockMutate);

      const expectedKey = wsId ? `${wsId}/templates` : null;
      expect(mockUseSWR).toHaveBeenCalledWith(expectedKey, expect.any(Function));
    });
  });

  it('should not make API call when wsId is not provided', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useTemplates());

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
    expect(WorkspaceAPI.getTemplates).not.toHaveBeenCalled();
  });

  it('should handle rerendering with different parameters', () => {
    mockUseSWR.mockReturnValue({
      data: mockTemplates,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result, rerender } = renderHook(({ wsId, filters }) => useTemplates(wsId, filters), {
      initialProps: { wsId: 'ws-1', filters: '' },
    });

    expect(result.current.templates).toEqual(mockTemplates);

    rerender({ wsId: 'ws-2', filters: 'status=active' });

    expect(result.current.templates).toEqual(mockTemplates);
  });

  it('should handle empty filters parameter', () => {
    const mockFetcher = vi.fn().mockResolvedValue(mockTemplates);
    (WorkspaceAPI.getTemplates as any).mockImplementation(mockFetcher);

    mockUseSWR.mockImplementation((key, fetcher) => {
      if (key && fetcher) {
        fetcher();
      }
      return {
        data: mockTemplates,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useTemplates('ws-1', ''));

    expect(WorkspaceAPI.getTemplates).toHaveBeenCalledWith('ws-1', '');
  });

  it('should handle null wsId parameter', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useTemplates(null as any));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should properly memoize with only templates dependency', () => {
    const mockUseMemo = vi.fn((fn) => fn());
    const React = require('react');
    React.useMemo = mockUseMemo;

    mockUseSWR.mockReturnValue({
      data: mockTemplates,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useTemplates('ws-1'));

    // Verify that useMemo is called with only templates in the dependency array
    expect(mockUseMemo).toHaveBeenCalledWith(expect.any(Function), [mockTemplates]);
  });

  it('should handle API errors gracefully', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
      error: new Error('API Error'),
    });

    const { result } = renderHook(() => useTemplates('ws-1'));

    expect(result.current.templates).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mutateTemplates).toBe(mockMutate);
  });
});
