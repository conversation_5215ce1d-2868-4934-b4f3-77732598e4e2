import { renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { TagAPI } from '@/services/api';
import { useTags } from './useTags';

// Mock dependencies
vi.mock('swr');
vi.mock('@/services/api', () => ({
  TagAPI: {
    getList: vi.fn(),
  },
}));

// Mock SWR
const mockUseSWR = useSWR as unknown as ReturnType<typeof vi.fn>;

describe('useTags', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('SWR integration', () => {
    it('should call useSWR with correct parameters when both wsId and objId are provided', () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';

      mockUseSWR.mockReturnValue({
        data: [],
        mutate: vi.fn(),
        isLoading: false,
      });

      renderHook(() => useTags(wsId, objId));

      expect(useSWR).toHaveBeenCalledWith(
        `data/workspaces/${wsId}/tags/${objId}`,
        expect.any(Function),
        { revalidateIfStale: true }
      );
    });

    it('should call useSWR with null key when wsId is missing', () => {
      const wsId = null;
      const objId = 'object-456';

      mockUseSWR.mockReturnValue({
        data: null,
        mutate: vi.fn(),
        isLoading: false,
      });

      renderHook(() => useTags(wsId, objId));

      expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), { revalidateIfStale: true });
    });

    it('should call useSWR with null key when objId is missing', () => {
      const wsId = 'workspace-123';
      const objId = null;

      mockUseSWR.mockReturnValue({
        data: null,
        mutate: vi.fn(),
        isLoading: false,
      });

      renderHook(() => useTags(wsId, objId));

      expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), { revalidateIfStale: true });
    });

    it('should call useSWR with null key when both parameters are missing', () => {
      const wsId = null;
      const objId = null;

      mockUseSWR.mockReturnValue({
        data: null,
        mutate: vi.fn(),
        isLoading: false,
      });

      renderHook(() => useTags(wsId, objId));

      expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), { revalidateIfStale: true });
    });

    it('should call useSWR with null key when wsId is empty string', () => {
      const wsId = '';
      const objId = 'object-456';

      mockUseSWR.mockReturnValue({
        data: null,
        mutate: vi.fn(),
        isLoading: false,
      });

      renderHook(() => useTags(wsId, objId));

      expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), { revalidateIfStale: true });
    });

    it('should call useSWR with null key when objId is empty string', () => {
      const wsId = 'workspace-123';
      const objId = '';

      mockUseSWR.mockReturnValue({
        data: null,
        mutate: vi.fn(),
        isLoading: false,
      });

      renderHook(() => useTags(wsId, objId));

      expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), { revalidateIfStale: true });
    });
  });

  describe('fetcher function', () => {
    it('should call TagAPI.getList with correct parameters', async () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';
      const mockTags = [
        { id: '1', name: 'important', color: '#ff0000' },
        { id: '2', name: 'urgent', color: '#00ff00' },
      ];

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: mockTags,
          mutate: vi.fn(),
          isLoading: false,
        };
      });

      (TagAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue(mockTags);

      renderHook(() => useTags(wsId, objId));

      const result = await swrFetcher();

      expect(TagAPI.getList).toHaveBeenCalledWith(wsId, objId);
      expect(result).toEqual(mockTags);
    });

    it('should call TagAPI.getList with empty strings when parameters are null', async () => {
      const wsId = null;
      const objId = null;

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: [],
          mutate: vi.fn(),
          isLoading: false,
        };
      });

      (TagAPI.getList as ReturnType<typeof vi.fn>).mockResolvedValue([]);

      renderHook(() => useTags(wsId, objId));

      const result = await swrFetcher();

      expect(TagAPI.getList).toHaveBeenCalledWith('', '');
      expect(result).toEqual([]);
    });

    it('should handle API errors gracefully', async () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';
      const apiError = new Error('API request failed');

      let swrFetcher: Function = () => {};
      mockUseSWR.mockImplementation((_key, fetcher) => {
        swrFetcher = fetcher;
        return {
          data: null,
          mutate: vi.fn(),
          isLoading: false,
          error: apiError,
        };
      });

      (TagAPI.getList as ReturnType<typeof vi.fn>).mockRejectedValue(apiError);

      renderHook(() => useTags(wsId, objId));

      await expect(swrFetcher()).rejects.toThrow('API request failed');
      expect(TagAPI.getList).toHaveBeenCalledWith(wsId, objId);
    });
  });

  describe('return value', () => {
    it('should return object with tags and mutate', () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';
      const mockTags = [{ id: '1', name: 'test', color: '#000000' }];
      const mockMutate = vi.fn();

      mockUseSWR.mockReturnValue({
        data: mockTags,
        mutate: mockMutate,
        isLoading: false,
      });

      const { result } = renderHook(() => useTags(wsId, objId));

      expect(result.current).toEqual({
        tags: mockTags,
        mutate: mockMutate,
      });
    });

    it('should return undefined tags when no data', () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';
      const mockMutate = vi.fn();

      mockUseSWR.mockReturnValue({
        data: undefined,
        mutate: mockMutate,
        isLoading: false,
      });

      const { result } = renderHook(() => useTags(wsId, objId));

      expect(result.current).toEqual({
        tags: undefined,
        mutate: mockMutate,
      });
    });

    it('should return null tags when API returns null', () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';
      const mockMutate = vi.fn();

      mockUseSWR.mockReturnValue({
        data: null,
        mutate: mockMutate,
        isLoading: false,
      });

      const { result } = renderHook(() => useTags(wsId, objId));

      expect(result.current).toEqual({
        tags: null,
        mutate: mockMutate,
      });
    });
  });

  describe('SWR options', () => {
    it('should pass revalidateIfStale option to useSWR', () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';

      mockUseSWR.mockReturnValue({
        data: [],
        mutate: vi.fn(),
        isLoading: false,
      });

      renderHook(() => useTags(wsId, objId));

      expect(useSWR).toHaveBeenCalledWith(expect.any(String), expect.any(Function), {
        revalidateIfStale: true,
      });
    });
  });

  describe('caching and revalidation', () => {
    it('should generate different SWR keys for different parameters', () => {
      mockUseSWR.mockReturnValue({
        data: [],
        mutate: vi.fn(),
        isLoading: false,
      });

      const { rerender } = renderHook(({ wsId, objId }) => useTags(wsId, objId), {
        initialProps: { wsId: 'workspace-1', objId: 'object-1' },
      });

      expect(useSWR).toHaveBeenCalledWith(
        'data/workspaces/workspace-1/tags/object-1',
        expect.any(Function),
        { revalidateIfStale: true }
      );

      rerender({ wsId: 'workspace-2', objId: 'object-2' });

      expect(useSWR).toHaveBeenCalledWith(
        'data/workspaces/workspace-2/tags/object-2',
        expect.any(Function),
        { revalidateIfStale: true }
      );
    });

    it('should use consistent SWR key for same parameters', () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';

      mockUseSWR.mockReturnValue({
        data: [],
        mutate: vi.fn(),
        isLoading: false,
      });

      const { rerender } = renderHook(() => useTags(wsId, objId));

      expect(useSWR).toHaveBeenCalledWith(
        `data/workspaces/${wsId}/tags/${objId}`,
        expect.any(Function),
        { revalidateIfStale: true }
      );

      rerender();

      // Should still use the same key
      expect(useSWR).toHaveBeenCalledWith(
        `data/workspaces/${wsId}/tags/${objId}`,
        expect.any(Function),
        { revalidateIfStale: true }
      );
    });
  });

  describe('edge cases', () => {
    it('should handle undefined parameters gracefully', () => {
      const wsId = undefined;
      const objId = undefined;

      mockUseSWR.mockReturnValue({
        data: null,
        mutate: vi.fn(),
        isLoading: false,
      });

      expect(() => renderHook(() => useTags(wsId, objId))).not.toThrow();

      expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), { revalidateIfStale: true });
    });

    it('should handle mixed valid and invalid parameters', () => {
      const wsId = 'workspace-123';
      const objId = undefined;

      mockUseSWR.mockReturnValue({
        data: null,
        mutate: vi.fn(),
        isLoading: false,
      });

      renderHook(() => useTags(wsId, objId));

      expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), { revalidateIfStale: true });
    });

    it('should work with complex tag data', () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';
      const complexTags = [
        {
          id: '1',
          name: 'High Priority',
          value: 'high-priority',
          color: '#ff4444',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-02T00:00:00Z',
          objectId: 'object-456',
        },
        {
          id: '2',
          name: 'Customer Request',
          value: 'customer-request',
          color: '#44ff44',
          createdAt: '2023-01-03T00:00:00Z',
          updatedAt: '2023-01-04T00:00:00Z',
          objectId: 'object-456',
        },
      ];

      const mockMutate = vi.fn();

      mockUseSWR.mockReturnValue({
        data: complexTags,
        mutate: mockMutate,
        isLoading: false,
      });

      const { result } = renderHook(() => useTags(wsId, objId));

      expect(result.current).toEqual({
        tags: complexTags,
        mutate: mockMutate,
      });
    });

    it('should handle empty tag list', () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';
      const mockMutate = vi.fn();

      mockUseSWR.mockReturnValue({
        data: [],
        mutate: mockMutate,
        isLoading: false,
      });

      const { result } = renderHook(() => useTags(wsId, objId));

      expect(result.current).toEqual({
        tags: [],
        mutate: mockMutate,
      });
    });

    it('should handle loading state properly', () => {
      const wsId = 'workspace-123';
      const objId = 'object-456';
      const mockMutate = vi.fn();

      mockUseSWR.mockReturnValue({
        data: undefined,
        mutate: mockMutate,
        isLoading: true,
      });

      const { result } = renderHook(() => useTags(wsId, objId));

      expect(result.current).toEqual({
        tags: undefined,
        mutate: mockMutate,
      });
    });
  });
});
