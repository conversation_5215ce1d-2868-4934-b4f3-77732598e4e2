import { RecordAPI } from '@/services/api';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useRecordsCount } from './useRecordsCount';

// Mock the services
vi.mock('@/services/api', () => ({
  RecordAPI: {
    recordsCount: vi.fn(),
  },
}));

// Mock SWR
vi.mock('swr');

// Mock React
vi.mock('react', async (importOriginal) => {
  const actual = await importOriginal<typeof import('react')>();
  return {
    ...actual,
    useMemo: vi.fn((fn) => fn()),
  };
});

// Mock SWR
const mockUseSWR = useSWR as unknown as ReturnType<typeof vi.fn>;

describe('useRecordsCount', () => {
  const mockRecordsCount = 42;
  const mockMutate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return records count when all parameters are provided', () => {
    mockUseSWR.mockReturnValue({
      data: mockRecordsCount,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useRecordsCount('ws-1', 'obj-1'));

    expect(result.current.recordsCount).toBe(mockRecordsCount);
    expect(result.current.recordsCountLoading).toBe(false);
    expect(result.current.mutateRecordsCount).toBe(mockMutate);
  });

  it('should call SWR with correct key when all parameters are provided and enabled is true', () => {
    mockUseSWR.mockReturnValue({
      data: mockRecordsCount,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount('ws-1', 'obj-1', true));

    expect(mockUseSWR).toHaveBeenCalledWith('data/ws-1/obj-1/recordsCount', expect.any(Function));
  });

  it('should call SWR with correct key when enabled is true by default', () => {
    mockUseSWR.mockReturnValue({
      data: mockRecordsCount,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount('ws-1', 'obj-1'));

    expect(mockUseSWR).toHaveBeenCalledWith('data/ws-1/obj-1/recordsCount', expect.any(Function));
  });

  it('should call SWR with null key when enabled is false', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount('ws-1', 'obj-1', false));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should call SWR with null key when wsId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount('', 'obj-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should call SWR with null key when objectId is missing', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount('ws-1', ''));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should call SWR with null key when both wsId and objectId are missing', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount('', ''));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should call SWR with null key when wsId is null', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount(null as any, 'obj-1'));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should call SWR with null key when objectId is null', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount('ws-1', null as any));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should return loading state when SWR is loading', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: true,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useRecordsCount('ws-1', 'obj-1'));

    expect(result.current.recordsCount).toBeUndefined();
    expect(result.current.recordsCountLoading).toBe(true);
    expect(result.current.mutateRecordsCount).toBe(mockMutate);
  });

  it('should call RecordAPI.recordsCount with correct parameters', () => {
    const mockFetcher = vi.fn().mockResolvedValue(mockRecordsCount);
    (RecordAPI.recordsCount as any).mockImplementation(mockFetcher);

    mockUseSWR.mockImplementation((key, fetcher) => {
      if (key && fetcher) {
        fetcher();
      }
      return {
        data: mockRecordsCount,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useRecordsCount('ws-1', 'obj-1'));

    expect(RecordAPI.recordsCount).toHaveBeenCalledWith('ws-1', 'obj-1');
  });

  it('should call RecordAPI.recordsCount with empty strings when parameters are null', () => {
    const mockFetcher = vi.fn().mockResolvedValue(mockRecordsCount);
    (RecordAPI.recordsCount as any).mockImplementation(mockFetcher);

    mockUseSWR.mockImplementation((_key, fetcher) => {
      if (fetcher) {
        fetcher();
      }
      return {
        data: undefined,
        isLoading: false,
        mutate: mockMutate,
      };
    });

    renderHook(() => useRecordsCount(null as any, null as any));

    expect(RecordAPI.recordsCount).toHaveBeenCalledWith('', '');
  });

  it('should use React.useMemo to memoize the return value', () => {
    const mockUseMemo = vi.fn((fn) => fn());
    const React = require('react');
    React.useMemo = mockUseMemo;

    mockUseSWR.mockReturnValue({
      data: mockRecordsCount,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount('ws-1', 'obj-1'));

    expect(mockUseMemo).toHaveBeenCalledWith(expect.any(Function), [
      mockRecordsCount,
      false,
      mockMutate,
    ]);
  });

  it('should handle different count values', () => {
    const testCases = [0, 1, 100, 999, 1000];

    testCases.forEach((count) => {
      mockUseSWR.mockReturnValue({
        data: count,
        isLoading: false,
        mutate: mockMutate,
      });

      const { result } = renderHook(() => useRecordsCount('ws-1', 'obj-1'));

      expect(result.current.recordsCount).toBe(count);
      expect(result.current.recordsCountLoading).toBe(false);
      expect(result.current.mutateRecordsCount).toBe(mockMutate);
    });
  });

  it('should handle undefined data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useRecordsCount('ws-1', 'obj-1'));

    expect(result.current.recordsCount).toBeUndefined();
    expect(result.current.recordsCountLoading).toBe(false);
    expect(result.current.mutateRecordsCount).toBe(mockMutate);
  });

  it('should handle null data from SWR', () => {
    mockUseSWR.mockReturnValue({
      data: null,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result } = renderHook(() => useRecordsCount('ws-1', 'obj-1'));

    expect(result.current.recordsCount).toBeNull();
    expect(result.current.recordsCountLoading).toBe(false);
    expect(result.current.mutateRecordsCount).toBe(mockMutate);
  });

  it('should return consistent structure regardless of SWR state', () => {
    const states = [
      { data: mockRecordsCount, isLoading: false, mutate: mockMutate },
      { data: undefined, isLoading: true, mutate: mockMutate },
      { data: null, isLoading: false, mutate: mockMutate },
      { data: 0, isLoading: false, mutate: mockMutate },
    ];

    states.forEach((state) => {
      mockUseSWR.mockReturnValue(state);

      const { result } = renderHook(() => useRecordsCount('ws-1', 'obj-1'));

      expect(result.current).toHaveProperty('recordsCount');
      expect(result.current).toHaveProperty('recordsCountLoading');
      expect(result.current).toHaveProperty('mutateRecordsCount');
      expect(typeof result.current.recordsCountLoading).toBe('boolean');
      expect(typeof result.current.mutateRecordsCount).toBe('function');
    });
  });

  it('should handle complex parameter combinations with enabled flag', () => {
    const testCases: [string, string, boolean][] = [
      ['ws-1', 'obj-1', true],
      ['workspace-123', 'object-456', false],
      ['ws', 'obj', true],
    ];

    testCases.forEach(([wsId, objectId, enabled]) => {
      mockUseSWR.mockReturnValue({
        data: enabled ? mockRecordsCount : undefined,
        isLoading: false,
        mutate: mockMutate,
      });

      const { result } = renderHook(() => useRecordsCount(wsId, objectId, enabled));

      expect(result.current.recordsCount).toBe(enabled ? mockRecordsCount : undefined);
      expect(result.current.recordsCountLoading).toBe(false);
      expect(result.current.mutateRecordsCount).toBe(mockMutate);

      const expectedKey = enabled ? `data/${wsId}/${objectId}/recordsCount` : null;
      expect(mockUseSWR).toHaveBeenCalledWith(expectedKey, expect.any(Function));
    });
  });

  it('should not make API call when enabled is false', () => {
    mockUseSWR.mockReturnValue({
      data: undefined,
      isLoading: false,
      mutate: mockMutate,
    });

    renderHook(() => useRecordsCount('ws-1', 'obj-1', false));

    expect(mockUseSWR).toHaveBeenCalledWith(null, expect.any(Function));
    expect(RecordAPI.recordsCount).not.toHaveBeenCalled();
  });

  it('should handle rerendering with different parameters', () => {
    mockUseSWR.mockReturnValue({
      data: mockRecordsCount,
      isLoading: false,
      mutate: mockMutate,
    });

    const { result, rerender } = renderHook(
      ({ wsId, objectId, enabled }) => useRecordsCount(wsId, objectId, enabled),
      { initialProps: { wsId: 'ws-1', objectId: 'obj-1', enabled: true } }
    );

    expect(result.current.recordsCount).toBe(mockRecordsCount);

    rerender({ wsId: 'ws-2', objectId: 'obj-2', enabled: false });

    expect(result.current.recordsCount).toBe(mockRecordsCount);
  });
});
