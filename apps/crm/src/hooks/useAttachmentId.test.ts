import { AttachmentAPI } from '@/services/api';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useAttachmentId } from './useAttachmentId';

// Mock the AttachmentAPI
vi.mock('@/services/api', () => ({
  AttachmentAPI: {
    get: vi.fn(),
  },
}));

// Mock useSWR
vi.mock('swr', () => ({
  default: vi.fn(),
}));

describe('useAttachmentId', () => {
  const mockAttachment = {
    id: 'attachment-123',
    url: 'https://example.com/attachment.pdf',
    name: 'test.pdf',
    size: 1024,
    type: 'application/pdf',
  };

  const defaultProps = {
    workspaceId: 'workspace-123',
    objectId: 'object-123',
    recordId: 'record-123',
    attachmentId: 'attachment-123',
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock useSWR implementation
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: mockAttachment,
      error: null,
      isLoading: false,
    });
  });

  it('should be defined', () => {
    expect(useAttachmentId).toBeDefined();
    expect(typeof useAttachmentId).toBe('function');
  });

  it('should return correct structure when all params are provided', () => {
    const { result } = renderHook(() => useAttachmentId(defaultProps));

    expect(result.current).toEqual({
      attachment: mockAttachment,
      url: mockAttachment.url,
      isLoading: false,
      error: null,
    });
  });

  it('should call useSWR with correct key when all params are provided and enabled is true', () => {
    renderHook(() => useAttachmentId(defaultProps));

    expect(useSWR).toHaveBeenCalledWith(
      [
        'attachment',
        defaultProps.workspaceId,
        defaultProps.objectId,
        defaultProps.recordId,
        defaultProps.attachmentId,
      ],
      expect.any(Function),
      {
        dedupingInterval: 5000,
        revalidateOnFocus: false,
        shouldRetryOnError: false,
        revalidateIfStale: false,
      }
    );
  });

  it('should call useSWR with null key when enabled is false', () => {
    renderHook(() => useAttachmentId({ ...defaultProps, enabled: false }));

    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      dedupingInterval: 5000,
      revalidateOnFocus: false,
      shouldRetryOnError: false,
      revalidateIfStale: false,
    });
  });

  it('should call useSWR with null key when workspaceId is missing', () => {
    renderHook(() => useAttachmentId({ ...defaultProps, workspaceId: '' }));

    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      dedupingInterval: 5000,
      revalidateOnFocus: false,
      shouldRetryOnError: false,
      revalidateIfStale: false,
    });
  });

  it('should call useSWR with null key when objectId is missing', () => {
    renderHook(() => useAttachmentId({ ...defaultProps, objectId: '' }));

    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      dedupingInterval: 5000,
      revalidateOnFocus: false,
      shouldRetryOnError: false,
      revalidateIfStale: false,
    });
  });

  it('should call useSWR with null key when recordId is missing', () => {
    renderHook(() => useAttachmentId({ ...defaultProps, recordId: '' }));

    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      dedupingInterval: 5000,
      revalidateOnFocus: false,
      shouldRetryOnError: false,
      revalidateIfStale: false,
    });
  });

  it('should call useSWR with null key when attachmentId is missing', () => {
    renderHook(() => useAttachmentId({ ...defaultProps, attachmentId: '' }));

    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      dedupingInterval: 5000,
      revalidateOnFocus: false,
      shouldRetryOnError: false,
      revalidateIfStale: false,
    });
  });

  it('should call useSWR with null key when multiple params are missing', () => {
    renderHook(() =>
      useAttachmentId({
        ...defaultProps,
        workspaceId: '',
        objectId: '',
        enabled: true,
      })
    );

    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      dedupingInterval: 5000,
      revalidateOnFocus: false,
      shouldRetryOnError: false,
      revalidateIfStale: false,
    });
  });

  it('should return empty url when attachment data is null', () => {
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: null,
      error: null,
      isLoading: false,
    });

    const { result } = renderHook(() => useAttachmentId(defaultProps));

    expect(result.current.url).toBe('');
  });

  it('should return empty url when attachment data has no url', () => {
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: { ...mockAttachment, url: undefined },
      error: null,
      isLoading: false,
    });

    const { result } = renderHook(() => useAttachmentId(defaultProps));

    expect(result.current.url).toBe('');
  });

  it('should handle loading state correctly', () => {
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: undefined,
      error: null,
      isLoading: true,
    });

    const { result } = renderHook(() => useAttachmentId(defaultProps));

    expect(result.current.isLoading).toBe(true);
    expect(result.current.attachment).toBeUndefined();
  });

  it('should handle error state correctly', () => {
    const mockError = new Error('Failed to fetch attachment');
    (useSWR as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: undefined,
      error: mockError,
      isLoading: false,
    });

    const { result } = renderHook(() => useAttachmentId(defaultProps));

    expect(result.current.error).toBe(mockError);
    expect(result.current.attachment).toBeUndefined();
  });

  it('should call AttachmentAPI.get with correct parameters when fetcher is called', async () => {
    let swrFetcher: Function = () => {};

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation(
      (_key, fetcher, _options) => {
        swrFetcher = fetcher;
        return {
          data: mockAttachment,
          error: null,
          isLoading: false,
        };
      }
    );

    (AttachmentAPI.get as ReturnType<typeof vi.fn>).mockResolvedValue(mockAttachment);

    renderHook(() => useAttachmentId(defaultProps));

    // Call the fetcher function
    await swrFetcher();

    expect(AttachmentAPI.get).toHaveBeenCalledWith({
      workspaceId: defaultProps.workspaceId,
      objectId: defaultProps.objectId,
      recordId: defaultProps.recordId,
      attachmentId: defaultProps.attachmentId,
    });
  });

  it('should throw error when fetcher is called with missing workspaceId', async () => {
    let swrFetcher: Function = () => {};

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation(
      (_key, fetcher, _options) => {
        swrFetcher = fetcher;
        return {
          data: undefined,
          error: null,
          isLoading: false,
        };
      }
    );

    renderHook(() => useAttachmentId({ ...defaultProps, workspaceId: '' }));

    await expect(swrFetcher()).rejects.toThrow('Missing required parameters');
  });

  it('should throw error when fetcher is called with missing objectId', async () => {
    let swrFetcher: Function = () => {};

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation(
      (_key, fetcher, _options) => {
        swrFetcher = fetcher;
        return {
          data: undefined,
          error: null,
          isLoading: false,
        };
      }
    );

    renderHook(() => useAttachmentId({ ...defaultProps, objectId: '' }));

    await expect(swrFetcher()).rejects.toThrow('Missing required parameters');
  });

  it('should throw error when fetcher is called with missing recordId', async () => {
    let swrFetcher: Function = () => {};

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation(
      (_key, fetcher, _options) => {
        swrFetcher = fetcher;
        return {
          data: undefined,
          error: null,
          isLoading: false,
        };
      }
    );

    renderHook(() => useAttachmentId({ ...defaultProps, recordId: '' }));

    await expect(swrFetcher()).rejects.toThrow('Missing required parameters');
  });

  it('should throw error when fetcher is called with missing attachmentId', async () => {
    let swrFetcher: Function = () => {};

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation(
      (_key, fetcher, _options) => {
        swrFetcher = fetcher;
        return {
          data: undefined,
          error: null,
          isLoading: false,
        };
      }
    );

    renderHook(() => useAttachmentId({ ...defaultProps, attachmentId: '' }));

    await expect(swrFetcher()).rejects.toThrow('Missing required parameters');
  });

  it('should default enabled to true when not provided', () => {
    const { workspaceId, objectId, recordId, attachmentId } = defaultProps;

    renderHook(() => useAttachmentId({ workspaceId, objectId, recordId, attachmentId }));

    expect(useSWR).toHaveBeenCalledWith(
      ['attachment', workspaceId, objectId, recordId, attachmentId],
      expect.any(Function),
      {
        dedupingInterval: 5000,
        revalidateOnFocus: false,
        shouldRetryOnError: false,
        revalidateIfStale: false,
      }
    );
  });

  it('should handle null/undefined values in params', () => {
    renderHook(() =>
      useAttachmentId({
        workspaceId: null as any,
        objectId: undefined as any,
        recordId: defaultProps.recordId,
        attachmentId: defaultProps.attachmentId,
      })
    );

    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function), {
      dedupingInterval: 5000,
      revalidateOnFocus: false,
      shouldRetryOnError: false,
      revalidateIfStale: false,
    });
  });

  it('should handle API errors gracefully', async () => {
    let swrFetcher: Function = () => {};

    (useSWR as unknown as ReturnType<typeof vi.fn>).mockImplementation(
      (_key, fetcher, _options) => {
        swrFetcher = fetcher;
        return {
          data: undefined,
          error: null,
          isLoading: false,
        };
      }
    );

    const apiError = new Error('API Error');
    (AttachmentAPI.get as ReturnType<typeof vi.fn>).mockRejectedValue(apiError);

    renderHook(() => useAttachmentId(defaultProps));

    await expect(swrFetcher()).rejects.toThrow('API Error');
  });
});
