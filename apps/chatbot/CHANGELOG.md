# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [0.21.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.21.0) (2025-07-23)

### Features

- **chatbot:** tk-3104 group item action menu ([#6237](https://github.com/resola-ai/deca-apps/issues/6237)) ([77671d0](https://github.com/resola-ai/deca-apps/commit/77671d004671b8e13e31d39997169bbc96555047))
- **chatbot:** tk-8937 update chatbot translation ([#6188](https://github.com/resola-ai/deca-apps/issues/6188)) ([95a6208](https://github.com/resola-ai/deca-apps/commit/95a6208f8ac2a6ddb81e46d0cdbd994e7c54e3f2))
- **chatbot:** tk-8937 update chatbot translation ([#6219](https://github.com/resola-ai/deca-apps/issues/6219)) ([a3fd28d](https://github.com/resola-ai/deca-apps/commit/a3fd28deae27864bb8ae62a1e80982f7c20325bb))
- **chatbot:** tk-8937 update chatbot translation ([#6313](https://github.com/resola-ai/deca-apps/issues/6313)) ([e1000e2](https://github.com/resola-ai/deca-apps/commit/e1000e25217fa6b8ea924457ae3f5a4d41cb5be2))

### Bug Fixes

- **chatbot:** TK-8152 Show the button type correctly in the log ([#6101](https://github.com/resola-ai/deca-apps/issues/6101)) ([377ad09](https://github.com/resola-ai/deca-apps/commit/377ad094563f9c1e2b278bc4c0b193ec921435bc))

## [0.20.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.20.0) (2025-06-30)

### Features

- **chatbot:** tk-6606 implement side input option ([#5514](https://github.com/resola-ai/deca-apps/issues/5514)) ([be13b97](https://github.com/resola-ai/deca-apps/commit/be13b975411dd723033b91283066a3708ee3a4df))
- **chatbot:** TK-7195 Frontend implementation for enable streaming message ([#5628](https://github.com/resola-ai/deca-apps/issues/5628)) ([44cc0f4](https://github.com/resola-ai/deca-apps/commit/44cc0f476f15bd2e571e7fced7af16992d9135a2))
- **chatbot:** tk-8506 adjust UI and update type when edit json ([#6046](https://github.com/resola-ai/deca-apps/issues/6046)) ([18a7afc](https://github.com/resola-ai/deca-apps/commit/18a7afc548118c4165bb8f3464afb976134d5413))
- **chatbot:** tk-8506 expand query section table card ([#5934](https://github.com/resola-ai/deca-apps/issues/5934)) ([62aca82](https://github.com/resola-ai/deca-apps/commit/62aca82bff67483a2756d224e065fd2a9a1ec4ab))

### Bug Fixes

- **chatbot:** tk-7141 cannot search node ([#5671](https://github.com/resola-ai/deca-apps/issues/5671)) ([a158c3d](https://github.com/resola-ai/deca-apps/commit/a158c3d757c24fc4d46f10099015cb68dbc01910))
- **chatbot:** tk-7141 cannot search node ([#5671](https://github.com/resola-ai/deca-apps/issues/5671)) ([fb83e98](https://github.com/resola-ai/deca-apps/commit/fb83e986bd7f6aff31eb5fda4f81aa63a45d7056))
- **chatbot:** TK-7195 Add ENV to set streaming support enable ([#5710](https://github.com/resola-ai/deca-apps/issues/5710)) ([485fbb0](https://github.com/resola-ai/deca-apps/commit/485fbb00c95ffff00236a0efff376ff479c48fdc))
- **chatbot:** TK-7195 Add ENV to set streaming support enable ([#5710](https://github.com/resola-ai/deca-apps/issues/5710)) ([6497f03](https://github.com/resola-ai/deca-apps/commit/6497f03f70805a08e6d082456b1ab29603e11d9a))
- **chatbot:** tk-7441 not zoom to target node when opening modal flow error ([#5638](https://github.com/resola-ai/deca-apps/issues/5638)) ([7cd662b](https://github.com/resola-ai/deca-apps/commit/7cd662bcef11cb1f674a5fb9c67b979fe94b6915))
- **chatbot:** tk-7441 not zoom to target node when opening modal flow error ([#5658](https://github.com/resola-ai/deca-apps/issues/5658)) ([92d9ddb](https://github.com/resola-ai/deca-apps/commit/92d9ddb450e17d6071930c73e09c5a10e2cfb926))
- **chatbot:** tk-7441 not zoom to target node when opening modal flow error ([#5658](https://github.com/resola-ai/deca-apps/issues/5658)) ([19ce033](https://github.com/resola-ai/deca-apps/commit/19ce0331c879ccd202b55fbf8330f56b144513fa))
- **chatbot:** tk-7441 not zoom to target node when switch other flows ([#5587](https://github.com/resola-ai/deca-apps/issues/5587)) ([150ab2f](https://github.com/resola-ai/deca-apps/commit/150ab2f6467bea209a44cc8d3f8354ab0f72300a))
- **chatbot:** tk-7446 paste node should be at pointer ([#5517](https://github.com/resola-ai/deca-apps/issues/5517)) ([851ee9b](https://github.com/resola-ai/deca-apps/commit/851ee9b6153a316aacb21980b3399c297b59a4e5))
- **chatbot:** tk-7446 update logic when pasting multiple node ([#5571](https://github.com/resola-ai/deca-apps/issues/5571)) ([1307f71](https://github.com/resola-ai/deca-apps/commit/1307f715b3ef0ef9aa7b956db683c7af812f4de6))
- **chatbot:** TK-7549 Add field to show BotID and FlowID ([#5910](https://github.com/resola-ai/deca-apps/issues/5910)) ([5b6ae32](https://github.com/resola-ai/deca-apps/commit/5b6ae32ca26c28f9a629d26a95a81419f2de33f0))
- **chatbot:** tk-8810 reset pagination when search ([#5976](https://github.com/resola-ai/deca-apps/issues/5976)) ([3eaa4c2](https://github.com/resola-ai/deca-apps/commit/3eaa4c242bd2e508f8f6fe0564299a66dc6efdbf))

## [0.19.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.19.0) (2025-06-09)

### Features

- **chatbot:** tk-6606 implement side input option ([#5514](https://github.com/resola-ai/deca-apps/issues/5514)) ([be13b97](https://github.com/resola-ai/deca-apps/commit/be13b975411dd723033b91283066a3708ee3a4df))
- **chatbot:** TK-7195 Frontend implementation for enable streaming message ([#5628](https://github.com/resola-ai/deca-apps/issues/5628)) ([44cc0f4](https://github.com/resola-ai/deca-apps/commit/44cc0f476f15bd2e571e7fced7af16992d9135a2))

### Bug Fixes

- **chatbot:** tk-7141 cannot search node ([#5671](https://github.com/resola-ai/deca-apps/issues/5671)) ([fb83e98](https://github.com/resola-ai/deca-apps/commit/fb83e986bd7f6aff31eb5fda4f81aa63a45d7056))
- **chatbot:** TK-7195 Add ENV to set streaming support enable ([#5710](https://github.com/resola-ai/deca-apps/issues/5710)) ([6497f03](https://github.com/resola-ai/deca-apps/commit/6497f03f70805a08e6d082456b1ab29603e11d9a))
- **chatbot:** tk-7441 not zoom to target node when opening modal flow error ([#5638](https://github.com/resola-ai/deca-apps/issues/5638)) ([7cd662b](https://github.com/resola-ai/deca-apps/commit/7cd662bcef11cb1f674a5fb9c67b979fe94b6915))
- **chatbot:** tk-7441 not zoom to target node when opening modal flow error ([#5658](https://github.com/resola-ai/deca-apps/issues/5658)) ([19ce033](https://github.com/resola-ai/deca-apps/commit/19ce0331c879ccd202b55fbf8330f56b144513fa))
- **chatbot:** tk-7441 not zoom to target node when switch other flows ([#5587](https://github.com/resola-ai/deca-apps/issues/5587)) ([150ab2f](https://github.com/resola-ai/deca-apps/commit/150ab2f6467bea209a44cc8d3f8354ab0f72300a))
- **chatbot:** tk-7446 paste node should be at pointer ([#5517](https://github.com/resola-ai/deca-apps/issues/5517)) ([851ee9b](https://github.com/resola-ai/deca-apps/commit/851ee9b6153a316aacb21980b3399c297b59a4e5))
- **chatbot:** tk-7446 update logic when pasting multiple node ([#5571](https://github.com/resola-ai/deca-apps/issues/5571)) ([1307f71](https://github.com/resola-ai/deca-apps/commit/1307f715b3ef0ef9aa7b956db683c7af812f4de6))

## [0.18.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.18.0) (2025-05-26)

### Features

- **chatbot:** TK-7184 Implement team selection for Livechat node ([#5414](https://github.com/resola-ai/deca-apps/issues/5414)) ([3b2ffcf](https://github.com/resola-ai/deca-apps/commit/3b2ffcf15313571947226ee847a98b5510beb6d6))

### Bug Fixes

- **chatbot:** TK-7184 Add Text description for Select team ([#5427](https://github.com/resola-ai/deca-apps/issues/5427)) ([d0b16c5](https://github.com/resola-ai/deca-apps/commit/d0b16c563c702026ae461d1e4c58611fda8d902c))

## [0.17.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.17.0) (2025-05-20)

### Features

- **chatbot:** tk-3247 jump to target node in action gotoblock ([#5172](https://github.com/resola-ai/deca-apps/issues/5172)) ([42ec432](https://github.com/resola-ai/deca-apps/commit/42ec432a85dca4c1c4926aab84965f77598cd7e3))
- **chatbot:** tk-3247 jump to target node in action gotoblock ([#5402](https://github.com/resola-ai/deca-apps/issues/5402)) ([f2725ca](https://github.com/resola-ai/deca-apps/commit/f2725ca99e072c41339321093ef71fd2c29e4f7b))
- **chatbot:** TK-7391 Implement variable suggestion in Text Node ([#5250](https://github.com/resola-ai/deca-apps/issues/5250)) ([24a5a11](https://github.com/resola-ai/deca-apps/commit/24a5a11c64259395160bb1a17b9685ed68937aad))

### Bug Fixes

- **chatbot:** tk-7228 url cant be saved in api node ([#5174](https://github.com/resola-ai/deca-apps/issues/5174)) ([f55c32e](https://github.com/resola-ai/deca-apps/commit/f55c32e66a050357a457ca7e1989bed37003c2b8))
- **chatbot:** tk-7621 cannot delete last button in dynamic node ([#5365](https://github.com/resola-ai/deca-apps/issues/5365)) ([58adf14](https://github.com/resola-ai/deca-apps/commit/58adf14c4f9b58648ec956733954b484ad841f3e))
- **chatbot:** tk-7710 reset search when clear search box ([#5173](https://github.com/resola-ai/deca-apps/issues/5173)) ([4eb6e2c](https://github.com/resola-ai/deca-apps/commit/4eb6e2c117598b4b912a46744aacee9ddacc358a))

## [0.16.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.16.0) (2025-04-28)

### Features

- **chatbot:** tk-5278 implement sorting function for kb node ([#5046](https://github.com/resola-ai/deca-apps/issues/5046)) ([df37354](https://github.com/resola-ai/deca-apps/commit/df37354b6d356e2d7bcc25e7cd3add78abb4bd89))
- **chatbot:** tk-5278 implement sorting function for kb node ([#5082](https://github.com/resola-ai/deca-apps/issues/5082)) ([8d8c271](https://github.com/resola-ai/deca-apps/commit/8d8c2713a0bf6f83cfdc005e1b08eaf4069635b7))
- **chatbot:** tk-5342 update icon setting block node ([#4967](https://github.com/resola-ai/deca-apps/issues/4967)) ([ebfb2ec](https://github.com/resola-ai/deca-apps/commit/ebfb2eca278c577ea53598a07a5e3342cc36c50b))
- **chatbot:** tk-5579 fix flow count when dont have node ([#5041](https://github.com/resola-ai/deca-apps/issues/5041)) ([b60a472](https://github.com/resola-ai/deca-apps/commit/b60a472ae704434a72764f3dafbb38364d987928))
- **chatbot:** tk-6305 fallback branch in dynamic node ([#4971](https://github.com/resola-ai/deca-apps/issues/4971)) ([12404fe](https://github.com/resola-ai/deca-apps/commit/12404fe48e7a3631a4ee87f401132ed45c6191f0))

### Bug Fixes

- **chatbot:** Fix TextInput cannot click left, right sections ([#5091](https://github.com/resola-ai/deca-apps/issues/5091)) ([4e0c13d](https://github.com/resola-ai/deca-apps/commit/4e0c13d9f91b50ab7211b32cf60a98add103333f))

## [0.15.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.15.0) (2025-04-09)

### Features

- **chatbot:** tk-3107 update logic cloned port ([#4835](https://github.com/resola-ai/deca-apps/issues/4835)) ([cc30a9d](https://github.com/resola-ai/deca-apps/commit/cc30a9ddc961bd30ea0d9e0e60b0c13f3157ad15))

### Bug Fixes

- **chatbot:** tk-5431 update condition to allow drag and drop ([#4809](https://github.com/resola-ai/deca-apps/issues/4809)) ([b3762ff](https://github.com/resola-ai/deca-apps/commit/b3762ff043136895d81f6690d56f39d7d2c4178d))
- **chatbot:** tk-5431 update logic to validate before drop ([#4892](https://github.com/resola-ai/deca-apps/issues/4892)) ([763954c](https://github.com/resola-ai/deca-apps/commit/763954c0e3f0e8cd309340274d77338c97033f0f))
- **chatbot:** tk-6127 update when click intent in flow panel ([#4845](https://github.com/resola-ai/deca-apps/issues/4845)) ([32e22d3](https://github.com/resola-ai/deca-apps/commit/32e22d31d17b96630d628f0336a79fc95cb40447))
- **chatbot:** TK-6392 Only reverse articles if have no query ([#4834](https://github.com/resola-ai/deca-apps/issues/4834)) ([e5b4868](https://github.com/resola-ai/deca-apps/commit/e5b4868821f4636246cb0d1bf2ec7248dce4a413))
- **chatbot:** TK-6392 Reverse article list in KB Node ([#4831](https://github.com/resola-ai/deca-apps/issues/4831)) ([007e85c](https://github.com/resola-ai/deca-apps/commit/007e85c5bd8ea3d58d08258201c47dffa92988bf))
- **chatbot:** Update enable Script node to ENV ([#4770](https://github.com/resola-ai/deca-apps/issues/4770)) ([862ca89](https://github.com/resola-ai/deca-apps/commit/862ca89be32542e11f8db89aa49526693247f6f8))

## [0.14.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.14.0) (2025-03-26)

### Features

- **chatbot:** TK-5556 Update KB card to support Doc type ([#4630](https://github.com/resola-ai/deca-apps/issues/4630)) ([220b68d](https://github.com/resola-ai/deca-apps/commit/220b68d4ee65f76723fdcb24a8e1f11aaffffa5d))

### Bug Fixes

- **chatbot:** Fix NumberInput can not click up down buttons ([#4691](https://github.com/resola-ai/deca-apps/issues/4691)) ([551eddb](https://github.com/resola-ai/deca-apps/commit/551eddbe2fd4bf2ee75cbe85669d820348e5ba92))
- **chatbot:** TK-5556 Add Icon file type Markdown for Document type ([#4700](https://github.com/resola-ai/deca-apps/issues/4700)) ([03d45f7](https://github.com/resola-ai/deca-apps/commit/03d45f783d73c8f32a6f9aa28be5009846c1c427))
- **chatbot:** TK-5556 Update to display Icon for Doc type ([#4686](https://github.com/resola-ai/deca-apps/issues/4686)) ([673849d](https://github.com/resola-ai/deca-apps/commit/673849d4789dd544c2445993ba1744270cf94b30))
- **chatbot:** TK-6189 Fix the most relevant article search results are at the bottom of the list in the KB Node ([#4725](https://github.com/resola-ai/deca-apps/issues/4725)) ([ed40e4c](https://github.com/resola-ai/deca-apps/commit/ed40e4c9e009866a50aec965d4def91323fe5280))
- **chatbot:** TK-6189 Fix The most relevant article search results are at the bottom of the list in the KB Node ([#4727](https://github.com/resola-ai/deca-apps/issues/4727)) ([49161b9](https://github.com/resola-ai/deca-apps/commit/49161b9a24f55fc51ff02249d4de8444d023523f))
- **chatbot:** TK-6189 Revert direction get Article ([#4729](https://github.com/resola-ai/deca-apps/issues/4729)) ([73cbf77](https://github.com/resola-ai/deca-apps/commit/73cbf772f7d6d240bfce48c4530df760204d1490))

### [0.13.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.13.1) (2025-03-18)

### Bug Fixes

- **chatbot:** tk-6072 safety get else port data in button setting ([#4617](https://github.com/resola-ai/deca-apps/issues/4617)) ([3847841](https://github.com/resola-ai/deca-apps/commit/3847841479411171f7b6e2e5ebdd6ab97c9eaf07))
- **chatbot:** tk-6132 update label when delete choice node ([#4615](https://github.com/resola-ai/deca-apps/issues/4615)) ([5929e6f](https://github.com/resola-ai/deca-apps/commit/5929e6fce4a069458762455979931c01850c2fef))

## [0.13.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.13.0) (2025-03-17)

### Features

- **chatbot:** TK-2617 Node Search Feature in Chatbot Flow Editor ([#4476](https://github.com/resola-ai/deca-apps/issues/4476)) ([40dccbd](https://github.com/resola-ai/deca-apps/commit/40dccbd76b0fad9cc62f5acaae47dcf2b9a26d83))
- **chatbot:** tk-3851 implement side input option ([#4406](https://github.com/resola-ai/deca-apps/issues/4406)) ([bd68518](https://github.com/resola-ai/deca-apps/commit/bd68518809022f1de0b01b9f17275240633dfe6f))
- **chatbot:** TK-5214 Update Livechat Node Configuration ([#4416](https://github.com/resola-ai/deca-apps/issues/4416)) ([382fdc7](https://github.com/resola-ai/deca-apps/commit/382fdc7176e38e38f645cf649f2b8a14b5b9c96e))
- **chatbot:** tk-5238 implement rich menu linking ([#4427](https://github.com/resola-ai/deca-apps/issues/4427)) ([18654e2](https://github.com/resola-ai/deca-apps/commit/18654e25e998f34230125aa4b3617ea4f0e54d06))

### [0.12.3](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.3) (2025-02-27)

### Bug Fixes

- **chatbot:** tk-5551 update logic get previous node ([#4374](https://github.com/resola-ai/deca-apps/issues/4374)) ([b01b657](https://github.com/resola-ai/deca-apps/commit/b01b6571aaeae61cd48abda712c642c40f70e4e8))
- **chatbot:** tk-5551 update logic get previous node ([#4377](https://github.com/resola-ai/deca-apps/issues/4377)) ([9ba8ab9](https://github.com/resola-ai/deca-apps/commit/9ba8ab97caa7fcfb49affeff3d0cddbba7eba5ca))

### [0.12.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.2) (2025-02-27)

### Bug Fixes

- **chatbot:** tk-5551 update logic when calculate action pos ([#4368](https://github.com/resola-ai/deca-apps/issues/4368)) ([34c8775](https://github.com/resola-ai/deca-apps/commit/34c8775c08f02e8271f8621b2a16db173cfa02fd))

### [0.12.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.1) (2025-02-26)

### Bug Fixes

- **chatbot:** tk-5410 settime out when reset data setting ([#4346](https://github.com/resola-ai/deca-apps/issues/4346)) ([3e3d2cf](https://github.com/resola-ai/deca-apps/commit/3e3d2cf51c1ab1c340a5e341c52971266a69624c))
- **chatbot:** tk-5410 check language is valid or not ([#4347](https://github.com/resola-ai/deca-apps/pull/4347)) ([b1d6ec8](https://github.com/resola-ai/deca-apps/pull/4347/commits/b1d6ec88aa4729782824ccb9f91c6d3220d1eb49))

## [0.12.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.0) (2025-02-25)

### Features

- **chatbot:** tk-3339 add internation for node name ([#4261](https://github.com/resola-ai/deca-apps/issues/4261)) ([82384bf](https://github.com/resola-ai/deca-apps/commit/82384bfb6c5f2cbdd6046afb652d951e4b1c5c64))
- **chatbot:** tk-3339 confirm modal when deleting node ([#4176](https://github.com/resola-ai/deca-apps/issues/4176)) ([37fe068](https://github.com/resola-ai/deca-apps/commit/37fe0682980ef0299c02a665740316e401faadb0))
- **chatbot:** tk-3339 update international for action node ([#4281](https://github.com/resola-ai/deca-apps/issues/4281)) ([d134764](https://github.com/resola-ai/deca-apps/commit/d13476410538aaa3d523ce6534cc86bd16e699ac))
- **chatbot:** tk-3339 update toglee and delete email node name ([#4305](https://github.com/resola-ai/deca-apps/issues/4305)) ([3b9819b](https://github.com/resola-ai/deca-apps/commit/3b9819baea1d794e51f76e62027c5f0178f0a52a))
- **chatbot:** tk-4332 insert record does work in type number ([#4255](https://github.com/resola-ai/deca-apps/issues/4255)) ([c10e0ba](https://github.com/resola-ai/deca-apps/commit/c10e0ba4d36b247958c888a8eaedbc836a2bd71e))
- **chatbot:** tk-4332 update sync data in update and find record ([#4288](https://github.com/resola-ai/deca-apps/issues/4288)) ([ff1cafc](https://github.com/resola-ai/deca-apps/commit/ff1cafcf55ae95ee97c1223847d117c01a0c3a52))
- **chatbot:** tk-4790 add submit in value and title ([#4236](https://github.com/resola-ai/deca-apps/issues/4236)) ([ebae9c3](https://github.com/resola-ai/deca-apps/commit/ebae9c37e0c4574ae1f3b684bb1e75da997c3afd))
- **chatbot:** tk-4790 dynamic button ([#4210](https://github.com/resola-ai/deca-apps/issues/4210)) ([aac0f47](https://github.com/resola-ai/deca-apps/commit/aac0f473cb05b41dca4d77459c67a1e1bce4a21d))
- **chatbot:** tk-4790 update init value and field of dynamic node ([#4226](https://github.com/resola-ai/deca-apps/issues/4226)) ([39aeef5](https://github.com/resola-ai/deca-apps/commit/39aeef511ae573b13594022dcbb8726497d27dfb))
- **chatbot:** tk-4790 update text desc in dynamic node ([#4310](https://github.com/resola-ai/deca-apps/issues/4310)) ([491c4c2](https://github.com/resola-ai/deca-apps/commit/491c4c25777be0a5fc3534edd189c7c1673d728b))
- **chatbot:** tk-4790 update text desc in dynamic node ([#4312](https://github.com/resola-ai/deca-apps/issues/4312)) ([2aea8d0](https://github.com/resola-ai/deca-apps/commit/2aea8d02bbf4618b3ad3e66ec9fdea3adfb2887c))
- **chatbot:** tk-5211 button setting text field update wrong ([#4277](https://github.com/resola-ai/deca-apps/issues/4277)) ([29a1e4c](https://github.com/resola-ai/deca-apps/commit/29a1e4c83739481137523f500f850fe0c32f315e))
- **chatbot:** tk-5211 fix get wrong data to update button node ([#4284](https://github.com/resola-ai/deca-apps/issues/4284)) ([3247cbe](https://github.com/resola-ai/deca-apps/commit/3247cbe621dff14aff744dd1c3a763d57bd88c8d))

### Bug Fixes

- **chatbot:** TK-4620 Mapping error code for chatbot ([#4180](https://github.com/resola-ai/deca-apps/issues/4180)) ([e75fbb1](https://github.com/resola-ai/deca-apps/commit/e75fbb1d62aaee8be9a34ea7bd1d129a790d029d))
- **chatbot:** TK-4921 Add dot color to ChatLogs page and update responsive ([#4270](https://github.com/resola-ai/deca-apps/issues/4270)) ([86fa004](https://github.com/resola-ai/deca-apps/commit/86fa00404bd433326945b07b3f74378d1b952f8f))
- **chatbot:** TK-4921 Display Session ID in Log UI with Color Coding ([#4240](https://github.com/resola-ai/deca-apps/issues/4240)) ([e8d23ee](https://github.com/resola-ai/deca-apps/commit/e8d23ee10b6b199bd3b83bfe2f858b0e243d08d7))
- **chatbot:** TK-5022 Update min max number of reference article ([#4165](https://github.com/resola-ai/deca-apps/issues/4165)) ([1fc2812](https://github.com/resola-ai/deca-apps/commit/1fc281252a90c61531212308c09f205b86dce0f7))
- **chatbot:** tk-5347 revert to text field in table ([#4313](https://github.com/resola-ai/deca-apps/issues/4313)) ([aa1b584](https://github.com/resola-ai/deca-apps/commit/aa1b584f03f5d3e4c718d40882252937463c51b8))
- **chatbot:** TK-5349 Add mapping for new error code Chatbot ([#4285](https://github.com/resola-ai/deca-apps/issues/4285)) ([0527a08](https://github.com/resola-ai/deca-apps/commit/0527a08b50cfd6d6203a6f36d25cc1b5dc829fb2))

### [0.11.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.11.1) (2025-02-07)

### Features

### Bug Fixes

- **chatbot:** TK-4949 Update FE to disable intents related feature ([#4140](https://github.com/resola-ai/deca-apps/issues/4140)) ([0af2e59](https://github.com/resola-ai/deca-apps/commit/0af2e599f27e12941f02bc73d2594d7463509c29))

## [0.11.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.11.0) (2025-02-06)

### Features

- **chatbot:** tk-4559 add exceeded path for livechat node ([#4011](https://github.com/resola-ai/deca-apps/issues/4011)) ([0a61f83](https://github.com/resola-ai/deca-apps/commit/0a61f83e1428d28aaf5d792eb98a086dea27ac26))
- **chatbot:** tk-4619 clean up session storage after restored ([#4093](https://github.com/resola-ai/deca-apps/issues/4093)) ([8030a31](https://github.com/resola-ai/deca-apps/commit/8030a315b0e40e3fd0b8a171f9792859a771f398))
- **chatbot:** tk-4619 improve ui ([#4113](https://github.com/resola-ai/deca-apps/issues/4113)) ([f4be807](https://github.com/resola-ai/deca-apps/commit/f4be8073a6a89c5e994b83ece46bfd9fabeadfd8))
- **chatbot:** tk-4619 improve ui ([#4120](https://github.com/resola-ai/deca-apps/issues/4120)) ([994455e](https://github.com/resola-ai/deca-apps/commit/994455ebcc8c6210d6045f2fa1b88d48c45ae948))

### Bug Fixes

- **chatbot:** TK-2457 Enable Code/Script card ([#4057](https://github.com/resola-ai/deca-apps/issues/4057)) ([5a746f8](https://github.com/resola-ai/deca-apps/commit/5a746f8e46c3ec37fcc527d64a278d7ae7f652ec))
- **chatbot:** TK-3356 Add finish chatbot text config ([#3987](https://github.com/resola-ai/deca-apps/issues/3987)) ([7013ae1](https://github.com/resola-ai/deca-apps/commit/7013ae12806b99a7ea961bba77498b9743c4d553))
- **chatbot:** TK-3356 Add option to enable/disable end conversation message ([#4019](https://github.com/resola-ai/deca-apps/issues/4019)) ([8624966](https://github.com/resola-ai/deca-apps/commit/8624966c8320310dbce28912cb20fa3a1677f809))
- **chatbot:** TK-3356 Update text description for finish chatbot text config ([#4031](https://github.com/resola-ai/deca-apps/issues/4031)) ([f54c2ba](https://github.com/resola-ai/deca-apps/commit/f54c2ba3a6a8507c41ddb076df18fd84628a4967))
- **chatbot:** TK-4452 Check empty content and empty value from editor ([#3918](https://github.com/resola-ai/deca-apps/issues/3918)) ([a993e5c](https://github.com/resola-ai/deca-apps/commit/a993e5ca6b72b3fa67e4ec096e9e0dc802a65499))
- **chatbot:** TK-4452 Trim multi break lines in markdown editor ([#3923](https://github.com/resola-ai/deca-apps/issues/3923)) ([86eb126](https://github.com/resola-ai/deca-apps/commit/86eb126a50fa70e92252c67fb59650b6c48d3712))
- **chatbot:** TK-4469 Improve Chatbot UI after updating Mantine V7 ([#3998](https://github.com/resola-ai/deca-apps/issues/3998)) ([1454384](https://github.com/resola-ai/deca-apps/commit/145438449819ac1b24a677e6687bf7e883a54daa))
- **chatbot:** TK-4469 Improve Chatbot UI after updating Mantine V7 ([#4038](https://github.com/resola-ai/deca-apps/issues/4038)) ([494ad82](https://github.com/resola-ai/deca-apps/commit/494ad823dbc4d1218281f61619302a7f61742eb2))
- **chatbot:** TK-4469 Improve Select component after updating Mantine V7 ([#4099](https://github.com/resola-ai/deca-apps/issues/4099)) ([be826b2](https://github.com/resola-ai/deca-apps/commit/be826b235a27c2788789112e75dffa3ef984b772))
- **chatbot:** TK-4472 Implement pagination for Versions page ([#3974](https://github.com/resola-ai/deca-apps/issues/3974)) ([46cac9a](https://github.com/resola-ai/deca-apps/commit/46cac9aa136279995cbeafb9ff0cabdc9eaa3051))
- **chatbot:** TK-4473 Add confirmation dialog allow input version name when create version ([#3985](https://github.com/resola-ai/deca-apps/issues/3985)) ([824f0eb](https://github.com/resola-ai/deca-apps/commit/824f0ebbd1baa71449788703fee4817e868038e6))

### [0.10.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.10.1) (2025-01-16)

### Features

- **chatbot:** TK-4452 Check empty content and empty value from editor ([#3918](https://github.com/resola-ai/deca-apps/issues/3918)) ([7eff0cd](https://github.com/resola-ai/deca-apps/commit/7eff0cdf917b418d65d62623351670d4ca721508))
- **chatbot:** TK-4452 Trim multi break lines in markdown editor ([#3923](https://github.com/resola-ai/deca-apps/issues/3923)) ([143a49b](https://github.com/resola-ai/deca-apps/commit/143a49b47a0552fee0494d3ddf7bd28eac6437ac))

## [0.10.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.10.0) (2025-01-14)

### Features

- **chatbot:** TK-2267 Allow to edit prompt on RAG mode in KB card ([#3776](https://github.com/resola-ai/deca-apps/issues/3776)) ([48931f9](https://github.com/resola-ai/deca-apps/commit/48931f9e820b09ba97f33f2cbc15462f87275bac))
- **chatbot:** TK-2457 Code/Script Card ([#3834](https://github.com/resola-ai/deca-apps/issues/3834)) ([c716cd7](https://github.com/resola-ai/deca-apps/commit/c716cd7eaca97c61a3a70e5bbf8f36f476037d02))
- **chatbot:** TK-2512 Integrate chatbot with Tolgee ([#3766](https://github.com/resola-ai/deca-apps/issues/3766)) ([c83b27e](https://github.com/resola-ai/deca-apps/commit/c83b27e024feee6226dfe8786ddc9f417c26d777))
- **chatbot:** TK-2543 Version of chatbot settings & flow (backup) ([#3819](https://github.com/resola-ai/deca-apps/issues/3819)) ([89f2e27](https://github.com/resola-ai/deca-apps/commit/89f2e272c3ac58d319ffd489f33f9e226b43c740))
- **chatbot:** tk-2546 update variable validation ([#3797](https://github.com/resola-ai/deca-apps/issues/3797)) ([461e1aa](https://github.com/resola-ai/deca-apps/commit/461e1aae4c3c4666e0e878385af754ac29f525d2))

### Bug Fixes

- **chatbot:** Fix intent validation message ([#3854](https://github.com/resola-ai/deca-apps/issues/3854)) ([35867ee](https://github.com/resola-ai/deca-apps/commit/35867ee435b8f97862514f5771216b0bd651bef4))
- **chatbot:** TK-2457 Hide Code/Script card temporarily ([#3868](https://github.com/resola-ai/deca-apps/issues/3868)) ([a3675d2](https://github.com/resola-ai/deca-apps/commit/a3675d298908e4b56ddf0ed3769db60ff4b2b57a))
- **chatbot:** TK-2512 Update default language ([#3795](https://github.com/resola-ai/deca-apps/issues/3795)) ([61d4127](https://github.com/resola-ai/deca-apps/commit/61d4127838d36594d94acd338ef58c26174fa11a))
- **chatbot:** TK-2543 Add empty state for Versions page ([#3828](https://github.com/resola-ai/deca-apps/issues/3828)) ([04627ec](https://github.com/resola-ai/deca-apps/commit/04627ecc7e18bfc7b3d7120e1cc0f5cabc41ef1e))
- **chatbot:** tk-2598 prevent edit default flow name ([#3788](https://github.com/resola-ai/deca-apps/issues/3788)) ([be10994](https://github.com/resola-ai/deca-apps/commit/be10994d3a88171bb6753ca92f16b7f32a66a8bb))
- **chatbot:** TK-4265 Fix crash app when use tolgee at modal ([#3837](https://github.com/resola-ai/deca-apps/issues/3837)) ([c1bbb34](https://github.com/resola-ai/deca-apps/commit/c1bbb3440c151ccb9d18ffaeeca92d1714763d6c))

### [0.9.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.9.1) (2024-12-24)

### Bug Fixes

- **chatbot:** tk-3940 remove last break line in text of button ([ea67eec](https://github.com/resola-ai/deca-apps/commit/ea67eecd857f277f1c2bbdbe43b40246d7944831))

## [0.9.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.9.0) (2024-12-23)

### Features

- **chatbot:** ckl-685 fix the way to update flow ([c67e597](https://github.com/resola-ai/deca-apps/commit/c67e5971787193fdbca32f96ec6df68bbdb45aa8))
- **chatbot:** ckl-685 fix update vars do not reset flow ([b9d5e6e](https://github.com/resola-ai/deca-apps/commit/b9d5e6ec9d12aab319f87b5e70e2f32312011c9d))
- **chatbot:** ckl-685 show error popup when save fail - genai node ([a318354](https://github.com/resola-ai/deca-apps/commit/a3183547da0aef9a1882077a08e2dc266deff222))
- **chatbot:** ckl-685 show error popup when save fail - revert prompt length ([2f25555](https://github.com/resola-ai/deca-apps/commit/2f255553c7598f8dc647c24dbfafd1fe70f794ce))
- **chatbot:** ckl-685 update error handler for other nodes ([8a61ddf](https://github.com/resola-ai/deca-apps/commit/8a61ddf952560b6070f66f8b095b965c9a84cc42))
- **chatbot:** ckl-685 update method get active flow ([63bc2c0](https://github.com/resola-ai/deca-apps/commit/63bc2c004c3219909d71c99f171258a5b85a2c8e))
- **chatbot:** ckl-685 update save delete variable ([b9ebec3](https://github.com/resola-ai/deca-apps/commit/b9ebec367931ef0557c51ea6866b895a8d3b715d))
- **chatbot:** TK-2547 Add fallback branch to Form card ([#3639](https://github.com/resola-ai/deca-apps/issues/3639)) ([323d40c](https://github.com/resola-ai/deca-apps/commit/323d40cb06d015a54f3fd4c09c1de50ca4554bca))
- **chatbot:** tk-2581 be able to add text button card ([ecf21bf](https://github.com/resola-ai/deca-apps/commit/ecf21bf0dd4bcd4bf569c8c6f5cb6ebe0b94f439))
- **chatbot:** tk-2581 update button node ([a6b0394](https://github.com/resola-ai/deca-apps/commit/a6b03941ae9e041c7399f558c9851dec2db02592))
- **chatbot:** TK-3077 Setting for timeout flow ([#3640](https://github.com/resola-ai/deca-apps/issues/3640)) ([326f254](https://github.com/resola-ai/deca-apps/commit/326f25460b3d437a96d82950b534e382cede7e51))
- **chatbot:** tk-3082 the connection line button node is disapear ([92de446](https://github.com/resola-ai/deca-apps/commit/92de446d41cedaddccde48efd054e3b43b85bb37))

### Bug Fixes

- **chatbot:** CKL-741 - Setting data of node doesn't reset when drag new node ([#3572](https://github.com/resola-ai/deca-apps/issues/3572)) ([ef24279](https://github.com/resola-ai/deca-apps/commit/ef2427917b90cbd6224fa8ce0f1729fcac07751e))
- **chatbot:** CKL-741 - Setting data of node doesn't reset when drag new node ([#3579](https://github.com/resola-ai/deca-apps/issues/3579)) ([4062a1e](https://github.com/resola-ai/deca-apps/commit/4062a1edf6dc528b2c28524e80e402d3a9588972))
- **chatbot:** ckl-742 prompt ai capture is blank - fix condition ([a4fa4bf](https://github.com/resola-ai/deca-apps/commit/a4fa4bf76c131238e6b76f6e717d26907795afa3))
- **chatbot:** tk-2519 handle null case when folders/kbs has been deleted ([#3711](https://github.com/resola-ai/deca-apps/issues/3711)) ([6b95b8d](https://github.com/resola-ai/deca-apps/commit/6b95b8de659c7941051a975c4614595bff9ee8ac))
- **chatbot:** tk-2519 update logic check deleted folder and kb ([#3714](https://github.com/resola-ai/deca-apps/issues/3714)) ([bc1c651](https://github.com/resola-ai/deca-apps/commit/bc1c651adc350295225f28f8234a90a30fb3a75b))
- **chatbot:** tk-2519 update logic check root folder ([#3716](https://github.com/resola-ai/deca-apps/issues/3716)) ([72dc6a9](https://github.com/resola-ai/deca-apps/commit/72dc6a993db3b6a06053987fbd67a599ba22303c))
- **chatbot:** tk-2519 update logic check root folder ([#3717](https://github.com/resola-ai/deca-apps/issues/3717)) ([83a3cc8](https://github.com/resola-ai/deca-apps/commit/83a3cc854100e3f2af78c415eb1e9dde0ec1966f))
- **chatbot:** tk-2535 fix reset data in intent, capture input, form, setvariable ([#3693](https://github.com/resola-ai/deca-apps/issues/3693)) ([29a43ac](https://github.com/resola-ai/deca-apps/commit/29a43ac289f019bf1c7202fac87b1f5a9ab82351))
- **chatbot:** tk-2535 setting text node doesnt reset ([4d84a34](https://github.com/resola-ai/deca-apps/commit/4d84a349f5ca5d6246259abf7c586fa0c920ae93))
- **chatbot:** TK-2544 - Move delete bot from Bot Detail to Bot settings ([#3540](https://github.com/resola-ai/deca-apps/issues/3540)) ([88cb56e](https://github.com/resola-ai/deca-apps/commit/88cb56e03bf29da0d9d08937e02faf9db73bab93))
- **chatbot:** tk-2900 intent label dont change after publish ([99c3dc0](https://github.com/resola-ai/deca-apps/commit/99c3dc0e489c984bd29353cc41dfc311f684c24a))
- **chatbot:** tk-2916 unexpected error in genai node ([31b04f7](https://github.com/resola-ai/deca-apps/commit/31b04f750aee96ed547b4a46f2d299729988f1a0))
- **chatbot:** TK-3077 Update timeout flow to change text when switch on off ([#3657](https://github.com/resola-ai/deca-apps/issues/3657)) ([bdf51da](https://github.com/resola-ai/deca-apps/commit/bdf51da7e5af921f75ee7773912e911826628fac))
- **chatbot:** tk-3082 update function drop block to block ([f00d64f](https://github.com/resola-ai/deca-apps/commit/f00d64f4a9d67d2f16994d1c1efceb59bd59ac07))
- **chatbot:** tk-3082 update function drop step to block ([f0993b9](https://github.com/resola-ai/deca-apps/commit/f0993b95805f70b7f7be1274a4ea91e6a4d16294))
- **chatbot:** tk-3082 update function drop step to block - fix build ([7348e7b](https://github.com/resola-ai/deca-apps/commit/7348e7b017ce7f80a68522194ba485ef5afab504))
- **chatbot:** tk-3383 notfound path cannot disable - update onNodeDelete ([a52ec21](https://github.com/resola-ai/deca-apps/commit/a52ec21bd86282c90e1a0d03d20eb32445afdffd))
- **chatbot:** TK-3663 Add save settings button ([#3720](https://github.com/resola-ai/deca-apps/issues/3720)) ([a0db3b8](https://github.com/resola-ai/deca-apps/commit/a0db3b8ef2ed4f610e10ce37336b310d6226206c))

### [0.8.3](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.8.3) (2024-11-20)

### Features

- **chatbot:** ckl-739 add limit prompt length for genai card ([e96467b](https://github.com/resola-ai/deca-apps/commit/e96467b34357fdc727cf41bc947c6b588d062026))
- **chatbot:** ckl-739 add limit prompt length for genai card ([1569347](https://github.com/resola-ai/deca-apps/commit/15693471ac58b3e3af8836d79a0f8ebabe1c4867))

### Bug Fixes

### [0.8.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.8.2) (2024-11-19)

### Features

- **chatbot:** CKL-715 - Add new option to store KB card result in variable ([#3436](https://github.com/resola-ai/deca-apps/issues/3436)) ([d5b8a24](https://github.com/resola-ai/deca-apps/commit/d5b8a24ced88c14103ebab211177811e567f48ff))

### Bug Fixes

- **chatbot:** CKL-722 Correct format when pasting variable link syntax to Markdown editor ([783a1c3](https://github.com/resola-ai/deca-apps/commit/783a1c307512a72e27e71ea3ec3c9e837fca03a1))
- **chatbot:** CKL-722 Correct format when pasting variable link syntax to Markdown editor ([7469d04](https://github.com/resola-ai/deca-apps/commit/7469d04d4976ccec3f6fb4070671b9e53a1d5571))
- **chatbot:** CKL-722 Handle decode special charaters in Chatbot Text Node ([16af17a](https://github.com/resola-ai/deca-apps/commit/16af17ac9c359b39807ba819b89456bb0870bec4))
- **chatbot:** CKL-722 Handle decode special charaters in Chatbot Text Node ([6e1cc32](https://github.com/resola-ai/deca-apps/commit/6e1cc32a0fd8014debd2b25990cb7fe8bb85281b))
- **chatbot:** CKL-722 Improve patsing markdown content format and link variable decoding ([96c0967](https://github.com/resola-ai/deca-apps/commit/96c0967e73901a82940ea19713e5200c02a181bf))
- **chatbot:** CKL-722 Improve patsing markdown content format and link variable decoding ([0146a90](https://github.com/resola-ai/deca-apps/commit/0146a906fbb5e392d5585fea61e3f0e307a7c4b0))
- **chatbot:** Upadte option to store KB card result in variable ([#3463](https://github.com/resola-ai/deca-apps/issues/3463)) ([f2ca862](https://github.com/resola-ai/deca-apps/commit/f2ca862b4dd0fb887e04be446f929faf6ba255ea))

### [0.8.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.8.1) (2024-11-18)

### Features

### Bug Fixes

- **chatbot:** CKL-722 Correct format when pasting variable link syntax to Markdown editor ([8392300](https://github.com/resola-ai/deca-apps/commit/83923006232f73ab545a2f6955882d07392fcc65))
- **chatbot:** CKL-722 Handle decode special charaters in Chatbot Text Node ([11010c5](https://github.com/resola-ai/deca-apps/commit/11010c5181c2d6430d7944a965c5dc0be49ec4f7))
- **chatbot:** CKL-722 Improve patsing markdown content format and link variable decoding ([f4220cf](https://github.com/resola-ai/deca-apps/commit/f4220cf29a95555d893738e1ab78b998c3084760))

## [0.8.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.8.0) (2024-11-12)

### Features

- **chatbot:** ckl-653 custom template card ([db2238f](https://github.com/resola-ai/deca-apps/commit/db2238f3ddec3e4bd1a6e2f5fced075e6d02cd3d))
- **chatbot:** ckl-653 custom template card ([213a850](https://github.com/resola-ai/deca-apps/commit/213a8509e3b568f2603d05f3756843f52d42c3f1))
- **chatbot:** ckl-653 custom template card add altext field ([405ffd8](https://github.com/resola-ai/deca-apps/commit/405ffd8f71a0def474abc7f456e99d8e880a655b))
- **chatbot:** ckl-653 custom template card add json input ([c25b52b](https://github.com/resola-ai/deca-apps/commit/c25b52b00fd461bcf345e580954f8c0a374ab630))
- **chatbot:** ckl-653 custom template card rename node ([09cd44f](https://github.com/resola-ai/deca-apps/commit/09cd44f9392045bfc868e7068d76d146106e8771))
- **chatbot:** ckl-655 check empty prompt ([e99545e](https://github.com/resola-ai/deca-apps/commit/e99545ea465f4db3d9bbff5466a5e4215ab85e07))
- **chatbot:** ckl-655 gen ai card ([868dc8d](https://github.com/resola-ai/deca-apps/commit/868dc8da341a64e8dcb1fa2b56f9c3cf8fbcf5d8))
- **chatbot:** ckl-655 gen ai card update i18n ([b81a318](https://github.com/resola-ai/deca-apps/commit/b81a31832102c639ab09265437fbe544fb198aef))
- **chatbot:** ckl-655 remove unique of qna node ([ca66659](https://github.com/resola-ai/deca-apps/commit/ca66659876b33c78362a9ad08f1e3f8ce8d44a60))
- **chatbot:** ckl-655 stringfy json data ([9fcea1a](https://github.com/resola-ai/deca-apps/commit/9fcea1ae0a19e3a26b4daa647df390b8cdaa65a3))

### Bug Fixes

- **chatbot:** ckl-696 goto block node action doesnt change label ([007cdb1](https://github.com/resola-ai/deca-apps/commit/007cdb10ed18242a28eb50a3d1b38e5d49cafec5))
- **chatbot:** CKL-713 Correct condition to remove edge from action and correct some warning issues ([793e1f7](https://github.com/resola-ai/deca-apps/commit/793e1f7eb6d2800dcef577cd84c4489d9e1454b7))

## [0.7.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.7.0) (2024-11-05)

### Features

- **chatbot:** ckl-389 AI capture card ([2e0d0ad](https://github.com/resola-ai/deca-apps/commit/2e0d0ad94670c5dcf3aee278a30862173fe4ca17))
- **chatbot:** ckl-389 AI capture card ([2d2df97](https://github.com/resola-ai/deca-apps/commit/2d2df97226a4173223d3be007d9a9d2d0e1d3e86))
- **chatbot:** ckl-389 AI capture card ([f98f13a](https://github.com/resola-ai/deca-apps/commit/f98f13af29ec1cba90f878b7fae7cd6d38e75c9c))
- **chatbot:** ckl-389 AI capture card ([f9c4cfb](https://github.com/resola-ai/deca-apps/commit/f9c4cfb50dedf11f0244b9dfb464441b3f41f75a))
- **chatbot:** ckl-389 AI capture card ([6e9bd4c](https://github.com/resola-ai/deca-apps/commit/6e9bd4c4542d6685e1b11371665075f13d2eae4f))
- **chatbot:** ckl-487 add option show reference articles in rag mode ([#2900](https://github.com/resola-ai/deca-apps/issues/2900)) ([7b8fbcb](https://github.com/resola-ai/deca-apps/commit/7b8fbcb9b8a38e0074c02ab5fd9bc066189693f5))
- **chatbot:** ckl-493 add new sort related article ([#2898](https://github.com/resola-ai/deca-apps/issues/2898)) ([607c7e9](https://github.com/resola-ai/deca-apps/commit/607c7e9b6d4b0d5fb257409f9f564839da4d8132))
- **chatbot:** ckl-493 hide select order when orderby is relevant ([#2950](https://github.com/resola-ai/deca-apps/issues/2950)) ([ebfa6f9](https://github.com/resola-ai/deca-apps/commit/ebfa6f9b12cc60a6647b85c2e0fd01bd346014d4))
- **chatbot:** ckl-554 add tooltip for name of folder and kb ([#3039](https://github.com/resola-ai/deca-apps/issues/3039)) ([3b99110](https://github.com/resola-ai/deca-apps/commit/3b99110751bd787dc8a31cf04a4486ad736fd2a3))
- **chatbot:** CKL-555 - Integration settings screen ([#3241](https://github.com/resola-ai/deca-apps/issues/3241)) ([4b16a67](https://github.com/resola-ai/deca-apps/commit/4b16a678c1beb3c107435a4de9b7e4b2b5289fc1))

### Bug Fixes

- **chatbot:** ckl-343 remove action nodes of else port when turn off notfound ([9b55ab5](https://github.com/resola-ai/deca-apps/commit/9b55ab50592daae5eae7b48f3a36290ad78ea3f1))
- **chatbot:** ckl-343 remove action nodes of else port when turn off notfound ([#2855](https://github.com/resola-ai/deca-apps/issues/2855)) ([5015879](https://github.com/resola-ai/deca-apps/commit/501587915c8307e35c493b0ccb7014bbfc4630e4))
- **chatbot:** CKL-386 Uncomment the threshold feature ([#2859](https://github.com/resola-ai/deca-apps/issues/2859)) ([72ba4f3](https://github.com/resola-ai/deca-apps/commit/72ba4f378be95b367fa92b8de1184ad856382add))
- **chatbot:** CKL-420 Handle empty paragraph as break lines in Block Note markdown ([7675f74](https://github.com/resola-ai/deca-apps/commit/7675f74e4f6b11a4d8973aa0222792891e81b506))
- **chatbot:** CKL-422 Correct encode and decode content from Text Card to adapt with URI safety ([7dbc764](https://github.com/resola-ai/deca-apps/commit/7dbc7646fd9be215c10b9a713ec5b77a57e158c6))
- **chatbot:** CKL-422 Correct encode and decode content from Text Card to adapt with URI safety ([06f8ba1](https://github.com/resola-ai/deca-apps/commit/06f8ba180700951b40b0f3e7037fc8de8edabe70))
- **chatbot:** CKL-422 Correct parsing url from Text Card editor ([b3d43f9](https://github.com/resola-ai/deca-apps/commit/b3d43f972dd3de64b517edc5ca5e2da0ce49a1be))
- **chatbot:** CKL-422 Correct parsing url from Text Card editor ([8312a29](https://github.com/resola-ai/deca-apps/commit/8312a299801180d6259f3ac4dec8b691e89ab3d8))
- **chatbot:** CKL-422 Correct url format from blocknote markdown ([614cb7a](https://github.com/resola-ai/deca-apps/commit/614cb7aaad0a3ac989d66c627e23fc053484515f))
- **chatbot:** ckl-446 preview button in related articles is disabled ([e135796](https://github.com/resola-ai/deca-apps/commit/e135796e8d6b3018c899fb236c5676e4ead0c0b4))
- **chatbot:** ckl-446 preview button in related articles is disabled ([8675e10](https://github.com/resola-ai/deca-apps/commit/8675e10a3ad9f6ef4ffeb1e6c7afb66e6a644a7d))
- **chatbot:** CKL-469 Correct wrong breakline export from block note ([e628585](https://github.com/resola-ai/deca-apps/commit/e628585adff57857a90f8ab3217775529ac85ee9))
- **chatbot:** CKL-469 Correct wrong breakline export from block note ([275349e](https://github.com/resola-ai/deca-apps/commit/275349eccb7d190ec5635b6a2d8969ab8ed6d2be))
- **chatbot:** CKL-496 - Move KB card threshould settings to advanced settings ([#2956](https://github.com/resola-ai/deca-apps/issues/2956)) ([8fec371](https://github.com/resola-ai/deca-apps/commit/8fec371f9b8c99b1eff09dcbd754eb85a98697fc))
- **chatbot:** CKL-508 - Improve email input in Email Node ([#2871](https://github.com/resola-ai/deca-apps/issues/2871)) ([df6f409](https://github.com/resola-ai/deca-apps/commit/df6f409b533376221d406854b31c5c157d0da0f9))
- **chatbot:** ckl-551 update showing integration id ([#2941](https://github.com/resola-ai/deca-apps/issues/2941)) ([5a2bd43](https://github.com/resola-ai/deca-apps/commit/5a2bd43554c9d660ef070845ca41c61db35c4449))
- **chatbot:** CKL-555 Add hook to handle api error ([#3260](https://github.com/resola-ai/deca-apps/issues/3260)) ([37cc6c6](https://github.com/resola-ai/deca-apps/commit/37cc6c65673a35364b3686573b00aebf810d7eef))
- **chatbot:** ckl-574 the order or article in kb node ([#3073](https://github.com/resola-ai/deca-apps/issues/3073)) ([a4cc48b](https://github.com/resola-ai/deca-apps/commit/a4cc48b67a895b939f0f9c3809a12cd68668d17d))
- **chatbot:** CKL-597 - [Flow] Show message for variable name error ([#3060](https://github.com/resola-ai/deca-apps/issues/3060)) ([bdfd36f](https://github.com/resola-ai/deca-apps/commit/bdfd36fc8229c7c078a93f2aee3637e53aeb851c))
- **chatbot:** Fix empty screen not showing when only have disconnected integrations ([#3261](https://github.com/resola-ai/deca-apps/issues/3261)) ([add89f4](https://github.com/resola-ai/deca-apps/commit/add89f431e48830f185d96700c3c31c13fab503b))
- **chatbot:** Fix menu link for integration page ([#3296](https://github.com/resola-ai/deca-apps/issues/3296)) ([d6c4eaf](https://github.com/resola-ai/deca-apps/commit/d6c4eaf24198a05d682da52b5882bba4b94971b6))
- **chatbot:** Update LINE channel ID selection ([#3272](https://github.com/resola-ai/deca-apps/issues/3272)) ([201e003](https://github.com/resola-ai/deca-apps/commit/201e0035cc5fc90f2007d10eb14b45594e71cbae))
- **chatbot:** Use chatwindow logo for chatwindow integration ([#3268](https://github.com/resola-ai/deca-apps/issues/3268)) ([7fdfd1d](https://github.com/resola-ai/deca-apps/commit/7fdfd1dbfc9e65838ef7a85d449582cf4f4391aa))
- **chatwindow,chatbot:** CKL-469 Correct encode issue to remove // character from URL ([4e8ddb7](https://github.com/resola-ai/deca-apps/commit/4e8ddb7a35b87851eb87c1d3eed145f78b929328))
- **chatwindow,chatbot:** CKL-469 Correct encode issue to remove // character from URL ([e1c6c8d](https://github.com/resola-ai/deca-apps/commit/e1c6c8daf7453e8bf6f4ce2e5cdd9245394ced6e))
- **chatwindow,chatbot:** CKL-469 Corrected blank row and % character from URL ([17129b1](https://github.com/resola-ai/deca-apps/commit/17129b174a4fa523f711abdb8b63206e2b6e2964))
- **chatwindow,chatbot:** CKL-469 Corrected blank row and % character from URL ([d7fc36b](https://github.com/resola-ai/deca-apps/commit/d7fc36b633b1e5c899602a810af62e78f67a0b9d))
- **chatwindow,chatbot:** CKL-469 Corrected spacing between lines in markdown text ([2f18042](https://github.com/resola-ai/deca-apps/commit/2f180428d3c6f3e7a7e95de8fd09e7b99dca6c7c))
- **chatwindow,chatbot:** CKL-469 Corrected spacing between lines in markdown text ([e24c64e](https://github.com/resola-ai/deca-apps/commit/e24c64e95f331830eb1c389f715668706f7580a9))

## [0.6.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.6.0) (2024-10-16)

### Features

- **chatbot:** ckl-487 add option show reference articles in rag mode ([#2900](https://github.com/resola-ai/deca-apps/issues/2900)) ([7b8fbcb](https://github.com/resola-ai/deca-apps/commit/7b8fbcb9b8a38e0074c02ab5fd9bc066189693f5))
- **chatbot:** ckl-493 add new sort related article ([#2898](https://github.com/resola-ai/deca-apps/issues/2898)) ([607c7e9](https://github.com/resola-ai/deca-apps/commit/607c7e9b6d4b0d5fb257409f9f564839da4d8132))
- **chatbot:** ckl-493 hide select order when orderby is relevant ([#2950](https://github.com/resola-ai/deca-apps/issues/2950)) ([ebfa6f9](https://github.com/resola-ai/deca-apps/commit/ebfa6f9b12cc60a6647b85c2e0fd01bd346014d4))
- **chatbot:** ckl-554 add tooltip for name of folder and kb ([#3039](https://github.com/resola-ai/deca-apps/issues/3039)) ([3b99110](https://github.com/resola-ai/deca-apps/commit/3b99110751bd787dc8a31cf04a4486ad736fd2a3))

### Bug Fixes

- **chatbot:** ckl-343 remove action nodes of else port when turn off notfound ([9b55ab5](https://github.com/resola-ai/deca-apps/commit/9b55ab50592daae5eae7b48f3a36290ad78ea3f1))
- **chatbot:** ckl-343 remove action nodes of else port when turn off notfound ([#2855](https://github.com/resola-ai/deca-apps/issues/2855)) ([5015879](https://github.com/resola-ai/deca-apps/commit/501587915c8307e35c493b0ccb7014bbfc4630e4))
- **chatbot:** CKL-386 Uncomment the threshold feature ([#2859](https://github.com/resola-ai/deca-apps/issues/2859)) ([72ba4f3](https://github.com/resola-ai/deca-apps/commit/72ba4f378be95b367fa92b8de1184ad856382add))
- **chatbot:** CKL-420 Handle empty paragraph as break lines in Block Note markdown ([7675f74](https://github.com/resola-ai/deca-apps/commit/7675f74e4f6b11a4d8973aa0222792891e81b506))
- **chatbot:** CKL-422 Correct encode and decode content from Text Card to adapt with URI safety ([7dbc764](https://github.com/resola-ai/deca-apps/commit/7dbc7646fd9be215c10b9a713ec5b77a57e158c6))
- **chatbot:** CKL-422 Correct encode and decode content from Text Card to adapt with URI safety ([06f8ba1](https://github.com/resola-ai/deca-apps/commit/06f8ba180700951b40b0f3e7037fc8de8edabe70))
- **chatbot:** CKL-422 Correct parsing url from Text Card editor ([b3d43f9](https://github.com/resola-ai/deca-apps/commit/b3d43f972dd3de64b517edc5ca5e2da0ce49a1be))
- **chatbot:** CKL-422 Correct parsing url from Text Card editor ([8312a29](https://github.com/resola-ai/deca-apps/commit/8312a299801180d6259f3ac4dec8b691e89ab3d8))
- **chatbot:** CKL-422 Correct url format from blocknote markdown ([614cb7a](https://github.com/resola-ai/deca-apps/commit/614cb7aaad0a3ac989d66c627e23fc053484515f))
- **chatbot:** ckl-446 preview button in related articles is disabled ([e135796](https://github.com/resola-ai/deca-apps/commit/e135796e8d6b3018c899fb236c5676e4ead0c0b4))
- **chatbot:** ckl-446 preview button in related articles is disabled ([8675e10](https://github.com/resola-ai/deca-apps/commit/8675e10a3ad9f6ef4ffeb1e6c7afb66e6a644a7d))
- **chatbot:** CKL-469 Correct wrong breakline export from block note ([e628585](https://github.com/resola-ai/deca-apps/commit/e628585adff57857a90f8ab3217775529ac85ee9))
- **chatbot:** CKL-469 Correct wrong breakline export from block note ([275349e](https://github.com/resola-ai/deca-apps/commit/275349eccb7d190ec5635b6a2d8969ab8ed6d2be))
- **chatbot:** CKL-496 - Move KB card threshould settings to advanced settings ([#2956](https://github.com/resola-ai/deca-apps/issues/2956)) ([8fec371](https://github.com/resola-ai/deca-apps/commit/8fec371f9b8c99b1eff09dcbd754eb85a98697fc))
- **chatbot:** CKL-508 - Improve email input in Email Node ([#2871](https://github.com/resola-ai/deca-apps/issues/2871)) ([df6f409](https://github.com/resola-ai/deca-apps/commit/df6f409b533376221d406854b31c5c157d0da0f9))
- **chatbot:** ckl-551 update showing integration id ([#2941](https://github.com/resola-ai/deca-apps/issues/2941)) ([5a2bd43](https://github.com/resola-ai/deca-apps/commit/5a2bd43554c9d660ef070845ca41c61db35c4449))
- **chatbot:** ckl-574 the order or article in kb node ([#3073](https://github.com/resola-ai/deca-apps/issues/3073)) ([a4cc48b](https://github.com/resola-ai/deca-apps/commit/a4cc48b67a895b939f0f9c3809a12cd68668d17d))
- **chatbot:** CKL-597 - [Flow] Show message for variable name error ([#3060](https://github.com/resola-ai/deca-apps/issues/3060)) ([bdfd36f](https://github.com/resola-ai/deca-apps/commit/bdfd36fc8229c7c078a93f2aee3637e53aeb851c))
- **chatwindow,chatbot:** CKL-469 Correct encode issue to remove // character from URL ([4e8ddb7](https://github.com/resola-ai/deca-apps/commit/4e8ddb7a35b87851eb87c1d3eed145f78b929328))
- **chatwindow,chatbot:** CKL-469 Correct encode issue to remove // character from URL ([e1c6c8d](https://github.com/resola-ai/deca-apps/commit/e1c6c8daf7453e8bf6f4ce2e5cdd9245394ced6e))
- **chatwindow,chatbot:** CKL-469 Corrected blank row and % character from URL ([17129b1](https://github.com/resola-ai/deca-apps/commit/17129b174a4fa523f711abdb8b63206e2b6e2964))
- **chatwindow,chatbot:** CKL-469 Corrected blank row and % character from URL ([d7fc36b](https://github.com/resola-ai/deca-apps/commit/d7fc36b633b1e5c899602a810af62e78f67a0b9d))
- **chatwindow,chatbot:** CKL-469 Corrected spacing between lines in markdown text ([2f18042](https://github.com/resola-ai/deca-apps/commit/2f180428d3c6f3e7a7e95de8fd09e7b99dca6c7c))
- **chatwindow,chatbot:** CKL-469 Corrected spacing between lines in markdown text ([e24c64e](https://github.com/resola-ai/deca-apps/commit/e24c64e95f331830eb1c389f715668706f7580a9))

### [0.5.6](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.6) (2024-10-01)

### Bug Fixes

- **chatbot:** ckl-343 remove action nodes of else port when turn off notfound ([9b55ab5](https://github.com/resola-ai/deca-apps/commit/9b55ab50592daae5eae7b48f3a36290ad78ea3f1))
- **chatbot:** ckl-343 remove action nodes of else port when turn off notfound ([#2855](https://github.com/resola-ai/deca-apps/issues/2855)) ([5015879](https://github.com/resola-ai/deca-apps/commit/501587915c8307e35c493b0ccb7014bbfc4630e4))
- **chatbot:** CKL-386 Uncomment the threshold feature ([#2859](https://github.com/resola-ai/deca-apps/issues/2859)) ([72ba4f3](https://github.com/resola-ai/deca-apps/commit/72ba4f378be95b367fa92b8de1184ad856382add))
- **chatbot:** CKL-420 Handle empty paragraph as break lines in Block Note markdown ([7675f74](https://github.com/resola-ai/deca-apps/commit/7675f74e4f6b11a4d8973aa0222792891e81b506))
- **chatbot:** CKL-422 Correct encode and decode content from Text Card to adapt with URI safety ([7dbc764](https://github.com/resola-ai/deca-apps/commit/7dbc7646fd9be215c10b9a713ec5b77a57e158c6))
- **chatbot:** CKL-422 Correct encode and decode content from Text Card to adapt with URI safety ([06f8ba1](https://github.com/resola-ai/deca-apps/commit/06f8ba180700951b40b0f3e7037fc8de8edabe70))
- **chatbot:** CKL-422 Correct parsing url from Text Card editor ([b3d43f9](https://github.com/resola-ai/deca-apps/commit/b3d43f972dd3de64b517edc5ca5e2da0ce49a1be))
- **chatbot:** CKL-422 Correct parsing url from Text Card editor ([8312a29](https://github.com/resola-ai/deca-apps/commit/8312a299801180d6259f3ac4dec8b691e89ab3d8))
- **chatbot:** CKL-422 Correct url format from blocknote markdown ([614cb7a](https://github.com/resola-ai/deca-apps/commit/614cb7aaad0a3ac989d66c627e23fc053484515f))
- **chatbot:** ckl-446 preview button in related articles is disabled ([e135796](https://github.com/resola-ai/deca-apps/commit/e135796e8d6b3018c899fb236c5676e4ead0c0b4))
- **chatbot:** ckl-446 preview button in related articles is disabled ([8675e10](https://github.com/resola-ai/deca-apps/commit/8675e10a3ad9f6ef4ffeb1e6c7afb66e6a644a7d))
- **chatbot:** CKL-469 Correct wrong breakline export from block note ([e628585](https://github.com/resola-ai/deca-apps/commit/e628585adff57857a90f8ab3217775529ac85ee9))
- **chatbot:** CKL-469 Correct wrong breakline export from block note ([275349e](https://github.com/resola-ai/deca-apps/commit/275349eccb7d190ec5635b6a2d8969ab8ed6d2be))
- **chatwindow,chatbot:** CKL-469 Correct encode issue to remove // character from URL ([4e8ddb7](https://github.com/resola-ai/deca-apps/commit/4e8ddb7a35b87851eb87c1d3eed145f78b929328))
- **chatwindow,chatbot:** CKL-469 Correct encode issue to remove // character from URL ([e1c6c8d](https://github.com/resola-ai/deca-apps/commit/e1c6c8daf7453e8bf6f4ce2e5cdd9245394ced6e))
- **chatwindow,chatbot:** CKL-469 Corrected blank row and % character from URL ([17129b1](https://github.com/resola-ai/deca-apps/commit/17129b174a4fa523f711abdb8b63206e2b6e2964))
- **chatwindow,chatbot:** CKL-469 Corrected blank row and % character from URL ([d7fc36b](https://github.com/resola-ai/deca-apps/commit/d7fc36b633b1e5c899602a810af62e78f67a0b9d))
- **chatwindow,chatbot:** CKL-469 Corrected spacing between lines in markdown text ([2f18042](https://github.com/resola-ai/deca-apps/commit/2f180428d3c6f3e7a7e95de8fd09e7b99dca6c7c))
- **chatwindow,chatbot:** CKL-469 Corrected spacing between lines in markdown text ([e24c64e](https://github.com/resola-ai/deca-apps/commit/e24c64e95f331830eb1c389f715668706f7580a9))

### [0.5.5](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.5) (2024-09-27)

### Bug Fixes

- **chatbot:** CKL-469 Correct wrong breakline export from block note ([ae10530](https://github.com/resola-ai/deca-apps/commit/ae1053019f207dac3240656320bd8434028a9886))

### [0.5.4](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.4) (2024-09-26)

### Bug Fixes

- **chatbot:** CKL-386 Revert the code change of ticket CKL-386 and Mantine version from PROD ([da0e242](https://github.com/resola-ai/deca-apps/commit/da0e242425493af088da48d7b626539bb2dca6ed))

### [0.5.3](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.3) (2024-09-26)

### Features

- **chatbot:** CKL-386 - [Flow] Add threshold setting for search & rag mode on KB card ([#2727](https://github.com/resola-ai/deca-apps/issues/2727)) ([7d85f32](https://github.com/resola-ai/deca-apps/commit/7d85f32e02efcbb7a58c061d52a62140b1c743ab))

### Bug Fixes

- **chatbot:** Add similarityThreshold setting ([#2731](https://github.com/resola-ai/deca-apps/issues/2731)) ([a8425e1](https://github.com/resola-ai/deca-apps/commit/a8425e120b2e2e10a2f794191fe381ca7725c36e))
- **chatbot:** CKL-420 Handle empty paragraph as break lines in Block Note markdown ([64bf4fd](https://github.com/resola-ai/deca-apps/commit/64bf4fd4c81b23d8664f0958e6df68b8d9f7feff))
- **chatbot:** CKL-422 Correct url format from blocknote markdown ([e52fcd6](https://github.com/resola-ai/deca-apps/commit/e52fcd6b9639e866fcb2ac5a33356d355f5ec30a))

- **chatwindow,chatbot:** CKL-469 Correct encode issue to remove // character from URL ([3541fe0](https://github.com/resola-ai/deca-apps/commit/3541fe0462186231418d0b2e549883da327da8a5))
- **chatwindow,chatbot:** CKL-469 Corrected blank row and % character from URL ([47d9887](https://github.com/resola-ai/deca-apps/commit/47d9887aeab732eb345578d852bf79c22d9b7b63))
- **chatwindow,chatbot:** CKL-469 Corrected spacing between lines in markdown text ([3310f52](https://github.com/resola-ai/deca-apps/commit/3310f52256a60f375d39d4014cf6fa38d3fe8114))

### [0.5.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.2) (2024-09-25)

### Features

-- no feature ---

### Bug Fixes

- **chatbot:** CKL-420 Handle empty paragraph as break lines in Block Note markdown ([7255bf7](https://github.com/resola-ai/deca-apps/commit/7255bf7270d9b5c2e95c937731df6c7a44a165d4))
- **chatbot:** CKL-422 Correct encode and decode content from Text Card to adapt with URI safety ([f7ff5b0](https://github.com/resola-ai/deca-apps/commit/f7ff5b0b46242b6fcf3bc7cf6dab12f6d6a250f1))
- **chatbot:** CKL-422 Correct parsing url from Text Card editor ([9513b20](https://github.com/resola-ai/deca-apps/commit/9513b204df0d2e9b955c7aa4fb37603e4b965ed6))
- **chatbot:** CKL-422 Correct url format from blocknote markdown ([533d4d9](https://github.com/resola-ai/deca-apps/commit/533d4d9d3e57e3093d606056a7de649976216329))

### [0.5.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.1) (2024-09-19)

### Features

- **chatbot:** ckl-334 update no found message ([9ec7506](https://github.com/resola-ai/deca-apps/commit/9ec7506d948cbaa777d28e57ea8165b657b1f756))

### Bug Fixes

- **chatbot:** ckl-441 auto select related article ([dbcf482](https://github.com/resola-ai/deca-apps/commit/dbcf482ee28708883fe600022eb303442a0b6c95))

## [0.5.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.0) (2024-09-19)

### Features

- **chatbot:** CKL-309 - Implement Duplication of Entire Bot Including Intents and settings ([#2653](https://github.com/resola-ai/deca-apps/issues/2653)) ([4acd5a3](https://github.com/resola-ai/deca-apps/commit/4acd5a3c8ddbb7427cf2d5570dfdf62819949e84))
- **chatbot:** CKL-310 - [Flow UI] Enable Duplication of Conversation Flows in Chatbot System ([#2473](https://github.com/resola-ai/deca-apps/issues/2473)) ([ce05153](https://github.com/resola-ai/deca-apps/commit/ce0515353dc66ed6b876a90c5ec916167ed7fcc7))
- **chatbot:** ckl-334 related article list ([44796ab](https://github.com/resola-ai/deca-apps/commit/44796ab47349f7a3ca64935ca3aaa45caaab4e3e))
- **chatbot:** ckl-334 related article list ([b6eeeb5](https://github.com/resola-ai/deca-apps/commit/b6eeeb510bf06b7bf4628d09b85089be7db7ffec))
- **chatbot:** ckl-334 related article list ([dcfb6ba](https://github.com/resola-ai/deca-apps/commit/dcfb6baf063aab1de6583d4999d062bb968063a6))
- **chatbot:** ckl-334 related article list ([1666d81](https://github.com/resola-ai/deca-apps/commit/1666d817aff57170d6e22532a388a62dcf032bb4))
- **chatbot:** ckl-334 related article list ([4602fea](https://github.com/resola-ai/deca-apps/commit/4602feaa185acf040b4e97a5a54f91992b19619b))
- **chatbot:** ckl-334 related article list ([c905f1d](https://github.com/resola-ai/deca-apps/commit/c905f1d4ab8983bda6035bb71e5bc47e4781ba5d))
- **chatbot:** ckl-334 related article list ([7fe0533](https://github.com/resola-ai/deca-apps/commit/7fe05339566a3e10e1fb91b130bbc1a08b3051ca))
- **chatbot:** ckl-334 related article list ([24af800](https://github.com/resola-ai/deca-apps/commit/24af800d0af568531d8f18e6b252c143cab643b9))
- **chatbot:** ckl-334 update body data ([168a812](https://github.com/resola-ai/deca-apps/commit/168a81242f21a2ac3f5eb4bdf0ab5c3c148f5225))
- **chatbot:** CKL-363 Support variable input in phone number link with Markdown ([8343197](https://github.com/resola-ai/deca-apps/commit/8343197190efcbf3f86e23c7cf357424dd6644c9))

### Bug Fixes

- **chatbot:** Always show intent message since it explains the feature ([#2660](https://github.com/resola-ai/deca-apps/issues/2660)) ([a3127f1](https://github.com/resola-ai/deca-apps/commit/a3127f1dc696c63f0d51d82036ead971fa8554bf))
- **chatbot:** ckl-339 fix cached folder in kb card ([039a0fb](https://github.com/resola-ai/deca-apps/commit/039a0fbcfd6dd0a947bc4cd9a6500ed8b7b76c0e))
- **chatbot:** CKL-342 - [Flow] Can not scroll on table card ([#2542](https://github.com/resola-ai/deca-apps/issues/2542)) ([4a1ed75](https://github.com/resola-ai/deca-apps/commit/4a1ed75398da04f7a1e78bd84fa5b74a0959fe8c))
- **chatbot:** CKL-36 - Add empty state for intent list ([#2645](https://github.com/resola-ai/deca-apps/issues/2645)) ([ac13698](https://github.com/resola-ai/deca-apps/commit/ac136986e869fc02d7422532faa0392a559e88db))

### [0.4.4](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.4) (2024-09-04)

### Features

- **chatbot:** CKL-287 Add Block Editor back to Text Card and add Tables, Forms apps to hub menu ([47d7e05](https://github.com/resola-ai/deca-apps/commit/47d7e05893cb7061187f733ef3eadabb820402b3))
- **chatbot:** ckl-299 implement search folder, kb, article ([a7f214f](https://github.com/resola-ai/deca-apps/commit/a7f214fb0a9703c96f9dc51205c528ba0e83795e))
- **chatbot:** ckl-299 implement search folder, kb, article ([22e9918](https://github.com/resola-ai/deca-apps/commit/22e991830e6cd8da7148ee9039e938e5259e38c5))
- **chatbot:** ckl-299 implement search folder, kb, article ([1b9e754](https://github.com/resola-ai/deca-apps/commit/1b9e75438172e3149a5445d3f4a4658ec9d23113))
- **chatbot:** ckl-299 implement search folder, kb, article ([66b91b1](https://github.com/resola-ai/deca-apps/commit/66b91b121999303b2c0b406383225dc4ecdabe82))
- **chatbot:** ckl-299 implement search folder, kb, article ([aa88f1a](https://github.com/resola-ai/deca-apps/commit/aa88f1a9ab57cdcaa103d880ad57aee6f5cdab52))

### Bug Fixes

- **chatbot:** CKL-287 Correct Markdown render with checkbox and adjust UI of editor ([8fbf10f](https://github.com/resola-ai/deca-apps/commit/8fbf10f698e4596212927971f67bbbb3c397b6a0))
- **chatbot:** CKL-287 Corrected markdown checking condition and some style issues with Markdown editor ([a68c4fd](https://github.com/resola-ai/deca-apps/commit/a68c4fd624c8374e10660d4ad02e8a46c7d4e76c))
- **chatbot:** CKL-287 Corrected markdown checking condition with specific strike quote ([1b1e9a2](https://github.com/resola-ai/deca-apps/commit/1b1e9a20483ecc45ecd3bef799dcd94e9054603a))
- **chatbot:** CKL-287 Custom Link renderer in Markdown to support open in new tab ([d6c0167](https://github.com/resola-ai/deca-apps/commit/d6c01679a4e0ac315944a939947bb5c4d71efebe))
- **chatbot:** CKL-287 Disabled text alignment from Markdown Editor that not supported ([d3fc6a4](https://github.com/resola-ai/deca-apps/commit/d3fc6a497d5964084679d3dbc17a4aa914051e5b))
- **chatbot:** ckl-90 the connection line is not removed after turnoff not found ([4df2f12](https://github.com/resola-ai/deca-apps/commit/4df2f129391bc77fc64f4830fd070c72512c3ad4))

### [0.4.3](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.3) (2024-08-26)

### Features

- **chatbot:** cb-1423 show system message about required intent ([a038d3a](https://github.com/resola-ai/deca-apps/commit/a038d3a35c00a8d49b4a83c4ce11a7b97adc5232))
- **chatbot:** cb-1495 improve capture intent card ([f7bae4d](https://github.com/resola-ai/deca-apps/commit/f7bae4de9e997d0b75b21611ddca51074b544a24))
- **chatbot:** cb-1511 add limit fixed code for bases and tables api ([968fd1f](https://github.com/resola-ai/deca-apps/commit/968fd1fc138db97a1c0b8215576fd13357ccc1b0))
- **chatbot:** cb-1511 tbale card ([e40a1c7](https://github.com/resola-ai/deca-apps/commit/e40a1c799d0e1e5716a0d56802b01530ebfb36f6))
- **chatbot:** cb-1511 update i18n file ([30faaae](https://github.com/resola-ai/deca-apps/commit/30faaaedfb55dc0dc1e4343f9515bcf897feee3c))
- **chatbot:** cb-1511 update scroll able in table setting ([2af1c45](https://github.com/resola-ai/deca-apps/commit/2af1c45db87e09e6ccefaec2b7a21d1e55f9fc37))
- **chatbot:** cb-1511 update scroll able in table setting ([79c875e](https://github.com/resola-ai/deca-apps/commit/79c875e1d237ec7cba35c89124ab41e4571788ea))
- **chatbot:** cb-1511 update table card ([3694e23](https://github.com/resola-ai/deca-apps/commit/3694e2341b7f6d1c2d36d021159b275004fc2ca3))
- **chatbot:** cb-1663 hide private kb on kb card ([5e8138b](https://github.com/resola-ai/deca-apps/commit/5e8138b99ce0c3914248ca432a3db247ed7c9859))
- **chatbot:** cb-1711 allow delete properties in update table ([2cedcfe](https://github.com/resola-ai/deca-apps/commit/2cedcfe9d059a6e923927332f3b0699201f7bf82))
- **chatbot:** ckl-215: update system messages ([9bc675c](https://github.com/resola-ai/deca-apps/commit/9bc675ca7f818603e781e5e75f296a6d35629eff))
- **chatbot:** CKL-240 Handle translation in Markdown Block Editor ([118f90d](https://github.com/resola-ai/deca-apps/commit/118f90df976ca88ed5a60525c742954c4cbcc03f))
- **chatbot:** CKL-240 Implement the Block Editor with Markdown support for Text Card ([89ff718](https://github.com/resola-ai/deca-apps/commit/89ff718e869e10124808f44cab00b83b7fea1cf4))
- **chatbot:** CKL-240 Temporary disabled markdown editor to move to release 3rd Sept ([e220c20](https://github.com/resola-ai/deca-apps/commit/e220c2063aa2496a6909a5215773baec8eb90276))
- **chatbot:** CKL-264 Implement the infinite scroll for KB List in Select KB modal ([7c19f12](https://github.com/resola-ai/deca-apps/commit/7c19f12f8679cc6280ea4aaec498fc600fbcbcc1))
- **chatbot:** ckl-81 add folder view and multi select kb ([47da815](https://github.com/resola-ai/deca-apps/commit/47da81525aaba792b5cef8e265207160b3f6917a))
- **chatbot:** ckl-81 add folder view and multi select kb ([017796e](https://github.com/resola-ai/deca-apps/commit/017796ea24e47d11985865dbdb76c982495b095c))
- **chatbot:** ckl-81 add folder view and multi select kb ([76db6ff](https://github.com/resola-ai/deca-apps/commit/76db6ff98976ff66f7d6f02f3424363bedfab2d1))
- **chatbot:** ckl-81 add folder view and multi select kb ([bd6d78f](https://github.com/resola-ai/deca-apps/commit/bd6d78f7b897728bc89dca170d3202e1b4f814b5))
- **chatbot:** ckl-81 add folder view and multi select kb ([cae14d0](https://github.com/resola-ai/deca-apps/commit/cae14d00204aa4b685dc4e79c058a1be63fae36c))

### Bug Fixes

- **chatbot:** cb-1539 update ja word on intent card & node ([a752939](https://github.com/resola-ai/deca-apps/commit/a752939c692f88ce7c268bb8d854827c33ed4570))
- **chatbot:** cb-1626 update validation schema ([951dc26](https://github.com/resola-ai/deca-apps/commit/951dc2629cdaa8d50d382346cdb7e7224116a698))
- **chatbot:** cb-1628 page is automacally reload when press button card ([d5a7866](https://github.com/resola-ai/deca-apps/commit/d5a78663aea9087187115a3605e6a613d20a6f46))
- **chatbot:** cb-1629 reset content kb node setting when switch ([bb525ef](https://github.com/resola-ai/deca-apps/commit/bb525ef537b568f1964ef526f3ad0783663509cd))
- **chatbot:** cb-1700 update logic to correct order of actions node ([a325972](https://github.com/resola-ai/deca-apps/commit/a3259726cdd06d013e5786f8e7a92b9ca4219055))
- **chatbot:** cb-1737 newest qna not appear in first of list qna ([d205fa0](https://github.com/resola-ai/deca-apps/commit/d205fa0fb9381aa3cdf35ea2e6e84cdb5a969dc1))
- **chatbot:** cb-1738 video and image not show in select qna modal ([d2b1b74](https://github.com/resola-ai/deca-apps/commit/d2b1b74410af9def3bbb516cc34b2baeb3b8beec))
- **chatbot:** cb-1738 video and image not show in select qna modal ([95ab0b8](https://github.com/resola-ai/deca-apps/commit/95ab0b89ecc7b90556f09fa48e333d68857bea26))
- **chatbot:** CKL-263 Corrected items [#1](https://github.com/resola-ai/deca-apps/issues/1), [#5](https://github.com/resola-ai/deca-apps/issues/5), [#6](https://github.com/resola-ai/deca-apps/issues/6), [#7](https://github.com/resola-ai/deca-apps/issues/7) from feedback on QnA Node ([5be7f2e](https://github.com/resola-ai/deca-apps/commit/5be7f2eb33a7d1cab21f697550d4fd2d7014e9d3))
- **chatbot:** CKL-270 Correct condition when checking selected root folder ([6b8918b](https://github.com/resola-ai/deca-apps/commit/6b8918b8a9c6817c28fcf327be697358d19d7951))

### [0.4.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.2) (2024-07-18)

### Features

- **chatbot:** cb-1423 show system message about required intent ([a038d3a](https://github.com/resola-ai/deca-apps/commit/a038d3a35c00a8d49b4a83c4ce11a7b97adc5232))
- **chatbot:** cb-1495 improve capture intent card ([f7bae4d](https://github.com/resola-ai/deca-apps/commit/f7bae4de9e997d0b75b21611ddca51074b544a24))
- **chatbot:** cb-1511 add limit fixed code for bases and tables api ([968fd1f](https://github.com/resola-ai/deca-apps/commit/968fd1fc138db97a1c0b8215576fd13357ccc1b0))
- **chatbot:** cb-1511 tbale card ([e40a1c7](https://github.com/resola-ai/deca-apps/commit/e40a1c799d0e1e5716a0d56802b01530ebfb36f6))
- **chatbot:** cb-1511 update i18n file ([30faaae](https://github.com/resola-ai/deca-apps/commit/30faaaedfb55dc0dc1e4343f9515bcf897feee3c))
- **chatbot:** cb-1511 update scroll able in table setting ([2af1c45](https://github.com/resola-ai/deca-apps/commit/2af1c45db87e09e6ccefaec2b7a21d1e55f9fc37))
- **chatbot:** cb-1511 update scroll able in table setting ([79c875e](https://github.com/resola-ai/deca-apps/commit/79c875e1d237ec7cba35c89124ab41e4571788ea))
- **chatbot:** cb-1511 update table card ([3694e23](https://github.com/resola-ai/deca-apps/commit/3694e2341b7f6d1c2d36d021159b275004fc2ca3))

### Bug Fixes

- **chatbot:** cb-1539 update ja word on intent card & node ([a752939](https://github.com/resola-ai/deca-apps/commit/a752939c692f88ce7c268bb8d854827c33ed4570))
- **chatbot:** cb-1626 update validation schema ([951dc26](https://github.com/resola-ai/deca-apps/commit/951dc2629cdaa8d50d382346cdb7e7224116a698))

### [0.4.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.1) (2024-07-01)

### Features

- **chatbot:** cb-1522 fix import duplicate ([edf1f5a](https://github.com/resola-ai/deca-apps/commit/edf1f5a62698d82ddb874b6247766d2ba55996ad))
- **chatbot:** cb-1522 support article KB ([9fd883a](https://github.com/resola-ai/deca-apps/commit/9fd883afaf67cf3fa3be34a1315201c544c58288))
- **chatbot:** cb-1522 support article KB ([db5bea1](https://github.com/resola-ai/deca-apps/commit/db5bea1f1dd613eaccc917730a282b5d118551bf))
- **chatbot:** cb-1522 update condition show select kb ([90f7a09](https://github.com/resola-ai/deca-apps/commit/90f7a094561eff3dadd4effc5c193f689745df02))
- **chatbot:** cb-1522 update logic qna setting ([467039f](https://github.com/resola-ai/deca-apps/commit/467039f908a8859a046ee991809882fa3a6a1185))
- **chatbot:** cb-1522 update paging when select kb ([9f7ce25](https://github.com/resola-ai/deca-apps/commit/9f7ce252ff11133178a9a8a29c7dd39527b1031f))

## [0.4.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.0) (2024-06-20)

### Features

- **chatbot:** cb-1394 improve button card behavior ([f1d4b25](https://github.com/resola-ai/deca-apps/commit/f1d4b2566267cd95393726fea5f3113f6a714d2e))
- **chatbot:** cb-1394 remove log ([fa7df0d](https://github.com/resola-ai/deca-apps/commit/fa7df0d04595cdb37ae98f6002fbfce87cb8396d))
- **chatbot:** cb-1394 update new specs always show nomatch ([8b932d5](https://github.com/resola-ai/deca-apps/commit/8b932d5168a2154278bc82ac5dec5d7ffab48d8a))
- **chatbot:** cb-1394 update new specs button card ([bb58646](https://github.com/resola-ai/deca-apps/commit/bb58646e41792afb026ff608790b7eb38d6bef00))
- **chatbot:** cb-1394 update specs button step ([5e1c864](https://github.com/resola-ai/deca-apps/commit/5e1c8647cdebeac97a829ff766cd1e2abcd5b777))
- **chatbot:** cb-1394 update text message ([8ffb53f](https://github.com/resola-ai/deca-apps/commit/8ffb53fa9f1426fa1ef3690b21b3c2a549aa56a7))

### Bug Fixes

- **chatbot:** CB-1492 update text resources for limitation ([7e09666](https://github.com/resola-ai/deca-apps/commit/7e0966696830d41baff3492afb602a3b4f3adff4))
- **chatbot:** cb-1503 blank page when delete action ([49e9a59](https://github.com/resola-ai/deca-apps/commit/49e9a598fa27043f20170b7b9f09dfa76c61680f))
- **chatbot:** cb-1503 remove redundant code ([dceb92c](https://github.com/resola-ai/deca-apps/commit/dceb92cacb361550c42aa75e9fcac4cd5ad2e43c))

### [0.3.3](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.3) (2024-06-06)

### Features

- **chatbot:** CB - 1374 - Add chatbot upload asset api ([cacb887](https://github.com/resola-ai/deca-apps/commit/cacb8875f5b63691f23274e1550834698ec427ce))
- **chatbot:** CB -1374 - integrate upload with create and save bots ([2ba1705](https://github.com/resola-ai/deca-apps/commit/2ba170567bf9363dbcc4bfbb37741c8bf38e2716))
- **chatbot:** CB-1262 setup limit notifications for bot and flow ([789f94f](https://github.com/resola-ai/deca-apps/commit/789f94f21353e0f3f7ca68ef08dbe0eea1687a97))
- **chatbot:** CB-1319 update limit node per flow ([61e8c81](https://github.com/resola-ai/deca-apps/commit/61e8c8109b230eff213ea9bada44d0bae80acb96))
- **chatbot:** CB-1320 setup limit step per node ([a482dbf](https://github.com/resola-ai/deca-apps/commit/a482dbf3d6405c50fdefeecc10d3b874de88f64f))
- **chatbot:** cb-1337 update date format chat logs page ([5e8a01f](https://github.com/resola-ai/deca-apps/commit/5e8a01f0c52dcf1e248bdb19b190cdf148fb5585))
- **chatbot:** CB-1379 prevent edit default flow name ([52d1267](https://github.com/resola-ai/deca-apps/commit/52d1267b20faab5f4fc46914ca3ca44488f7a438))
- **chatbot:** cb-1395 add option to search/rag by using var value ([dc4258e](https://github.com/resola-ai/deca-apps/commit/dc4258e50c0f149b18f030155811d2c7487e39e8))
- **chatbot:** cb-1396 remove all actions of last step when has new steps ([355ff9b](https://github.com/resola-ai/deca-apps/commit/355ff9b796fbdf3fbe1e9d73ce14a28f1aba3a5e))
- **chatbot:** cb-1396 update condition to drop step to block ([0de6525](https://github.com/resola-ai/deca-apps/commit/0de6525174d950e850d18293c41e74404959f746))
- **chatbot:** cb-1396 update import and remove log ([c84a162](https://github.com/resola-ai/deca-apps/commit/c84a162620a8897d4dce6c016b64d724c8cd322e))
- **chatbot:** cb-1421 change select to input qna setting ([494f387](https://github.com/resola-ai/deca-apps/commit/494f3871be2030c3f391052776166f1f8dcf048e))
- **chatbot:** cb-1421 update title when update intent ([82c7708](https://github.com/resola-ai/deca-apps/commit/82c770856889e6e6f7017b87e07ca354f6fbaf61))
- **chatbot:** cb-1446 fixed removed title ([5f27ca9](https://github.com/resola-ai/deca-apps/commit/5f27ca93bcd3f9647f964ed176085962284bf2c3))
- **chatbot:** cb-1446 remove type KB when mode type specified ([bbccc56](https://github.com/resola-ai/deca-apps/commit/bbccc56b2a6488fa01e8f4b20aa2e7ae3426afaf))
- **chatbot:** CB-1453 normalize button data if connected with kb node ([f98a5bc](https://github.com/resola-ai/deca-apps/commit/f98a5bc13960e3b27479c74b755749c8daf23201))
- **chatbot:** CB-620 add capture intent node settings ([8c11cb9](https://github.com/resola-ai/deca-apps/commit/8c11cb9d65c97ea495df47c4b1e5132a33f351fe))
- **chatbot:** reject html tag on chat-logs ([d4d431f](https://github.com/resola-ai/deca-apps/commit/d4d431f4ab86a2258e353bde382bfe2a46bd6db0))
- **chatbot:** reject html tag on chat-logs ([6330220](https://github.com/resola-ai/deca-apps/commit/633022039c4c57bc3bcaeb8726e6f2f4fa0ebf37))

### Bug Fixes

- **chatbot:** add intent utterances validation text, adjust flow name text ([53354a9](https://github.com/resola-ai/deca-apps/commit/53354a94687c4c5294e4caef6270d368f7a95eeb))
- **chatbot:** CB-1139 default flow disappear ([a1d282f](https://github.com/resola-ai/deca-apps/commit/a1d282f9b90deb5ea91009440fe4caa74ddd5495))
- **chatbot:** CB-1139 update default flow at bottom list and sort new flow ([178de4f](https://github.com/resola-ai/deca-apps/commit/178de4f731e16a3c028f52ddba9d5b07fef26a11))
- **chatbot:** CB-1139 update start flow position ([3111f7f](https://github.com/resola-ai/deca-apps/commit/3111f7fc5d28395e4af8786aa69dfd0bbdbefe79))
- **chatbot:** CB-1318 update limit flow per bot ([752bdf9](https://github.com/resola-ai/deca-apps/commit/752bdf99a84e09793e988e31574635ce9e7fbd39))
- **chatbot:** CB-1319 improve caching for alert ([de00640](https://github.com/resola-ai/deca-apps/commit/de006405cb5d44d51d0f803dea1e9be9bb7f78b5))
- **chatbot:** CB-1320 update condition reset position when drag stop ([a6422be](https://github.com/resola-ai/deca-apps/commit/a6422be014a8e80c8358444f7d4dd4b00fa0c4ee))
- **chatbot:** CB-1320 update config on toolbar ([0991f7c](https://github.com/resola-ai/deca-apps/commit/0991f7c542cd27a821771238a38aca30c25a8ee9))
- **chatbot:** CB-1320 update limit step when drag from toolbar ([c4b870e](https://github.com/resola-ai/deca-apps/commit/c4b870e6f7c313ec48254e0041ca19e22a52bb6d))
- **chatbot:** CB-1373 Correct dependencies in Intent scope to correct the flow data updating many times ([e5dcd6d](https://github.com/resola-ai/deca-apps/commit/e5dcd6d3dde1315bc0e192db8d46c1bc15928a39))
- **chatbot:** CB-1373 Improve behavior in Intent scope when user toggle fastly ([ce0a607](https://github.com/resola-ai/deca-apps/commit/ce0a607337d89bd21ad9f3cabbe1c12826ff968a))
- **chatbot:** CB-1373 Remove unuse async syntax ([7527840](https://github.com/resola-ai/deca-apps/commit/75278404fb38e4e0c759f68bc854f5acceaf9986))
- **chatbot:** CB-1382 make action block name form work properly ([df99c55](https://github.com/resola-ai/deca-apps/commit/df99c55a404af6de6562d0b82cdf2453bf244cb5))
- **chatbot:** CB-1384 hide navbar and notification when showing no access page ([2d9467b](https://github.com/resola-ai/deca-apps/commit/2d9467b9a2aec951b9ba8bb4d34c955fdfc5fd81))
- **chatbot:** CB-1387 fix build failed ([46284fe](https://github.com/resola-ai/deca-apps/commit/46284fe465b8fde5e99f37a8d03c374cf305d613))
- **chatbot:** CB-1414 update limit node condition ([99fc3dc](https://github.com/resola-ai/deca-apps/commit/99fc3dcb3fa98d4c59f3a736b0f75a8b0b887101))
- **chatbot:** CB-1419 update japanese for kb node ([864c428](https://github.com/resola-ai/deca-apps/commit/864c4288a257948527b49d6d5a3682fa0819ff4c))
- **chatbot:** CB-1438 excule intent node from limitation ([2251eea](https://github.com/resola-ai/deca-apps/commit/2251eea1fc7ee8e0a2cee1b87b2dc3a6a42af344))
- **chatbot:** CB-1438 update limit node per flow logic ([3da9fbb](https://github.com/resola-ai/deca-apps/commit/3da9fbb5f730d93a702dad43bfec1effe7849bc0))
- **chatbot:** fix failed ([151bf31](https://github.com/resola-ai/deca-apps/commit/151bf31d15d322477ff1fb5740b0bd6d84eb7f27))
- **chatbot:** Only translate nodeName on JA lang ([349c03e](https://github.com/resola-ai/deca-apps/commit/349c03e63e151bc58f47b4cc666c28a35d8b5995))
- **chatbot:** translate nodeName on node action ([e743251](https://github.com/resola-ai/deca-apps/commit/e743251d40605c81613c0c01be1476adb3bcaa3a))
- **chatbot:** translate nodeName on node action ([543fa52](https://github.com/resola-ai/deca-apps/commit/543fa52cbfffac15ca2a3d0c39c6ea757f68b092))
- **chatbot:** update intent utterances validation, translate correct nodename ([b9b77cf](https://github.com/resola-ai/deca-apps/commit/b9b77cf12879a09566c92e7c77d6ba1df727f79a))

### [0.3.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.2) (2024-05-16)

### Bug Fixes

- **chatbot:** add locale text to action node ([f7448ee](https://github.com/resola-ai/deca-apps/commit/f7448ee39f82ac8fb13f5d1d4a12bdbabf720998))
- **chatbot:** CB-1059 improve drag and drop from toolbar with dndkit ([4e8e14c](https://github.com/resola-ai/deca-apps/commit/4e8e14cea487124fc563a8ee4ff756a4017f68d8))
- **chatbot:** CB-1285 Improve UI and label text info in Form Card settings ([e43fd5f](https://github.com/resola-ai/deca-apps/commit/e43fd5fa31abc6c4d1e0dd2617a328a3f175d590))
- **chatbot:** cb-1286 connection with action and improve delete action ([ddd7585](https://github.com/resola-ai/deca-apps/commit/ddd75851367f916159c6a440452de2d82b58e10f))
- **chatbot:** cb-1286 update convention for define type ([6e040bb](https://github.com/resola-ai/deca-apps/commit/6e040bba1f35f5ba4e3cfdbe4c056f61abcd2028))
- **chatbot:** CB-1309 Correct deleting button and action inside ([dbc2932](https://github.com/resola-ai/deca-apps/commit/dbc29325926d0280b7b57f7aa6f39a9e7edde8d5))
- **chatbot:** CB-1315 Correct undefined error Sentry reported about coords and improve useHeightRef code ([bc390ad](https://github.com/resola-ai/deca-apps/commit/bc390ad6f3c50dcc8e3824771d3bcdaf97e8a7ab))
- **chatbot:** CB-1323 update placeholder for dropdown ([6b3bb12](https://github.com/resola-ai/deca-apps/commit/6b3bb12165a89e0384334339236ec93b74ed652b))
- **chatbot:** CB-1328 update toolbar hover ([e4b8e5d](https://github.com/resola-ai/deca-apps/commit/e4b8e5db5d32d6fbc18c382fddf7647196b7015b))
- **chatbot:** CB-1330 Correct issue drop Block to action and correct width for Action intent ([60884e8](https://github.com/resola-ai/deca-apps/commit/60884e8c03597b127d90e2704bdd5991dbdb99c6))
- **chatbot:** CB-1351 Correct delete Action behavior in Condition node ([4fa5829](https://github.com/resola-ai/deca-apps/commit/4fa58293c8c9ebea667fa84e3d06b4d076576ae5))
- **chatbot:** CB-1362 Increase the length of top-level domain in URL regex ([b707777](https://github.com/resola-ai/deca-apps/commit/b707777abd002912548f3bd09ac83f04f4c30711))
- **chatbot:** CB-1371 search flow name in JA ([9d07ecb](https://github.com/resola-ai/deca-apps/commit/9d07ecb4770dbd4079b367e91ec3c785600a0e4d))
- **chatbot:** CB-1372 Correct the comment message for new util ([97652c6](https://github.com/resola-ai/deca-apps/commit/97652c6fced5e4db739c66384293a92785e52b47))
- **chatbot:** CB-1372 Correct the handler logic in Action goto node ([f4c357f](https://github.com/resola-ai/deca-apps/commit/f4c357fd43d44bb75097cf169d8147dc7b26ed7b))
- **chatbot:** CB-1372 Improve the logic checking related to Action Node ([3373bd6](https://github.com/resola-ai/deca-apps/commit/3373bd6167b38b5d47eae353fc79b98fb363b54c))
- **chatbot:** update to show correct language onerror and flow label ([5746b33](https://github.com/resola-ai/deca-apps/commit/5746b334e44fed6a2c8f12602b3f216e282a756d))

### [0.3.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.1) (2024-05-09)

### Features

- **chatbot:** cb-1125 intent node improvement ([51c1e5b](https://github.com/resola-ai/deca-apps/commit/51c1e5baab3fac9d9e498b86cb9e11787e6aaa00))
- **chatbot:** cb-1125 remove start icon ([9783d8c](https://github.com/resola-ai/deca-apps/commit/9783d8ce913616633c82ee0765839c11f3373a1f))
- **chatbot:** cb-1125 update empty intent ([0e4d9f5](https://github.com/resola-ai/deca-apps/commit/0e4d9f559afd6f593b264fc13c8d3557806f63fa))
- **chatbot:** cb-1125 update locale ([3c7a22e](https://github.com/resola-ai/deca-apps/commit/3c7a22eea02b2e59d1dc2d01ee0050f4614b9ba1))
- **chatbot:** CB-1210 - Fix widgets scroll not work ([d777d65](https://github.com/resola-ai/deca-apps/commit/d777d65550f78422d8b76f1f65f3e06a9230e968))
- **chatbot:** CB-1274 add no access page chatbot ([78054f0](https://github.com/resola-ai/deca-apps/commit/78054f081cfb427d210866a7b2da909f5169c381))
- **chatbot:** CB-1283 Correct dependencies and handle reset form in Goto Node action ([8599317](https://github.com/resola-ai/deca-apps/commit/85993178558eeaa36b11f00ed15563447850c0d2))
- **chatbot:** CB-1284 update dropdown name in form settings ([05899e6](https://github.com/resola-ai/deca-apps/commit/05899e6011b66501aca2fdf1a61fd5436accc055))
- **chatbot:** CB-1290 Handle update data flow immediately after updating and correct ResizeObserver error ([19a8add](https://github.com/resola-ai/deca-apps/commit/19a8add24567083f991c98fe57d29eb674704605))
- **chatbot:** CB-1290 Update dependencies for useEffect when tracking data flow update ([0300e81](https://github.com/resola-ai/deca-apps/commit/0300e812ac915de9c34fe2af6d28df9146d2a2d2))

### Bug Fixes

- **chatbot:** CB-1280 fix chatbot item image ([a1cbe63](https://github.com/resola-ai/deca-apps/commit/a1cbe639e9b5a8d25ddae4ffa375c04ae0cde3d6))
- **chatbot:** CB-1281 update special nodes japanese text ([0bcca92](https://github.com/resola-ai/deca-apps/commit/0bcca92203e4ef512c792c5111142d5623359162))
- **chatbot:** CB-1292 validation for intent label, refetch flow if intent change ([19c68ed](https://github.com/resola-ai/deca-apps/commit/19c68ed7a68c8aa0700150fe3a87b188f3f61d74))
- **chatbot:** CB-1296 update dropdown form setting ([f99b853](https://github.com/resola-ai/deca-apps/commit/f99b853ae9dd20c75b6bc753bab50e5ffbd09dfb))
- **chatbot:** CB-1296 update scroll for option when overflow ([9e7d418](https://github.com/resola-ai/deca-apps/commit/9e7d418f3ae3d03445817b51aa324d5cfccb2fa1))
- **chatbot:** CB-1301 change intent node icon ([0207b8b](https://github.com/resola-ai/deca-apps/commit/0207b8b5d2576b9b2c3f957a3a770a3139781615))
- **chatbot:** CB-1301 fix merge conflict ([4d80750](https://github.com/resola-ai/deca-apps/commit/4d807505c6efa3e9788e1df7ee5ce41fcd1bacd5))
- **chatbot:** CB-1304 Correct the pagination condition in QnA and implement the loading state ([a8737b6](https://github.com/resola-ai/deca-apps/commit/a8737b6ced8de98a7c3426d9691520276100edeb))
- **chatbot:** CB-1305 Correct error handling in update flow ([2da92d9](https://github.com/resola-ai/deca-apps/commit/2da92d937e3ee3c515c8e33b1935c058a1ddfd2f))
- **chatbot:** fix default flow name not translate ([a38dad5](https://github.com/resola-ai/deca-apps/commit/a38dad507e620652429c229e884bc8495776906e))
- **chatbot:** fix issue remove wrong index intent and show incorrect button name ([6ca6a1e](https://github.com/resola-ai/deca-apps/commit/6ca6a1ec3faee49229d7c00fe2b44b0bccf16c9a))
- **chatbot:** update correct flow in intent ([3c8aae0](https://github.com/resola-ai/deca-apps/commit/3c8aae077a54e05d1230f81f3f1727b646f8955e))

## [0.3.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.0) (2024-05-07)

### Features

- **chatbot:** add integration type, show message log correctly ([2e205d1](https://github.com/resola-ai/deca-apps/commit/2e205d1adb4e151b96637b1a5b8329228ccae0bc))
- **chatbot:** CB - 1103 - Add re-order function ([cfd119c](https://github.com/resola-ai/deca-apps/commit/cfd119c0827d54b4c04968438642b950822f1f86))
- **chatbot:** CB-1010 Correct feedback related to unnecessary useEffect ([f2f13de](https://github.com/resola-ai/deca-apps/commit/f2f13de833d0913d92e428cf4c927c9ba06a2312))
- **chatbot:** CB-1010 Integrate Action Chain to Carousel node and clean up component ([bf174f7](https://github.com/resola-ai/deca-apps/commit/bf174f72a96d8f457585e973e0e972bc15ead386))
- **chatbot:** CB-1010 Integrate Action Chain to Condition node and ajust Condition Settings form ([d7ace40](https://github.com/resola-ai/deca-apps/commit/d7ace40967ddb98d5ae2e2611a379d6010dc3af8))
- **chatbot:** CB-1010 Refactor Button Settings form and correct the Action events ([d086dc7](https://github.com/resola-ai/deca-apps/commit/d086dc70b7c49ea04915311c6f80ae4779f5f758))
- **chatbot:** CB-1010 Sync and adjust with new Deleting node logic for Actions ([f2d2712](https://github.com/resola-ai/deca-apps/commit/f2d27129415ba7765a2e16158a293d675de7ebb6))
- **chatbot:** CB-1016 Double check on Validation flow and correct port and sort issues in Node Steps ([d7377ae](https://github.com/resola-ai/deca-apps/commit/d7377ae1cb635087029543c883fac49ca8ad5754))
- **chatbot:** CB-1018 Update action info for Action Node to more clearly ([f4da54b](https://github.com/resola-ai/deca-apps/commit/f4da54bac62b850ded273503d3ec07f367d1816d))
- **chatbot:** CB-1020 Correct the word wrap style in Chatbox message ([9abf912](https://github.com/resola-ai/deca-apps/commit/9abf9125c1ef1974be2942cfffdf636dfac4567f))
- **chatbot:** CB-1022 improve API call, clean code ([7623763](https://github.com/resola-ai/deca-apps/commit/7623763a5c42bcb9f6145a888f8392fed7cc3d97))
- **chatbot:** CB-1022 support URL query params ([041356c](https://github.com/resola-ai/deca-apps/commit/041356ceb839643d70ad7f0b61a0c815bb55b478))
- **chatbot:** CB-1027 Correct position issue in Action ([02a4e26](https://github.com/resola-ai/deca-apps/commit/02a4e26e9b5a9b521becd886bb17a069be367215))
- **chatbot:** CB-1030 Correct data in button and condition when change new setting data ([75ffb18](https://github.com/resola-ai/deca-apps/commit/75ffb189b527cc8e24aaa8f991a6ecc25bd02703))
- **chatbot:** CB-1030 Remove duplicated Delete execution when remove action ([72da02c](https://github.com/resola-ai/deca-apps/commit/72da02c3f59f03fe34aec4dde5d7927adc135861))
- **chatbot:** CB-1033 highlight selected block node and group selection ([1f734cb](https://github.com/resola-ai/deca-apps/commit/1f734cb85a6648913a3d0050b8665596bb092b41))
- **chatbot:** CB-1034 Replace current delete Action node function by new logic and improve some minors ([8a38026](https://github.com/resola-ai/deca-apps/commit/8a380267cd4503416c4999c676bc97a33e84bf5d))
- **chatbot:** CB-1035 Update response structure for get Bot Flows API ([8f9e92f](https://github.com/resola-ai/deca-apps/commit/8f9e92f2a7c76731d65d23065fa2adbe557275d4))
- **chatbot:** CB-1043 Correct API delete flow query params and add falsy for QnA data list ([c33396f](https://github.com/resola-ai/deca-apps/commit/c33396f97e4bbff54fc8ded065665ce502ebf33d))
- **chatbot:** cb-1046 update viewport when dragging node ([922baf9](https://github.com/resola-ai/deca-apps/commit/922baf932047ef593ebc49a639d5698aad90eb4c))
- **chatbot:** cb-1047 correct steps order ([b6200be](https://github.com/resola-ai/deca-apps/commit/b6200be897479585da933afc3bc9d77a40968947))
- **chatbot:** cb-1047 update postion editor when reset viewport ([b4e91e5](https://github.com/resola-ai/deca-apps/commit/b4e91e500433d7a49b468ee4903be77bccfe1702))
- **chatbot:** CB-1049 CB-1073 improve chatlog search and condition, show botId correctly ([8fceaf7](https://github.com/resola-ai/deca-apps/commit/8fceaf7313b1ffd085bc4a7f069673a5ac249b17))
- **chatbot:** CB-1051 Make variable syntax consistent and improve default value in Variable field ([5d073bd](https://github.com/resola-ai/deca-apps/commit/5d073bd35efdba589d33419561ef94369891cc09))
- **chatbot:** cb-1053 add selection box in form ([4e16e77](https://github.com/resola-ai/deca-apps/commit/4e16e77edec1d768f7a5d512c201e1a8e7c825bb))
- **chatbot:** cb-1053 update lodash import ([7cad016](https://github.com/resola-ai/deca-apps/commit/7cad016da9e81768741bbfca7417f93a329fbb3d))
- **chatbot:** CB-1063 Handle show selected node ID on URL ([da956fe](https://github.com/resola-ai/deca-apps/commit/da956fe7b4c737965824cde0ee481f3f30b55c4f))
- **chatbot:** CB-1066 add node validation when drag from toolbar ([4254b5b](https://github.com/resola-ai/deca-apps/commit/4254b5b852c7999dbc1a0a49bd2a8c0bbed84ac9))
- **chatbot:** CB-1070 Correct modified time checking field ([9542105](https://github.com/resola-ai/deca-apps/commit/9542105256417f51c19afae1514d6fac757ece1f))
- **chatbot:** CB-1070 Correct sync updated field between server and client when fetch new data ([bbd4f79](https://github.com/resola-ai/deca-apps/commit/bbd4f798d2f5543f041860027327cb28512054c6))
- **chatbot:** CB-1070 Correct the constant name for error codes and error types ([5cdc4db](https://github.com/resola-ai/deca-apps/commit/5cdc4db8a3cddda64894cc765bfec14c9f2c48d1))
- **chatbot:** CB-1070 Correct updated time before sync to API when the session field cannot sync immediately ([51ef314](https://github.com/resola-ai/deca-apps/commit/51ef3141a85751b1976ca57d7d6d7e8e2580de00))
- **chatbot:** CB-1070 Exclude the mofiedAt from the update payload ([ddb6e97](https://github.com/resola-ai/deca-apps/commit/ddb6e979f08fddef48c313da674c541c7ea4d9dd))
- **chatbot:** CB-1070 Improve promt modal padding style ([ebee6f0](https://github.com/resola-ai/deca-apps/commit/ebee6f00f2f8e7ca0b73febc0f4c484ad955b9d4))
- **chatbot:** CB-1070 Remove unused console log ([ca260c3](https://github.com/resola-ai/deca-apps/commit/ca260c39c056ee8b9247f3aeee70eb8d0d3d9833))
- **chatbot:** cb-1089 action menu ([e5f6722](https://github.com/resola-ai/deca-apps/commit/e5f672218b4bf67b0452294d0ede869e398a3527))
- **chatbot:** CB-1097 improve livechat node rename ([ad53e6c](https://github.com/resola-ai/deca-apps/commit/ad53e6ca8e736509a0f1313ae8f8fef3b56cdc18))
- **chatbot:** CB-1097 improve rename block node ([a22f305](https://github.com/resola-ai/deca-apps/commit/a22f305cb6e1562b70462661f4141c27132f4741))
- **chatbot:** CB-1102 add drag and sort to button node ([00ee043](https://github.com/resola-ai/deca-apps/commit/00ee043880e1fb29164380f79b96dc82666554fa))
- **chatbot:** CB-1102 new design for button node, pagination for intent list ([9807846](https://github.com/resola-ai/deca-apps/commit/980784602fbbca756ee88c184c8d568b7ed7c5cf))
- **chatbot:** CB-1112 Handle check Flow Outdated and show reload dialog ([2bd971e](https://github.com/resola-ai/deca-apps/commit/2bd971e4b7879ccae7e91f67cac8642a6abafd87))
- **chatbot:** CB-1114 auto connect when edge drag end into block ([41341be](https://github.com/resola-ai/deca-apps/commit/41341be3025cebc71796d79f8c1ccf68b830de6c))
- **chatbot:** CB-1126 - [Flow] Add Global No Match setting ([#1185](https://github.com/resola-ai/deca-apps/issues/1185)) ([e6e48b8](https://github.com/resola-ai/deca-apps/commit/e6e48b8ae3a93954ed07ead8656ad8dc52ec1a38))
- **chatbot:** CB-1127 show intents in flow ([bfbace2](https://github.com/resola-ai/deca-apps/commit/bfbace23dd1aaaa84f46a455d19e719291fec9a8))
- **chatbot:** CB-1127 show intents in flow ([977c263](https://github.com/resola-ai/deca-apps/commit/977c26306e6e1fb876ed047c8af77ef5c0306cb9))
- **chatbot:** CB-1127 show intents in flow ([2f6eaff](https://github.com/resola-ai/deca-apps/commit/2f6eaff00803cbd961e5dcbbdcadb6d2f8aa88d7))
- **chatbot:** CB-1127 Show intents in flow list ([2c7c040](https://github.com/resola-ai/deca-apps/commit/2c7c0409df5df7533bb1dc1eeb275a89c7fa037f))
- **chatbot:** cb-1132 add end node type ([d47fee6](https://github.com/resola-ai/deca-apps/commit/d47fee647ee84f4ad60cb294addb3816f66db606))
- **chatbot:** cb-1132 default special nodes ([c8d6a76](https://github.com/resola-ai/deca-apps/commit/c8d6a761483da5dd6be7dd75a8d5c400d1be5d91))
- **chatbot:** cb-1132 update finish node ui and revert end node ([77dd7d4](https://github.com/resola-ai/deca-apps/commit/77dd7d4c45ca239d3aa3bad76a9d21de12fc87f7))
- **chatbot:** CB-1148 Remove Node Id in goto Node action option ([3bdcd34](https://github.com/resola-ai/deca-apps/commit/3bdcd34805bf39d1d5ba136d9be05315f3436b27))
- **chatbot:** cb-1150 validation action menu ([5643c54](https://github.com/resola-ai/deca-apps/commit/5643c54d32d712716adc369eb4d1718cfeacae46))
- **chatbot:** CB-1152 Integrate API for KB Document page for get list, upload, remove and view document ([fa52d18](https://github.com/resola-ai/deca-apps/commit/fa52d18a64cc9450786bd7421d64d6fab042332f))
- **chatbot:** cb-1185 update hover node when connecting ([5c60bfc](https://github.com/resola-ai/deca-apps/commit/5c60bfc22e6bef154aac6abfbe2ad16d1cbea1fd))
- **chatbot:** cb-1185 update save flows logic ([5ea5010](https://github.com/resola-ai/deca-apps/commit/5ea5010dff8a9a6dcdab794366232b0604797b9e))
- **chatbot:** CB-1186 Correct flow not sync to other tab and correct warning from Add Button ([6686169](https://github.com/resola-ai/deca-apps/commit/66861692faa311c6bf1791f746643a179faed942))
- **chatbot:** CB-1186 Handle async when update flow before set status to published ([498b941](https://github.com/resola-ai/deca-apps/commit/498b94138bad63c3c296c453754ae857014db4ae))
- **chatbot:** CB-1233 CB-1237 CB-1240 update text for bot modal, block and default flow ([ef8a769](https://github.com/resola-ai/deca-apps/commit/ef8a769cee384a53857dba44443ee0e880c91122))
- **chatbot:** CB-1234 update chatbot default image ([30278a6](https://github.com/resola-ai/deca-apps/commit/30278a6870a89e7a519d1d40ec12be8a9809c1a6))
- **chatbot:** CB-1241 CB-1242 CB-1243 CB-1248 CB-1249 update text resources node setting and integration ([8f82022](https://github.com/resola-ai/deca-apps/commit/8f8202217e0a5a5c8a203b3aa56af91f4454db3e))
- **chatbot:** CB-1254 improve variable management ([5f309d1](https://github.com/resola-ai/deca-apps/commit/5f309d12c0d84dd34499fcb31218f621e3db4c1d))
- **chatbot:** CB-1263 Add custom separators to custom Tags Input and correct box shadow style ([31e3280](https://github.com/resola-ai/deca-apps/commit/31e32809ae70934858573400a6f21586f1a6b8b5))
- **chatbot:** CB-1264 Improve Chatbot Flow Intent list in sidebar and add right-click menu context ([7ee15ad](https://github.com/resola-ai/deca-apps/commit/7ee15ad9c20c69857d78945c02ac6f39f1dea47a))
- **chatbot:** CB-1272 Show date in Japanese format on japanese version ([66fa685](https://github.com/resola-ai/deca-apps/commit/66fa685e88da77234459fa691763dd911202a578))
- **chatbot:** CB-959 handle intent node and settings ([eb4996e](https://github.com/resola-ai/deca-apps/commit/eb4996e5e8a51340b4d0250de2d20a6d05e4b88e))
- **chatbot:** CB-989 Handle validation for Action URL field ([9fe1a2d](https://github.com/resola-ai/deca-apps/commit/9fe1a2d3c8d64eb9342469c33430e828cb9bb009))
- **chatbot:** CB-989 Improve the URL action schema getting ([4ea4e49](https://github.com/resola-ai/deca-apps/commit/4ea4e4987a8a22fa404c86de21cbb65ce669b74e))
- **chatbot:** CB-992 add more types ([c7ff036](https://github.com/resola-ai/deca-apps/commit/c7ff036def5c1afe2254512e311ac37878d1987b))
- **chatbot:** CB-992 support paginate, make datepicker work properly ([757002e](https://github.com/resola-ai/deca-apps/commit/757002e8e02b9a409891d8deb762fff0786a1d53))
- **chatbot:** format code ([9fa1ef7](https://github.com/resola-ai/deca-apps/commit/9fa1ef703c595e8ab78b8d53f9ae13b3a1d053dd))
- **chatbot:** improve intent and chatlog list page ([bd246fa](https://github.com/resola-ai/deca-apps/commit/bd246fa4a33a58f0d64223a1967b17c21920473b))
- **chatbot:** minor updates on chatlogs ([0b89d2b](https://github.com/resola-ai/deca-apps/commit/0b89d2bcfdd25b4f41c7252aab6e53e557277919))
- **chatbot:** resolve conflict ([ff60a3e](https://github.com/resola-ai/deca-apps/commit/ff60a3ece2af96f6e0078269e231e0d313b81ea8))
- **chatbot:** resolve conflict ([87ee59b](https://github.com/resola-ai/deca-apps/commit/87ee59b1bf490d04cb5e78ac04a328a390b11814))
- **chatbot:** resolve conflict ([51bebfd](https://github.com/resola-ai/deca-apps/commit/51bebfdbd628d0ab72d2f707c0a0c9866c19f5ab))
- **chatbot:** resolve conflict ([4e997ec](https://github.com/resola-ai/deca-apps/commit/4e997ecc63b75f169065d9356c0e8dd12b931ccc))
- **chatbot:** use cursor-based pagination for chatlog list ([623956f](https://github.com/resola-ai/deca-apps/commit/623956f6966e7de710cf9e19a3845abdf31e55ed))
- **chatbot:** use datepicker singledate ([58de058](https://github.com/resola-ai/deca-apps/commit/58de058c2c49279583cbece840c5cacef7975e91))

### Bug Fixes

- **chatbot:** CB-1025 fix duplicate action type prop ([83dc808](https://github.com/resola-ai/deca-apps/commit/83dc808a2c68a3475f7804f97449428c06372513))
- **chatbot:** CB-1025 remove action if don't have parent ([29261fc](https://github.com/resola-ai/deca-apps/commit/29261fcc984e378d8b07fe65c820fee6438783d1))
- **chatbot:** CB-1025 remove prevent delete action ([2b90166](https://github.com/resola-ai/deca-apps/commit/2b901661f11a481dda409849aab64378f7a93d5a))
- **chatbot:** CB-1025 update delete livechat node ([71f6c91](https://github.com/resola-ai/deca-apps/commit/71f6c91be9f1d8a186ef4fb6dd11058be800d5c4))
- **chatbot:** CB-1026 fix circular dependency/import and refactor import code ([2f0ac49](https://github.com/resola-ai/deca-apps/commit/2f0ac49e3044e97d4b3adc41e6ad9654cb8e7035))
- **chatbot:** CB-1030 handle delete step in block ([4ba173a](https://github.com/resola-ai/deca-apps/commit/4ba173a91402ba290d7c0ad24075114ba0777dac))
- **chatbot:** CB-1030 hotfix drop step to block ([6d973b9](https://github.com/resola-ai/deca-apps/commit/6d973b935af13d0f36b63954c347b7c8f972ba46))
- **chatbot:** CB-1030 remove unused import ([e2573a2](https://github.com/resola-ai/deca-apps/commit/e2573a21aff0766d2400f7f8213506541d7cd470))
- **chatbot:** CB-1030 update delete event ([1039869](https://github.com/resola-ai/deca-apps/commit/1039869964740b7c8492148914482b6919ecf7a0))
- **chatbot:** CB-1030 update node settings affect delete event ([2fc7c49](https://github.com/resola-ai/deca-apps/commit/2fc7c497509325286a9da58ca30feb323ea47842))
- **chatbot:** CB-1030 update validation after delete node in block ([d17fc94](https://github.com/resola-ai/deca-apps/commit/d17fc94ad3cfa2774bcee30b610eb361c1aaeb46))
- **chatbot:** CB-1030 validate parent node before checking step ([5651db4](https://github.com/resola-ai/deca-apps/commit/5651db429783efa41d48e81fed68007e7beb6197))
- **chatbot:** cb-1031 zoom reset after edited ([5d52e99](https://github.com/resola-ai/deca-apps/commit/5d52e99a70b2d8a1938573dfdfceece5dc83ddaf))
- **chatbot:** CB-1038 update email node setting ([64d8341](https://github.com/resola-ai/deca-apps/commit/64d8341af3285b68eb20dbdf0c48bd402a3af0d5))
- **chatbot:** CB-1042 refactor api flow in home page and bot details ([838ba89](https://github.com/resola-ai/deca-apps/commit/838ba89c7ea9863daffcc94e5bec8a5b79ba37d7))
- **chatbot:** CB-1042 update api flow ([94ad044](https://github.com/resola-ai/deca-apps/commit/94ad044fc91910fa1f15300f0e230d9ea3e5ae31))
- **chatbot:** CB-1042 update chatbot list ([a44cba4](https://github.com/resola-ai/deca-apps/commit/a44cba477e975fc6cd0f4c58cc6cdc8791f65d11))
- **chatbot:** CB-1044 prevent save if data null ([7288667](https://github.com/resola-ai/deca-apps/commit/7288667c8aa8bfce78c2e0b8fb9e672d8ea4baf1))
- **chatbot:** CB-1044 refactor bot api flow ([37af280](https://github.com/resola-ai/deca-apps/commit/37af280aedfcd55cd66fe05b44703210551bccb6))
- **chatbot:** CB-1044 update api node settings ([ed43ad9](https://github.com/resola-ai/deca-apps/commit/ed43ad917da4fb2f897cb4e34f5376cd06995c07))
- **chatbot:** CB-1048 fix publish button not update state ([70095f3](https://github.com/resola-ai/deca-apps/commit/70095f3449eb2b5aa56aa1227348b47ecee09613))
- **chatbot:** CB-1052 - Hide Quick action section ([#905](https://github.com/resola-ai/deca-apps/issues/905)) ([23fe125](https://github.com/resola-ai/deca-apps/commit/23fe125c900a65ae5d9ae1ce0c917cb81e9375bc))
- **chatbot:** CB-1052 - Navigate to correct path when click on quick action ([#897](https://github.com/resola-ai/deca-apps/issues/897)) ([d6f3baa](https://github.com/resola-ai/deca-apps/commit/d6f3baa396297efa3af390178b452bddf38dd7bc))
- **chatbot:** CB-1059 improve drag and drop from toolbar ([8ba13f0](https://github.com/resola-ai/deca-apps/commit/8ba13f00966d4e13092014f154c37af0d292ee48))
- **chatbot:** CB-1149 update capture variable to input ([32b4384](https://github.com/resola-ai/deca-apps/commit/32b4384487788f7f4fa879af2d13efeee48bb4cb))
- **chatbot:** cb-1195 zoom value cant be negative ([2f571ca](https://github.com/resola-ai/deca-apps/commit/2f571ca544258903c2b727987742cd9518e5f787))
- **chatbot:** CB-1212 update variable error msg and behavior ([3eee40b](https://github.com/resola-ai/deca-apps/commit/3eee40b25bdcc38107557cbcfee6740c4544b14c))
- **chatbot:** cb-1218 connection auto connect to own node ([b16a27c](https://github.com/resola-ai/deca-apps/commit/b16a27cca307d1ea13a496e1146ebbf7de6ea74b))
- **chatbot:** CB-1234 remove unused import ([9f255a3](https://github.com/resola-ai/deca-apps/commit/9f255a308ea72d3af25eb59f2c7fe88f0e6f64df))
- **chatbot:** cb-1256 remove dedundant code ([ed6d422](https://github.com/resola-ai/deca-apps/commit/ed6d422181c055253327d0a91952da3bf02440e5))
- **chatbot:** cb-1256 sync update and publish flow ([e2db40c](https://github.com/resola-ai/deca-apps/commit/e2db40cf8b366484cc255f73755dd24a2932b107))
- **chatbot:** CB-1279 fix navigation issue on flow editor ([6cd0e63](https://github.com/resola-ai/deca-apps/commit/6cd0e6376fb86e2bba2ed4c7266fa4e147c1ae93))
- **chatbot:** CB-678 fix browser back button not working correctly ([fa5ecf5](https://github.com/resola-ai/deca-apps/commit/fa5ecf5e31c0f220dd7718f0873ae4978cfff1a1))
- **chatbot:** CB-678 update navigation from package share ([9512d06](https://github.com/resola-ai/deca-apps/commit/9512d06ef7f49d9246c571bb668fab17face1589))

### [0.2.7](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.7) (2024-04-03)

### Features

- **chatbot:** add chatlogs type, minor improvement ([a77d6a6](https://github_resola/resola-ai/deca-apps/commit/a77d6a6c4193f400613d2dc2387b3cf845af1fd0))
- **chatbot:** CB-1003 add livechat node setting and refactor some nodes ([f0a9a3b](https://github_resola/resola-ai/deca-apps/commit/f0a9a3b2e5d9e0631eeeef6a9b1dfdd93175b93c))
- **chatbot:** CB-1010 Correct coords undefined when adjust chaining actions position ([cabf5ec](https://github_resola/resola-ai/deca-apps/commit/cabf5ec992845d5335fcd1d7dce012ae3cc98c03))
- **chatbot:** CB-1010 Correct some issues on Button Settings after syncing the Chaining actions ([df1e2be](https://github_resola/resola-ai/deca-apps/commit/df1e2be02b3e79651bcc17e9fe3b16ae4035f193))
- **chatbot:** CB-1019 improve api node validation ([c684173](https://github_resola/resola-ai/deca-apps/commit/c684173d232af479318f9a6371924bcd86cd44c3))
- **chatbot:** CB-960 CB-957 apply locale for datepicker, improve search, add i18n text ([93ef867](https://github_resola/resola-ai/deca-apps/commit/93ef867f1bb81dc82b768ce09d445f57d89a9c96))
- **chatbot:** cb-971 remove factory folder ([3ecc0e6](https://github_resola/resola-ai/deca-apps/commit/3ecc0e652a7c1869c55a4186f3fd93a581453000))
- **chatbot:** cb-971 update position action when dnd ([efdd101](https://github_resola/resola-ai/deca-apps/commit/efdd10134ddf3410b8bda8cf3d4601784990f7e1))
- **chatbot:** cb-974 can not save name of flow ([9d1340f](https://github_resola/resola-ai/deca-apps/commit/9d1340f3d5f8eed89911d08826f5331906088eb5))
- **chatbot:** CB-980 Adjust actions chain behavior after removing from chain ([4509414](https://github_resola/resola-ai/deca-apps/commit/45094143310b59380cc3923e55174a71e518b6b7))
- **chatbot:** CB-980 Adjust actions chain position when the chain was updated ([e02d19b](https://github_resola/resola-ai/deca-apps/commit/e02d19b14e4119a705962489a8ceae7cbd142c12))
- **chatbot:** CB-980 Correct schema syncing and build error ([c1a9f3f](https://github_resola/resola-ai/deca-apps/commit/c1a9f3f7b089a44785fe9cce9d4c9e784701e75b))
- **chatbot:** CB-980 Create useAPINodeStyles to share style in API node components ([f54849f](https://github_resola/resola-ai/deca-apps/commit/f54849f87261b5975aea68e19059fac7ae64c740))
- **chatbot:** CB-980 Filter Action options base on current settings and adjust Action type to prevent operator as ([283f34b](https://github_resola/resola-ai/deca-apps/commit/283f34bd76b599de776a5d718eee093a962f501c))
- **chatbot:** CB-980 Remove server-schema folder ([86335b1](https://github_resola/resola-ai/deca-apps/commit/86335b19089c909b35ec8ba11977e4fdd07c95fd))
- **chatbot:** CB-980 Update schema for Chaining actions ([8fdf5b9](https://github_resola/resola-ai/deca-apps/commit/8fdf5b93d48c9674e4dddb2a8baf10047f38eb8d))
- **chatbot:** CB-987 Improve the utils and constants import to ignore index navigator ([ef25cc8](https://github_resola/resola-ai/deca-apps/commit/ef25cc8cc1ae04cba243c637a7086bd24702d43e))
- **chatbot:** cb-998 save data session instead local ([646c503](https://github_resola/resola-ai/deca-apps/commit/646c503847bae3a34ad866de8750001d6183f4eb))
- **chatbot:** cb-999 update sync data from local storage and api ([eed19bb](https://github_resola/resola-ai/deca-apps/commit/eed19bb160569bcf8497f5e7106bedca1f67e3a7))
- **chatbot:** improve logs page, set col width, prevent spamming, update locales ([ec58575](https://github_resola/resola-ai/deca-apps/commit/ec585751a7193c54aea41c07674927c31bb960f5))
- **chatbot:** open chatbox from integration ([e170d89](https://github_resola/resola-ai/deca-apps/commit/e170d891240719dabb3dc83953f450805490d03b))

### Bug Fixes

- **chatbot:** CB-1006 remove default value of api node settings ([70e73c9](https://github_resola/resola-ai/deca-apps/commit/70e73c92b6aed30c02cf5f1a2d7b182bcaff4a5e))
- **chatbot:** CB-1006 remove unused import ([1664ce7](https://github_resola/resola-ai/deca-apps/commit/1664ce7c0144b1eb90f6f0a9e405ff05ba9aaf5d))
- **chatbot:** CB-1019 fix typo ([8b9e70b](https://github_resola/resola-ai/deca-apps/commit/8b9e70b3767da55f0588e08277f810fad9712561))
- **chatbot:** CB-1019 update api node validation ([a95c29e](https://github_resola/resola-ai/deca-apps/commit/a95c29eaa7e95d0b7942ae8ecc1f95e359c73610))
- **chatbot:** CB-1019 update api status enum ([4570e03](https://github_resola/resola-ai/deca-apps/commit/4570e03c9fc318be38fd56c86311210df71430f5))
- **chatbot:** CB-988 update api node setting ([3c17197](https://github_resola/resola-ai/deca-apps/commit/3c17197b55ec1f44bf57131e7be93fd387d7c32b))
- **chatbot:** CB-990 prevent email duplicate in setting ([abd5b21](https://github_resola/resola-ai/deca-apps/commit/abd5b214df5880400750a2d90d2853404fef19ea))
- **chatbot:** CB-990 update japanese text ([7b792a7](https://github_resola/resola-ai/deca-apps/commit/7b792a7fdaca2e6c7cd8d415acb8fd7cbe14e22d))
- **chatbot:** CB-997 prevent text overflow in flow toolbar ([8534075](https://github_resola/resola-ai/deca-apps/commit/8534075b43606ac2ba44464732dc84a94c7c264d))
- **chatbot:** minor update to clean code ([0fac44d](https://github_resola/resola-ai/deca-apps/commit/0fac44ddd4bb4109aadcfa58cea6397b248bfce8))

### [0.2.6](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.6) (2024-03-28)

### Features

- **chatbot:** CB - 741 - Integrate integrations with api ([95ac6dc](https://github_resola/resola-ai/deca-apps/commit/95ac6dcc0111e8c11b8f3d62ae392b51fb323ae2))
- **chatbot:** CB - 930 - update height for Chat State ([20b74e6](https://github_resola/resola-ai/deca-apps/commit/20b74e6d6717cf0c7e3f2a9a7565dbd027cfdc89))
- **chatbot:** CB - 975 - Update scroll behavior for pages ([94cf5ba](https://github_resola/resola-ai/deca-apps/commit/94cf5ba898636d73925d2a3c2e9052d3e0d07494))
- **chatbot:** CB-925 Correct the chaining actions position and Button label issue ([c54d18a](https://github_resola/resola-ai/deca-apps/commit/c54d18acd36278a4ab4a342001d27184fd7c8c67))
- **chatbot:** CB-925 Implement the chaining action in flow editor ([826f4db](https://github_resola/resola-ai/deca-apps/commit/826f4db8c1c9cc06c1551b28238562f8eb5b56c4))
- **chatbot:** cb-944 correctly actions position after update ([96de162](https://github_resola/resola-ai/deca-apps/commit/96de162f3a621e35f081b473847307a61697c374))
- **chatbot:** cb-944 fixed conflict ([49a3145](https://github_resola/resola-ai/deca-apps/commit/49a31453c535a5f4d38d553e138307f6d2823930))
- **chatbot:** CB-958 Correct dependencies in onEdgeClick ([d863408](https://github_resola/resola-ai/deca-apps/commit/d863408dc7a017c8c22cfb6566a355c4bffaa735))
- **chatbot:** CB-958 Handle close setting form when clicking on Edge ([0e6e3a0](https://github_resola/resola-ai/deca-apps/commit/0e6e3a004a141c28f4bd3dbdbbd7f770c54e3636))
- **chatbot:** CB-962 Hide Chaining Actions in temp ([1bf77d1](https://github_resola/resola-ai/deca-apps/commit/1bf77d1d6b2401bbb5edc4226196477b6c6c4bfa))
- **chatbot:** CB-964 improve api node and validation ([6bdd5f5](https://github_resola/resola-ai/deca-apps/commit/6bdd5f55dc14c2135bfef627e69406114d8b82dc))
- **chatbot:** CB-968 Correct feedbacks and handle cloneDeep for nodes ([4f13612](https://github_resola/resola-ai/deca-apps/commit/4f136128abd31803a04ff6cf68f0f798026f8567))
- **chatbot:** CB-968 Handle sync url from URL action to Parent Node button ([b26bfcd](https://github_resola/resola-ai/deca-apps/commit/b26bfcd22d548ac49828fbe81ff96c87fc8aa953))
- **chatbot:** CB-968 Remove destructure in parentNode ([beaa73b](https://github_resola/resola-ai/deca-apps/commit/beaa73b2a14a128a5c42ac8f008044d60341dd9c))

### Bug Fixes

- **chatbot:** CB-963 update set variable node setting ([b0c3360](https://github_resola/resola-ai/deca-apps/commit/b0c3360a2c7767a0017f55b09a20b824e23f902d))
- **chatbot:** CB-966 fix kb node setting not save data ([ee5742b](https://github_resola/resola-ai/deca-apps/commit/ee5742bb38b834e2dde10610fc530eebbf4ff008))

### [0.2.5](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.5) (2024-03-27)

### Features

- **chatbot:** CB-447 chat logs ([890a842](https://github_resola/resola-ai/deca-apps/commit/890a842173eff0b77a57839f6bbacfa0e64e6977))
- **chatbot:** CB-447 chat logs ([6dc2466](https://github_resola/resola-ai/deca-apps/commit/6dc2466e7002fe1866d4e24808490720fc115da2))
- **chatbot:** CB-447 Chat Logs ([ff3430e](https://github_resola/resola-ai/deca-apps/commit/ff3430eedaefe69ddc40b2b6d6e0d654c6c702c1))
- **chatbot:** CB-447 Chat Logs ([62c6c49](https://github_resola/resola-ai/deca-apps/commit/62c6c495e327b5e1c410a7e2b2f381e081fd2420))
- **chatbot:** CB-447 Chatlogs page ([7fac79a](https://github_resola/resola-ai/deca-apps/commit/7fac79a4f53e86bedca93f27ad50ad8827562e9b))
- **chatbot:** CB-447 split code, add integration column ([eb2dc82](https://github_resola/resola-ai/deca-apps/commit/eb2dc821477b40ef0db52cf51b2f4796d131fdda))
- **chatbot:** CB-874 - [Flow] Condition Node (Simple) ([#704](https://github_resola/resola-ai/deca-apps/issues/704)) ([4750737](https://github_resola/resola-ai/deca-apps/commit/4750737e8f6df0665e013978167b41677af45463))
- **chatbot:** cb-894 delete lastest port when drop from toolbar ([c22360d](https://github_resola/resola-ai/deca-apps/commit/c22360d313d04716a616f9a25dab7b729e3780e5))
- **chatbot:** cb-896 node validation ([3f3deb3](https://github_resola/resola-ai/deca-apps/commit/3f3deb3aa1d27aced57a379a94d08dbce975d1a3))
- **chatbot:** CB-928 update email node can have variable handlebar ([61b5d93](https://github_resola/resola-ai/deca-apps/commit/61b5d93cc70dd3e9eaa7f26c5adb822d3f594eea))
- **chatbot:** Hide react-flow watermark, improve textarea input space ([e878950](https://github_resola/resola-ai/deca-apps/commit/e87895013bf223bfa1e3e0e5703bf58b5d25b2a7))
- **chatbot:** Remove unused lines ([38321e2](https://github_resola/resola-ai/deca-apps/commit/38321e292a1f250cc14398655ad45d6a197bea25))
- **chatbot:** split code, add locales text, minor improvements ([c01c428](https://github_resola/resola-ai/deca-apps/commit/c01c4289cf353ca84f1da097c88bc01ccf567d9b))

### Bug Fixes

- **chatbot:** CB-948 fix form node setting layout ([4f28119](https://github_resola/resola-ai/deca-apps/commit/4f28119636895fbb4f579df56476fba075bd74e1))
- **chatbot:** CB-948 update text resources form node ([7eb15b2](https://github_resola/resola-ai/deca-apps/commit/7eb15b2d22cf9c5c69c1492d29879501a5e2b867))
- **chatbot:** CB-955 prevent reset typing value on node settings after autosave ([0f97c17](https://github_resola/resola-ai/deca-apps/commit/0f97c176845ee94beb4fb075256cfa4a4d3aa58c))

### [0.2.4](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.4) (2024-03-26)

### Features

- **chatbot:** CB - 892 - Carousel node improvements ([8de68a0](https://github_resola/resola-ai/deca-apps/commit/8de68a0700a407d933ed2d06a675da8a8093d126))
- **chatbot:** CB - 924 - Update input to textarea ([55bb154](https://github_resola/resola-ai/deca-apps/commit/55bb154c8159cb3ed16a2a83d649ca7bca92ab92))
- **chatbot:** cb-851 recalculate position of action node ([c1a13af](https://github_resola/resola-ai/deca-apps/commit/c1a13af60f27894879167795e63c2e670ddd9e37))
- **chatbot:** CB-907 Improve folder structure and data sync/reset in API Node form ([22e149d](https://github_resola/resola-ai/deca-apps/commit/22e149dc4e72de91f9dec9ceeff8b74218053dd9))
- **chatbot:** CB-917 improve flow editor and action node ([b5a8257](https://github_resola/resola-ai/deca-apps/commit/b5a82573c807464af5c261284a47da8150c68018))
- **chatbot:** CB-922 improve text and node settings on reactflow ([11d7c72](https://github_resola/resola-ai/deca-apps/commit/11d7c72a5367c32924b8c54610df9656fedc82d8))
- **chatbot:** CB-922 normalize nodes before execute api ([d77c312](https://github_resola/resola-ai/deca-apps/commit/d77c312058d92f91e2d165961d7e8d932415ddf7))

### Bug Fixes

- **chatbot:** CB-917 fix email node setting show wrong data ([1e4f06a](https://github_resola/resola-ai/deca-apps/commit/1e4f06aa5fafc3f1bae5d4910bdcac0a30284250))
- **chatbot:** CB-917 fix text overflow on text node ([dc7c0f2](https://github_resola/resola-ai/deca-apps/commit/dc7c0f29a90e5902ffda5f8fb6c97ee2a53937c6))
- **chatbot:** CB-917 remove delay open setting sidebar ([e445486](https://github_resola/resola-ai/deca-apps/commit/e4454868d592a29bd946e1b7f9a7678e0031dd92))
- **chatbot:** CB-917 update no match text of kb node ([a07829b](https://github_resola/resola-ai/deca-apps/commit/a07829b2e144f68d5827020ccb0ba871f46a5e30))
- **chatbot:** CB-917 update reset data for all nodes ([3c6598f](https://github_resola/resola-ai/deca-apps/commit/3c6598f660b9ef9fde6fbe591d247402a4e301d9))
- **chatbot:** CB-922 remove redundant dependency ([54b7d64](https://github_resola/resola-ai/deca-apps/commit/54b7d646f7e2b9a97818f7ebc43fef184edebc86))
- **chatbot:** CB-922 remove unused code ([434b0fc](https://github_resola/resola-ai/deca-apps/commit/434b0fc055874f31d7f7657d2a5688ed2c84e7c9))
- **chatbot:** CB-922 update email node and form node ([76fb0bf](https://github_resola/resola-ai/deca-apps/commit/76fb0bf535e0962376645fdfaadbcd93fdfa90d2))
- **chatbot:** CB-922 update flow context ([32be7e7](https://github_resola/resola-ai/deca-apps/commit/32be7e75358b9f86d22693c3cfc67ad3cbc2173a))
- **chatbot:** CB-935 update not found english text ([338408e](https://github_resola/resola-ai/deca-apps/commit/338408eaa0a9904fc85a9d06a6eae0c9dfd1e282))
- **chatbot:** CB-935 update qna node text resources ([ee5ca49](https://github_resola/resola-ai/deca-apps/commit/ee5ca49abb7d3f45677f56ff8cc08d4a0c49e3cf))
- **chatbot:** CB-942 fix livechat node size ([160ea33](https://github_resola/resola-ai/deca-apps/commit/160ea333d62c87180ea492fad397967e5aac6298))

### [0.2.3](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.3) (2024-03-24)

### Bug Fixes

- **chatbot:** CB-920 - [Flow] Send email node sidebar setting bug ([65f1e62](https://github_resola/resola-ai/deca-apps/commit/65f1e62731d1f70a1669275044492f337e75734b))
- **chatbot:** CB-920 - [Flow] Send email node sidebar setting bug ([6b3fe72](https://github_resola/resola-ai/deca-apps/commit/6b3fe72f3bae0e8f20bf37f4ec06f6d1872249f8))

### [0.2.2](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.2) (2024-03-23)

### Features

- **chatbot:** CB-914 - [Flow] Should not send API request every time input change ([049fb82](https://github_resola/resola-ai/deca-apps/commit/049fb821197fc1f271a08fe9fa04dc3a5bc8337e))

### [0.2.1](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.1) (2024-03-23)

### Bug Fixes

- **chatbot:** CB-906 improve flow and block name ([630f750](https://github_resola/resola-ai/deca-apps/commit/630f750131d463a6074a3cfc80ff1faf19d002f8))
- **chatbot:** CB-906 update form input scroll area ([dde235c](https://github_resola/resola-ai/deca-apps/commit/dde235cb77b3896128daa85bb7cfd0bdef152431))
- **chatbot:** CB-916 - [Chatbot Flow] Improve KB Node UI issues ([a91dbc5](https://github_resola/resola-ai/deca-apps/commit/a91dbc55ab48784cbf5cc3ed3481cddf8e214491))

## [0.2.0](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.0) (2024-03-22)

### Features

- **chatbot:** CB - 835 - Card & Carousel Improvement ([9bf8871](https://github_resola/resola-ai/deca-apps/commit/9bf8871ed87d792a17c3bfce57736f881198e984))
- **chatbot:** CB - 891 - Improve action node behavior ([b67b4c0](https://github_resola/resola-ai/deca-apps/commit/b67b4c08bb95069295ce800c585761aa3daaa467))
- **chatbot:** CB-446 Correct the header title locale key ([50241c3](https://github_resola/resola-ai/deca-apps/commit/50241c35f138a36b5eb42f6e63f3fb7faf6f8a2e))
- **chatbot:** CB-446 Correct type and layout for API Node settings ([15a0e9b](https://github_resola/resola-ai/deca-apps/commit/15a0e9b1ddf681c12d37cb99cf1030a6ccf85de9))
- **chatbot:** CB-446 Implement schema and node UI for API connect ([55c1fe5](https://github_resola/resola-ai/deca-apps/commit/55c1fe5f7e1dce9a0b8ccd138fb22dd19b71f08e))
- **chatbot:** CB-446 Implement UI and layout for APINodeSettings ([9912233](https://github_resola/resola-ai/deca-apps/commit/9912233b723010f908f06179779b1ede714932a7))
- **chatbot:** cb-841 remove dead code ([f1f4c99](https://github_resola/resola-ai/deca-apps/commit/f1f4c9960360a10a1447f10d134e5eeeed68b0d1))
- **chatbot:** cb-841 update schema form ([98a4e12](https://github_resola/resola-ai/deca-apps/commit/98a4e12e12d0e88757184fc72252696a7e9aa229))
- **chatbot:** cb-842 update schema variable ([55158c8](https://github_resola/resola-ai/deca-apps/commit/55158c873b1c6330fc61294f23ee3db3576b01d9))
- **chatbot:** CB-843 - [Flow API Integration] Update schema for KB ([#580](https://github_resola/resola-ai/deca-apps/issues/580)) ([05cfa05](https://github_resola/resola-ai/deca-apps/commit/05cfa05ef7b242c6b999a1bc3c6fb98135ffec13))
- **chatbot:** CB-849 add goto block action ([1553e6a](https://github_resola/resola-ai/deca-apps/commit/1553e6a943c94697fde320f1009c0520a11ae479))
- **chatbot:** CB-852 Enable source handle in Action Node and handle remove Action Variable item ([533a060](https://github_resola/resola-ai/deca-apps/commit/533a0602f84fccd294b8b2b3805fb4282ba14119))
- **chatbot:** CB-852 Update variable field from name to variable and correct the type validation ([4387400](https://github_resola/resola-ai/deca-apps/commit/438740033823719ace5ad82093cca3fdcbed0133))
- **chatbot:** CB-854 improve drag animation ([2b56b22](https://github_resola/resola-ai/deca-apps/commit/2b56b222d31600909bc556444f14f3030fbc6fdb))
- **chatbot:** CB-855 Correct the key Button name field in form input ([90f05ac](https://github_resola/resola-ai/deca-apps/commit/90f05ac7ea104b6cc5db921cf809f673f205f383))
- **chatbot:** cb-859 Improve remove step, time update, and create variable ([74e8b5c](https://github_resola/resola-ai/deca-apps/commit/74e8b5ce1b73b63d0af031694c9ea70e8d9e624b))
- **chatbot:** cb-860 allow edit form title ([27e3693](https://github_resola/resola-ai/deca-apps/commit/27e36934515e7ef1004d89e2bb7d23f4d9949b52))
- **chatbot:** cb-860 should not throw error when create new flow ([e676a5d](https://github_resola/resola-ai/deca-apps/commit/e676a5d14bab179d7f2de3d6b96fcf4687ccee48))
- **chatbot:** CB-869 Update Button node schema about the noMatch field ([440f3f4](https://github_resola/resola-ai/deca-apps/commit/440f3f4022f1832f56f764338fee723a9f22ad21))
- **chatbot:** cb-877 can not update empty capture node after reloaded ([367ffc0](https://github_resola/resola-ai/deca-apps/commit/367ffc0434838b8835458d92c148fbc7cede80ea))
- **chatbot:** cb-877 Destructure form data ([c1c3280](https://github_resola/resola-ai/deca-apps/commit/c1c3280a2bc280ad7741b9b51d8858651abc8864))
- **chatbot:** cb-877 Fix default value and button ([32379ef](https://github_resola/resola-ai/deca-apps/commit/32379ef732e8c54b9b6c039a0a7c77507e9dbad7))
- **chatbot:** cb-877 Update data form node ([c25996a](https://github_resola/resola-ai/deca-apps/commit/c25996a164810936f9d94ca8ebf593ae9e90b521))
- **chatbot:** cb-877 Update data form node ([934da83](https://github_resola/resola-ai/deca-apps/commit/934da83c686862b31084ade219c7efeb8e5ed614))
- **chatbot:** cb-877 Update default data ([d59ac60](https://github_resola/resola-ai/deca-apps/commit/d59ac605bbea0397590f42d9451b14f23c103fdb))
- **chatbot:** cb-877 Update retry when error ([ae6f52b](https://github_resola/resola-ai/deca-apps/commit/ae6f52bc535567de865e00c973f83b9184b4ea87))
- **chatbot:** CB-879 Add default value for API key value field ([9f19bf2](https://github_resola/resola-ai/deca-apps/commit/9f19bf2f8da7aa0194b627626d16479548041210))
- **chatbot:** CB-879 Adjust UI for API node with source handler position ([9ab42f5](https://github_resola/resola-ai/deca-apps/commit/9ab42f53dedaecc5514182acae6acdb472ac84f4))
- **chatbot:** CB-879 Correct API Node structure and handle sync data for Headers field ([979836e](https://github_resola/resola-ai/deca-apps/commit/979836ee86638c8f9729c9ec2598ab1fad48ee07))
- **chatbot:** CB-879 Handle get value with safe ([81146ba](https://github_resola/resola-ai/deca-apps/commit/81146badd39d1a065454133dcc24476453ae0b7b))
- **chatbot:** CB-879 Integrate form and handle sync data for API Node ([96e2ea9](https://github_resola/resola-ai/deca-apps/commit/96e2ea9afcad88857d9c0d06561c267d00b34c24))
- **chatbot:** CB-879 Integrate Success Status and Body Content to API settings node ([d194fa6](https://github_resola/resola-ai/deca-apps/commit/d194fa610a1dde164906db5f5c22d588fa29ad29))
- **chatbot:** CB-879 Remove console log ([1420edf](https://github_resola/resola-ai/deca-apps/commit/1420edfa19a8444541c995b671cc279e8f2251a5))
- **chatbot:** CB-879 Remove console log ([5fd0f41](https://github_resola/resola-ai/deca-apps/commit/5fd0f4170d988392f045d044f3cbf43441a371e5))
- **chatbot:** CB-879 Replace isNil by isNil lodash ([ea23e99](https://github_resola/resola-ai/deca-apps/commit/ea23e99611343709174858e7be4c118ce91e4e1e))
- **chatbot:** CB-879 Update expectedStatus from string to array of number ([3d6c135](https://github_resola/resola-ai/deca-apps/commit/3d6c135d05322ca30719b4864e37c75f1d8d41c3))
- **chatbot:** cb-882: update mapping data ([768b2a9](https://github_resola/resola-ai/deca-apps/commit/768b2a928333c6d0e9d0ecb85e46c9f938ffb4f4))
- **chatbot:** cb-884 set variable data is not update after blur ([e2b5c61](https://github_resola/resola-ai/deca-apps/commit/e2b5c617bea68db13f3efef639b412570c9c3f88))
- **chatbot:** CB-886 allow livechat block rename ([f6234df](https://github_resola/resola-ai/deca-apps/commit/f6234dfe88f1cb9201a3dae6b2f5e1eef81f476f))
- **chatbot:** cb-895 improve connection point ([9c91baf](https://github_resola/resola-ai/deca-apps/commit/9c91baf982204869b3663a08e6e840b67997611e))
- **chatbot:** CB-905 Correct the errors related to API Node ([270bd03](https://github_resola/resola-ai/deca-apps/commit/270bd034b687adae1ea609a4d5c676f1d1ae0778))
- **chatbot:** CB-905 Update message for API node ([32fb2f3](https://github_resola/resola-ai/deca-apps/commit/32fb2f3019c13b8bc9ed01d37e714ff9fc84369a))
- **chatbot:** update variable field in the form node ([6ad95f2](https://github_resola/resola-ai/deca-apps/commit/6ad95f2f431a4638bbf58445c80ad927f8492e03))

### Bug Fixes

- **chatbot:** CB-849 update block list type ([e917039](https://github_resola/resola-ai/deca-apps/commit/e9170399f15fec9c5418feb2d93d42a7c39d75e0))
- **chatbot:** CB-854 update readable code ([664f9bc](https://github_resola/resola-ai/deca-apps/commit/664f9bc3921ae980aaec5872f5355ccad65b322b))
- **chatbot:** CB-861 turn off setting when change flow ([ed0eab9](https://github_resola/resola-ai/deca-apps/commit/ed0eab994685d1fb71014c1eac21b1be01dd4290))
- **chatbot:** CB-862 update condition checking port type ([a57b12a](https://github_resola/resola-ai/deca-apps/commit/a57b12a321aba1572ebbb4d251f206651a195f00))
- **chatbot:** CB-862 update edge type connected to action ([825bbba](https://github_resola/resola-ai/deca-apps/commit/825bbba76731797f4c1211ead55edf1e4a463855))
- **chatbot:** CB-863 update flow toolbar style ([96ab53c](https://github_resola/resola-ai/deca-apps/commit/96ab53c2a665398ef89ea463cd069dcea7e06d06))
- **chatbot:** CB-864 update flow and variable style ([9fbff24](https://github_resola/resola-ai/deca-apps/commit/9fbff24348385f3424cb2754bee346a85c69fa1f))
- **chatbot:** CB-886 trim the changed name ([256b993](https://github_resola/resola-ai/deca-apps/commit/256b993a6b96c2ceeac55d0e959ebc17647ffe0d))
- **chatbot:** CB-887 update label settings consistent ([61e492f](https://github_resola/resola-ai/deca-apps/commit/61e492f347b8af994d30277703d5c9dced0580ca))
- **chatbot:** CB-902 update bot before publish ([a6fd40b](https://github_resola/resola-ai/deca-apps/commit/a6fd40ba750b8e4f7b7e3033665cbd36c55bde9e))
- **chatbot:** CB-902 update flow before publish bot ([f947fe5](https://github_resola/resola-ai/deca-apps/commit/f947fe5d3478e1b46f821f3eeb6dc7cbd5d9390d))
- **chatbot:** CB-906 improve email and set variable nodes ([5022419](https://github_resola/resola-ai/deca-apps/commit/5022419a60353cf6bbad8dff07793ff39e021059))
- **chatbot:** CB-906 update cannot save when setting close ([3139cc7](https://github_resola/resola-ai/deca-apps/commit/3139cc79b2c88ce8be5ffbd594b61ffd02000063))

## 0.1.0 (2024-03-19)

### Features

- **chatbot:** CB - 784 - Implement Carousel Sidebar Settings ([3db383f](https://github_resola/resola-ai/deca-apps/commit/3db383f16ed497a9aef40714e0c7c7a323eb7e92))
- **chatbot:** CB-403 - Chatbot list page ([38d4ded](https://github_resola/resola-ai/deca-apps/commit/38d4dede540bb4ab04a4edd41ddda7d608a516e1))
- **chatbot:** CB-408 - Chatbot detail UI ([8419a20](https://github_resola/resola-ai/deca-apps/commit/8419a20102b7844b22823478e572e7d12b599ecb))
- **chatbot:** CB-421 - setup infra ([08b0c60](https://github_resola/resola-ai/deca-apps/commit/08b0c6097f9cf1a8ab721610a4f318fa2d093abc))
- **chatbot:** cb-440: Custom node and edge ([fc6616e](https://github_resola/resola-ai/deca-apps/commit/fc6616edb946ba558bbf6d4dc9ed1de49a5b89db))
- **chatbot:** cb-440: remove redundant code ([5fa357f](https://github_resola/resola-ai/deca-apps/commit/5fa357f0317418d99f710261ab160d3a231beebc))
- **chatbot:** cb-442 prevent enter input ([674cb8b](https://github_resola/resola-ai/deca-apps/commit/674cb8bfe13ecd115fc2b12801b6413047ed08de))
- **chatbot:** cb-442 text message ([a28bb3d](https://github_resola/resola-ai/deca-apps/commit/a28bb3d744f83ff027a59445dab53d6c86507916))
- **chatbot:** CB-443 - Flow UI - KB ([#414](https://github_resola/resola-ai/deca-apps/issues/414)) ([c4d873b](https://github_resola/resola-ai/deca-apps/commit/c4d873bc77e682d50e4477f00e397da8030833e1))
- **chatbot:** CB-444 FlowUI Livechat message ([dcbf8bf](https://github_resola/resola-ai/deca-apps/commit/dcbf8bf4b4b3facd0ddcc4f2f63e82be33f2b7ff))
- **chatbot:** cb-445: add form values ([fc5ce8d](https://github_resola/resola-ai/deca-apps/commit/fc5ce8dc6584f627db6fec6d223b6c94adcc8f18))
- **chatbot:** cb-445: binding data to form ([104f2d5](https://github_resola/resola-ai/deca-apps/commit/104f2d5728bd3a7489e6bdfb2fe3f4edf654b74b))
- **chatbot:** cb-445: create form message node ([c7e0086](https://github_resola/resola-ai/deca-apps/commit/c7e0086c84fde0eea6d67f318abf77f259109144))
- **chatbot:** cb-445: render data to block node ([5b9c9f3](https://github_resola/resola-ai/deca-apps/commit/5b9c9f34ea9e2206b09bd05b6b28b701446cf1ea))
- **chatbot:** CB-454 Correct feedback from PR ([267df13](https://github_resola/resola-ai/deca-apps/commit/267df135d7c7c8ad7ad6a113fd9997ec84ff97ff))
- **chatbot:** CB-454 Implement the ButtonNode setting form ([bc74a58](https://github_resola/resola-ai/deca-apps/commit/bc74a582f747b0d732972ef63805494e4c935f08))
- **chatbot:** CB-454 Refactor component data type base on API model ([d977bd5](https://github_resola/resola-ai/deca-apps/commit/d977bd5a61e0686049756df0374349e33cacbf07))
- **chatbot:** CB-454 Refactor file structure and correct feedbacks from PR ([5e11991](https://github_resola/resola-ai/deca-apps/commit/5e11991fe1e9dbafafb1f6b5d240b3d74f95cddd))
- **chatbot:** CB-468 - Japanese ver adaptation for chatbot ([#279](https://github_resola/resola-ai/deca-apps/issues/279)) ([dc17b87](https://github_resola/resola-ai/deca-apps/commit/dc17b8703932eb370fc9113202ae0be85933f7d2))
- **chatbot:** CB-474 - improve pr preview ([a68ba1c](https://github_resola/resola-ai/deca-apps/commit/a68ba1cdd7ac21807565abd0a74fb9948e6fdc28))
- **chatbot:** CB-474 - improve pr preview ([abe3679](https://github_resola/resola-ai/deca-apps/commit/abe36791d948cd8fdcfdf5f04960572122b63dd0))
- **chatbot:** CB-474 - improve pr preview ([9b191a9](https://github_resola/resola-ai/deca-apps/commit/9b191a96c2615924a9ac809a26d1dbcba65573a2))
- **chatbot:** CB-481 - Add and edit bot modal ([#203](https://github_resola/resola-ai/deca-apps/issues/203)) ([aba8d58](https://github_resola/resola-ai/deca-apps/commit/aba8d58cc5ef3544b9974dae39679536c30407f7))
- **chatbot:** cb-484 enable dnd ([971ebd4](https://github_resola/resola-ai/deca-apps/commit/971ebd458d8b7e93103e922404c2786a1605e592))
- **chatbot:** CB-486, CB-487 - Create, update, delete Flow, Variable in Sidebar Menu ([#221](https://github_resola/resola-ai/deca-apps/issues/221)) ([9a149ff](https://github_resola/resola-ai/deca-apps/commit/9a149ff0ae686401434a2ece6705a590018daf85))
- **chatbot:** CB-501 - Add Upload image component to create and edit Bot screen ([#299](https://github_resola/resola-ai/deca-apps/issues/299)) ([81e3a1a](https://github_resola/resola-ai/deca-apps/commit/81e3a1a7f2e2eb0ca47022edcf6a0947df2ef621))
- **chatbot:** CB-503 - Chatbot API Integration - Bots (list, create, update, delete) ([#257](https://github_resola/resola-ai/deca-apps/issues/257)) ([4c4335b](https://github_resola/resola-ai/deca-apps/commit/4c4335b5149f009d5de381118c002b40f989c5c1))
- **chatbot:** cb-536 placeholder when dragging ([6b8d96d](https://github_resola/resola-ai/deca-apps/commit/6b8d96da4da485c89897ffdfebec98bb4118837b))
- **chatbot:** cb-537 drag item from toolbar and cb-561 open model edit node ([95bd3e2](https://github_resola/resola-ai/deca-apps/commit/95bd3e2ac1da2d140dbd25c924ee5ab7df9774e2))
- **chatbot:** cb-537 fixed build fail ([3017063](https://github_resola/resola-ai/deca-apps/commit/3017063bdd3371c067d6cece99a77f2af3085df7))
- **chatbot:** cb-537 move func outside jsx and remove redundant code ([9e1ff7b](https://github_resola/resola-ai/deca-apps/commit/9e1ff7b5fedbac2f1de2d0546fa61441db55b212))
- **chatbot:** cb-537 update type name ([5f34205](https://github_resola/resola-ai/deca-apps/commit/5f3420552202563a4e6e6e1422652f6b7ab5901d))
- **chatbot:** cb-562 edit title block node and cb-570 wrapped other node with block node ([bfdbf2b](https://github_resola/resola-ai/deca-apps/commit/bfdbf2bc9ca2821d7cc883ec40450c16fc86d78a))
- **chatbot:** cb-562 remove magic number ([635b057](https://github_resola/resola-ai/deca-apps/commit/635b0578b04e6ec81fc9ed2faf53704fff3d4d34))
- **chatbot:** cb-562 update function edit title ([5d83bb6](https://github_resola/resola-ai/deca-apps/commit/5d83bb68b772f601907218f4cfaab34b455334b6))
- **chatbot:** cb-581 Update data structure to manage nodes - cb-572 drop parent to parent ([f368b12](https://github_resola/resola-ai/deca-apps/commit/f368b12c60ee616a6a0ce5ad8361b4b17091165e))
- **chatbot:** CB-583 add email node and update toolbar models ([fcbaa50](https://github_resola/resola-ai/deca-apps/commit/fcbaa50abfd96744423b8425d4b97751ae63387b))
- **chatbot:** CB-584 - update email node setting and submit flow ([120736f](https://github_resola/resola-ai/deca-apps/commit/120736f1dbe8d1f3af7de7c1c172098c38878742))
- **chatbot:** cb-588 ports validation ([935d4a1](https://github_resola/resola-ai/deca-apps/commit/935d4a165e4ce04f3c7fa80ed045a5f96ab87b53))
- **chatbot:** CB-592 - setup select step flow for sidebar ([b1e2f39](https://github_resola/resola-ai/deca-apps/commit/b1e2f39b93a9dd8aaab709656939a955e5bb3515))
- **chatbot:** cb-605 port structure, edge custom cb-613 open menu when right click ([adbd0f5](https://github_resola/resola-ai/deca-apps/commit/adbd0f57a2f1a81badaae55b9b0451c3d21ab022))
- **chatbot:** cb-606 cache data and prepare implement with API ([da5a390](https://github_resola/resola-ai/deca-apps/commit/da5a390e4ee42564259520e9be77dd798698a7ec))
- **chatbot:** cb-606 update flow data and create flow ([3f18c53](https://github_resola/resola-ai/deca-apps/commit/3f18c53945b358055fac847d432e1cf04607fd7a))
- **chatbot:** CB-608 Apply fieldArray from react hook form to Button setting ([d517710](https://github_resola/resola-ai/deca-apps/commit/d51771017794436b56d7066b0404b6fed97df5de))
- **chatbot:** CB-608 Correct suggestion improvement from Github ([17f1f90](https://github_resola/resola-ai/deca-apps/commit/17f1f90f006fcece09b20f03b4745d6a00332afd))
- **chatbot:** CB-608 Implement sync data for Button Node and setting up the form schema for action ([8ca38ff](https://github_resola/resola-ai/deca-apps/commit/8ca38ff24a8b4740ee9b66bf6a88662cec49bf4e))
- **chatbot:** CB-608 Replace the z.enum by z.literal ([5ac336a](https://github_resola/resola-ai/deca-apps/commit/5ac336aee33e210bcb135456d3a79f5c968a01cc))
- **chatbot:** CB-609 Update schema and rewrite for Action Variable ([6dc9cf6](https://github_resola/resola-ai/deca-apps/commit/6dc9cf69a8d58243973c3e9f5000f4c5d9537ae1))
- **chatbot:** CB-612 - Apply infinite scrolling for chatbot list ([#335](https://github_resola/resola-ai/deca-apps/issues/335)) ([c478870](https://github_resola/resola-ai/deca-apps/commit/c4788705939ba699f969e15792c3c36a73756ee6))
- **chatbot:** CB-618 add capture variable node ([8b37cf4](https://github_resola/resola-ai/deca-apps/commit/8b37cf42c43e80ad1371fb375f3a0bf8bd32ae96))
- **chatbot:** CB-618 update capture variable node settings ([687ccb0](https://github_resola/resola-ai/deca-apps/commit/687ccb084f52a5bb8d3d8089a4f47b4ddc5ed9bb))
- **chatbot:** CB-619 add capture intent node ([c765618](https://github_resola/resola-ai/deca-apps/commit/c765618c804346c8ee03ee5cf5c2207bff1dc8a7))
- **chatbot:** CB-621 add chatbot integration ui ([c0db090](https://github_resola/resola-ai/deca-apps/commit/c0db090b8fd63d73c1159ff963d474402d7f89fc))
- **chatbot:** CB-623 add Chatbot General Setting page UI ([61ebd0a](https://github_resola/resola-ai/deca-apps/commit/61ebd0aa6ecdc9378c29c00ed4af56a682ffab8c))
- **chatbot:** CB-655 Add Set Variable to Chatbot flow ([237975a](https://github_resola/resola-ai/deca-apps/commit/237975afd32b2f9c50c928bac5d4a6c737e589fd))
- **chatbot:** CB-655 change to use component from mantine hook form ([94d35cc](https://github_resola/resola-ai/deca-apps/commit/94d35cc8018520d6197083c154ed15dd1938458d))
- **chatbot:** CB-655 remove unused vars ([24b790f](https://github_resola/resola-ai/deca-apps/commit/24b790f0e67f7d12f2727c97fc419bbd601e2e61))
- **chatbot:** CB-655 remove unused vars ([9b12ab0](https://github_resola/resola-ai/deca-apps/commit/9b12ab084efd3d8cad798ca763a1ef1a0dc6c5ee))
- **chatbot:** CB-655 resolve conflict ([a9e2c64](https://github_resola/resola-ai/deca-apps/commit/a9e2c640785c1bba35c2939b05bf4d5ba36978af))
- **chatbot:** CB-655 revert default type of variableid ([02aab20](https://github_resola/resola-ai/deca-apps/commit/02aab20d633e04dba2a6169101801c4e70a3543b))
- **chatbot:** CB-682 Correct the source edge position in Button Node ([2a18177](https://github_resola/resola-ai/deca-apps/commit/2a1817785dce248f4fb99ce37d78e8fb4a4fb5a5))
- **chatbot:** CB-683 Handle open and direct to Action setting form when click on Action Node ([5942f0f](https://github_resola/resola-ai/deca-apps/commit/5942f0f16ca8dc5b6310c6da15aa67208d07266f))
- **chatbot:** CB-689 Correct behavior when clicking on Button Node and Action Node ([9ee4c8a](https://github_resola/resola-ai/deca-apps/commit/9ee4c8a618e0c8be61b4f3d8b89e0231fc3727c4))
- **chatbot:** CB-690 CB-691 Correct sync data inside Node Button and improve the type definition ([0e13a08](https://github_resola/resola-ai/deca-apps/commit/0e13a089c36e3c1f25e4d792ff2e3c132d9e2dfb))
- **chatbot:** CB-700 add publish chatbot modal ([a60db7b](https://github_resola/resola-ai/deca-apps/commit/a60db7ba9ae43d3a3b6170dbc17f8682284cedf3))
- **chatbot:** CB-731 publish chatbot flow with api ([248e8a9](https://github_resola/resola-ai/deca-apps/commit/248e8a9c38ee8bde6093dd310dc695f7b18b776b))
- **chatbot:** CB-733 Adjust className in Button Node props that duplicated ([541d75c](https://github_resola/resola-ai/deca-apps/commit/541d75ccd2c6160855125ad63240b8e830112109))
- **chatbot:** CB-733 Implement Active state for Step Node when user selected it ([e4335fc](https://github_resola/resola-ai/deca-apps/commit/e4335fcf9e4b6d043ff318284b13a84293444983))
- **chatbot:** CB-733 Remove important and replace by padding props from Card component ([2beb5a2](https://github_resola/resola-ai/deca-apps/commit/2beb5a23935a1722304cde19212af27e73350fd9))
- **chatbot:** CB-733 Remove state and effect from withStepNode hoc ([c2a8adc](https://github_resola/resola-ai/deca-apps/commit/c2a8adcc881a15119ab804af235c8d4b626c339c))
- **chatbot:** CB-733 Removed overrideSpacing from theme configuration and correct spacing from styles following dev environment ([e228b41](https://github_resola/resola-ai/deca-apps/commit/e228b41ca4f480538d2233995f24e2cc9866fda0))
- **chatbot:** CB-733 Set important spacing in Chatbot Card to override default spacing ([0a3e237](https://github_resola/resola-ai/deca-apps/commit/0a3e2377841b87b88fc48aad6aecc1240f5eb12d))
- **chatbot:** CB-737 improve chatbot item ([baf5f73](https://github_resola/resola-ai/deca-apps/commit/baf5f73b72980d6002721674afc3cd41f9a0e680))
- **chatbot:** CB-748 Correct generated fields in Action Variable ([bc4e348](https://github_resola/resola-ai/deca-apps/commit/bc4e3485fb0b4f4edf79b864ca0dbe32b11944dc))
- **chatbot:** CB-748 Custom Action Options for action selector ([a1dfe89](https://github_resola/resola-ai/deca-apps/commit/a1dfe89783d9b7d9742ff220720872ecc47b02dc))
- **chatbot:** CB-748 Move Action to Flow Node and handle settings via Step Setting Dialog ([35158bf](https://github_resola/resola-ai/deca-apps/commit/35158bf934d659d08bb0708fe840e458de99d2e1))
- **chatbot:** CB-748 Remove unused schema in Action Node ([22285ad](https://github_resola/resola-ai/deca-apps/commit/22285ad7f2370b9df4c1f8900f2601ae25b38638))
- **chatbot:** CB-748 Update schema and field structure for Action Node ([1b17a95](https://github_resola/resola-ai/deca-apps/commit/1b17a9562f5af4eea4352fac4ae3f801bd348c11))
- **chatbot:** cb-754 api fetch flow cb-753 api update flow ([824d054](https://github_resola/resola-ai/deca-apps/commit/824d0544e0f2496d663c3dadcd9b28e7b1f248ea))
- **chatbot:** cb-754 update api create flow and allow multiple conn edge ([32daa15](https://github_resola/resola-ai/deca-apps/commit/32daa15b9cdb72d3e961850380e50b9609ee2a17))
- **chatbot:** cb-754 update type and call flow when botid changed ([0d54252](https://github_resola/resola-ai/deca-apps/commit/0d5425256c4642c265b6480346cb97eb3608ed61))
- **chatbot:** cb-755 update structure node data and logic ([ed9a11e](https://github_resola/resola-ai/deca-apps/commit/ed9a11e9aaa44dd9237b131994f6f8f6eb9ff1f8))
- **chatbot:** CB-758 - [Flow] Integrate KB node with API ([#540](https://github_resola/resola-ai/deca-apps/issues/540)) ([a1ba4ae](https://github_resola/resola-ai/deca-apps/commit/a1ba4aec96e011e7a8037c37b47dee1a33c2ce71))
- **chatbot:** CB-780 Correct Action Setting sync data from setting to Flow ([d59887c](https://github_resola/resola-ai/deca-apps/commit/d59887c0685766bcb50ee9722bca1d97f3f6d389))
- **chatbot:** CB-781 handle drag toolbar item to flow editor ([9b1fd01](https://github_resola/resola-ai/deca-apps/commit/9b1fd0191a316d8d2033b624bf7bae6eb20e35f8))
- **chatbot:** CB-781 update placeholder for item toolbar ([2e779fe](https://github_resola/resola-ai/deca-apps/commit/2e779fedbbb36b0019724ac385fddb117f399bd9))
- **chatbot:** CB-786 Add Intent list page ([32ba326](https://github_resola/resola-ai/deca-apps/commit/32ba3266d657e797adcc705db9b4e151abc7947c))
- **chatbot:** CB-793 Handle remove port and Action Node when remove action ([56df663](https://github_resola/resola-ai/deca-apps/commit/56df6636aa3659d39b37edd92e2d5ec6305626a7))
- **chatbot:** CB-793 Handle target source for Action Node ([7f5e4af](https://github_resola/resola-ai/deca-apps/commit/7f5e4af16e10c3835248a8f1a8cae0fb6f1c2e34))
- **chatbot:** CB-793 Update ActionNode data and correct Port in button ([7f801e9](https://github_resola/resola-ai/deca-apps/commit/7f801e95d590949a825a5ec1ae59d3b18ad14e54))
- **chatbot:** cb-796 fix can not parse JSON ([48f6197](https://github_resola/resola-ai/deca-apps/commit/48f61971f1047996e84e7893c9f0cc2ec0b9bc37))
- **chatbot:** cb-796 update logic calculate position of node ([37edac3](https://github_resola/resola-ai/deca-apps/commit/37edac302fd52c16b2aa283e688ce016aba05b9f))
- **chatbot:** CB-807 update default flow data ([9ab70be](https://github_resola/resola-ai/deca-apps/commit/9ab70bed44814d470fd67176359f10a7b859b11d))
- **chatbot:** CB-810 update position after drag ([d4ce637](https://github_resola/resola-ai/deca-apps/commit/d4ce637916bb9d6b616005b9702538ee4baa4f47))
- **chatbot:** CB-814 Add offsetTop and replace parentId to parentNode ([5f1822f](https://github_resola/resola-ai/deca-apps/commit/5f1822f73c9ec898274873701b0899de8b7fba69))
- **chatbot:** CB-814 Correct code review in Action Selector ([5770c28](https://github_resola/resola-ai/deca-apps/commit/5770c281b6879f1b6bdb58698a2fa25ad4c971a0))
- **chatbot:** CB-814 Correct position of Action Node when creating and handle Back button event ([91bdbc2](https://github_resola/resola-ai/deca-apps/commit/91bdbc221d85f91ff352f21830845149df57d0ba))
- **chatbot:** CB-814 Move calculateActionNodePostion to utils ([c730523](https://github_resola/resola-ai/deca-apps/commit/c730523ba66ce6f991d84d7eac1b4d9c93e37f3a))
- **chatbot:** CB-814 Remove unused code in Action Selector ([bf08856](https://github_resola/resola-ai/deca-apps/commit/bf088562a608100050c142baaee230bef8b1fac7))
- **chatbot:** cb-816 update logic delete node and edge ([84a7f9e](https://github_resola/resola-ai/deca-apps/commit/84a7f9efa5e27ed21226a76da5667177a5726a34))
- **chatbot:** cb-817 select first flow when different botid ([d8598fd](https://github_resola/resola-ai/deca-apps/commit/d8598fd3801022d8953a953e17aa95d6cdb2cda2))
- **chatbot:** cb-819 variables integration ([c1d371a](https://github_resola/resola-ai/deca-apps/commit/c1d371aa29e3f3c5619f527dffc1bb56779a6bf9))
- **chatbot:** CB-820 Handle delete related Action Node after removing Parent Node ([c3af08c](https://github_resola/resola-ai/deca-apps/commit/c3af08c108f47ecb7e3227c51be740bb949dab11))
- **chatbot:** CB-822 Correct Action Node position, and behavior when remove the Button in List ([fb14ed6](https://github_resola/resola-ai/deca-apps/commit/fb14ed672884a2b8aaf63a31122ee1dc36dc7d41))
- **chatbot:** CB-823 - sync data sidebar to carousel node ([10ab095](https://github_resola/resola-ai/deca-apps/commit/10ab0950f5a579120de16f5e457c4b02ed1954e9))
- **chatbot:** CB-826 - [Flow] Can either select KB or type KB ID ([#542](https://github_resola/resola-ai/deca-apps/issues/542)) ([95218c4](https://github_resola/resola-ai/deca-apps/commit/95218c43d872209b1f949dbad7fa17ffca835a6d))
- **chatbot:** cb-829 hide form step ([2923c1d](https://github_resola/resola-ai/deca-apps/commit/2923c1dd61b9ca5562ae007f9c11b5c7a12d5146))
- **chatbot:** cb-829 update schema ([4efa28a](https://github_resola/resola-ai/deca-apps/commit/4efa28a265e815eb33946437213160cadc996ce2))
- **chatbot:** cb-829 update schema and generate id func ([c7fedbf](https://github_resola/resola-ai/deca-apps/commit/c7fedbfd4fced5d3782cd4896cae2f1347a21bd6))
- **chatbot:** cb-829 update schema and type in nodes and step ([b0dcbff](https://github_resola/resola-ai/deca-apps/commit/b0dcbff5d5fa9c4d7660e466c8fa82c89629d4ff))
- **chatbot:** Fix change to home when displaying double ([1a44477](https://github_resola/resola-ai/deca-apps/commit/1a444773af3c872b71f1cd38216f5aacdc788b3a))
- **chatbott:** CB-592 update switch case for render form in sidebar setting ([7bd2d88](https://github_resola/resola-ai/deca-apps/commit/7bd2d88ee027b969d6d26ba6be90bfbd64681f95))

### Bug Fixes

- **chatbot:** cb-440: update reactflow ([d40d793](https://github_resola/resola-ai/deca-apps/commit/d40d7932a87aee021cd521b7a41ede3e2cb3167f))
- **chatbot:** CB-617 fix bug rerender on email setting ([f5c8a33](https://github_resola/resola-ai/deca-apps/commit/f5c8a336a7485f86eca886497945bd758096c866))
- **chatbot:** CB-618 reuse variable on flow context ([bdae2e7](https://github_resola/resola-ai/deca-apps/commit/bdae2e7feb32955a3dbded35ac1e2a698f7eed2f))
- **chatbot:** CB-619 remove handle condition ([b493f86](https://github_resola/resola-ai/deca-apps/commit/b493f867a73b7b0bcd92d85fc8cbfa2466f1ff49))
- **chatbot:** CB-737 ellipsis title for 2 lines ([8eca1b3](https://github_resola/resola-ai/deca-apps/commit/8eca1b32dd284e41e900aacbab6812195caac578))
- **chatbot:** CB-737 update jp text ([4840618](https://github_resola/resola-ai/deca-apps/commit/48406188d970bd6f5d54a48aaaa53ceb99790bbe))
- **chatbot:** CB-756 update flow toolbar hover ([68b6301](https://github_resola/resola-ai/deca-apps/commit/68b6301bc247e2f728dbb78492667e48ba675e69))
- **chatbot:** CB-762 update button on home page ([3c10740](https://github_resola/resola-ai/deca-apps/commit/3c10740d89eb63ecf1ea442c5db057566ef5b053))
- **chatbot:** CB-762 update header logo ([374bb95](https://github_resola/resola-ai/deca-apps/commit/374bb95a6e0a25c5dbc8b853651acdc6078a1924))
- **chatbot:** CB-781 update hover toolbar ([42b4faf](https://github_resola/resola-ai/deca-apps/commit/42b4faf28e337a700fc5234852e2182cf09e8db6))
- **chatbot:** CB-803 remove name of new node and update source edge ([8de42e5](https://github_resola/resola-ai/deca-apps/commit/8de42e52f99281eb246118f64614c0d764c6e024))
- **chatbot:** CB-803 update default step name ([9da0af8](https://github_resola/resola-ai/deca-apps/commit/9da0af8c6cfca8e1e98385cdcdc233dba85abe51))
- **chatbot:** CB-804 fix step icon size ([c518f4f](https://github_resola/resola-ai/deca-apps/commit/c518f4f15006f2292e1082cdfc9fb665c66b2efb))
- **chatbot:** CB-810 update condition when calculate ([00045ff](https://github_resola/resola-ai/deca-apps/commit/00045ffe60df00724152b632f92126dda1dc061f))
- **chatbot:** CB-810 update flow context and calculate func ([9361d7e](https://github_resola/resola-ai/deca-apps/commit/9361d7e5816c4a61f2f612589a2a67e269e9523d))
- **chatbot:** CB-838 update flow icon and flow delete condition ([c879021](https://github_resola/resola-ai/deca-apps/commit/c8790213439e8a53d97324264a2e35975d073c60))
- **chatbot:** CB-848 update flow item in input mode ([506a51d](https://github_resola/resola-ai/deca-apps/commit/506a51de95462f6e46225698c3fc14d6296445e0))
- **chatbot:** fix error type of props ([d07e586](https://github_resola/resola-ai/deca-apps/commit/d07e586aa11938903a5b7a28a8a72aceead13fe8))
