# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [0.16.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.16.0) (2025-07-23)


### Features

* **chatbox:** TK-3116: handle QA streaming message ([#6111](https://github.com/resola-ai/deca-apps/issues/6111)) ([03a3632](https://github.com/resola-ai/deca-apps/commit/03a3632d061990cc5143b47c9cf66009fd3ed6a5))
* **chatbox:** TK-3116: improve qa streaming message ([#6373](https://github.com/resola-ai/deca-apps/issues/6373)) ([bf11b72](https://github.com/resola-ai/deca-apps/commit/bf11b7206436708b817cd210f32b27b8dd3b7eff))
* **chatbox:** TK-8606: fix tolgee issue ([#6342](https://github.com/resola-ai/deca-apps/issues/6342)) ([15039c0](https://github.com/resola-ai/deca-apps/commit/15039c09326bfe6cb06be0028bbcf61a2b17ee40))
* **chatbox:** TK-8606: tolgee integrate cw ([#6145](https://github.com/resola-ai/deca-apps/issues/6145)) ([7f483f0](https://github.com/resola-ai/deca-apps/commit/7f483f0fcdd98ea93c3404fe2330296f4a1b9d40))
* **chatbox:** TK-8951: migrate to biome for cw ([#6217](https://github.com/resola-ai/deca-apps/issues/6217)) ([35219be](https://github.com/resola-ai/deca-apps/commit/35219bea61a27564504b9c7643d955c67e2f5ca4))
* **chatbox:** TK-8990 Improve test coverage for chat window hooks ([#6282](https://github.com/resola-ai/deca-apps/issues/6282)) ([5287c3c](https://github.com/resola-ai/deca-apps/commit/5287c3c6dccac093f59907e0c7cd49947fad84a7))
* **chatbox:** TK-9146: improve store livechat conversationId ([#6289](https://github.com/resola-ai/deca-apps/issues/6289)) ([ad2035b](https://github.com/resola-ai/deca-apps/commit/ad2035b67983c382ed9071e98b1488c90e211fe6))
* **chatbox:** TK-9146: send conversationId when force disconnect lc ([#6151](https://github.com/resola-ai/deca-apps/issues/6151)) ([43c43ad](https://github.com/resola-ai/deca-apps/commit/43c43ad96c8eae4bbdd5a1fc91454901c05ab3ff))
* **chatbox:** TK-9219: update vite config file ([#6153](https://github.com/resola-ai/deca-apps/issues/6153)) ([4b19adb](https://github.com/resola-ai/deca-apps/commit/4b19adb7713b5a173b5a40f44ede62ff3e21ec77))

### Bug Fixes

* **chatbox:** TK-8275 Commit test to checking chatbox build pineline ([#6137](https://github.com/resola-ai/deca-apps/issues/6137)) ([1e89fd9](https://github.com/resola-ai/deca-apps/commit/1e89fd9ce8a3bc4aca31975752846ab0f66a44ca))
* **chatbox:** TK-8733: fix auto scroll behavior ([#6171](https://github.com/resola-ai/deca-apps/issues/6171)) ([429d38a](https://github.com/resola-ai/deca-apps/commit/429d38a948ce1dbaa12fe0f6229541695db5e1a6))
* **chatbox:** TK-8733: fix scroll behavior ([#6116](https://github.com/resola-ai/deca-apps/issues/6116)) ([7129057](https://github.com/resola-ai/deca-apps/commit/7129057c1f7837d2b22d7fa77a758cb65b3f177c))
* **chatbox:** TK-9430: improve trigger chatbot flow ([#6297](https://github.com/resola-ai/deca-apps/issues/6297)) ([2ceabeb](https://github.com/resola-ai/deca-apps/commit/2ceabebc97aeccca72ef66fd7f658f6bf8944989))
* **chatwindow:** TK-7700 Implement Quote rendering with > character in message ([#6321](https://github.com/resola-ai/deca-apps/issues/6321)) ([a4593e8](https://github.com/resola-ai/deca-apps/commit/a4593e805ae07061e867e3fb063f26aa0aee3599))


## [0.15.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.15.0) (2025-07-07)


### Bug Fixes

* **chatbox:** TK-8733: fix auto scroll behavior ([#6171](https://github.com/resola-ai/deca-apps/issues/6171)) ([8498a1b](https://github.com/resola-ai/deca-apps/commit/8498a1b85a14364f81977f418e875ec48380ad57))
* **chatbox:** TK-8733: fix scroll behavior ([#6116](https://github.com/resola-ai/deca-apps/issues/6116)) ([fcecb11](https://github.com/resola-ai/deca-apps/commit/fcecb111652390752c552e6f1daf9e3619d329a1))

## [0.14.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.14.0) (2025-07-01)


### Features

* **chatbox:** remove Datadog client service integration from Chat Window Client (TK-8522) ([18464ec](https://github.com/resola-ai/deca-apps/commit/18464ecc2dd720705cfe6b6e85ccf83dd2f142f8))
* **chatbox:** remove Datadog client service integration from Chat Window Client (TK-8522) ([#5791](https://github.com/resola-ai/deca-apps/issues/5791)) ([ae0e470](https://github.com/resola-ai/deca-apps/commit/ae0e4709a72dcfcaabdf4578b35fc1837d08823e))
* **chatbox:** TK-3116: fix linebreak ([#5951](https://github.com/resola-ai/deca-apps/issues/5951)) ([3d242ce](https://github.com/resola-ai/deca-apps/commit/3d242cebc4f479068cce6d2884f0378283d53ff5))
* **chatbox:** TK-3116: implement streaming data cw ([#5799](https://github.com/resola-ai/deca-apps/issues/5799)) ([808b670](https://github.com/resola-ai/deca-apps/commit/808b670033753f64e67cf4561461b2feb6e124f1))
* **chatbox:** TK-8050: can remove launcher icon ([#5813](https://github.com/resola-ai/deca-apps/issues/5813)) ([d46d1bd](https://github.com/resola-ai/deca-apps/commit/d46d1bd121a5a35bbfd6a71cc941ff04b4cb64bb))
* **chatbox:** TK-8309: handle messages when Livechat is down ([#5722](https://github.com/resola-ai/deca-apps/issues/5722)) ([26718f2](https://github.com/resola-ai/deca-apps/commit/26718f2ded560e8dcdf637baa6389eb4a469a773))
* **chatbox:** TK-8377 Implement media upload validation in CW message ([#5723](https://github.com/resola-ai/deca-apps/issues/5723)) ([6bf8c67](https://github.com/resola-ai/deca-apps/commit/6bf8c67bcb49b13f5a151f4af78df1921684faa9))
* **chatbox:** TK-8803: fix image url ([#5941](https://github.com/resola-ai/deca-apps/issues/5941)) ([31a704e](https://github.com/resola-ai/deca-apps/commit/31a704e6f16be011998da18d7c7f2e8466434c81))
* **chatbox:** TK-8803: using cdn for assets ([#5938](https://github.com/resola-ai/deca-apps/issues/5938)) ([34dbce6](https://github.com/resola-ai/deca-apps/commit/34dbce65698a5f17f0fa9e5f305b431c26dea599))
* **chatwindow:** TK-7444 implement Video and Document messages rendering in CW ([#5663](https://github.com/resola-ai/deca-apps/issues/5663)) ([81ab416](https://github.com/resola-ai/deca-apps/commit/81ab41616f9fdeafd1e88f98a83906d490bd320e))
* **chatwindow:** TK-8860 Add custom field Timestamp to message event to support sorting from LiveChat ([#5974](https://github.com/resola-ai/deca-apps/issues/5974)) ([5cfc823](https://github.com/resola-ai/deca-apps/commit/5cfc823c43e1392a46d6572914ddb3ccbfc6f6a2))

### Bug Fixes

* **chatwindow:** TK-7444 Add x ORG ID to presigned url api ([#5696](https://github.com/resola-ai/deca-apps/issues/5696)) ([5386c48](https://github.com/resola-ai/deca-apps/commit/5386c4862ff2079f705336e9575830c368897d18))
* **chatwindow:** TK-7444 Correct media upload delete function and conversationId missing ([#5693](https://github.com/resola-ai/deca-apps/issues/5693)) ([67364ec](https://github.com/resola-ai/deca-apps/commit/67364ecfbdf7b2d88fe086999c9d161523241d18))
* **chatwindow:** TK-8179 Correct breaklines syntax from chatwindow input message ([#5706](https://github.com/resola-ai/deca-apps/issues/5706)) ([bbca04e](https://github.com/resola-ai/deca-apps/commit/bbca04ed75a66d2b07af395ea1a33c3c4e95db70))
* **chatwindow:** TK-8487 Correct document filename from Chatwindow message ([#6085](https://github.com/resola-ai/deca-apps/issues/6085)) ([bd1521d](https://github.com/resola-ai/deca-apps/commit/bd1521da6d9c4bc2813bcf202028a4d06020adc4))
* **chatwindow:** TK-8487 Correct Max Files upload validation in CW media ([#5913](https://github.com/resola-ai/deca-apps/issues/5913)) ([956a0ce](https://github.com/resola-ai/deca-apps/commit/956a0cec0becd06712f5565d54980a54425ae573))
* **chatwindow:** TK-8487 Improve validation logic related to maximum files number ([#6065](https://github.com/resola-ai/deca-apps/issues/6065)) ([1bde72e](https://github.com/resola-ai/deca-apps/commit/1bde72e79cecdad62bd5c5e63dff6425b936afcc))

## [0.13.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.13.0) (2025-06-10)


### Features

* **chatbox:** TK-6992: implement incident message input ([12db600](https://github.com/resola-ai/deca-apps/commit/12db60070b86362d93ad5c72a88c111ad2677a19))
* **chatbox:** TK-6993: implement incident msg for cw client ([#5518](https://github.com/resola-ai/deca-apps/issues/5518)) ([c18e1e5](https://github.com/resola-ai/deca-apps/commit/c18e1e5ad73d25d9bb66c1d5798d25941a1298ce))
* **chatbox:** TK-6993: improve call healthcheck ([#5576](https://github.com/resola-ai/deca-apps/issues/5576)) ([3c207e8](https://github.com/resola-ai/deca-apps/commit/3c207e8143c06ac831a7af3a4dbb63a11db67e39))
* **chatbox:** TK-8309: handle messages when Livechat is down ([#5722](https://github.com/resola-ai/deca-apps/issues/5722)) ([97a3749](https://github.com/resola-ai/deca-apps/commit/97a3749130834221d5d3e3ecd0e5f198929c0ad7))
* **chatbox:** TK-8309: improve sending forceDisconnectLivechat ([#5750](https://github.com/resola-ai/deca-apps/issues/5750))  ([d51a34d](https://github.com/resola-ai/deca-apps/commit/d51a34d153a22260633742d99fe30a34a2d3f325))
* **chatbox:** TK-8377 Implement media upload validation in CW message ([#5723](https://github.com/resola-ai/deca-apps/issues/5723)) ([0b6ff1e](https://github.com/resola-ai/deca-apps/commit/0b6ff1efd0f17f3dcdc9688c4bc61d619b32adc1))
* **chatwindow:** TK-7444 Correct upload media api and message rendering in cw ([#5640](https://github.com/resola-ai/deca-apps/issues/5640)) ([99fd0b6](https://github.com/resola-ai/deca-apps/commit/99fd0b672e89f07d9cb207a8fe0abd804224c078))
* **chatwindow:** TK-7444 implement Video and Document messages rendering in CW ([#5663](https://github.com/resola-ai/deca-apps/issues/5663)) ([8f0f131](https://github.com/resola-ai/deca-apps/commit/8f0f13142413b9c2c87dfb352d16b3ee957e53a7))
* **chatwindow:** TK-7802 Implement media upload in Message Input in Chatwindow ([#5538](https://github.com/resola-ai/deca-apps/issues/5538)) ([847707a](https://github.com/resola-ai/deca-apps/commit/847707a66098e982d3718fdf5e117ddf4c5ec2ea))
* **chatwindow:** TK-7803 Integrate media uploading API and handle LC message sending ([#5594](https://github.com/resola-ai/deca-apps/issues/5594)) ([140a3d2](https://github.com/resola-ai/deca-apps/commit/140a3d21d4b657c3c1b78e5233911127b501f66f))


### Bug Fixes

* **chatbox:** TK-7699: fix display text incorrectly ([#5432](https://github.com/resola-ai/deca-apps/issues/5432)) ([b0ba910](https://github.com/resola-ai/deca-apps/commit/b0ba9104f1d465461bf4e8b59955659c67696236))
* **chatwindow:** TK-7444 Add x ORG ID to presigned url api ([#5696](https://github.com/resola-ai/deca-apps/issues/5696)) ([4831dd8](https://github.com/resola-ai/deca-apps/commit/4831dd86843e24da941e92742f26d7d290c557ba))
* **chatwindow:** TK-7444 Correct media upload delete function and conversationId missing ([#5693](https://github.com/resola-ai/deca-apps/issues/5693)) ([b1f9bbc](https://github.com/resola-ai/deca-apps/commit/b1f9bbc03059d40e5fb86580e94551e165453971))
* **chatwindow:** TK-7803 Adjust the file thumbail size on firefox ([#5773](https://github.com/resola-ai/deca-apps/issues/5773)) ([5f541b6](https://github.com/resola-ai/deca-apps/commit/5f541b677e521f8208c63062146142dd761beefb))
* **chatwindow:** TK-7803 Correct image cannot shown on firefox and hover issue in dropzone ([#5771](https://github.com/resola-ai/deca-apps/issues/5771)) ([6714bad](https://github.com/resola-ai/deca-apps/commit/6714bad794a6bc6834f22ba99d88c79e5db0b4f2))
* **chatwindow:** TK-8179 Correct breaklines syntax from chatwindow input message ([#5706](https://github.com/resola-ai/deca-apps/issues/5706)) ([db8d360](https://github.com/resola-ai/deca-apps/commit/db8d36089e186c367f9b10f952d60ee007aa6e07))
* **chatwindow:** TK-8377 Add validation message provider to sample state ([#5745](https://github.com/resola-ai/deca-apps/issues/5745)) ([95d0a32](https://github.com/resola-ai/deca-apps/commit/95d0a32ed22509f566c1308ecca288e2aa930852))

## [0.12.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.12.0) (2025-05-16)

### Features

* **chatbox:** TK-7111: improve embedded script ([#5255](https://github.com/resola-ai/deca-apps/issues/5255)) ([c648fea](https://github.com/resola-ai/deca-apps/commit/c648fea1506d73cbe8c8305a94ac71f7691b40bd))

## [0.11.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.11.0) (2025-05-02)

### Features

* **chatbox:** TK-6907: send originArticleId to kb ([#4985](https://github.com/resola-ai/deca-apps/issues/4985)) ([7fff8b1](https://github.com/resola-ai/deca-apps/commit/7fff8b12db0b68890cd11705cd059e2279633186))
* **chatbox:** TK-6907: send originArticleId to kb ([#5003](https://github.com/resola-ai/deca-apps/issues/5003)) ([a0c985a](https://github.com/resola-ai/deca-apps/commit/a0c985a74ccbab8878e25b6e72690e38eb21f848))


### Bug Fixes

* **chatwindow:** TK-6857 Correct breaking spaces in message content with markdown breaklines ([#5077](https://github.com/resola-ai/deca-apps/issues/5077)) ([4bd54ac](https://github.com/resola-ai/deca-apps/commit/4bd54acd14abee80e0f5c412320401530547f053))
* **chatwindow:** TK-6857 Correct format markdown when rendering in cw ([#5127](https://github.com/resola-ai/deca-apps/issues/5127)) ([44e7e35](https://github.com/resola-ai/deca-apps/commit/44e7e35ddbf0f73f808770b0df1efcd576a03587))
* **chatwindow:** TK-6857 Improve RAG mode message rendering in Chatwindow ([#4937](https://github.com/resola-ai/deca-apps/issues/4937)) ([4034f84](https://github.com/resola-ai/deca-apps/commit/4034f840d900614f2e4dc44af3dddf5c5a4af908))

### [0.10.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.10.1) (2025-04-28)


### Bug Fixes

* **chatwindow:** TK-7198: improve fetching message history ([760f6e8](https://github.com/resola-ai/deca-apps/commit/760f6e84d8f5cb67aaeac047057848fa68c798e7))

## [0.10.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.10.0) (2025-04-09)

### Features

* **chatbox:** TK-6431: convert text to hyperlink, update form styles ([e6a545a](https://github.com/resola-ai/deca-apps/commit/e6a545a83179ede75ec3e7952ab94cd264f07f20))
* **chatbox:** TK-6431: replace text by hyperlink ([5600147](https://github.com/resola-ai/deca-apps/commit/5600147fe7f6424aadd5a6886b2da2af1ecbe383))
* **chatwindow:** TK-6431 Improve anchor link replacement logic to support Markdown removed ([#4773](https://github.com/resola-ai/deca-apps/issues/4773)) ([75d3bbc](https://github.com/resola-ai/deca-apps/commit/75d3bbc2610fa0ec5c52699ae7465c8dab8b7dca))

## [0.9.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.9.0) (2025-03-26)

### Features

* **chatbox:** TK-5558: update reference item for document ([#4634](https://github.com/resola-ai/deca-apps/issues/4634)) ([cce087a](https://github.com/resola-ai/deca-apps/commit/cce087a9224d2ac5016a05587eec1ba8a97d217d))

### Bug Fixes

* **chatbox:** TK-6056: fix issue missing styles ([#4707](https://github.com/resola-ai/deca-apps/issues/4707)) ([99c6d44](https://github.com/resola-ai/deca-apps/commit/99c6d44aca2a72da27fa60fb7e788fb21d2be1bc))
* **chatbox:** TK-6378: update styles for form ([#4730](https://github.com/resola-ai/deca-apps/issues/4730)) ([100b0b4](https://github.com/resola-ai/deca-apps/commit/100b0b4380cc97888370e75221ce6e6948ef14ce))

## [0.8.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.8.0) (2025-03-17)


### Features

* **chatbox:** TK-2438: hide livechat conversation history screen from integration page ([#4456](https://github.com/resola-ai/deca-apps/issues/4456)) ([d0fc896](https://github.com/resola-ai/deca-apps/commit/d0fc89617a17d7f6ed4a438ef6c5f25aab56c754))
* **chatbox:** TK-5019: add testid attribute to element ([#4475](https://github.com/resola-ai/deca-apps/issues/4475)) ([0efdf24](https://github.com/resola-ai/deca-apps/commit/0efdf24fbd3c08ddced508927fefc0345fdabd7d))
* **chatbox:** TK-5236: implement livechat team selection ([#4419](https://github.com/resola-ai/deca-apps/issues/4419)) ([9a2507b](https://github.com/resola-ai/deca-apps/commit/9a2507b6834d08d9787a88b2ed5273563772cbc4))
* **chatbox:** TK-5236: update field name ([#4433](https://github.com/resola-ai/deca-apps/issues/4433)) ([d6b5a25](https://github.com/resola-ai/deca-apps/commit/d6b5a25aa4396253f3f8a9f689c15245cc7a7cf5))
* **chatbox:** TK-5236: update payload ([aab27a7](https://github.com/resola-ai/deca-apps/commit/aab27a7b0fe56308fcc43d2ad8e4f79bfd34c489))
* **chatbox:** TK-5236: update payload ([#4481](https://github.com/resola-ai/deca-apps/issues/4481)) ([ab006c9](https://github.com/resola-ai/deca-apps/commit/ab006c99d446b0463b3fe707e513d3ccaf42d4ae))
* **chatbox:** TK-5705: improve sending a display events to livechat ([#4430](https://github.com/resola-ai/deca-apps/issues/4430)) ([8126a1c](https://github.com/resola-ai/deca-apps/commit/8126a1cf9c441126ad7127a33b76663a1f086852))
* **chatbox:** TK-5705: update payload livechat display events ([#4440](https://github.com/resola-ai/deca-apps/issues/4440)) ([8c485e6](https://github.com/resola-ai/deca-apps/commit/8c485e6868494b6a2db87c0a414927b252b071bd))


### Bug Fixes

* **chatbox:** TK-6097: fix icon in rightSection not working ([3bd820a](https://github.com/resola-ai/deca-apps/commit/3bd820af6d347511bd769a77438fe31dacc5b95c))

## [0.7.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.7.0) (2025-02-24)


### Features

* **chatbox:** TK-2438: setting to hide livechat history screen ([#4148](https://github.com/resola-ai/deca-apps/issues/4148)) ([2c2e6fb](https://github.com/resola-ai/deca-apps/commit/2c2e6fb4bdb859b1858026e4c6136993a7eb564a))
* **chatbox:** TK-4065: send display events to livechat ([#4159](https://github.com/resola-ai/deca-apps/issues/4159)) ([8fff0df](https://github.com/resola-ai/deca-apps/commit/8fff0dfbb2c99c22b79be2c8fadb9544463e2a43))
* **chatbox:** TK-4065: update payload, improve functions ([#4215](https://github.com/resola-ai/deca-apps/issues/4215)) ([7f7bf07](https://github.com/resola-ai/deca-apps/commit/7f7bf0710ca47e5e497d98e05908d74534f74cc9))
* **chatbox:** TK-4075: add buttonValue to payload ([#4257](https://github.com/resola-ai/deca-apps/issues/4257)) ([9740f08](https://github.com/resola-ai/deca-apps/commit/9740f08f08062618c49390f1de2e5b43f55a7732))

### Bug Fixes

* **chatbox:** TK-3962: avoid email address when replace non-protocol url ([0ef5c09](https://github.com/resola-ai/deca-apps/commit/0ef5c09d8225bd6e849fa65d2976580d3aca2bb9))

## [0.6.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.6.0) (2025-02-06)

### Features

* **chatbox:** TK-3357: create disableChatwindow setting in CW admin ([d333940](https://github.com/resola-ai/deca-apps/commit/d3339403d73cb51de97dab4aa11272093b379639))
* **chatbox:** TK-3357: update condition ([4c69d8b](https://github.com/resola-ai/deca-apps/commit/4c69d8baf4bb8b1b4fa796f01adbef18d4750989))
* **chatbox:** TK-3357: update setting enable cw admin ([#3994](https://github.com/resola-ai/deca-apps/issues/3994)) ([94d308a](https://github.com/resola-ai/deca-apps/commit/94d308a9e9803635fe639fb78806ceec3de67109))
* **chatbox:** TK-4497: fix button ([#4131](https://github.com/resola-ai/deca-apps/issues/4131)) ([453f7fb](https://github.com/resola-ai/deca-apps/commit/453f7fb2bb81dba095f3ff7608c15b200af3552e))
* **chatbox:** TK-4497: handle click Image in HTMl,QA messages ([#4065](https://github.com/resola-ai/deca-apps/issues/4065)) ([e37c132](https://github.com/resola-ai/deca-apps/commit/e37c13285c6f4bb2b19c7b6686e06c39bb96f509))
* **chatbox:** TK-4497: improve CW UI after migrate Manntine v7 ([#3989](https://github.com/resola-ai/deca-apps/issues/3989)) ([c4f08c0](https://github.com/resola-ai/deca-apps/commit/c4f08c0ffc56f77d1eab565b6f0c7adaa218f1df))
* **chatbox:** TK-4497: improve form message, mobile UI for cw client ([#4123](https://github.com/resola-ai/deca-apps/issues/4123)) ([63da5ad](https://github.com/resola-ai/deca-apps/commit/63da5aded05c9562e13655b9fcdbd177c77cd444))
* **chatbox:** TK-4497: improve previewImage, buttons ([#3990](https://github.com/resola-ai/deca-apps/issues/3990)) ([7a2d6e9](https://github.com/resola-ai/deca-apps/commit/7a2d6e9fdfa4981141ea89b5015a9eb32eca53bd))
* **chatbox:** TK-4497: improve select control ([#4058](https://github.com/resola-ai/deca-apps/issues/4058)) ([202ef4d](https://github.com/resola-ai/deca-apps/commit/202ef4de9c2119539b14fadccfccd4c4be44774e))
* **chatbox:** TK-4497: update icons ([#4067](https://github.com/resola-ai/deca-apps/issues/4067)) ([7904406](https://github.com/resola-ai/deca-apps/commit/7904406663029fed641d8dd78b0d39f60c387cf8))
* **chatbox:** TK-4497: update text ([#4130](https://github.com/resola-ai/deca-apps/issues/4130)) ([31b72ca](https://github.com/resola-ai/deca-apps/commit/31b72cacb9d2b04cd9289e4b8362e9e1ce3972f9))
* **chatbox:** TK-4590: break linebreak in html ([#4016](https://github.com/resola-ai/deca-apps/issues/4016)) ([233d722](https://github.com/resola-ai/deca-apps/commit/233d7221694c2a57c49b707e916182f2351e250a))

### Bug Fixes

* **chatbox:** TK-4438: create useClickImage hook for  QA and HTML message ([f6b6581](https://github.com/resola-ai/deca-apps/commit/f6b6581c712107a3aceb2463100cfd6629765d47))
* **chatbox:** TK-4452: trim text and check empty string. ([#3922](https://github.com/resola-ai/deca-apps/issues/3922)) ([d023edb](https://github.com/resola-ai/deca-apps/commit/d023edbc8674d1bd428f5bdac4f7f36575759bb0))
* **chatbox:** TK-4563: Wrapped the CW client in a shadow DOM ([#4000](https://github.com/resola-ai/deca-apps/issues/4000)) ([8f79558](https://github.com/resola-ai/deca-apps/commit/8f795589c45011d4aefa5a2d116be1dbb5a8984a))

### [0.5.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.1) (2025-01-16)


### Bug Fixes

* **chatbox:** TK-4452: trim text and check empty string. ([#3922](https://github.com/resola-ai/deca-apps/issues/3922)) ([3c4c068](https://github.com/resola-ai/deca-apps/commit/3c4c068b023f6dd8c8558f4dce9e79613e09e90a))

## [0.5.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.0) (2025-01-14)

### Features

* **chatbox:** TK-3177: improve reset button ([#3785](https://github.com/resola-ai/deca-apps/issues/3785)) ([b865ec5](https://github.com/resola-ai/deca-apps/commit/b865ec59f1393394e9d8653dfbe6b0efdd2cccfc))
* **chatbox:** TK-3177: update reset button text ([8bc65ce](https://github.com/resola-ai/deca-apps/commit/8bc65ced76da44a27f9a24c15c726a7b1ede46f6))
* **chatbox:** TK-3704:  fix duplicate request if using multiple cw ([#3875](https://github.com/resola-ai/deca-apps/issues/3875)) ([287afb5](https://github.com/resola-ai/deca-apps/commit/287afb5ba3c48946dad0fcb44f425665f43c9efc))
* **chatbox:** TK-3704: implement collect analytic event data for articles ([#3838](https://github.com/resola-ai/deca-apps/issues/3838)) ([f481e82](https://github.com/resola-ai/deca-apps/commit/f481e82377f7b2a66581ed44eaf8cf522521fd8f))
* **chatbox:** TK-3704: lowercase source field ([#3866](https://github.com/resola-ai/deca-apps/issues/3866)) ([35ba888](https://github.com/resola-ai/deca-apps/commit/35ba888ffac71abf973d92e8a6267c7a73c65369))
* **chatbox:** TK-3704: remove feedback in main article, update ui ([#3847](https://github.com/resola-ai/deca-apps/issues/3847)) ([d74e76c](https://github.com/resola-ai/deca-apps/commit/d74e76cf4546174688387fe92fe1174f463b1e5f))
* **chatbox:** TK-3704: update env variables for prod ([#3877](https://github.com/resola-ai/deca-apps/issues/3877)) ([08f9419](https://github.com/resola-ai/deca-apps/commit/08f941912514a74c913f9e0319970aa716878f3c))
* **chatbox:** TK-3704: update hook url, userAgent ([#3880](https://github.com/resola-ai/deca-apps/issues/3880)) ([7f58e77](https://github.com/resola-ai/deca-apps/commit/7f58e779e6031d5186edffef6a2963fe4035d49c))
* **chatbox:** TK-3976: handle execution message ([#3771](https://github.com/resola-ai/deca-apps/issues/3771)) ([f95a0af](https://github.com/resola-ai/deca-apps/commit/f95a0af9feb67072bb77e0d3cc035a0b02c0336c))

### Bug Fixes

* **chatbox:** TK-2631: fix fontsize input ([947f017](https://github.com/resola-ai/deca-apps/commit/947f017e73838aebd6571055aee7b75c62fb6c8c))

### [0.4.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.1) (2024-12-27)


### Features

* **chatbox:** TK-3976: handle execution message ([#3771](https://github.com/resola-ai/deca-apps/issues/3771)) ([a5b0ff8](https://github.com/resola-ai/deca-apps/commit/a5b0ff88b73df6d0363fc993e15f8fe80363bef3))

## [0.4.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.0) (2024-12-23)

### Features

* **chatbox:** TK-2539: improve show image after create new one ([004abd7](https://github.com/resola-ai/deca-apps/commit/004abd76ce7c2b260fec376b4200b6fea7da2b8d))
* **chatbox:** TK-2540: open image in new tab from htmlMessage ([fbd5c5d](https://github.com/resola-ai/deca-apps/commit/fbd5c5dcbfb4c9d801469e8899c85206d26c753d))
* **chatbox:** TK-2540: open image in new tab from livechat ([135f465](https://github.com/resola-ai/deca-apps/commit/135f465bd3718fb32ccee6625a2c8aa294ad7f4b))
* **chatbox:** TK-2540: remove unused code ([8a03710](https://github.com/resola-ai/deca-apps/commit/8a0371059ac5a8d1de0e342ac8b23dea7d2bdc85))
* **chatbox:** TK-2977: update buttons template ([20a4898](https://github.com/resola-ai/deca-apps/commit/20a4898618639e97e08c5a0c98ab9e08deaa0f9c))
* **chatbox:** TK-2977: update style for message ([5c61c9f](https://github.com/resola-ai/deca-apps/commit/5c61c9feaa6fb30d19a93e7cdb37655fcac2c857))
* **chatbox:** TK-3479: update navbar icons ([8b10b20](https://github.com/resola-ai/deca-apps/commit/8b10b2097819c119653d371d4fbf6f097d56e525))


### Bug Fixes

* **chatwindow:** Correct email link format when rendering via server variable in Chatwindow ([a1d8b15](https://github.com/resola-ai/deca-apps/commit/a1d8b15eaac4dd1ef4719b9a05ca5076ce453790))
* **chatwindow:** Improve Markdown condition when rendering in Chatwindow messages ([634c7b5](https://github.com/resola-ai/deca-apps/commit/634c7b5887192abf9187f43f660f04a400d2d0f4))
* **chatwindow:** TK-2633 Correct email detect in Markdown message and text input message ([e8c7eee](https://github.com/resola-ai/deca-apps/commit/e8c7eee543e0da82fa24e413fe179b36608d1fb3))


### [0.3.21](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.21) (2024-11-20)


### Features

* **chatbox:** ckl-659: apply setting for launcher ([5f257f6](https://github.com/resola-ai/deca-apps/commit/5f257f6f4d17db89eed8375b97c5eef8a9ba556e))
* **chatbox:** ckl-659: create collapse button ui ([b4e3f89](https://github.com/resola-ai/deca-apps/commit/b4e3f89d61717618a21dd3e1f3cf0c16c8167f9a))
* **chatbox:** ckl-659: fix issue upload image ([02ead4d](https://github.com/resola-ai/deca-apps/commit/02ead4d3ba2a09f98e5df326e4c5ef51292d2f33))
* **chatbox:** ckl-659: implement launcher ui setting ([9570af1](https://github.com/resola-ai/deca-apps/commit/9570af115ccba4bbc1c79cd3755decf02789e6bd))
* **chatbox:** ckl-659: update words, styles ([9a9007f](https://github.com/resola-ai/deca-apps/commit/9a9007f54fa17550268606490d7569671e3d2c07))
* **chatbox:** ckl-720: update condition to disable input ([989202a](https://github.com/resola-ai/deca-apps/commit/989202a1885b32c986c0f4fdcbfd9329dbef7c4b))
* **chatbox:** ckl-735: update words ([9740cef](https://github.com/resola-ai/deca-apps/commit/9740cef3136b92450337d7442fbc258acea3821b))

### Bug Fixes

* **chatbox:** ckl-659: fix image ([e6bec73](https://github.com/resola-ai/deca-apps/commit/e6bec731658b9558d39151183c3385b604abb9d6))
* **chatbox:** ckl-659: fix image size ([af7850f](https://github.com/resola-ai/deca-apps/commit/af7850fb1bfdd68a3e259f6d224d857ef2dd136a))
* **chatbox:** ckl-731: missing label ([34a121d](https://github.com/resola-ai/deca-apps/commit/34a121ddc282490502bd84c19e7578d7f46816bf))
* **chatbox:** ckl-731: remove unused code ([55b5179](https://github.com/resola-ai/deca-apps/commit/55b5179ca05a49a2e182c43b866387604cc8ce9c))

### [0.3.20](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.20) (2024-11-18)

### Features

### Bug Fixes

- **chatwindow:** CKL-721 Handle decode special charaters from ChatBot variable when rendering in ChatWindow ([e897fdf](https://github.com/resola-ai/deca-apps/commit/e897fdfc6bb06676287eff788d7a493bea49f476))
- **chatwindow:** CKL-722 Correct markdown breaklines style and paste event prevent condition in Markdown editor ([6ea5ff7](https://github.com/resola-ai/deca-apps/commit/6ea5ff76f9cee9b40854c24134d729a736fc9b1b))

### [0.3.19](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.19) (2024-11-12)

### Features

- **chatbox:** ckl-660: apply BlockNoteMarkdown ([43adedd](https://github.com/resola-ai/deca-apps/commit/43adedde963ac44f483239c649b6716c3e1f1259))
- **chatbox:** ckl-660: fix drawer size ([08d782e](https://github.com/resola-ai/deca-apps/commit/08d782eef4ed6f4d42a7569d958d3a96bddf0267))
- **chatbox:** ckl-660: support showing markdown in widget articles ([344e8d6](https://github.com/resola-ai/deca-apps/commit/344e8d6edac446c2f9e8c96e154bb39cf0a1dda7))
- **chatbox:** CKL-680 - Reduce built size by tree shaking ([6a54aa8](https://github.com/resola-ai/deca-apps/commit/6a54aa8535a88cb93e79e1dc3efc98ad772ab4f5))
- **chatbox:** CKL-680 - Reduce the built size by tree shaking (update setting) ([2a2b2b1](https://github.com/resola-ai/deca-apps/commit/2a2b2b106aff722ccfdab7433d14f141326fb0e9))
- **chatbox:** ckl-684: suppport ai capture card ([dfcc6d4](https://github.com/resola-ai/deca-apps/commit/dfcc6d4e821a2541e63b8f2ca8124322689bb4d6))
- **chatbox:** ckl-700: move toggle launcher to styling setting page ([e67095f](https://github.com/resola-ai/deca-apps/commit/e67095ffd0dc21faf3150dac84a7317412d4f1b6))
- **chatbox:** ckl-719: add wstoken to payload ([78800e0](https://github.com/resola-ai/deca-apps/commit/78800e068593e391e3698585f3bec11a8944d578))

### Bug Fixes

- **chatbox:** ckl-668: fix no showing hyperlink ([36f0273](https://github.com/resola-ai/deca-apps/commit/36f0273e652ef924a75f6ef63fabf567ced5df06))
- **chatbox:** ckl-668: format ([e905bfc](https://github.com/resola-ai/deca-apps/commit/e905bfc4405981e2165c6fcdc024aa11aac445b9))
- **chatbox:** ckl-676: fix url pattern ([8100d2e](https://github.com/resola-ai/deca-apps/commit/8100d2ee7eba5bece3e8bfc7566fc8dec06f9b69))
- **chatwindow:** CKL-464 Apply hyperlink color to markdown message in ChatWindow ([ae40439](https://github.com/resola-ai/deca-apps/commit/ae4043927c47149349a9fe56746049888180849c))

### [0.3.18](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.18) (2024-10-29)

### Features

- **chatbox:** ckl-515: show time stamp on message bubble ([6e95054](https://github.com/resola-ai/deca-apps/commit/6e950543da26daea2541f21d40d75dfcd5b3eab5))
- **chatbox:** ckl-632: adding new option for button widget ([c49c105](https://github.com/resola-ai/deca-apps/commit/c49c1059ce1b52e995c9f5f8ecaf5979e51dd04a))
- **chatbox:** ckl-633: improve article widget ([97ac78e](https://github.com/resola-ai/deca-apps/commit/97ac78ef26112e70d472218406c0f900e16ddb45))

### Bug Fixes

- **chatbox:** ckl-611: fix character incorect in CW ([6dd0381](https://github.com/resola-ai/deca-apps/commit/6dd0381fd7f30e0c376fccef936986c2b5cd5653))
- **chatbox:** ckl-611: fix characters display incorrect ([#3128](https://github.com/resola-ai/deca-apps/issues/3128)) ([624d410](https://github.com/resola-ai/deca-apps/commit/624d410c86725f7bb42793bccaded6972dd55d0b))
- **chatbox:** ckl-611: fix display incorrect text ([#3132](https://github.com/resola-ai/deca-apps/issues/3132)) ([e70785e](https://github.com/resola-ai/deca-apps/commit/e70785e444fb31758505a5e42db07337d41172b9))

### [0.3.17](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.17) (2024-10-15)

### Features

- **chatbox:** ckl-489: fix issue line break, refactor fix escape function ([#3023](https://github.com/resola-ai/deca-apps/issues/3023)) ([f8e3ab5](https://github.com/resola-ai/deca-apps/commit/f8e3ab53979e4e14a52b20208cbe1fc8fcf7ddfa))
- **chatbox:** ckl-567: fix pattern event lc, fix escapeCharacter function ([#3005](https://github.com/resola-ai/deca-apps/issues/3005)) ([c089660](https://github.com/resola-ai/deca-apps/commit/c089660c595ea01a8dd6c27629f4c7e6d2f9c2e1))
- **chatbox:** ckl-567: handle livechat events ([#3001](https://github.com/resola-ai/deca-apps/issues/3001)) ([c56743a](https://github.com/resola-ai/deca-apps/commit/c56743a0382aca4637883a9fa187b432d69a203e))
- **chatbox:** ckl-572: handle hide CW when server is down ([#3025](https://github.com/resola-ai/deca-apps/issues/3025)) ([4786a1b](https://github.com/resola-ai/deca-apps/commit/4786a1b81fbabd054b39bb00904490d348b1f81c))

### Bug Fixes

- **chatbox:** ckl-485-486: fix escape character on ui when user input ([#2927](https://github.com/resola-ai/deca-apps/issues/2927)) ([91decb2](https://github.com/resola-ai/deca-apps/commit/91decb2b0b91dcae9367bc019b191c1e78e62382))
- **chatbox:** ckl-485: fixing message text ([#2959](https://github.com/resola-ai/deca-apps/issues/2959)) ([91ac8be](https://github.com/resola-ai/deca-apps/commit/91ac8bed0cf1bd38572a636c37ef518f1d7145bd))
- **chatbox:** ckl-549: fix header of sample mesages ([#2934](https://github.com/resola-ai/deca-apps/issues/2934)) ([a2f7454](https://github.com/resola-ai/deca-apps/commit/a2f74542b15234fb6c374c5435edf28cd1ffbd4d))
- **chatbox:** ckl-560: fix missing variables in req body ([#3038](https://github.com/resola-ai/deca-apps/issues/3038)) ([df8316c](https://github.com/resola-ai/deca-apps/commit/df8316cefd4c038850a75912d94a468e31176322))
- **chatbox:** ckl-586: Font style is displayed differently depending on the page ([#3046](https://github.com/resola-ai/deca-apps/issues/3046)) ([69749fd](https://github.com/resola-ai/deca-apps/commit/69749fdc48e1f1d7a43ea43a56d6ca7ed54d847b))

### [0.3.16](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.16) (2024-09-30)

### Features

- **chatbox:** ckl-465: improve auto open chatwindow setting ([3defa9f](https://github.com/resola-ai/deca-apps/commit/3defa9feacfc04c08b4e87b319a8e6af0f57b22b))
- **chatbox:** ckl-465: improve chat header ([a561504](https://github.com/resola-ai/deca-apps/commit/a561504b65a1c357c5307286b5e54a647891e6e9))
- **chatbox:** ckl-465: improve chatchat ui ([02f1ddc](https://github.com/resola-ai/deca-apps/commit/02f1ddc1f8e9e11939b12210a07d069ccea6b21f))
- **chatbox:** ckl-465: improve codebase ([cb27fd9](https://github.com/resola-ai/deca-apps/commit/cb27fd9b81dcef0cb4ae38ba9d755c88041ef274))
- **chatbox:** ckl-500: improve wording message ([78e1892](https://github.com/resola-ai/deca-apps/commit/78e18923287673f4251cb2019cc59b0629acc4b4))
- **chatbox:** ckl-500: update text ([f9cc7b4](https://github.com/resola-ai/deca-apps/commit/f9cc7b461ce66bd697aaf770d6928e32ac9e0492))

### Bug Fixes

- **chatbox:** ckl-433: fix header ui ([d286831](https://github.com/resola-ai/deca-apps/commit/d286831fc3d7ec28d596abc3a4368312700690e2))
- **chatbox:** ckl-498: fix cannot use setVariable in flow ([c9d225e](https://github.com/resola-ai/deca-apps/commit/c9d225e2b7be5e576bc5090025f0b2f4c390d036))

### [0.3.15](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.15) (2024-09-27)

### Bug Fixes

- **chatwindow:** CKL-469 Correct breakline with LC connected ([aaf39e4](https://github.com/resola-ai/deca-apps/commit/aaf39e489a9268d0a754a75d21f45793173d4d67))

### [0.3.14](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.14) (2024-09-26)

### Bug Fixes

- **chatwindow,chatbot:** CKL-469 Correct encode issue to remove // character from URL ([4e8ddb7](https://github.com/resola-ai/deca-apps/commit/4e8ddb7a35b87851eb87c1d3eed145f78b929328))
- **chatwindow,chatbot:** CKL-469 Corrected blank row and % character from URL ([17129b1](https://github.com/resola-ai/deca-apps/commit/17129b174a4fa523f711abdb8b63206e2b6e2964))
- **chatwindow,chatbot:** CKL-469 Corrected spacing between lines in markdown text ([2f18042](https://github.com/resola-ai/deca-apps/commit/2f180428d3c6f3e7a7e95de8fd09e7b99dca6c7c))

### [0.3.13](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.13) (2024-09-25)

### Features

- **chatwindow:** CKL-420 Handle render phone number link and variable in ChatWindow ([b21bc41](https://github.com/resola-ai/deca-apps/commit/b21bc4120add6094da375f59f6bda9be58a51838))
- **chatwindow:** CKL-420 Remove CHatBox handle link from chatwindow ([82764f2](https://github.com/resola-ai/deca-apps/commit/82764f22938626c8e04fcf356185a1b758bf7085))
- ckl-222 ([6c26d12](https://github.com/resola-ai/deca-apps/commit/6c26d1286ce66cfe0127553b0bea8975efda7fd2))

### Bug Fixes

- **chatbox:** CKL-422 Correct format url with tag a wrapped ([73d0a59](https://github.com/resola-ai/deca-apps/commit/73d0a59f0bdd6454c983397b91d826d0fffc1779))
- **chatwindow:** CKL-422 Correct duplicated tag in text message that url included ([213d70d](https://github.com/resola-ai/deca-apps/commit/213d70dd8ca09f6274a9abb3ba65807d82523a6c))
- **chatwindow:** CKL-422 Corrrect eslint warning in widget engine ([b84e35f](https://github.com/resola-ai/deca-apps/commit/b84e35f858de1193070d8406c9eea0d8f6cf2420))
- **chatwindow:** CKL-422 Corrrect link rendering without protocol in Custom Text ([4726685](https://github.com/resola-ai/deca-apps/commit/472668513228002dcd52514ae2c61ac94bca5e85))
- **chatwindow:** CKL-422 Corrrect open new tab in URL from text message ([da67fa8](https://github.com/resola-ai/deca-apps/commit/da67fa834506ad3785f2085c121893fb85af64f7))

### [0.3.12](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.12) (2024-09-19)

### Features

- **chatbox:** ckl-335: change name, binding data model ([c85dbef](https://github.com/resola-ai/deca-apps/commit/c85dbefe4af10ecf988ac70599b67e542fb4d078))
- **chatbox:** ckl-335: create related article template ([44422c2](https://github.com/resola-ai/deca-apps/commit/44422c2c07fc010034154de2cfb9aacbf016a840))
- **chatbox:** ckl-335: fix params ([2329deb](https://github.com/resola-ai/deca-apps/commit/2329debe1c2112c3b5c12b9d4ddb437b678b1aec))
- **chatbox:** ckl-335: improve qa message, refactor custom configs ([21d1fd0](https://github.com/resola-ai/deca-apps/commit/21d1fd0a40c533da817afbce3654bdb9cb9e8ca2))
- **chatbox:** ckl-335: refactor custom configs ([13b9f24](https://github.com/resola-ai/deca-apps/commit/13b9f24a2f95eca94a2f5cf81bfbdf3029c1278e))
- **chatbox:** ckl-335: refactor type chat response ([d9d08bf](https://github.com/resola-ai/deca-apps/commit/d9d08bf741e26ae4242a4bbe1db812db69b9c9b8))
- **chatbox:** ckl-335: showing not found message ([7a86e21](https://github.com/resola-ai/deca-apps/commit/7a86e2178c7ee0f86d98a5d3c0f0ab2a9749c9ed))
- **chatbox:** ckl-335: showing qa text ([a79d46a](https://github.com/resola-ai/deca-apps/commit/a79d46ae92f1d4ed628734023e88b4d765f35376))
- **chatbox:** ckl-335: update styles ([b7670b8](https://github.com/resola-ai/deca-apps/commit/b7670b81c886a3e4eeaa34c82309395afcc1033f))
- **chatbox:** ckl-335: update ui ([d1373f2](https://github.com/resola-ai/deca-apps/commit/d1373f2fbff1af5e611810b9cfa69c6a3c46f780))
- **chatbox:** ckl-366: update livechat event ([7f8f278](https://github.com/resola-ai/deca-apps/commit/7f8f278c7e3de3fa717fb41f563e707a8ad3ef71))
- **chatbox:** ckl-417: fix issue flicker launcher of cw ([3cf7b92](https://github.com/resola-ai/deca-apps/commit/3cf7b92df98607b7f5a85e0d7111d31bf792d5c8))
- **chatbox:** ckl-417: update cw script ui ([a4854f8](https://github.com/resola-ai/deca-apps/commit/a4854f8285f34e20234c4140c7a082a34c8ec1ee))
- **chatwindow:** CKL-420 Handle render phone number link and variable in ChatWindow ([b21bc41](https://github.com/resola-ai/deca-apps/commit/b21bc4120add6094da375f59f6bda9be58a51838))
- **chatwindow:** CKL-420 Remove CHatBox handle link from chatwindow ([82764f2](https://github.com/resola-ai/deca-apps/commit/82764f22938626c8e04fcf356185a1b758bf7085))

### Bug Fixes

- **chatbox:** ckl-368: fix issues chatbox preview ([4a405d4](https://github.com/resola-ai/deca-apps/commit/4a405d48c16882ea7e49bd44b7e51aecc5747536))
- **chatbox:** ckl-368: fix issues when embeded more chatwindow ([535ddf3](https://github.com/resola-ai/deca-apps/commit/535ddf33095f1d981c45459de62d3ceac96d8ecd))
- **chatbox:** ckl-72: update logic to disable input ([5d0adff](https://github.com/resola-ai/deca-apps/commit/5d0adff14a00e8a5b930f3ad4008b59db3258c9e))
- **chatbox:** Handle null file in image upload components ([#2558](https://github.com/resola-ai/deca-apps/issues/2558)) ([8c8e8cd](https://github.com/resola-ai/deca-apps/commit/8c8e8cde2b5b37fe1f45f2e7e1856e2945ecf872))

### [0.3.11](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.11) (2024-09-05)

### Features

- No new features were added.

### Bug Fixes

- **chatbox:** CKL-336 Prevent markdown rendering with KB generated messages ([378fded](https://github.com/resola-ai/deca-apps/commit/378fded037f730fc3a66f5d23c1f77b4c346835c))
- **chatbox:** CKL-336 Make quick fix on markdown detection pattern in text message ([a12a759](https://github.com/resola-ai/deca-apps/commit/a12a7591e0db103381c748e59e2ee8842da733d6))

### [0.3.10](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.10) (2024-09-04)

### Features

- **chatbox:** ckl-214: fix issue load history ([b156b0f](https://github.com/resola-ai/deca-apps/commit/b156b0f5657a3c6d8be92d83102f784b6d72e953))
- **chatbox:** ckl-214: fix size of loader ([c0c4d48](https://github.com/resola-ai/deca-apps/commit/c0c4d48d35a558330dd9969204e5b0b02d676b3e))
- **chatbox:** ckl-214: update elementId for chatwindow ([e385ab4](https://github.com/resola-ai/deca-apps/commit/e385ab4c3c12fdb4582297d91f1cba19539ab35e))
- **chatbox:** ckl-214: update locales ([885f40e](https://github.com/resola-ai/deca-apps/commit/885f40e17bf2b554218c333f5d7e887c204be97a))
- **chatbox:** ckl-214: update system message ([6d223d5](https://github.com/resola-ai/deca-apps/commit/6d223d5315a9c51c281109dc7bc41d3388757e48))
- **chatbox:** ckl-214: update system messages ([88a33bf](https://github.com/resola-ai/deca-apps/commit/88a33bf2b0a4e623536db5626aad105c457957f5))
- **chatbox:** ckl-227: add bot trigger component, update localesand value constants ([2c24e54](https://github.com/resola-ai/deca-apps/commit/2c24e5431639baeed9fa4403573c28e835660ed1))
- **chatbox:** ckl-227: add code snippet ([0509409](https://github.com/resola-ai/deca-apps/commit/0509409d0273fc8436d7165f2c8f4f838cb56014))
- **chatbox:** ckl-227: create triggerChatWindow function, clean code ([21c2edd](https://github.com/resola-ai/deca-apps/commit/21c2edd76c17eba5dc8fa21a682466ecb4918314))
- **chatbox:** ckl-227: fix icon svg select ([79f412e](https://github.com/resola-ai/deca-apps/commit/79f412e4e9ccce769fb9d3f876c37d56e327520b))
- **chatbox:** ckl-227: modified trigger selection control ([3a8d82b](https://github.com/resola-ai/deca-apps/commit/3a8d82bcba259bfa6febaecd6a8b2f25b9707152))
- **chatbox:** ckl-227: update locales, styling ([be30aed](https://github.com/resola-ai/deca-apps/commit/be30aed27c136bf069ce93f8523b2462a076db3c))
- **chatbox:** ckl-70: improve loading animation ([fa32aae](https://github.com/resola-ai/deca-apps/commit/fa32aae359eab3b499f24b01640116a9d3ebd187))

### [0.3.9](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.9) (2024-08-26)

### Features

- **chatbox:** ckl-136: showing image from livechat ([33a61ca](https://github.com/resola-ai/deca-apps/commit/33a61caf488505532bef2cbe5d20ddee3c5afee8))
- **chatbox:** ckl-146: create new button template ([8d83321](https://github.com/resola-ai/deca-apps/commit/8d83321130ccac1e1de0d79a3528b69f5e042e4b))
- **chatbox:** ckl-146: pass props to button template ([aab547e](https://github.com/resola-ai/deca-apps/commit/aab547e701ad8d492f3914ae46bee39f211aa475))
- **chatbox:** ckl-146: refactor button template, binding clicking event ([75bff30](https://github.com/resola-ai/deca-apps/commit/75bff309576af98c2b77cbe16759a016ff46a73e))
- **chatbox:** ckl-146: refactor submit form events, clean code ([8f1044d](https://github.com/resola-ai/deca-apps/commit/8f1044d80710589715eddd4642c105fede7bfa35))
- **chatbox:** ckl-146: update chatbot types constant ([b084b57](https://github.com/resola-ai/deca-apps/commit/b084b57cab33eb283d8edea4d1dab72a6d6cb7c3))
- **chatbox:** ckl-253: add logic handle buttons ([ebf044a](https://github.com/resola-ai/deca-apps/commit/ebf044a51ada09ac9a496fda45597288bba6b5b0))
- **chatbox:** ckl-253: define url ([fc0da4c](https://github.com/resola-ai/deca-apps/commit/fc0da4c8ede734b5bcd909d887053e05d6d8169d))
- **chatbox:** ckl-253: integrate chatbox history ([2bb873f](https://github.com/resola-ai/deca-apps/commit/2bb873fed37f3a82381557f19cf6f35ca5f97e4d))
- **chatbox:** ckl-253: rename hook ([bb194df](https://github.com/resola-ai/deca-apps/commit/bb194df570a8791edee3ecd11118871d130816aa))
- **chatbox:** CKL-256 Handle render markdown content from Flow to ChatWindow message ([eb59031](https://github.com/resola-ai/deca-apps/commit/eb5903114dd8c65e6d8a9aaf17668a3b4be0ee10))
- **chatbox:** ckl-68: add order as query param ([728f959](https://github.com/resola-ai/deca-apps/commit/728f959d0fab190fccf679b52a9a1f7db374ac39))

### Bug Fixes

- **chatbox:** ckl-187: fix button ui ([5a7f49b](https://github.com/resola-ai/deca-apps/commit/5a7f49b36fc710cef83b3d4de567a3b88e867913))
- **chatbox:** ckl-261: create new mockChatState for sample ([7ed02b3](https://github.com/resola-ai/deca-apps/commit/7ed02b31db716d92f3ca9c7cfbc93a6ecd61ff1b))
- **chatbox:** ckl-261: fix livechat status ([1eadd3b](https://github.com/resola-ai/deca-apps/commit/1eadd3b9547ac8bec8690f4963f79e6c450e722d))
- **chatbox:** ckl-267: fix button submit form ([e4211b3](https://github.com/resola-ai/deca-apps/commit/e4211b3ef7e285124df2ee15302ce9baebba3b43))
- **chatbox:** ckl-275: fix disable input field ([f49f4a1](https://github.com/resola-ai/deca-apps/commit/f49f4a1e671b41b2592054a51c5e2cec5755ee60))
- **chatbox:** ckl-53: replace tab spacing char ([33fdf21](https://github.com/resola-ai/deca-apps/commit/33fdf21d45539f6393dd4faee28d623aa6638e53))
- **chatbox:** ckl-71: catch error and refresh token ([541fa99](https://github.com/resola-ai/deca-apps/commit/541fa99c77bfee43fe25ac6ef819a26b0fd0d644))
- **chatbox:** CKL-73 Correct height and warning in chatbox related to rendering ([2b3e981](https://github.com/resola-ai/deca-apps/commit/2b3e981348904dea95b08680bbeb3bb07778226e))
- **chatbox:** ckl-75: calculate height of chatbox to fit with screens ([e83317d](https://github.com/resola-ai/deca-apps/commit/e83317d2005bc7cc7460f0adb7b370162ea4afed))
- **chatbox:** ckl-75: create useWindowResize hook ([895875b](https://github.com/resola-ai/deca-apps/commit/895875bf3209c224fbb710850161aa6a5c2e774d))
- **chatbox:** ckl-75: update max value for bottomSpacing ([4cc6e76](https://github.com/resola-ai/deca-apps/commit/4cc6e766dd645fca7480c4ace607e57c55056797))

### [0.3.8](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.8) (2024-07-23)

### Features

- **chatbox:** cb-1682: change rem to px to prevent affect css ([7e4d651](https://github.com/resola-ai/deca-apps/commit/7e4d6516e88e9762aa4c3dc71f7750fef5f38823))
- **chatbox:** cb-1682: remove unused code ([906bc36](https://github.com/resola-ai/deca-apps/commit/906bc360db683529d38ed8133e7a2ab9c71b27e7))
- **chatbox:** cb-1682: update css ([ca446d7](https://github.com/resola-ai/deca-apps/commit/ca446d74d9038e84aa5b2d748a811af5f827b476))
- **chatbox:** cb-1682: update ui ([ea1023d](https://github.com/resola-ai/deca-apps/commit/ea1023da6ba20db3da2a76db438ff80d1ec0624d))
- **chatbox:** cb-1682: update ui ([777b06a](https://github.com/resola-ai/deca-apps/commit/777b06a9b0a6132192e71d398545c8701f9f18b3))
- **chatbox:** cb-1696: fix ui issue ([c169826](https://github.com/resola-ai/deca-apps/commit/c16982607177e3a2ca9ad83e989b73ef697c80d9))
- **chatbox:** cb-1696: remove spoiler ([4d4f850](https://github.com/resola-ai/deca-apps/commit/4d4f850e25b085219f70fa2efdfa5f9d19c07622))

### Bug Fixes

- **chatbox:** CB-1605 fix japanese ime input event isssue ([4efd9fd](https://github.com/resola-ai/deca-apps/commit/4efd9fdace5e9b616fa53e57813634b395a455c7))
- **chatbox:** cb-1685: add events for button whenever toggle state ([a4689ce](https://github.com/resola-ai/deca-apps/commit/a4689ce350397362381b6d0ea26244d0b81adc73))
- **chatbox:** CB-1692 update css for spoiler to handle viewer position ([fc7a6c2](https://github.com/resola-ai/deca-apps/commit/fc7a6c256d142c4dcd8a46b9de4cb8aeecbc34dd))
- **chatbox:** CB-1697 Correct build in Spoiler component ([3a90b68](https://github.com/resola-ai/deca-apps/commit/3a90b688a5a71c7cdf1c713034ba48ff3b80092a))
- **chatbox:** CB-1697 Create custom spoiler component and apply to chatbox ([f87ae11](https://github.com/resola-ai/deca-apps/commit/f87ae11ce7e6fb63add1b8833b82bd65041b91a5))
- **chatbox:** cb-1697: apply spoiler to article ([31b038e](https://github.com/resola-ai/deca-apps/commit/31b038e4880cd47587ab82e6fcae3695921030b9))

### [0.3.7](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.7) (2024-07-18)

### Features

- **chatbox:** cb-1584: add textarea in livechat mode ([a136317](https://github.com/resola-ai/deca-apps/commit/a1363178f66c832c80fc4245538a2d6da37db159))
- **chatbox:** cb-1584: improve input text ([6526755](https://github.com/resola-ai/deca-apps/commit/652675562d0a3e8fb1361f88e840d075b781471a))
- **chatbox:** cb-1584: update input props ([0e342e5](https://github.com/resola-ai/deca-apps/commit/0e342e5971efc84730cb1c64cd23b59f0a2abe85))
- **chatbox:** cb-1585: change design of disconnect livechat button ([7826c1b](https://github.com/resola-ai/deca-apps/commit/7826c1bea34f84596f4f4c71a8191fa9a64e63b0))
- **chatbox:** cb-1594: add color for hyperlink from kb article ([7225997](https://github.com/resola-ai/deca-apps/commit/7225997b73de374ae0954b804a9507d25886d546))
- **chatbox:** cb-1594: add settings for link color ([3645e2b](https://github.com/resola-ai/deca-apps/commit/3645e2b3cf0ef828e5ed40be65c5b462d50f70c5))
- **chatbox:** cb-1594: clean code ([ce1c088](https://github.com/resola-ai/deca-apps/commit/ce1c08800b9b1c60d82545c4c84721fbccd074a1))
- **chatbox:** cb-1594: update model ([1ee1e98](https://github.com/resola-ai/deca-apps/commit/1ee1e986723ed4a7e2b985317414b13a442f1861))
- **chatbox:** cb-1594: update type ([22cc091](https://github.com/resola-ai/deca-apps/commit/22cc091b1ae8d7f9e3cf8667b1c06522891e4eed))
- **chatbox:** cb-1648: add condition to import inline css ([7dabcf6](https://github.com/resola-ai/deca-apps/commit/7dabcf6940279b4dd07748ab81d440eb961374fc))

### Bug Fixes

- **chatbox:** cb-1574: fix cant click the link on client page ([7a5f11b](https://github.com/resola-ai/deca-apps/commit/7a5f11b2572af21b160e4399efe4894162a7e6d2))
- **chatbox:** cb-1577: fix blocknote styling issue ([f6a92d3](https://github.com/resola-ai/deca-apps/commit/f6a92d3854197c0f0a4653fa1ec51c24e195389a))
- **chatbox:** cb-1577: fix cdn issue ([83bafbb](https://github.com/resola-ai/deca-apps/commit/83bafbb82db881be20c64b3b15e07b6d9d575516))
- **chatbox:** cb-1630: fix issue disable message input and loading ([ba14628](https://github.com/resola-ai/deca-apps/commit/ba146285abd3f9792c930ec10f86b52087a4fb6b))
- **chatbox:** cb-1630: prevent undefined data ([c11ca6d](https://github.com/resola-ai/deca-apps/commit/c11ca6df7b3d08b4de3690a344b58d0f28773667))
- **chatbox:** cb-1630: revert ([644f2c6](https://github.com/resola-ai/deca-apps/commit/644f2c63a37547ec7600bd64836acdb52932c871))
- **chatbox:** cb-1630: update constant name ([61478a3](https://github.com/resola-ai/deca-apps/commit/61478a32c874fa2ffb2b23ba28ec7c436ac95748))
- **chatbox:** cb-1656: add shadow root ([d1247a8](https://github.com/resola-ai/deca-apps/commit/d1247a80cfc43be4aa7d6b9898199e04cd33b487))
- **chatbox:** cb-1656: fixing styling ([b390801](https://github.com/resola-ai/deca-apps/commit/b390801cfd3bf22e07c5c12b31e5d3b4a0d6d1d0))
- **chatbox:** cb-1676: add break word css ([59934f3](https://github.com/resola-ai/deca-apps/commit/59934f3a85eb511d0e29fb567273d43995eff62a))

### [0.3.6](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.6) (2024-07-04)

### Bug Fixes

- **chatbox:** cb-1574: fix cant click the link on client page ([1fa5bee](https://github.com/resola-ai/deca-apps/commit/1fa5beeea3c1c643e67aaa55c746d920fc7b0050))

### [0.3.5](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.5) (2024-07-01)

### Features

- **chatbox:** CB-1341 Improve render Article with BlockNote styles inside Chatbox Client messages ([0f62fdc](https://github.com/resola-ai/deca-apps/commit/0f62fdc165a23dd9528403d06e2a0947189e5fcf))
- **chatbox:** cb-1341: add mockdata ([1fb8e02](https://github.com/resola-ai/deca-apps/commit/1fb8e02761e38155948dae892de8a48e2be37f04))
- **chatbox:** cb-1341: add translation, improve engine data in chatState ([4fecc99](https://github.com/resola-ai/deca-apps/commit/4fecc9907e28a3b69aaef6791a6bdfdcd910e9f1))
- **chatbox:** cb-1341: change name ([403d179](https://github.com/resola-ai/deca-apps/commit/403d1797c415f513fcd05db6c057cf45e3d43ee6))
- **chatbox:** cb-1341: render view ([5a60887](https://github.com/resola-ai/deca-apps/commit/5a6088711263447bbd8d22e2d035731264a39700))
- **chatbox:** cb-1341: update schema ([27a0b04](https://github.com/resola-ai/deca-apps/commit/27a0b048b6cf87894b2740aae335b64193df0872))
- **chatbox:** cd-1341: create kb article template ([b41106e](https://github.com/resola-ai/deca-apps/commit/b41106ef2828d9847140c11b218abad0972b0e95))

### Bug Fixes

- **chatbox, kb:** Adjust Article render UI in chatbox and improve the infinite scrolling ([b1c463c](https://github.com/resola-ai/deca-apps/commit/b1c463c8a1adefd1b1ef25378a55baced965e404))
- **chatbox, kb:** CB-1561 Correct some feedbacks related to Article viewer in Chatbox ([f7d9bfb](https://github.com/resola-ai/deca-apps/commit/f7d9bfbad9730d002a07698b6f03833904f2da06))
- **chatbox, kb:** CB-1561 Remove unused falsy check syntax ([0246bcc](https://github.com/resola-ai/deca-apps/commit/0246bcc4b7f10e1f48131c74a6ea5b0407c12f1f))
- **chatbox, kb:** Remove unused style and add dependencies ([3ab3b3c](https://github.com/resola-ai/deca-apps/commit/3ab3b3c8caedfb99dd5ceedac5d1c66503b93d6d))
- **chatbox:** Adjust spoiler rendering with article content length ([9fd6c83](https://github.com/resola-ai/deca-apps/commit/9fd6c8396e8204e23e927a89c94398948927904f))
- **chatbox:** cb-1559: enable input field when get lc events ([3fd07ad](https://github.com/resola-ai/deca-apps/commit/3fd07add7420a6070c11d11c931860293e847f87))
- **chatbox:** cb-1562: fix undefined error ([3cc5bfd](https://github.com/resola-ai/deca-apps/commit/3cc5bfdd4ec3e46121ba8aaf089d6bfb3a321abb))
- **chatbox:** cb-1563: build css in umd file ([e4aa439](https://github.com/resola-ai/deca-apps/commit/e4aa439f701079062455c11f532247e391b2d56b))

### [0.3.4](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.4) (2024-06-25)

### Features

- **chatbox:** cb-1531: refactor clear text behavior ([18e6788](https://github.com/resola-ai/deca-apps/commit/18e6788c67abd107986fffe85f3b869b393e07ff))
- **chatbox:** cb-1542: add new lc event ([53158cb](https://github.com/resola-ai/deca-apps/commit/53158cb2f62681f615e0c804e5db9b33381fe1f0))

### [0.3.3](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.3) (2024-06-20)

### Features

- **chatbox:** cb-1321: add a warning message ([e2f148b](https://github.com/resola-ai/deca-apps/commit/e2f148b45c6883ce761e488230ae484b0daebba9))
- **chatbox:** cb-1321: check limitation before add widgets ([bf54d8e](https://github.com/resola-ai/deca-apps/commit/bf54d8e073e0e016025e17322458f2d9cc7e08b4))
- **chatbox:** cb-1490: limit chatboxex per org ([d7d0c1a](https://github.com/resola-ai/deca-apps/commit/d7d0c1af457379ddd38130753b31bbc785942742))
- **chatbox:** cb-1491: handle integration bot error ([1d8bb07](https://github.com/resola-ai/deca-apps/commit/1d8bb0797fd353b2252e48a436153caecf4b2428))
- **chatbox:** cb-1493: improve input message based on buttons card ([c07193b](https://github.com/resola-ai/deca-apps/commit/c07193b7f721816682f036c25d5fd10ddcbdfe53))
- **chatbox:** cb-1496: clear text when click button ([ab31368](https://github.com/resola-ai/deca-apps/commit/ab31368d561766a48c8506781bd041a21390178e))

### Bug Fixes

- **chatbox:** update package.json ([ab353d0](https://github.com/resola-ai/deca-apps/commit/ab353d0c305dca5393e7d5e5f492320e3b93729f))

### [0.3.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.2) (2024-06-06)

### Features

- **chatbox:** CB-1078 - Add Search box for chatbox and chatbot listing ([#1359](https://github.com/resola-ai/deca-apps/issues/1359)) ([b818b98](https://github.com/resola-ai/deca-apps/commit/b818b9899944066c19f8a00bfc8e04e436dbda71))
- **chatbox:** cb-1082: add design system colors ([2688566](https://github.com/resola-ai/deca-apps/commit/2688566da537b80e2b32e1b5a5d118d2ee2d2e6b))
- **chatbox:** cb-1082: add welcomeMessageColor input ([773a7af](https://github.com/resola-ai/deca-apps/commit/773a7afc306850914f3a03b5242e7351f2c85d03))
- **chatbox:** cb-1082: apply color setting to elements ([ede8bb9](https://github.com/resola-ai/deca-apps/commit/ede8bb9894fa0526442a4c2ce620d1aa69b587a0))
- **chatbox:** cb-1082: binding data to colorSetting ([223fb88](https://github.com/resola-ai/deca-apps/commit/223fb8844f87f3e4aa2fe8f008346b684905f205))
- **chatbox:** cb-1082: change field name, binding color to element ([976db25](https://github.com/resola-ai/deca-apps/commit/976db259d1803877207a0f878eba0ca4a6ca38f3))
- **chatbox:** cb-1082: create customTheme UI ([9384b76](https://github.com/resola-ai/deca-apps/commit/9384b76ba9f42ddca43c0b153ca883069b957ef5))
- **chatbox:** cb-1082: fix error ([d50bbf7](https://github.com/resola-ai/deca-apps/commit/d50bbf70a156238530463eea0ec0804b4d0e5d5a))
- **chatbox:** cb-1082: remove ColorBrandSection ([35fffd0](https://github.com/resola-ai/deca-apps/commit/35fffd02a21f0d3e9bd8e46db52cd4499bb29c6e))
- **chatbox:** cb-1082: remove unused file ([a61b653](https://github.com/resola-ai/deca-apps/commit/a61b653f4b8dc3b84a2e301eb94dbf6f13bcbfad))
- **chatbox:** cb-1082: render themePicker, binding color to elements ([021144a](https://github.com/resola-ai/deca-apps/commit/021144a9ed7ef0c4294c2fc4e248ec8ec16c2b90))
- **chatbox:** cb-1082: save themeColor ([9c17881](https://github.com/resola-ai/deca-apps/commit/9c17881db92620a3319582fba0a867f5558de7a2))
- **chatbox:** cb-1082: update color for callToAction button ([bb51157](https://github.com/resola-ai/deca-apps/commit/bb51157a931507d70d7dcf12571a8c2f2f55041a))
- **chatbox:** cb-1082: update colors, remove theme default ([a9383d5](https://github.com/resola-ai/deca-apps/commit/a9383d5efec7875646c23a04224921c48c557dc5))
- **chatbox:** cb-1082: update design system colors ([9c2d041](https://github.com/resola-ai/deca-apps/commit/9c2d041549dba73e863c0e41cd16819dd610fe83))
- **chatbox:** cb-1082: update locales ([e4e2f81](https://github.com/resola-ai/deca-apps/commit/e4e2f8177dacc4c9e9161e4bc998fa1b3b105f7f))
- **chatbox:** CB-1083 update config for preview message sample ([a96ace8](https://github.com/resola-ai/deca-apps/commit/a96ace8371dae40821779df7b6ed8b13483a9b27))
- **chatbox:** cb-1083: implement chatbox with sample data ([d78ddbc](https://github.com/resola-ai/deca-apps/commit/d78ddbc1cb947257886e543fca58edd8178bd9c0))
- **chatbox:** cb-1083: update form button ([d333122](https://github.com/resola-ai/deca-apps/commit/d333122023717037196d996e24dd1e94d2d7fd24))
- **chatbox:** CB-1119 - Add option to hide launcher when opening chatbox ([#1328](https://github.com/resola-ai/deca-apps/issues/1328)) ([9cbb188](https://github.com/resola-ai/deca-apps/commit/9cbb188ac0af8c6e7dea39e34eacbb394b5b0be7))
- **chatbox:** cb-1376: fix import i18n instance ([d1bc416](https://github.com/resola-ai/deca-apps/commit/d1bc416d3669c6df03fa0ca8d5e2459bb7da6c10))
- **chatbox:** cb-1376: revert changes in 118n file ([f5d18c0](https://github.com/resola-ai/deca-apps/commit/f5d18c09c27a26bb8ece35c27e50c4953e3b2a37))
- **chatbox:** cb-1393: handle wrapup status from livechat conv ([97877f6](https://github.com/resola-ai/deca-apps/commit/97877f62743d45d7894143fb5246e919af5900e0))
- **chatbox:** cb-1420: remove loader after 3s ([f22f1f3](https://github.com/resola-ai/deca-apps/commit/f22f1f3ade194df7004c2ed7b3617fd27dd8457b))
- **chatbox:** cb-1420: update ref ([da3bf75](https://github.com/resola-ai/deca-apps/commit/da3bf758af49c41bd169fc911744959835cb6763))
- **chatbox:** cb-1441: create handle reset kb function ([66573b2](https://github.com/resola-ai/deca-apps/commit/66573b2e58dd7fe82f716ff4b9755051bb2763b1))
- **chatbox:** cb-1441: improve loader in kb mode ([46d54f3](https://github.com/resola-ai/deca-apps/commit/46d54f324b0b9d7e547b0fffcc64f0f9b7e2776d))

### Bug Fixes

- **chatbox:** button should not miss the id ([1633e7e](https://github.com/resola-ai/deca-apps/commit/1633e7e370b2fb5b2d87952e0ea75b6086fd77e7))
- **chatbox:** buttons should be clicked by mapping correct id with event listener ([92b747f](https://github.com/resola-ai/deca-apps/commit/92b747ff4ab2c231da9681f59041cdd6289e662e))
- **chatbox:** cb-1418: fix height of chatbox ([757b728](https://github.com/resola-ai/deca-apps/commit/757b72810612caec7e60cd5568ffab4891e2f9b4))

### [0.3.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.1) (2024-05-09)

### Features

- **chatbox:** cb-1291: update css ([4d37e1e](https://github.com/resola-ai/deca-apps/commit/4d37e1e49e85542055600623063e9558cd45556a))
- **chatbox:** format code ([6b9384f](https://github.com/resola-ai/deca-apps/commit/6b9384fe316ba1cda5e15bb55b774f97e2125c59))
- **chatbox:** format code ([933d267](https://github.com/resola-ai/deca-apps/commit/933d267675552c575d4a2d3430b0a9ef6611e498))

### Bug Fixes

- **chatbox:** CB - 1287 - fix cont when turn off welcome ([2d29abf](https://github.com/resola-ai/deca-apps/commit/2d29abf46129c43eae84101ff3294ac08ee528f7))
- **chatbox:** CB - 1293 - Fix dropdown form not shown ([e4099d3](https://github.com/resola-ai/deca-apps/commit/e4099d31474751cdac623266656faeade99de908))

## [0.3.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.0) (2024-05-07)

### Features

- **chatbox:** CB - 1080 - Content display ([3524cfd](https://github.com/resola-ai/deca-apps/commit/3524cfd6e9dfa751e8542bdc01788a0d70be9870))
- **chatbox:** CB - 1163 - Fix layout is broken ([b5e37c7](https://github.com/resola-ai/deca-apps/commit/b5e37c75780710ae1df7a6edb1d4ea685740936a))
- **chatbox:** CB - 1163 - Update chatbox mobile view ([a55ac60](https://github.com/resola-ai/deca-apps/commit/a55ac6022c6ef9671a3c096429550de38394d730))
- **chatbox:** CB - 1226 - Update create new popup wording change ([7241798](https://github.com/resola-ai/deca-apps/commit/7241798ab53a6861fe3c3fb14a74d57f1b3e553d))
- **chatbox:** CB - 1227 - Update del chatbox popup texts ([974fc6c](https://github.com/resola-ai/deca-apps/commit/974fc6c1d766b49099be8e1e9b0d47fac71cad18))
- **chatbox:** CB - 1228 - Update text for content settings page ([ef430c1](https://github.com/resola-ai/deca-apps/commit/ef430c1efca8086883e3f8bc0949c38324c63f8f))
- **chatbox:** CB - 1229 - Update styling settings text ([c520bc1](https://github.com/resola-ai/deca-apps/commit/c520bc16155c24b2b098053b01639e7abd773c74))
- **chatbox:** CB - 1230 - Update Integrations text ([3714146](https://github.com/resola-ai/deca-apps/commit/3714146050011f51397132cf0ef12e26e3c13736))
- **chatbox:** CB -1 216 - Fix welcome message not sync ([ac79b91](https://github.com/resola-ai/deca-apps/commit/ac79b91ed21f7555646ba6f5b0d9c04917ed4b1e))
- **chatbox:** CB -1163 - Remove parent scroll when bot is opened ([b0a6b63](https://github.com/resola-ai/deca-apps/commit/b0a6b639663987b84abd2dc018ca2cdd24b21df0))
- **chatbox:** cb-1009: check conversationId before request LC history ([153dd76](https://github.com/resola-ai/deca-apps/commit/153dd76916a574ee03d9abbe9448c769a66e32b0))
- **chatbox:** cb-1009: create mapping service, getLiveChatHistory function ([d4f8bac](https://github.com/resola-ai/deca-apps/commit/d4f8bac2615d94dd90cde88521b45c9e195ec0f7))
- **chatbox:** cb-1009: create url, update service mapping params ([3efbdc1](https://github.com/resola-ai/deca-apps/commit/3efbdc182bb6f7ed8aac0b8dea5c21608a9a6b44))
- **chatbox:** cb-1009: define convId key, set remove key on localstorage ([287cdb8](https://github.com/resola-ai/deca-apps/commit/287cdb800d20e8a5c07a420cd5c73795cee717b7))
- **chatbox:** cb-1009: fix endpoint, add params to headers ([09463b8](https://github.com/resola-ai/deca-apps/commit/09463b8c2dc7004b39c9493b084abbd184e86969))
- **chatbox:** cb-1009: integrate history message ([a72657a](https://github.com/resola-ai/deca-apps/commit/a72657accd0093f11673c80c7992a70a4e154fd3))
- **chatbox:** cb-1009: integrate with mock api ([af5be4d](https://github.com/resola-ai/deca-apps/commit/af5be4d0d3e361f2479f1dcbdad221000f0f82e7))
- **chatbox:** cb-1009: refactor history api ([50569e6](https://github.com/resola-ai/deca-apps/commit/50569e661e9f50db9a9745c78b7acf81b5710745))
- **chatbox:** cb-1009: remove convId on localstorage ([f2ef4b0](https://github.com/resola-ai/deca-apps/commit/f2ef4b07316e4e33c259425b05f1d674efd52f4c))
- **chatbox:** cb-1009: set currentConvId ([0762733](https://github.com/resola-ai/deca-apps/commit/0762733a0579f8a7a95f068f500242e9b0e6608e))
- **chatbox:** cb-1009: update params ([f083aa5](https://github.com/resola-ai/deca-apps/commit/f083aa57dfca647ff7f3502b7eca9db9715da85c))
- **chatbox:** CB-1037 - [Chatbox Client] Japanese font should be included in build ([8462794](https://github.com/resola-ai/deca-apps/commit/84627943be73a60709b86232256a6dd20de2dcdf))
- **chatbox:** CB-1037 - [Chatbox Client] Japanese font should be included in build ([faea1df](https://github.com/resola-ai/deca-apps/commit/faea1df55d032f7acb227ebe37d12228038acd19))
- **chatbox:** cb-1056: remove unused code ([6d71c7c](https://github.com/resola-ai/deca-apps/commit/6d71c7cee9e5cd8bf1fbcaca349d84af9bc1c42b))
- **chatbox:** CB-1084 Implement UI for outdated data checking and API integration ([a0bdb95](https://github.com/resola-ai/deca-apps/commit/a0bdb9517564dcf02193fce8fd1ac40780feabb5))
- **chatbox:** CB-1085 - [Chatbox admin] Add restart button option ([#1070](https://github.com/resola-ai/deca-apps/issues/1070)) ([a205854](https://github.com/resola-ai/deca-apps/commit/a205854504e01746c7986efe6fc0c6b1cef86369))
- **chatbox:** CB-1090 - Improve setting section for message tab ([#1062](https://github.com/resola-ai/deca-apps/issues/1062)) ([6d4a6e9](https://github.com/resola-ai/deca-apps/commit/6d4a6e933f87836d0f2a2fab21a4761c0712585d))
- **chatbox:** cb-1093: implement lc conversation with mock data ([e10d901](https://github.com/resola-ai/deca-apps/commit/e10d9017a956886b629e4823786c40b9d54221c2))
- **chatbox:** cb-1093: update ui for conversation item ([27a0fb7](https://github.com/resola-ai/deca-apps/commit/27a0fb7d4aab35d0a4d81b1282e0b33b7ce1573d))
- **chatbox:** cb-1094: add day locale ([bb2ebe7](https://github.com/resola-ai/deca-apps/commit/bb2ebe77cda3e6dd808e386a0a5ca3426b7c1ab9))
- **chatbox:** cb-1094: add ja locale ([5e7022b](https://github.com/resola-ai/deca-apps/commit/5e7022b9f3fef83224cd817a53473c43aedc2044))
- **chatbox:** cb-1094: improve loading conversation list ([d299e07](https://github.com/resola-ai/deca-apps/commit/d299e0790f3036741d797aacf9372881cd03d3d4))
- **chatbox:** cb-1094: integrate conversation list api ([ccf5c41](https://github.com/resola-ai/deca-apps/commit/ccf5c4154bd6e7ab8dad18c97e04c5c472ebba3e))
- **chatbox:** cb-1094: render conversation list with mock data, update model ([3e6a70c](https://github.com/resola-ai/deca-apps/commit/3e6a70c3ab905b5cae276b83d233f5a257f26dbc))
- **chatbox:** cb-1094: save current conversation ([155f37e](https://github.com/resola-ai/deca-apps/commit/155f37e16e44a880d8e29907255a224d5c27e5a2))
- **chatbox:** cb-1094: update pagination field ([4d0d65c](https://github.com/resola-ai/deca-apps/commit/4d0d65c22696f7babf564249c93f7b2fb0ae587d))
- **chatbox:** cb-1095: add ui if conversation no assinee ([5ef1065](https://github.com/resola-ai/deca-apps/commit/5ef10650cd2166d2e30b6f73c9784747bf4127d1))
- **chatbox:** cb-1095: change schema ([a8781a4](https://github.com/resola-ai/deca-apps/commit/a8781a43d3ede71213029653a6a0b5c8e2f02993))
- **chatbox:** cb-1095: fix css ([e16d088](https://github.com/resola-ai/deca-apps/commit/e16d0887ba2ad4227a77c98b609b0566048e27db))
- **chatbox:** cb-1095: fix issue scrolling top ([df3bad8](https://github.com/resola-ai/deca-apps/commit/df3bad87e21874017836bbbffde09d6e6c3deb68))
- **chatbox:** cb-1095: fix loading ([439f91f](https://github.com/resola-ai/deca-apps/commit/439f91f6161fa04c9ff54eb7801912ba4e6fae02))
- **chatbox:** cb-1095: fix status ([4c6420e](https://github.com/resola-ai/deca-apps/commit/4c6420e8e573d8e00474b608c27ed7914a20f56c))
- **chatbox:** cb-1095: fix ts error ([fc54af9](https://github.com/resola-ai/deca-apps/commit/fc54af9226ea8c63fa6d36650fd383d0bdfd0b93))
- **chatbox:** cb-1095: improve fetch conversation list ([d30f58c](https://github.com/resola-ai/deca-apps/commit/d30f58c4ae52d0e435d5bace7a0b9a9e04e2988f))
- **chatbox:** cb-1095: improve fetch conversation list ([505d2cf](https://github.com/resola-ai/deca-apps/commit/505d2cf69c2f6d40c155e0628cf6410a249ea721))
- **chatbox:** cb-1095: improve fetch conversation list ([4117266](https://github.com/resola-ai/deca-apps/commit/411726632dfb4f9917b95c45c4b49ee31a7ea1b1))
- **chatbox:** cb-1095: improve fetch message and conversation ([cacd9f0](https://github.com/resola-ai/deca-apps/commit/cacd9f0420dd2f4421d8df2dd7fbadc4f4968520))
- **chatbox:** cb-1095: improve fetch message history ([cb4ba61](https://github.com/resola-ai/deca-apps/commit/cb4ba617d60381c0fa0707272df76b23fe32ea5d))
- **chatbox:** cb-1095: improve StartConversation button ([f8b0283](https://github.com/resola-ai/deca-apps/commit/f8b028320f4a0c3bfcf5af7de5bc89215ece152a))
- **chatbox:** cb-1095: improve url, remove unused code ([3d4ab1c](https://github.com/resola-ai/deca-apps/commit/3d4ab1c7ef48ac60f0a0ff6c238721e32cfc1fc3))
- **chatbox:** cb-1095: load chatbot message from localstorage ([7e86925](https://github.com/resola-ai/deca-apps/commit/7e86925a3928334a3405a0647255a8c839266637))
- **chatbox:** cb-1095: no add lc messages if conversation is completed ([9282508](https://github.com/resola-ai/deca-apps/commit/9282508716ee32c81294338eda3679a22aede30b))
- **chatbox:** cb-1095: refactor for chat header ([c16aae1](https://github.com/resola-ai/deca-apps/commit/c16aae15abc4b5a0d16c48f60d273f676833d5c1))
- **chatbox:** cb-1095: refactor, remove unused code ([19de8f5](https://github.com/resola-ai/deca-apps/commit/19de8f506a07e54b877ca8335521fa4dd9370fa6))
- **chatbox:** cb-1095: remove console log, disabled input when conversation is completed ([01a5fa0](https://github.com/resola-ai/deca-apps/commit/01a5fa0feeec3ac6b7a0eed8e6212d4008f4524c))
- **chatbox:** cb-1095: update chat header when click one conversation ([7787cf8](https://github.com/resola-ai/deca-apps/commit/7787cf8bafcff5fee24187c2cd4782381f2e059e))
- **chatbox:** cb-1095: update conversation item and chat header ([7dd2ec8](https://github.com/resola-ai/deca-apps/commit/7dd2ec86f1ea7d6b3c3d7f8673459b99cadde6cd))
- **chatbox:** cb-1128: improve sender type ([f1f584c](https://github.com/resola-ai/deca-apps/commit/f1f584ca1ebd65376ab15fac90a9f4a76f174554))
- **chatbox:** cb-1192: improve handle restart ([f3514a8](https://github.com/resola-ai/deca-apps/commit/f3514a869e65d761d79aa019c344cb248e1c361b))
- **chatbox:** cb-1192: integrate restart button ([d23ef1d](https://github.com/resola-ai/deca-apps/commit/d23ef1d202cbff6908e0ccf005125241a317ea3d))
- **chatbox:** cb-1192: update payload ([52bff31](https://github.com/resola-ai/deca-apps/commit/52bff31e7d628ab1d55988f0d0ceee548ad27402))
- **chatbox:** cb-1209: format files ([13b4c03](https://github.com/resola-ai/deca-apps/commit/13b4c03737f971b9265e838957df5e1d4d8272d9))
- **chatbox:** cb-1209: handle finish state in chatbot ([7aa5c95](https://github.com/resola-ai/deca-apps/commit/7aa5c95f1afa8a006acf74ffdafb62014bb11daa))
- **chatbox:** cb-1209: improve ui, handle finish state after reload ([df960db](https://github.com/resola-ai/deca-apps/commit/df960dba917f3059f4a7c7ba89e4cd772ff819a1))
- **chatbox:** CB-1234 update default image ([9356594](https://github.com/resola-ai/deca-apps/commit/93565942ed27613b57a8397f725d45cc38919065))
- **chatbox:** cb-1250: improve fetch conversations ([1cbea68](https://github.com/resola-ai/deca-apps/commit/1cbea6810b34fe9e37a3019df1676b758d71f7a4))
- **chatbox:** cb-1250: loading chatbot message from localStorage ([bd054e6](https://github.com/resola-ai/deca-apps/commit/bd054e675471ace2d81ac06a0dfa5919a0777077))
- **chatbox:** cb-1270: disable input field ([e37c52e](https://github.com/resola-ai/deca-apps/commit/e37c52e8ca075099e124aab37e8cce25664954f5))
- **chatbox:** cb-811: send variable ([6c46946](https://github.com/resola-ai/deca-apps/commit/6c4694604edd97b1c06fbd420323d52cbd7d5db8))
- **chatbox:** cb-811: send variables along hook request ([fd1c22f](https://github.com/resola-ai/deca-apps/commit/fd1c22f75dcfc14cc565eb745cac55f5ee1e6289))

### Bug Fixes

- **chatbox:** CB-1118 - [Chatbox Client] Can not render chatbox client ([ad2fbfa](https://github.com/resola-ai/deca-apps/commit/ad2fbfac3c78875c5949687e89b74f09ba082757))
- **chatbox:** CB-1234 update unit test ([84e5b6a](https://github.com/resola-ai/deca-apps/commit/84e5b6aa815951c806868f53a74b57c3da304cb0))
- **chatbox:** cb-1236: fix issue display messages ([1120cd7](https://github.com/resola-ai/deca-apps/commit/1120cd75f47df8988050f0ea2df1a63530cc8d2c))
- **chatbox:** cb-1236: fix message displays incorrect ([8650243](https://github.com/resola-ai/deca-apps/commit/8650243d98b1c49db8de7f1a331e7b13da38e699))
- **chatbox:** cb-1259: check newMessage ([ac7f2ad](https://github.com/resola-ai/deca-apps/commit/ac7f2adf99ce196c24dce9030465447ba25082a1))
- **chatbox:** cb-1259: fix sender field is undefined ([55ee749](https://github.com/resola-ai/deca-apps/commit/55ee7494c110a6b57e2b073db2371407c953a5af))
- **chatbox:** cb-1267: prevent crash app in form response ([421aacb](https://github.com/resola-ai/deca-apps/commit/421aacb63f56887276d87009f1d72c9220631d50))
- **chatbox:** cb-1267: update function ([51468a2](https://github.com/resola-ai/deca-apps/commit/51468a22674f0904fd0786f2f2b3ffc9a6a678aa))
- **chatbox:** cb-1267: update function name ([68ddb00](https://github.com/resola-ai/deca-apps/commit/68ddb0007b163239ab7cf01c6ade505b1887f161))
- **chatbox:** cb-1268: fix missing colors ([9975189](https://github.com/resola-ai/deca-apps/commit/997518914a9e0a6a9b475642492811d39e1b56cb))
- **chatbox:** cb-1269: enable input when response are buttons or form ([5e437a1](https://github.com/resola-ai/deca-apps/commit/5e437a156104fd0702a6c2e36bb2b5c9a2ef42b6))
- **chatbox:** CB-678 update browser navigation chatbox ([88081e5](https://github.com/resola-ai/deca-apps/commit/88081e5280babb0100f8abf1b741ddcbe7ff44f9))

### [0.2.8](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.8) (2024-04-12)

### Features

- **chatbox:** cb-1000: add select control to form ([972bd96](https://github_resola/resola-ai/deca-apps/commit/972bd96fe83954e322708f499e97c0bcc03bf31a))
- **chatbox:** cb-1040: fix width ([a7e93e3](https://github_resola/resola-ai/deca-apps/commit/a7e93e364b7d7a19fe5bfafef9527da237dcfc88))
- **chatbox:** cb-1088: improve button response ([5bc7ebe](https://github_resola/resola-ai/deca-apps/commit/5bc7ebe4d5ed779cf2d7d7a6df652539ee174038))
- **chatbox:** cb-1101: change const key name ([10dd2ea](https://github_resola/resola-ai/deca-apps/commit/10dd2eabc8e9563abbb127dcc21519984d76e508))
- **chatbox:** cb-1101: remove unnecessary keys ([78c598e](https://github_resola/resola-ai/deca-apps/commit/78c598e51d457fa04fdb5c8186a5eaa718a701b2))
- **chatbox:** cb-1101: remove unsed code ([96e6841](https://github_resola/resola-ai/deca-apps/commit/96e684198666adad7f2a8fdda082a9ec711b0fae))
- **chatbox:** cb-1101: throw error if missing required fields ([dd18752](https://github_resola/resola-ai/deca-apps/commit/dd18752aea9617ca917b8567de650acccd73ad08))
- **chatbox:** cb-1101: throw error in client page if missing fields ([b7cd028](https://github_resola/resola-ai/deca-apps/commit/b7cd028249dccdf6a57fb02a1f24896b7418ffb7))
- **chatbox:** cb-1101: use const ([8e37a42](https://github_resola/resola-ai/deca-apps/commit/8e37a4293d278ab81ac394d1cb2c6ef136c3dc61))
- **shared:** CB-1091 - app navigator should open in the same tab ([df6b488](https://github_resola/resola-ai/deca-apps/commit/df6b48857c6cb7d682545d2992ea673bf3cfc507))
- **shared:** CB-837 - Feature/cb-837 auto create sync pr ([#811](https://github_resola/resola-ai/deca-apps/issues/811)) ([1749542](https://github_resola/resola-ai/deca-apps/commit/1749542e71b7b3d325eb6383224d6715ecec72f4))
- **shared:** CB-837 - Improve auto create sync PR ([3d73222](https://github_resola/resola-ai/deca-apps/commit/3d73222a88986f954b5f82e74f6453c824a16258))
- **shared:** CB-846 - Setup Merged Workflow to resolve Github Webhook limit for Amplify App ([433de74](https://github_resola/resola-ai/deca-apps/commit/433de74112e0f96a778215453196a24e0e70377e))

### Bug Fixes

- **chatbox:** cb-1045: fix zod schema ([df80ff3](https://github_resola/resola-ai/deca-apps/commit/df80ff31c9b961415405c7c5d78b154f249ce209))
- **chatbox:** cb-1045: remove duplicated function ([499d172](https://github_resola/resola-ai/deca-apps/commit/499d1721855f707bec0ba368706b82f2a140bbc7))

### [0.2.7](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.7) (2024-04-01)

### Features

- **chatbox:** cb-604: prevent re-render when hover on launch ([807daa3](https://github_resola/resola-ai/deca-apps/commit/807daa3a16534886b4c0ec426abe4dab0f419ca0))
- **chatbox:** cb-984: fix issue on chatheader ([7cc29d2](https://github_resola/resola-ai/deca-apps/commit/7cc29d230414d97faa698ee4aa38373b32f23754))
- **chatbox:** cb-985: up fontsize to 14 ([ed5473a](https://github_resola/resola-ai/deca-apps/commit/ed5473a32347c6dcf29d81b2619b013b035c7393))
- **chatbox:** chore: remove history hashing ([ed5473a](https://github_resola/resola-ai/deca-apps/commit/2a760606a6628f2da93e344d18ead0f36d9c811b))

### [0.2.6](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.6) (2024-03-28)

### Features

- **chatbox:** [Chatbox Client] Generate new userId for chatbox admin (after refresh the page) ([84e96d6](https://github_resola/resola-ai/deca-apps/commit/84e96d687664711f6ef74ad457de7cbd144980b1))
- **chatbox:** CB-969 - [Chatbox Client] Generate new userId for chatbox admin (after refresh the page) ([20406c5](https://github_resola/resola-ai/deca-apps/commit/20406c53d796bf25f86906d5f68d9e2c632ee195))
- **chatbox:** cb-972: fix behavior of button url ([b958d08](https://github_resola/resola-ai/deca-apps/commit/b958d08c87df491405d58f4150deddff47ebc5b4))
- **chatbox:** cb-972: fix payload url ([8c1ac61](https://github_resola/resola-ai/deca-apps/commit/8c1ac6193b1ac222fff0f8e3da85872f64bc2d89))
- **chatbox:** cb-972: fix url ([ae02d1e](https://github_resola/resola-ai/deca-apps/commit/ae02d1e794ba69c20dd0428c395167e7c4238204))
- **chatbox:** CB-976 - [Chatbox Client] For chatbox admin, add prefix `demo` before userId ([e983854](https://github_resola/resola-ai/deca-apps/commit/e9838540cc7560044a41d5d8fa768250a4519fe9))
- **chatbox:** CB-981 - [Chatbox Client] refactor the ApiService, move some logics to AppService and hook util ([479dd16](https://github_resola/resola-ai/deca-apps/commit/479dd16788dc082328c37062aeeb310c5b149b8b))
- **chatbox:** CB-982 - [Chatbox Client] History in FE should be saved by userId ([98cb782](https://github_resola/resola-ai/deca-apps/commit/98cb78214f33eae6a10e97a57b39a3b47c759650))
- **chatbox:** CB-983 - [Chatbox Client] Improve UI issue in prod - overwidth ([e915df7](https://github_resola/resola-ai/deca-apps/commit/e915df7bf9b043f8f5363fa6a8e24dc8c57c968c))
- **chatbox:** CB-983 - [Chatbox Client] Improve UI issue in prod - overwidth ([0e74e4a](https://github_resola/resola-ai/deca-apps/commit/0e74e4a8ee2bf5ed67fde81c0b2538c26905f43d))
- **chatbox:** CB-983 - [Chatbox Client] Improve UI issue in prod - overwidth ([4072966](https://github_resola/resola-ai/deca-apps/commit/4072966d00982bc338c6d3de3b76424869384731))
- **chatbox:** CB-983 - [Chatbox Client] Improve UI issue in prod - overwidth ([e153227](https://github_resola/resola-ai/deca-apps/commit/e153227e60375e92c21af2167543b5b211fb787d))
- **chatbox:** CB-983 - [Chatbox Client] Improve UI issue in prod - overwidth ([1bce794](https://github_resola/resola-ai/deca-apps/commit/1bce79454d9baf0deda33b20b4dc15aba46bc9dc))
- **chatbox:** CB-983 - [Chatbox Client] Improve UI issue in prod - overwidth ([83447b7](https://github_resola/resola-ai/deca-apps/commit/83447b7c3f572c9d6e68352b5b936adb1a039e56))
- **chatbox:** Remove unused styling ([2b77d2f](https://github_resola/resola-ai/deca-apps/commit/2b77d2f1684889f3358b9ef6b3155945344a2bcc))
- **shared:** CB-979 - [General] Improve vscode performance in deca-apps ([8fb6eef](https://github_resola/resola-ai/deca-apps/commit/8fb6eefa895677d58d9dd5ba80347fc70462e82d))

### [0.2.5](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.5) (2024-03-27)

### Features

- **chatbox:** CB-965 - [Chatbox Client] The embed script should not affect the origin styling of the portal page (like font, color, etc.) ([b0fc84d](https://github_resola/resola-ai/deca-apps/commit/b0fc84d01206b30b0d2824cfeddbaf7aff11c262))
- **chatbox:** CB-965 - [Chatbox Client] The embed script should not affect the origin styling of the portal page (like font, color, etc.) ([510df2f](https://github_resola/resola-ai/deca-apps/commit/510df2f7ec0627c6e4c6ac0b2cb7b44ee00c1a33))

### [0.2.4](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.4) (2024-03-27)

### Features

- **chatbox:** CB - 945 - replace button to custom button ([dda321c](https://github_resola/resola-ai/deca-apps/commit/dda321c3228d4b79de755c6ae75f7d80824ad684))
- **chatbox:** CB-910 - [Chatbox Client] Rename **CHATBOT_CLIENT** into **DECA_CLIENT** ([8d3b6a3](https://github_resola/resola-ai/deca-apps/commit/8d3b6a39d88671c8cce3f9c70deb8b6745018106))
- **chatbox:** CB-910 - [Chatbox Client] Rename **CHATBOT_CLIENT** into **DECA_CLIENT** ([592f4e1](https://github_resola/resola-ai/deca-apps/commit/592f4e1f9fe4b21e8dba4a45d712c38bb05db0c4))
- **chatbox:** cb-933: fix name ([2ad0b15](https://github_resola/resola-ai/deca-apps/commit/2ad0b150a29b13f2dba3283715bccd1c5dc1b22a))
- **chatbox:** cb-933: hide loading after connect livechat ([630514c](https://github_resola/resola-ai/deca-apps/commit/630514c4fce61016ca1d55ca10d4c51aa95104a5))
- **chatbox:** cb-943: add memo ([8fde36f](https://github_resola/resola-ai/deca-apps/commit/8fde36f8775cc4cfc7072c61702ba98812cfee41))
- **chatbox:** cb-943: move userAgent to context ([619839a](https://github_resola/resola-ai/deca-apps/commit/619839abbce9c48ecb5224381642e71a9d36a09c))
- **chatbox:** CB-949 - [Chatbox Client] Save messages data to localStorage by botId ([2e714e6](https://github_resola/resola-ai/deca-apps/commit/2e714e6cc550949951671076a3c0e6c9c8eaef47))
- **chatbox:** CB-953 - [Chatbox Client] Improve embed script ([c5e7ced](https://github_resola/resola-ai/deca-apps/commit/c5e7ced23f048d4b2354ce7e0b50bdffb833f57e))
- **chatbox:** CB-953 - [Chatbox Client] Improve embed script ([08ddb70](https://github_resola/resola-ai/deca-apps/commit/08ddb70a2a8bd2e63297c94658f202a2aac12478))
- **chatbox:** CB-953 - [Chatbox Client] Improve embed script ([ff9aee6](https://github_resola/resola-ai/deca-apps/commit/ff9aee6652c746d59356331077269cd1ee194463))
- **chatbox:** Fix height when switching display ([d229d2b](https://github_resola/resola-ai/deca-apps/commit/d229d2b920653ecf063d02d53bc557b31386c3cf))
- **shared:** CB - 945 - Update Button Scheme ([973a22d](https://github_resola/resola-ai/deca-apps/commit/973a22da01a839e5ddd5c510712cc80fe2aa7ed7))
- **shared:** CB - 945 - Update Button Scheme ([4500027](https://github_resola/resola-ai/deca-apps/commit/4500027fab0b3dd524d093c79ffd107b94e27de4))

### [0.2.3](https://github_resola/resola-ai/deca-apps/compare/<EMAIL>@0.2.3) (2024-03-26)

### Features

- **chatbox:** CB-916 - [Chatbot Flow] Improve KB Node UI issues ([a91dbc5](https://github_resola/resola-ai/deca-apps/commit/a91dbc55ab48784cbf5cc3ed3481cddf8e214491))
- **chatbox:** CB-921 - [Chatbox Client] Save history in Frontend and load history in the Frontend ([32d8366](https://github_resola/resola-ai/deca-apps/commit/32d8366f24415d360834ad3ead66e11e6e13e847))
- **chatbox:** cb-926: disabled message input if message type are form/buttons ([016ed08](https://github_resola/resola-ai/deca-apps/commit/016ed08eee5a0829306471e6c23548d9ce8b9d87))
- **chatbox:** cb-927: add user agent to payload ([d875d62](https://github_resola/resola-ai/deca-apps/commit/d875d620729e600d42b73640bc13efef9eedb77e))
- **chatbox:** cb-927: fix user agent utils ([0d26952](https://github_resola/resola-ai/deca-apps/commit/0d26952da251b05b07563045aae51f211278bda8))
- **chatbox:** cb-941: update event integration livechat ([a0ed214](https://github_resola/resola-ai/deca-apps/commit/a0ed21464e39a91b5d1b7f65659f734d239744f4))

### Bug Fixes

- **chatbox:** cb-923: fix the headset icon ([64e65ac](https://github_resola/resola-ai/deca-apps/commit/64e65ac4194e2a22f5980d0f6faa788d83d6df6a))
- **chatbox:** cb-937: fix zIndex ([1c7ac35](https://github_resola/resola-ai/deca-apps/commit/1c7ac35c265ae2a2d1372610588923594773edd4))
- **chatbox:** cb-938: fix enable message input after click button ([dbc4c8d](https://github_resola/resola-ai/deca-apps/commit/dbc4c8d25507a2b850a8bf7b39fa027b56c1e065))

### [0.2.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.2) (2024-03-24)

c292917bad00fc0be4b8bb28510ea5b49c533287 - hotfix(chatbox): CB-919 - [Chatbox Client] Should not encode button text ([c292917](https://github.com/resola-ai/deca-apps/commit/c292917bad00fc0be4b8bb28510ea5b49c533287))
2925d83010966a5dab53ffa1f8ba08ab47e3ae63 - hotfix(chatbox): CB-918 - [Chatbox Client] Messages should not be overlap with input text ([2925d83](https://github.com/resola-ai/deca-apps/commit/2925d83010966a5dab53ffa1f8ba08ab47e3ae63)

### [0.2.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.1) (2024-03-23)

### Features

- **chatbox:** CB-915 - [Chatbox] Still showing the Chatbox Client UI in Admin even if there is no botId, orgid or Integration ([73107dd](https://github.com/resola-ai/deca-apps/commit/73107dd3e971bc0fb85dbd7f0a48781d4b21262a))

## [0.2.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.0) (2024-03-22)

### Features

- **chatbox:** cb-788: create form response template ([94b5ec7](https://github.com/resola-ai/deca-apps/commit/94b5ec7914232d2c3223a1a6ab5cf94b936bbaf0))
- **chatbox:** cb-788: disable message input when message type if form ([3db7d27](https://github.com/resola-ai/deca-apps/commit/3db7d273545e501ef397444e8e38e83afc8c2f0f))
- **chatbox:** cb-788: disabled input if type of message is form ([fd4ed16](https://github.com/resola-ai/deca-apps/commit/fd4ed169e066c7d3bf209bc78424d8e09c527777))
- **chatbox:** cb-788: fix css message ([67bdfbb](https://github.com/resola-ai/deca-apps/commit/67bdfbbee006addf5e617878eaf4333195250c47))
- **chatbox:** cb-788: handle submit form, styling form ([d85ba3b](https://github.com/resola-ai/deca-apps/commit/d85ba3b82f95e7ce079874cb97d56cd3dd113ed5))
- **chatbox:** cb-788: hide form after submitting ([fa844eb](https://github.com/resola-ai/deca-apps/commit/fa844eb1a4fc2d12826f523bd15292d45ba48d68))
- **chatbox:** cb-788: remove form item template ([7e51660](https://github.com/resola-ai/deca-apps/commit/7e51660c560794ed15703118ed8eb6033dc664bc))
- **chatbox:** cb-788: remove variable field, split input control to be smaller ([19f4d63](https://github.com/resola-ai/deca-apps/commit/19f4d63c4fb20664bfe421b39bd9eece996e26ea))
- **chatbox:** cb-788: render form based on props ([32672b1](https://github.com/resola-ai/deca-apps/commit/32672b199fae59e7a200332642b70d1c18a1bd7d))
- **chatbox:** cb-788: set language to error message ([bc365cd](https://github.com/resola-ai/deca-apps/commit/bc365cda61deb18f140c383101bf8f1f80c4a87e))
- **chatbox:** cb-788: update form response ([30c9705](https://github.com/resola-ai/deca-apps/commit/30c9705f658c072811d0716777e4b2bcf316739d))
- **chatbox:** cb-788: update languages ([18c2bb6](https://github.com/resola-ai/deca-apps/commit/18c2bb616db4e0798b56edd1edc76cabd29602c0))
- **chatbox:** CB-795 - [Chatbox Client] [Gakuei] Implement Interface to pass custom param to Chatbox Client ([1d12740](https://github.com/resola-ai/deca-apps/commit/1d12740a64294a20c6f76b144889e41c80b0c43f))
- **chatbox:** CB-901 - [Chatbox Client] The chatbox client should be always on top (zIndex should be high) ([7935102](https://github.com/resola-ai/deca-apps/commit/7935102eace1f05325aa98a78acd1d8e4f099a58))
- **chatbox:** cb-908: update behavior when connecting livechat ([75c1877](https://github.com/resola-ai/deca-apps/commit/75c187734352593b0fbc3362dfb7fa14135c40dc))
- **chatbox:** CB-909 - [Chatbox Client] Integrate to create hook url from configUrl Json file ([b2ed9ae](https://github.com/resola-ai/deca-apps/commit/b2ed9aecbd270bc25db237489f42988c8abe3f6f))

### [0.1.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.1.1) (2024-03-21)

### Features

- **chatbox:** CB-398 Chatbox integration with API ([b62a694](https://github.com/resola-ai/deca-apps/commit/b62a694ef9943ad83a2c002d7c27d65156923bc5))
- **chatbox:** CB-398 enhance chatbox status, livechat always on ([e7697c5](https://github.com/resola-ai/deca-apps/commit/e7697c537c5ce10d6aa5bd215e55bd282b2485a2))
- **chatbox:** CB-608 added the pbpm-lock.yaml after syncing from chatbot/develop ([d719e74](https://github.com/resola-ai/deca-apps/commit/d719e74e77977a4d50532e11f2f4c5b304673294))
- **chatbox:** cb-770: implement chat header ([7ebfef7](https://github.com/resola-ai/deca-apps/commit/7ebfef7eb580b215e3095cc2b58a846a97191018))
- **chatbox:** cb-770: update chatbot header ([c052c9b](https://github.com/resola-ai/deca-apps/commit/c052c9b79302ff770f744dd29209a23a03327a57))
- **chatbox:** cb-770: update color ([aa37caf](https://github.com/resola-ai/deca-apps/commit/aa37caf9116c4504e72ab48fa902e487307723ad))
- **chatbox:** cb-770: update data ([cf0573c](https://github.com/resola-ai/deca-apps/commit/cf0573c86670303ad2ffc26b91c3700a5d960423))
- **chatbox:** cb-776: change remove to cancel in widget detail ([c42422b](https://github.com/resola-ai/deca-apps/commit/c42422bd4baa22458abd54fe3506cf93163d3e84))
- **chatbox:** cb-776: remove unused code ([bb5b106](https://github.com/resola-ai/deca-apps/commit/bb5b10603940440829d0e20c34b902ec58407c0f))
- **chatbox:** cb-777: handle remove widget and format files ([5ee55a8](https://github.com/resola-ai/deca-apps/commit/5ee55a8dbbcdc3b75bf1edd202c9d2f4e6dcafb4))
- **chatbox:** cb-778: no add loader if connect livechat ([4090c3d](https://github.com/resola-ai/deca-apps/commit/4090c3d1f3dd2487c2a54871d6d6338dc15e7d7e))
- **chatbox:** cb-778: no render event in message list ([be62305](https://github.com/resola-ai/deca-apps/commit/be62305f1553e1751c4f785479e939f88bb1a148))
- **chatbox:** cb-778: remove loader after 3s ([517a1b6](https://github.com/resola-ai/deca-apps/commit/517a1b6692537202788e20c3599fe3df5d6d3599))
- **chatbox:** cb-778: use const ([5c09e01](https://github.com/resola-ai/deca-apps/commit/5c09e01dde79f37c3449436409f4c1ac1c3f1010))
- **chatbox:** cb-779: no editting when toggle is off ([91fe145](https://github.com/resola-ai/deca-apps/commit/91fe1451ef334d07e3d44de4e76577a2b0975083))
- **chatbox:** cb-783: fix ui issue ([c45c328](https://github.com/resola-ai/deca-apps/commit/c45c328102382492ae50df17957a6e24e41b2730))
- **chatbox:** CB-787 - Improve chatbox logo ([9423580](https://github.com/resola-ai/deca-apps/commit/942358032d4118de40abce05b6c2185de6ebf210))
- **chatbox:** CB-787 - Improve chatbox logo ([d410734](https://github.com/resola-ai/deca-apps/commit/d4107348d611ef5e9e23472520d7113a971e6aec))
- **shared:** CB - 785 - Move ImageImageDropzone to package sharing ([d645d9d](https://github.com/resola-ai/deca-apps/commit/d645d9df795072ffffb8af2e00993308b3425306))
- **shared:** CB - 785 - Move ImageImageDropzone to package sharing ([699261e](https://github.com/resola-ai/deca-apps/commit/699261e8620d39cdda76ca672ae57fc1c70fad1f))
- **shared:** CB - 785 - Move ImageImageDropzone to package sharing ([b0ba974](https://github.com/resola-ai/deca-apps/commit/b0ba97476b1304389889e3c72996f3148a57e2d8))
- **shared:** CB - 834 - Improve Image dropzone uploading ([3613ba9](https://github.com/resola-ai/deca-apps/commit/3613ba9247813836eb3ba9b82a534bb43b5dc6e7))
- **shared:** CB - 834 - Improve Image dropzone uploading ([6f76a94](https://github.com/resola-ai/deca-apps/commit/6f76a949f4378f206a3039a86f75b3dd5468ed1e))
- **shared:** CB - 834 - Improve Image dropzone uploading ([63a4b0e](https://github.com/resola-ai/deca-apps/commit/63a4b0e7152487d1bbe1639eb1ad7a8b5f2c5913))
- **shared:** CB -785 - downgrade mantine dropzone lib ([445b7f4](https://github.com/resola-ai/deca-apps/commit/445b7f4d35bcd66507c554720b3c2aac66ed092f))
- **shared:** CB -785 - downgrade mantine dropzone lib ([bb58d5d](https://github.com/resola-ai/deca-apps/commit/bb58d5da8c7dfecbaec6eec2b789bf8d7d6a7b60))
- **shared:** CB -785 - downgrade mantine dropzone lib ([cbfbd42](https://github.com/resola-ai/deca-apps/commit/cbfbd4233b2fc1afbf22aab5dade0b72ea87a0b9))
- **shared:** CB -785 - Remove unused codes ([518dfa7](https://github.com/resola-ai/deca-apps/commit/518dfa7381b90d5627cfe1138578b28a87bb369a))
- **shared:** CB -785 - Remove unused codes ([10f0957](https://github.com/resola-ai/deca-apps/commit/10f09576907f9b8b1f24323385cb514a54c7a481))
- **shared:** CB -785 - Remove unused codes ([a5d855b](https://github.com/resola-ai/deca-apps/commit/a5d855ba2388638302791024ef80408b0fab74a5))
- **shared:** CB-872 - Set timeout 5 minutes for all workflow in Github Action ([ef6ced4](https://github.com/resola-ai/deca-apps/commit/ef6ced476b3b554bf9f76ebdde8b06e02923004d))

### Bug Fixes

- **chatbox:** add integration route back ([21b520a](https://github.com/resola-ai/deca-apps/commit/21b520a5ea911a3fa3a364661479fec0fe6c1c65))
- **chatbox:** CB-398 restructure integration API based on BE update, add i18n ([79fd6da](https://github.com/resola-ai/deca-apps/commit/79fd6daa4c2cf9921b4be2f139c11ea225572c77))
- **chatbox:** cb-766: fix hook url ([5787523](https://github.com/resola-ai/deca-apps/commit/57875239c930a2e6670569b9c7c32d99941587e1))
- **chatbox:** cb-778: fix issue render ([203d604](https://github.com/resola-ai/deca-apps/commit/203d6048a0519f84923bdebf26f719f5a46d02e3))
- **chatbox:** CF-398 add error msg and loading state ([0bef127](https://github.com/resola-ai/deca-apps/commit/0bef1273110886e5e30f3ca2baef9097dcd7e469))

## 0.1.0 (2024-03-12)

### Features

- add pr agent ([7fa2295](https://github.com/resola-ai/deca-apps/commit/7fa2295a1584e6db3562c88e0985e0845fbba729))
- cb-215: create files structure for chatbot ([f7f678f](https://github.com/resola-ai/deca-apps/commit/f7f678f0b5e868cc1030daf7b93a89075151599b))
- cb-215: setup husky, lint checking ([88b7ba7](https://github.com/resola-ai/deca-apps/commit/88b7ba7a447d55ee70aca9ecb9d0367fd5bf83d0))
- cb-224 - init unit test for chatbot ([840b2f9](https://github.com/resola-ai/deca-apps/commit/840b2f95721a42380c8c90f7a83386a52bf6ef81))
- cb-224 - init unit test for chatbot ([36c4541](https://github.com/resola-ai/deca-apps/commit/36c4541d69133a8a435ca567e1a97229e3e7597f))
- cb-224 - init unit test for chatbox ([072c533](https://github.com/resola-ai/deca-apps/commit/072c53353c5bff574c58c2197154bb2257ac7386))
- cb-225 - add check branch name into CI ([3797256](https://github.com/resola-ai/deca-apps/commit/37972569d5e19e7f376417bc367d05c7a8124e2b))
- cb-225 - cb-226 - add check branch into commit-msg hook ([d46781c](https://github.com/resola-ai/deca-apps/commit/d46781c48fdbe4852ccdf0e2c56ffd77871d988a))
- cb-225 - cb-226 - add commit message convention and branch name convention ([8010cf9](https://github.com/resola-ai/deca-apps/commit/8010cf93e8bb3e85d2dbdc4dc2354943551d8f2a))
- cb-225 - cb-226 - grant permission for commit-msg hook and scripts file ([4c8ec58](https://github.com/resola-ai/deca-apps/commit/4c8ec581315b11d5346b0af6cbedd6409222f770))
- CB-225 - CB-226 - update check-commit-message.js and check-branch-name.js ([00a01ba](https://github.com/resola-ai/deca-apps/commit/00a01babc96fac4bb5b61dd1a1ae9232c1bf3c3a))
- CB-225 - CB-226 - update check-commit-message.js and check-branch-name.js ([f37429a](https://github.com/resola-ai/deca-apps/commit/f37429a404e96893b30b90c7b71f99e331e7f0b1))
- CB-225 - CB-226 - update commit-msg hook to use javascript file to check for branch and commit message convention ([bba7088](https://github.com/resola-ai/deca-apps/commit/bba7088785276d404b233fb1b923ab11f5465a12))
- CB-225 - put ci logic into javascript file to check branch name ([45f3878](https://github.com/resola-ai/deca-apps/commit/45f38780b78a9d620ec6dfbac64e0c3906668669))
- cb-225 - update check branch name ci ([d36ea41](https://github.com/resola-ai/deca-apps/commit/d36ea4193c3e6f2aee821ee940ccb3ff5b1478ea))
- cb-225 - update check branch name yml ([6dadcf9](https://github.com/resola-ai/deca-apps/commit/6dadcf925e12c2a01440315580237791e4e88fdc))
- cb-225 - update check branch name yml ([ce8bba1](https://github.com/resola-ai/deca-apps/commit/ce8bba199bd186841348a853d62ba0daf264cf01))
- CB-225 - update check-branch-name.js ([600686c](https://github.com/resola-ai/deca-apps/commit/600686cf2d74049e07e37f4d303e475935994079))
- CB-225 - update check-branch-name.js ([e8d1e29](https://github.com/resola-ai/deca-apps/commit/e8d1e2993da20630eaab0f75221d590898d0b4ca))
- CB-225 - update check-branch-name.js ([b6635d0](https://github.com/resola-ai/deca-apps/commit/b6635d0ec7d987e75075bc6d63739e75246c9195))
- CB-225 - update checkbranch.sh ([01e8829](https://github.com/resola-ai/deca-apps/commit/01e88295dcdc926f0c9ccd46c932a5701b2ac9df))
- CB-226 - add check commit message into ci ([61e8867](https://github.com/resola-ai/deca-apps/commit/61e8867bc6f8c193c01cbb3d0488766a1d5d354e))
- CB-226 - update check commit message ci ([5f5a17c](https://github.com/resola-ai/deca-apps/commit/5f5a17cf5ceacc45b2ab5949a1389022d7f95fa2))
- CB-226 - update check commit message ci ([74d6e34](https://github.com/resola-ai/deca-apps/commit/74d6e34545438ca5ca6bc539b3797d87e0a96d51))
- CB-226 - update check commit message ci ([772cd21](https://github.com/resola-ai/deca-apps/commit/772cd210c1d9b9948aef3652359ff78dc5b1d593))
- CB-226 - update check commit message ci ([10925ce](https://github.com/resola-ai/deca-apps/commit/10925ce39c9b1911e9ae8c4ae6f34435c35b8c5c))
- CB-226 - update check commit message ci ([e320444](https://github.com/resola-ai/deca-apps/commit/e320444f9da8c1edb3347051b749214d8ec89793))
- CB-226 - update checkmessage.sh ([cbdf3f8](https://github.com/resola-ai/deca-apps/commit/cbdf3f8712bd6db9e3fbc6b7c61c45d6d5053245))
- cb-227 - add test to turbo ([5b0f2a6](https://github.com/resola-ai/deca-apps/commit/5b0f2a609adeb636d8b8d0cb57ce28b938704d1b))
- cb-227 - CB-227 - Run testing and building before push for deca-apps ([cda7cfa](https://github.com/resola-ai/deca-apps/commit/cda7cfa84595dd7f6cabf551d568cf2c3634b990))
- cb-227 - grant pre-push permission ([f9ebeb0](https://github.com/resola-ai/deca-apps/commit/f9ebeb0a611de40325a14c98f64ebabcb41e521c))
- cb-227 - improve pre-commit command ([dd2c294](https://github.com/resola-ai/deca-apps/commit/dd2c294a9998b1e09b00ffcd8cf7a063a187f2c3))
- cb-228 - update pr agent workflow ([7fcbe76](https://github.com/resola-ai/deca-apps/commit/7fcbe7648990ed25159c4cea4364b7d43a79b861))
- CB-229 - Allow PR Agent to auto write PR description for deca-apps ([65d2ff7](https://github.com/resola-ai/deca-apps/commit/65d2ff7e25b55cce5f85dd9792e98d5715b5c9bc))
- CB-229 - Allow PR Agent to auto write PR description for deca-apps ([28876dd](https://github.com/resola-ai/deca-apps/commit/28876dd6864d53c7f1f706fdfbf59d2790a1992e))
- CB-229 - Allow PR Agent to auto write PR description for deca-apps ([d285685](https://github.com/resola-ai/deca-apps/commit/d285685431b78a6a3d6defb89fdcc92b29e9ad89))
- CB-229 - Allow PR Agent to auto write PR description for deca-apps ([c3db6cd](https://github.com/resola-ai/deca-apps/commit/c3db6cde179afb11545b71f893a8d3df212dcae3))
- CB-232 - Add Github Flow as CI to run unit tests and run build for deca-apps ([3f54c98](https://github.com/resola-ai/deca-apps/commit/3f54c9882f57a6da5a8ec45edd67d5058c4df92f))
- CB-233 - add test coverage ([e4a4bfd](https://github.com/resola-ai/deca-apps/commit/e4a4bfd831857182a5ef72ea240151f7aa2bbd3d))
- CB-234 - Add script for vitest unit test to run without watching for deca-apps ([ed5a71e](https://github.com/resola-ai/deca-apps/commit/ed5a71ed4488845a61e2f3a43933c907b217756b))
- CB-244 - add chatbox ui layout ([3587a92](https://github.com/resola-ai/deca-apps/commit/3587a92dac626b33a58dd13e6ddc552dbd74c02c))
- CB-244 - add emotion styled for styled component ([9ce389f](https://github.com/resola-ai/deca-apps/commit/9ce389ff810ddf3f0529ef82055e5a7455792e9b))
- cb-289: add color design list ([1530c4d](https://github.com/resola-ai/deca-apps/commit/1530c4de9c1a23cadfb8c2f0b769a05ecb0eaa00))
- cb-289: add header and navbar ui ([374d4a3](https://github.com/resola-ai/deca-apps/commit/374d4a3697a1894f58d6f7c76c576198865f9883))
- cb-289: add internal packages to chatbox ([33f66dc](https://github.com/resola-ai/deca-apps/commit/33f66dc672877ede87858d18c9ebb0e1fd0d5f46))
- cb-289: add router ([8034798](https://github.com/resola-ai/deca-apps/commit/80347983d3ba4b748f676c9ab0fd2f27df856516))
- cb-289: downgrade mantine to v6 ([0baefed](https://github.com/resola-ai/deca-apps/commit/0baefed3523183fbd170390d6acd47a87092d3be))
- cb-289: fix eslint ([2aa6a15](https://github.com/resola-ai/deca-apps/commit/2aa6a157680b85d7df45ce83a3a2d8af8d9e0675))
- cb-289: fix unit test, format files ([c40b3b5](https://github.com/resola-ai/deca-apps/commit/c40b3b56e59be859e8c6a48c9b9eca4567ddf56f))
- cb-289: implement logo header, add configs ([375d79a](https://github.com/resola-ai/deca-apps/commit/375d79a13b4550effa8864f97994575457c211bf))
- cb-289: remove used unit test ([62b2adb](https://github.com/resola-ai/deca-apps/commit/62b2adbf0f2dc0e9956453cefef01881ccd7b383))
- cb-289: remove used unit test file ([71c44dd](https://github.com/resola-ai/deca-apps/commit/71c44dd20e0d3b48ad1dee13ccfb67649d4cf57f))
- cb-289: split components in navbar ([f9d1c1a](https://github.com/resola-ai/deca-apps/commit/f9d1c1a45986a15a7568e96627e4d582e9adff20))
- cb-289: split components in navbar and format ([539473c](https://github.com/resola-ai/deca-apps/commit/539473c77f5fe103cac7e145d69b88a250010e1e))
- cb-289: split navigationIcons from navbar ([bd17312](https://github.com/resola-ai/deca-apps/commit/bd17312771f5d14840c2ff71e35de430c1c4d7e3))
- CB-291 - Chatbox UI preview ([30ed0a3](https://github.com/resola-ai/deca-apps/commit/30ed0a37a3d533c4d34373c50e49774122bea840))
- CB-291 - Chatbox UI preview ([db45197](https://github.com/resola-ai/deca-apps/commit/db45197df6275e140f6e14778403d5a6e02ce966))
- CB-292 - Integrate with json config file ([1d0d7a5](https://github.com/resola-ai/deca-apps/commit/1d0d7a5c01732b8b4251fe714d1ed46038bc9e93))
- CB-293 - Render launcher (custom side spacing, custom bottom spacing, custom position, custom icon url) ([18b9521](https://github.com/resola-ai/deca-apps/commit/18b9521093e452e4bedda753c2b8a5751c2d1e0a))
- CB-294 - Render chatbox container (custom size) ([2a9128b](https://github.com/resola-ai/deca-apps/commit/2a9128b6a6d0f5842b914df3e584d4e73d6bc4ff))
- CB-294 - Render chatbox container (custom size) ([384e42d](https://github.com/resola-ai/deca-apps/commit/384e42d2a247ed145d9c4fd7b8f52ac8c440e4a5))
- CB-294 - Render chatbox container (custom size) ([a78a4fe](https://github.com/resola-ai/deca-apps/commit/a78a4fedc816503e722c9af8b9fb8e17f1428173))
- CB-295 - Render home (custom title, styling, widgets) ([ba829d2](https://github.com/resola-ai/deca-apps/commit/ba829d296072ae95a2790ee05e2d8f946cbb60e6))
- CB-295 - Render home (custom title, styling, widgets) ([11aa52e](https://github.com/resola-ai/deca-apps/commit/11aa52ec0cad5cc9e3b2eafbddb9a1c8ff68ae02))
- CB-296 - Integrate with json config file ([0def611](https://github.com/resola-ai/deca-apps/commit/0def6114003ed5fb750ed6f18e12b3990669acf8))
- CB-297 - improve render background image ([7a0ba8a](https://github.com/resola-ai/deca-apps/commit/7a0ba8adaf2d825958e6d78613348b9445c0cc64))
- CB-297 - Render background color ([32d7eea](https://github.com/resola-ai/deca-apps/commit/32d7eead957113c028733a0de21b92b60823a351))
- CB-297 - Render background color ([8178d0b](https://github.com/resola-ai/deca-apps/commit/8178d0b117e994143610aaebf652d62852ab49d4))
- CB-297 - Render background color - support gradient and image url with fade background to white switch ([8a0482c](https://github.com/resola-ai/deca-apps/commit/8a0482ceb67d5bc643edb816951acb69056462b6))
- CB-298 - Render navigator ([9c81af2](https://github.com/resola-ai/deca-apps/commit/9c81af28c88eab6f42aeb89e21c08db810d48678))
- CB-298 - Render navigator ([5b01522](https://github.com/resola-ai/deca-apps/commit/5b01522a9fc23ed82f8df99f0e97fee2e34d23cd))
- CB-298 - Render navigator ([9c812e4](https://github.com/resola-ai/deca-apps/commit/9c812e408c0aa18feb0dc436b6fc73f951642a85))
- CB-299 - Render brand (action color, bg color) ([9599ace](https://github.com/resola-ai/deca-apps/commit/9599ace17f919be162a3491b5b884d927d23ff8d))
- CB-299 - Render brand (action color, bg color) ([3e9d78c](https://github.com/resola-ai/deca-apps/commit/3e9d78cc8933fde1cdb3f93cb0d68f4d8192adf5))
- cb-300: add language browser detector ([3170b98](https://github.com/resola-ai/deca-apps/commit/3170b989d3d3bbb3ebdbd5b6d36a50ae8f39e039))
- cb-300: add locales files, update i18n file ([ded0590](https://github.com/resola-ai/deca-apps/commit/ded0590c96c2de4331a396290c04ebd65749e390))
- cb-300: change mock bot avatar ([11e05e6](https://github.com/resola-ai/deca-apps/commit/11e05e687cbcdc77e8f885e9bd0ef2fb33127152))
- cb-300: fix typescript error ([ae1386a](https://github.com/resola-ai/deca-apps/commit/ae1386aabd96a7b97f81b2e43de5561eb70ac3e2))
- cb-300: keep query param on url ([5ed59b3](https://github.com/resola-ai/deca-apps/commit/5ed59b34c50fbc419e7eb2abcaaa1e849f62b1ad))
- cb-301: add configurations for auth0 integration ([a0aefe3](https://github.com/resola-ai/deca-apps/commit/a0aefe39ffcea12d1796c49ef334261470d42c31))
- cb-301: integrate auth0 and add axios service ([cf54743](https://github.com/resola-ai/deca-apps/commit/cf54743d6f83c8c39b076904955b1e6e6b041930))
- CB-302 - Trigger chatbox from launcher ([b3654be](https://github.com/resola-ai/deca-apps/commit/b3654bee254136871ede9e6e2cc5894df4944aee))
- CB-303 - Add support for WCAG (auto change text color according to background color) ([7d62e54](https://github.com/resola-ai/deca-apps/commit/7d62e54369a2d8da316dbeb6ea6f972195c3f474))
- CB-306 - Render message page (simple) (custom background) ([67a39a1](https://github.com/resola-ai/deca-apps/commit/67a39a198ab3c58fc7bce1ff3e13904c5f9b2b47))
- CB-309 - add cdn prefix ([57817a7](https://github.com/resola-ai/deca-apps/commit/57817a703fee7d3f5a663c3bb696427ce99a4e80))
- CB-310 - Add close button ([99a90ca](https://github.com/resola-ai/deca-apps/commit/99a90ca3f034125d973dc43d614e280b6cb47123))
- cb-311: refactor code for router ([6a70256](https://github.com/resola-ai/deca-apps/commit/6a70256e9dd7586981c70806282aa52ccc393f4d))
- cb-313: fix lint error ([9962911](https://github.com/resola-ai/deca-apps/commit/996291137616ce7a2b47f839ccc64c0602994bc0))
- cb-313: format code ([15a8094](https://github.com/resola-ai/deca-apps/commit/15a809410c8183aaebd588f85e42513be7e69325))
- cb-313: init packages, some configs for chatbot app ([21e3fd4](https://github.com/resola-ai/deca-apps/commit/21e3fd4d262eeecbe5beac2813ffcb57ee2ff01e))
- cb-313: setup router, auth0, configurations app, i18n setting, custom hooks for chatbot management ([43dc0a1](https://github.com/resola-ai/deca-apps/commit/43dc0a1d7d0a0332f50f3430bb4c1f4edca680cd))
- cb-319: add hook to custom url with organization name ([ce039e7](https://github.com/resola-ai/deca-apps/commit/ce039e785f7610897f279bec6e034d1aece96745))
- **CB-322:** Create Chatbox dashboard page (home page) ([#78](https://github.com/resola-ai/deca-apps/issues/78)) ([2f35969](https://github.com/resola-ai/deca-apps/commit/2f3596920f3d9cbb4f13d11727dbe7db994cf6f8))
- cb-333: apply font-family ([d5e4ce9](https://github.com/resola-ai/deca-apps/commit/d5e4ce9723c6e1a619d2ef09a9fa52edfbf78252))
- CB-338 - [widget-engine-app] update tools to help easier to design template on widget engine app ([eb1c3d3](https://github.com/resola-ai/deca-apps/commit/eb1c3d39287a54248f575b2f2e42aec8bb99aaba))
- CB-340 - Fix chatbox window size not working ([23ddb62](https://github.com/resola-ai/deca-apps/commit/23ddb62acfb82700b248314e2933203a9a94668e))
- CB-362 - [Chatbox UI] Improve navigator behavior in chatbox UI when change chatbox admin setting ([29172bb](https://github.com/resola-ai/deca-apps/commit/29172bbe74ea0727bcdbce7e1c3fdd174f743da4))
- CB-365 - temperarily fix image uploading ([8330e53](https://github.com/resola-ai/deca-apps/commit/8330e533280cb4f32a59768c30ea13d059d97b15))
- cb-383 - improve widget template ([9839a20](https://github.com/resola-ai/deca-apps/commit/9839a2091ca80660aacfbef4691003cb22e894b8))
- CB-407 - [Chatbox Widget] Add Call To Action Button Template ([0fbd8aa](https://github.com/resola-ai/deca-apps/commit/0fbd8aa6acb8e5093a915041a2f5150d1d405576))
- cb-407 - update type name for call to action into button ([9273b46](https://github.com/resola-ai/deca-apps/commit/9273b46b0b8671cace588f7d5821687b733eb546))
- cb-407 - update type name for call to action into button ([663943b](https://github.com/resola-ai/deca-apps/commit/663943b41cf12dca95722f131fa8870bfa9e6c5f))
- CB-409 - [Chatbox Widget] Small Image Link Template ([2c9b09f](https://github.com/resola-ai/deca-apps/commit/2c9b09ffd17944c776225b719471572a39c6cbc2))
- CB-409 - [Chatbox Widget] Small Image Link Template ([8197e89](https://github.com/resola-ai/deca-apps/commit/8197e89e31cde4e19cdb08e6e0214c34197e1ea6))
- cb-411 - [Chatbox Widget] Large Image Link Template ([d584b97](https://github.com/resola-ai/deca-apps/commit/d584b974ee00a411909f56b08245668754721cee))
- **CB-465:** Implement the Knowledge Base List page ([970196a](https://github.com/resola-ai/deca-apps/commit/970196aa15ce0439d299df86d5bde8c01a4d8acb))
- **CB-495:** Implement Create KB Modal component and handle pagination from KB Grid ([0f04c83](https://github.com/resola-ai/deca-apps/commit/0f04c836905481fe47365905df234316998320af))
- **CB-495:** Remove duplicated component and update translation ([a11cbb2](https://github.com/resola-ai/deca-apps/commit/a11cbb2e5f334f223401aa03c4c2cc90a2a23b45))
- **CB-508:** Implement the empty state for KB List page ([20293b2](https://github.com/resola-ai/deca-apps/commit/20293b2905f400178d30c9c85fa1a6c9627895f4))
- **CB-519:** Correct the Editor and EditorLabels type and integrate the Dropcursor extension to Editor ([f09a50f](https://github.com/resola-ai/deca-apps/commit/f09a50fcea202b667c3eac14a6aace2fec4e70d7))
- **CB-519:** Define colors constant for package UI and handle Labels prop for Editor ([1045ac9](https://github.com/resola-ai/deca-apps/commit/1045ac976bb4079f16207046b8ed97973310a40f))
- **CB-519:** Implement RichTextEditor and move RoundedButton to common component ([df2bbe6](https://github.com/resola-ai/deca-apps/commit/df2bbe64c23c9ec619c1e59625bca08aea8b1df2))
- **chatbot:** Fix change to home when displaying double ([1a44477](https://github.com/resola-ai/deca-apps/commit/1a444773af3c872b71f1cd38216f5aacdc788b3a))
- **chatbox:** add sentry react lib ([25b77a6](https://github.com/resola-ai/deca-apps/commit/25b77a67d0fbd0ae48e0adb8bb6fc053bce256e6))
- **chatbox:** apply i18n for integration page ([76a34b1](https://github.com/resola-ai/deca-apps/commit/76a34b14ce233fd1717401ef80e72846445b90db))
- **chatbox:** CB - 491 - background home as image not work ([27ae7d7](https://github.com/resola-ai/deca-apps/commit/27ae7d7e2111b6877e4db3a25b1b40ccb470ca4a))
- **chatbox:** CB - 510 - update on image uploading ([c218bf4](https://github.com/resola-ai/deca-apps/commit/c218bf414f75354b0d5102b92c63eb6ff0a1a0e3))
- **chatbox:** CB - 512 - Add loading status ([b0ad002](https://github.com/resola-ai/deca-apps/commit/b0ad00287273dd9f54fb0f17e1b09ab1d52ad43b))
- **chatbox:** CB - 522 - Content settings integration ([c13123a](https://github.com/resola-ai/deca-apps/commit/c13123adac41c9d1a6a435feb02077b90eb78a6d))
- **chatbox:** CB - 545 - Update form ([b227699](https://github.com/resola-ai/deca-apps/commit/b22769957247e7eb03c0247b4b761ac3924886ed))
- **chatbox:** CB - 565 - Add installation in general settings ([ff52d6c](https://github.com/resola-ai/deca-apps/commit/ff52d6c1ed2f3e75c15a7def121e82ae9264a059))
- **chatbox:** CB - 578 - Add modal confirm save changes ([bd4f35e](https://github.com/resola-ai/deca-apps/commit/bd4f35e9a5dded695c52aa370485d33d452ae7b1))
- **chatbox:** CB - 582 - Hide brand color, styling home when displaying as only message ([4922037](https://github.com/resola-ai/deca-apps/commit/49220370b4d11d56fadfc6ec01228c6c1d94f7f3))
- **chatbox:** CB - 622 - Add header and input elements to chatbox ([2922d02](https://github.com/resola-ai/deca-apps/commit/2922d02db57a3815bb57dba0298bcfa34c2541e0))
- **chatbox:** CB - 632 - integrate with general settings ([a282d0f](https://github.com/resola-ai/deca-apps/commit/a282d0f1f3af68402139c573822b01c08568523d))
- **chatbox:** CB - 653 -654 - Fix chatbox client message input ([7b87126](https://github.com/resola-ai/deca-apps/commit/7b871261d80aae1da3e8b215d642e40d3793e9a6))
- **chatbox:** CB - 657 - change image styling ([00b5e7f](https://github.com/resola-ai/deca-apps/commit/00b5e7f3d16d4165378d30c49b58ac1287372988))
- **chatbox:** CB - 658 - update css ([478817e](https://github.com/resola-ai/deca-apps/commit/478817e34ae6f3f7892739b12cedb60f8247d7e8))
- **chatbox:** CB - 660 - Sync up colors to chatbox client ([2eb951d](https://github.com/resola-ai/deca-apps/commit/2eb951df0e7c69eea7d94ebfe38bb7022c5f2415))
- **chatbox:** CB - change welcome message to textarea ([99390de](https://github.com/resola-ai/deca-apps/commit/99390def479a4d8e466b537a5256b726ab40c22f))
- **chatbox:** CB -578 - add japanese text ([b0bff35](https://github.com/resola-ai/deca-apps/commit/b0bff353b46e5eb3eb588bbe16bd378aef2e9da9))
- **chatbox:** cb-281: fix error lint ([30b9d42](https://github.com/resola-ai/deca-apps/commit/30b9d42e6407fc641382f9b6154982723b89b292))
- **chatbox:** cb-281: init ws centrifugo for chatbox app ([f4d6365](https://github.com/resola-ai/deca-apps/commit/f4d6365d4c2ec8268849074f6b307ade9465b12e))
- **chatbox:** CB-312 - CB-328 - update text response template for received text and ai generated text ([57cfa1a](https://github.com/resola-ai/deca-apps/commit/57cfa1ad4e11f126ab42d9415198572194abb7e8))
- **chatbox:** CB-312 - improve common response template ([fc6e554](https://github.com/resola-ai/deca-apps/commit/fc6e55491c2f6cb1554bbe5d53e885bb1f1e8a1b))
- **chatbox:** CB-312 - improve response input data ([66d7b9c](https://github.com/resola-ai/deca-apps/commit/66d7b9cbdcb9781284589d28fea1eb09a75c96c1))
- **chatbox:** CB-312 - improve response template with from ([5f75fd3](https://github.com/resola-ai/deca-apps/commit/5f75fd3c026dfd5f563deef2c663c010a2c4088b))
- **chatbox:** CB-312 - map avatar to response data ([9bab41b](https://github.com/resola-ai/deca-apps/commit/9bab41bc25f840d4b3f5fb06a509a89f182ce179))
- **chatbox:** CB-312 - refactor chatbox response ([6d784e8](https://github.com/resola-ai/deca-apps/commit/6d784e8721ef1219611ab6ea6e12590f13bd6b46))
- **chatbox:** CB-323 - Create Integration page ([#84](https://github.com/resola-ai/deca-apps/issues/84)) ([b6f6179](https://github.com/resola-ai/deca-apps/commit/b6f61790c9b451b0a660364c950aac82419274f3))
- **chatbox:** CB-324 - General setting page ([#99](https://github.com/resola-ai/deca-apps/issues/99)) ([c934878](https://github.com/resola-ai/deca-apps/commit/c9348787f9e8e7c2985f92a6af6544a188d983fc))
- **chatbox:** CB-326 - Integrate widget engine with chatbox ([73c2670](https://github.com/resola-ai/deca-apps/commit/73c2670811565319f584411d22912a47c699cd6b))
- **chatbox:** CB-327 CB-328 CB-329 CB-330 - add template layout, add message chat state, add text template for sent text and received text ([7f25f6c](https://github.com/resola-ai/deca-apps/commit/7f25f6c4ac8e0a7e570f72615f6dd1d77cd70796))
- **chatbox:** CB-329 - Create template for image card ([72c7e47](https://github.com/resola-ai/deca-apps/commit/72c7e47f1cb6f4bb7cb82053c5c08193a3bd321d))
- **chatbox:** CB-329 - improve image to use with http url ([77c5702](https://github.com/resola-ai/deca-apps/commit/77c570295110fd5800faa36991b1450e834e45a6))
- **chatbox:** CB-329 - set default image if not avatar available ([86ab1a4](https://github.com/resola-ai/deca-apps/commit/86ab1a44fe04bf91aff9b4274348d1a166ea931c))
- **chatbox:** CB-330 - add template for card response ([0c94dd5](https://github.com/resola-ai/deca-apps/commit/0c94dd58d8522c538328316fd6a0e00b1056a9c8))
- **chatbox:** CB-330 - Create template for card response ([3c958ad](https://github.com/resola-ai/deca-apps/commit/3c958adf46994600bf75f42104ae6355455e2567))
- **chatbox:** cb-331: create ui for each widgets ([54d2c06](https://github.com/resola-ai/deca-apps/commit/54d2c06b9cee6736af39412750a82ac2b65680cb))
- **chatbox:** cb-331: create widget form edit, basic layout for content setting page ([a986276](https://github.com/resola-ai/deca-apps/commit/a986276266b8580650d0bf1b8f3aa5ebed5500f3))
- **chatbox:** cb-331: init widget ui ([57a686a](https://github.com/resola-ai/deca-apps/commit/57a686a2053c52d6d3525f14d26f6daa659b12fe))
- **chatbox:** cb-331: render widget and widget form edit, add functions for icon controls ([09b76e3](https://github.com/resola-ai/deca-apps/commit/09b76e3af870cfbbf77a283fe12f048e28357f63))
- **chatbox:** cb-331: update function on icon groups ([d266874](https://github.com/resola-ai/deca-apps/commit/d266874ad38d8dab0ff9fc0891333f22991e81a0))
- **chatbox:** CB-350 - Launcher Improvement ([efd3140](https://github.com/resola-ai/deca-apps/commit/efd31400d3e687bbf6d2f7d642877f0ccc329134))
- **chatbox:** CB-351 - Create Welcome message Widget Template (Admin and Client) ([9614954](https://github.com/resola-ai/deca-apps/commit/9614954f4daed2db5639cf0f353e979ecfa58707))
- **chatbox:** CB-352: update property of props ([e29c162](https://github.com/resola-ai/deca-apps/commit/e29c162db23d03bf2f7fe28aab459c8f94a2272e))
- **chatbox:** cb-354: refactors code. reuse logic in package-sharing ([59eb660](https://github.com/resola-ai/deca-apps/commit/59eb660bd1910cbf58e6e1e15fa1b3303a32d651))
- **chatbox:** CB-367 - [Chatbox Admin UI] Improve bgColor mapping in home styling and message styling ([03565a7](https://github.com/resola-ai/deca-apps/commit/03565a7bef2822b0bc4c755fc7e87a14f480b7de))
- **chatbox:** CB-368 - create context, hooks, api for integration settings ([df17db6](https://github.com/resola-ai/deca-apps/commit/df17db6e85728c2ad27eff8e3b1f90c4e1627e26))
- **chatbox:** CB-368 - dashboard integration with api ([4472fc7](https://github.com/resola-ai/deca-apps/commit/4472fc73faeab4deaeed25207708e1d297866eba))
- **chatbox:** CB-371 - Save chatbox content setting to api ([#166](https://github.com/resola-ai/deca-apps/issues/166)) ([6e7c849](https://github.com/resola-ai/deca-apps/commit/6e7c8493aaf23f1c6982495a22dca572d6d17e00))
- **chatbox:** CB-372 - Save chatbox general setting to api ([#120](https://github.com/resola-ai/deca-apps/issues/120)) ([8d12b7d](https://github.com/resola-ai/deca-apps/commit/8d12b7dc309be8de855dd4ba09017624b9463db3))
- **chatbox:** CB-388 - uploading-image ([d92ef08](https://github.com/resola-ai/deca-apps/commit/d92ef08c3800095af1947452db8f15f069789209))
- **chatbox:** cb-391: add env preview ([6809897](https://github.com/resola-ai/deca-apps/commit/68098978f81ebe5594932ef9e83dcfaa0091e207))
- **chatbox:** cb-391: integrate ws api ([9323460](https://github.com/resola-ai/deca-apps/commit/93234603b387111707018357a21c0111e03bb395))
- **chatbox:** cb-391: remove unused code ([0db893d](https://github.com/resola-ai/deca-apps/commit/0db893dd814b1ad3e19891ddc85d444d10a428ac))
- **chatbox:** cb-414: add props ([c0350d4](https://github.com/resola-ai/deca-apps/commit/c0350d4259b054799e528671bdb0588411c8721a))
- **chatbox:** cb-414: add styling context for quick answer widget ([914176d](https://github.com/resola-ai/deca-apps/commit/914176d1f05aaca1b7c8f4902c8ac94f06441ffd))
- **chatbox:** cb-414: create quick answer widget ([bc67710](https://github.com/resola-ai/deca-apps/commit/bc6771042f0f5bc3e204a8432a4d38c2c79bca33))
- **chatbox:** cb-414: fix colors props ([0964f6a](https://github.com/resola-ai/deca-apps/commit/0964f6a64b2e6d5e82780c4703198536a787fbb9))
- **chatbox:** cb-414: fix props ([9f0fbca](https://github.com/resola-ai/deca-apps/commit/9f0fbcafaa8fa8cdaee81eb1d097da21b9ea6443))
- **chatbox:** cb-414: remove unused code ([0b51386](https://github.com/resola-ai/deca-apps/commit/0b513868fc5b5fd518a9fe42b1d9b76deb0777a7))
- **chatbox:** cb-417: add props ([dc87332](https://github.com/resola-ai/deca-apps/commit/dc8733252c1aee8420d7082614274cc3e66f9443))
- **chatbox:** cb-417: add styling for sendingMessage widget ([fc94adc](https://github.com/resola-ai/deca-apps/commit/fc94adc797f3effb18cb5778fcbf20c35657d75b))
- **chatbox:** cb-417: create sending message widget template ([a15833b](https://github.com/resola-ai/deca-apps/commit/a15833be71a6d2df0fbac08b62f06bd3b7c6c806))
- **chatbox:** cb-417: fix type ([47bca30](https://github.com/resola-ai/deca-apps/commit/47bca30440d329b5273b45411d8aab26f766247b))
- **chatbox:** cb-417: format files ([d4ff7f8](https://github.com/resola-ai/deca-apps/commit/d4ff7f8b3d68a33723e3958ac80476b80480b729))
- **chatbox:** CB-458 - check on bug height ([509d499](https://github.com/resola-ai/deca-apps/commit/509d499635afe4131ae7459b6beb1873766eb180))
- **chatbox:** CB-460 - update color picker not working ([203c5b0](https://github.com/resola-ai/deca-apps/commit/203c5b0f54354b8ccc1a28532efe67dd0964b3cd))
- **chatbox:** CB-472 - Update Japanese language for Chatbox ([#196](https://github.com/resola-ai/deca-apps/issues/196)) ([0f086b1](https://github.com/resola-ai/deca-apps/commit/0f086b10248f2609f31b39893e810eda41523f9b))
- **chatbox:** cb-488: add enum ([0aa06ce](https://github.com/resola-ai/deca-apps/commit/0aa06ce8b0c4ae86b6b6f068d0f0535acaa92401))
- **chatbox:** cb-488: create callToActionButton widget using engine ([a16207e](https://github.com/resola-ai/deca-apps/commit/a16207ece0a66c328a12e81f01ee9a7ddd7fc935))
- **chatbox:** cb-488: create largeLinkImage widget using engine ([ddcf417](https://github.com/resola-ai/deca-apps/commit/ddcf4173bc55635be4f4b45017c6552f53a184d6))
- **chatbox:** cb-488: create smallLinkImage widget using engine ([0a9b759](https://github.com/resola-ai/deca-apps/commit/0a9b7597f1e37c82c18ff18397b2a37339ec0dc7))
- **chatbox:** cb-488: format fiels ([ab0904c](https://github.com/resola-ai/deca-apps/commit/ab0904cd5cd3b4f886afad6700cce39793cd9fb7))
- **chatbox:** cb-488: remove unused spaces ([7972a49](https://github.com/resola-ai/deca-apps/commit/7972a499201454371c12b8dbc1551642d517ab05))
- **chatbox:** cb-488: remove unused spacing ([c80a644](https://github.com/resola-ai/deca-apps/commit/c80a64457376af490a1dc0f20416259bee4bc5b9))
- **chatbox:** CB-489 - update required for input form ([3e99871](https://github.com/resola-ai/deca-apps/commit/3e99871650f29eabc522613ec8ae6f0489e491e7))
- **chatbox:** CB-492 - script to compile chatbox client to file ([5169477](https://github.com/resola-ai/deca-apps/commit/5169477ca1e5790cd12134713cf1465da9ad73b3))
- **chatbox:** CB-492 - script to compile chatbox client to file ([33f9260](https://github.com/resola-ai/deca-apps/commit/33f92609d354957426c4ed03733975abdcb041ae))
- **chatbox:** CB-492 - script to compile chatbox client to file ([6319758](https://github.com/resola-ai/deca-apps/commit/63197581f936862c2ed2c9794462565770b6de8c))
- **chatbox:** CB-492 - script to compile chatbox client to file ([c13ba95](https://github.com/resola-ai/deca-apps/commit/c13ba952612295b027e922cfbc20c9503bd6eed5))
- **chatbox:** CB-492 - script to compile chatbox client to file ([1d04745](https://github.com/resola-ai/deca-apps/commit/1d0474563bdc81724f71b96907cb49a16f49a0d2))
- **chatbox:** CB-492 - script to compile chatbox client to file ([52eefc9](https://github.com/resola-ai/deca-apps/commit/52eefc9395205d2b95be5b7666a00a94a19f5380))
- **chatbox:** CB-494 - add drag and drop ([63c1a52](https://github.com/resola-ai/deca-apps/commit/63c1a52e3bc2aec94eeecca84bdb2b96bb4d70c2))
- **chatbox:** CB-499 - cleanup ([018c3a5](https://github.com/resola-ai/deca-apps/commit/018c3a57c7092a0c2bddd3ab080c39030faae92f))
- **chatbox:** CB-499 - cleanup ([1b8c923](https://github.com/resola-ai/deca-apps/commit/1b8c923c98e0b3cc0c1874b3ccab285869074b71))
- **chatbox:** CB-499 - do not inclue inline asset ([1637c80](https://github.com/resola-ai/deca-apps/commit/1637c80a4731e168a075de470abddb48a350a355))
- **chatbox:** CB-499 - fix get public url ([8af2696](https://github.com/resola-ai/deca-apps/commit/8af269654a52c3e65bf1e5fdb993997f8ceab2a9))
- **chatbox:** CB-499 - fix launcher url ([7e72680](https://github.com/resola-ai/deca-apps/commit/7e72680c96af191c8f6860adf12e0ef10a824959))
- **chatbox:** CB-499 - improve build umd chatboxClient lib ([eaaacf3](https://github.com/resola-ai/deca-apps/commit/eaaacf3f9a885104bf605d5a61cee29d28b7d3d6))
- **chatbox:** CB-499 - remove unused ([60e45ed](https://github.com/resola-ai/deca-apps/commit/60e45ed737e0a35b05df5f8d5ff281912c5c5f35))
- **chatbox:** CB-499 - update data and embed script to load chatbox client ([af0bbb7](https://github.com/resola-ai/deca-apps/commit/af0bbb729e6840b86790550e53ea2a37658e3145))
- **chatbox:** CB-499 - update json file ([47b7c58](https://github.com/resola-ai/deca-apps/commit/47b7c58c7a04194605a8498d99deb736b2d55d3e))
- **chatbox:** CB-499 - update setting as props of chatbox client ([3965620](https://github.com/resola-ai/deca-apps/commit/3965620a5437f59761a8b86dc695bb93ebeacb20))
- **chatbox:** CB-499 - update umd package name ([4d98188](https://github.com/resola-ai/deca-apps/commit/4d981884064a577bf6653572a961cfa6d6b2421d))
- **chatbox:** CB-500 - Update Japanese language for Chatbox ([#201](https://github.com/resola-ai/deca-apps/issues/201)) ([2072481](https://github.com/resola-ai/deca-apps/commit/2072481c7224ec3da2e4942fc6097d4eb25dc95c))
- **chatbox:** CB-504: Apply using form for settings ([f52089b](https://github.com/resola-ai/deca-apps/commit/f52089b2fd13ddd7e609109355d0a56a7d61e0e6))
- **chatbox:** cb-505: add translation for navigator chatbox ([a53efaf](https://github.com/resola-ai/deca-apps/commit/a53efaf5a4e1f266e04bb75baa97b76cc08a965e))
- **chatbox:** cb-505: add translation for widgets ([0bb089c](https://github.com/resola-ai/deca-apps/commit/0bb089c91959131aa92dc2f993d89391c38f0dd8))
- **chatbox:** cb-517: clean widgets for mvp version ([b486665](https://github.com/resola-ai/deca-apps/commit/b486665061490f382bb2305209d5fa1708bbca68))
- **chatbox:** cb-517: create welcome message widget, modified props in widgets ([da5144d](https://github.com/resola-ai/deca-apps/commit/da5144d8368b4d2dab560e5c20adecc64b5e392a))
- **chatbox:** CB-532 - change build chatbox client outdir ([eafae17](https://github.com/resola-ai/deca-apps/commit/eafae1764c4d1b71e7d7874f331226de9cb35d9f))
- **chatbox:** CB-532 - update amplify script to sync with s3 to upload/update ([3fce7ac](https://github.com/resola-ai/deca-apps/commit/3fce7acec6463adc25ef90f47ce01e14ba5fe876))
- **chatbox:** CB-532 - update amplify.yml file - add script to build chatbox client ([10425db](https://github.com/resola-ai/deca-apps/commit/10425db3cf108451c2d3e2189b3e1ed418e48270))
- **chatbox:** CB-532 - update amplify.yml file - add script to build chatbox client ([942c771](https://github.com/resola-ai/deca-apps/commit/942c771f6a2b06d27049285067368efc7fca08cb))
- **chatbox:** CB-546 - Remove unused widgets ([a6229b6](https://github.com/resola-ai/deca-apps/commit/a6229b6b1cfcb174550eb6d98b8ee9bef8790082))
- **chatbox:** CB-547 - refresh list after saving ([4645aeb](https://github.com/resola-ai/deca-apps/commit/4645aebf4c72ad745669230d70504c131d233565))
- **chatbox:** cb-550: add dropzone to imageWidgetForm ([0e658ee](https://github.com/resola-ai/deca-apps/commit/0e658ee79974bacd712d06451f36fcf7b8a8a1cb))
- **chatbox:** cb-550: clean code in context ([c7743e0](https://github.com/resola-ai/deca-apps/commit/c7743e0a2af196a40210bd3959af074e1df0ec42))
- **chatbox:** cb-550: fix issue commas ([b02ca2e](https://github.com/resola-ai/deca-apps/commit/b02ca2ea50453cd03340b9503d87405aed90866e))
- **chatbox:** cb-550: format file ([77b8b0c](https://github.com/resola-ai/deca-apps/commit/77b8b0ca3f7874c32e0fd07d7d5664145c1f9cee))
- **chatbox:** cb-550: handle event after upload image ([97dc8ec](https://github.com/resola-ai/deca-apps/commit/97dc8ec157849f24bac054b5166b6914f555ad94))
- **chatbox:** cb-550: init dropzone ([9d8dfcb](https://github.com/resola-ai/deca-apps/commit/9d8dfcb8b5e876c325b1d52e23b890d6d3c59cca))
- **chatbox:** cb-553: update jp version ([0df2294](https://github.com/resola-ai/deca-apps/commit/0df22943ae72d52cd34042da769008468b187ad5))
- **chatbox:** cb-554: create remove widget modal ([de935da](https://github.com/resola-ai/deca-apps/commit/de935da8112b90c07485808922b23a3c9f915d26))
- **chatbox:** cb-554: prevent close drawer by esc ([ef93904](https://github.com/resola-ai/deca-apps/commit/ef939040927ee764d098a314532c69e9214bd983))
- **chatbox:** cb-559: change props ([52154a2](https://github.com/resola-ai/deca-apps/commit/52154a239245a9be08b852931b1f7146797c4cfe))
- **chatbox:** cb-559: declare types, pass widgets as props ([c1fb63b](https://github.com/resola-ai/deca-apps/commit/c1fb63bde815b523e26dd76354dea67d81cd3fcd))
- **chatbox:** cb-559: format files ([796d9c8](https://github.com/resola-ai/deca-apps/commit/796d9c8468423556acfd8baf952d16b2e0a9a5f5))
- **chatbox:** cb-559: pass widgets as props to all pages ([867f5df](https://github.com/resola-ai/deca-apps/commit/867f5df579b7f35a54bd4003b6471e616746f982))
- **chatbox:** cb-559: render widget on home section in preview ([718dcc1](https://github.com/resola-ai/deca-apps/commit/718dcc15b8ea633c982a5ae50f89e9612c192b22))
- **chatbox:** cb-559: update css for widgets ([86521fd](https://github.com/resola-ai/deca-apps/commit/86521fdfcfe2a94377bf0380762f02184f7f6fe8))
- **chatbox:** cb-559: update welcomeWidget template ([b116e23](https://github.com/resola-ai/deca-apps/commit/b116e236529c8fc6747fbafd8600e3571e2d6641))
- **chatbox:** CB-560 - add demo page ([03e60b1](https://github.com/resola-ai/deca-apps/commit/03e60b1cd5bb46efb6410c7a62b60c9c5a715fca))
- **chatbox:** CB-560 - add script to dev chatbox client ([bbe0305](https://github.com/resola-ai/deca-apps/commit/bbe0305485afdd9e10fc53fb89aec02f985c0cb1))
- **chatbox:** CB-560 - reformat demo page ([449ebdd](https://github.com/resola-ai/deca-apps/commit/449ebdd571a9c041051b32840718a0c0c0bf3103))
- **chatbox:** CB-585 - cleanup ([cdd3e1c](https://github.com/resola-ai/deca-apps/commit/cdd3e1cb989dd1b09fde07dbb6c6a9b1a73d88dd))
- **chatbox:** CB-585 - render widget in home tab in chatbox client ([c90295b](https://github.com/resola-ai/deca-apps/commit/c90295b4fdf2c1a616b3bc66e27899d2440c1b84))
- **chatbox:** CB-586 - add chatbox cliet embed script template ([b25f202](https://github.com/resola-ai/deca-apps/commit/b25f202e42ed1e2600706edab5e8050d4a9df2b0))
- **chatbox:** CB-586 - handle error in demo page ([0487639](https://github.com/resola-ai/deca-apps/commit/048763960586c42204d1ee9c58f92f2a6a9bcad2))
- **chatbox:** CB-586 - rename from Installation to Integration ([123d38e](https://github.com/resola-ai/deca-apps/commit/123d38ef382e61701946d9c4b709e09f94d0e80c))
- **chatbox:** CB-586 - reupdate the demo page ([2d8c77a](https://github.com/resola-ai/deca-apps/commit/2d8c77af42af048e14d4ebb1401ed63ed739372b))
- **chatbox:** CB-586 - show embed scripts with dynamic bundleUrl and configUrl and domId ([3cb3d01](https://github.com/resola-ai/deca-apps/commit/3cb3d010566c9fb04a5fdb0d6dc494b99733857f))
- **chatbox:** CB-586 - update client test html ([d64e1eb](https://github.com/resola-ai/deca-apps/commit/d64e1ebe3edb74ae3a4bec39d06fea57d39fcbd5))
- **chatbox:** CB-586 - update japanese text ([edf2ff0](https://github.com/resola-ai/deca-apps/commit/edf2ff04fa2bbf0682e795caf209870ac9d9b834))
- **chatbox:** CB-589 - add sentry for chatbot admin and kb admin ([38b8743](https://github.com/resola-ai/deca-apps/commit/38b8743b3537db60cc2b207f5ce4c2f8dcff3d4c))
- **chatbox:** CB-589 - add sentry for chatbox admin ([325b954](https://github.com/resola-ai/deca-apps/commit/325b9542db0e512a6e89863531a8774c3e4e1c4e))
- **chatbox:** CB-589 - add sentry for chatbox client ([da43aee](https://github.com/resola-ai/deca-apps/commit/da43aee58e60165775055b220394bdb9755037f9))
- **chatbox:** CB-599 Update chatbox integration UI ([c5c5076](https://github.com/resola-ai/deca-apps/commit/c5c50760e384e40ae0b115c7c55f43e7bb848c92))
- **chatbox:** CB-611 - Apply infinite scrolling for chatbox list ([#326](https://github.com/resola-ai/deca-apps/issues/326)) ([3ffb833](https://github.com/resola-ai/deca-apps/commit/3ffb83364ee68df7b159230b9b5ee88a22038a3d))
- **chatbox:** cb-625: binding event to button ([007682e](https://github.com/resola-ai/deca-apps/commit/007682e7638961a136a27817252ebc8c6b8a3bf6))
- **chatbox:** cb-625: fix binding event ([b301df0](https://github.com/resola-ai/deca-apps/commit/b301df0e542b199282245096ed3cb774b436bc01))
- **chatbox:** cb-625: integrate ws ([8d6ab48](https://github.com/resola-ai/deca-apps/commit/8d6ab48f217ce020bf7831fbf951d9d17982beed))
- **chatbox:** cb-625: pnpm lock ([f65164f](https://github.com/resola-ai/deca-apps/commit/f65164fcb22caed8efc1186c097b549ac1cb4cb7))
- **chatbox:** cb-625: render message from ws ([64054cf](https://github.com/resola-ai/deca-apps/commit/64054cf7a7ee489d265d5c9d91775c0ea386ec19))
- **chatbox:** cb-625: update mock data ([647de48](https://github.com/resola-ai/deca-apps/commit/647de48d63864ce6fdb1d68986b9d35ff0600928))
- **chatbox:** cb-625: use wsUserId ([d594645](https://github.com/resola-ai/deca-apps/commit/d5946457e3f4cd5b7abd89fce2381232d55df408))
- **chatbox:** cb-626: create template for actions url, postback, add type datetime ([f677023](https://github.com/resola-ai/deca-apps/commit/f677023961ce216f1c74fd69b9f71a8cd295fca0))
- **chatbox:** cb-626: update types ([6eef020](https://github.com/resola-ai/deca-apps/commit/6eef020eb8078e08af5d2907adbb0b1eb3d11ea3))
- **chatbox:** cb-627: add mock url ([7434b54](https://github.com/resola-ai/deca-apps/commit/7434b54fbb1a71228c2f3c154174ef15df2b07a2))
- **chatbox:** cb-627: change options to action field ([b1e77b9](https://github.com/resola-ai/deca-apps/commit/b1e77b95d907360a692928b08c0ae31c18e92f43))
- **chatbox:** cb-627: generate action button by type ([4229e8a](https://github.com/resola-ai/deca-apps/commit/4229e8a9f914d588776e00d85251089a8372877a))
- **chatbox:** cb-631: fix issue background, border for image widgets ([f3fc5fb](https://github.com/resola-ai/deca-apps/commit/f3fc5fb449a6faeac3bfe551473964feb88733fd))
- **chatbox:** cb-634 improve scrollbar behavior ([71e7ac3](https://github.com/resola-ai/deca-apps/commit/71e7ac3365dea51b600b5c35198fd176ac6fb62d))
- **chatbox:** cb-634 remove deadcode ([2d11d98](https://github.com/resola-ai/deca-apps/commit/2d11d989101bf08ba431c3e906e256c9049e3225))
- **chatbox:** CB-636 Correct Conversation Text color ([87c0bc2](https://github.com/resola-ai/deca-apps/commit/87c0bc2bebdab150ca4267495ba0529823fc9999))
- **chatbox:** CB-636 Correct layout for header ([18906cc](https://github.com/resola-ai/deca-apps/commit/18906cc8862af21ec4354bc70d21819d51f73a22))
- **chatbox:** CB-636 Implement Conversation Message screen and restructure of the message screens ([e03d383](https://github.com/resola-ai/deca-apps/commit/e03d3839901107789fd4184c35aadedad8785e78))
- **chatbox:** cb-646: update image link widget ([8a7d180](https://github.com/resola-ai/deca-apps/commit/8a7d1802b81a07a9a402db501fbd87d89b3504af))
- **chatbox:** CB-647 Adjust more color style for tolltip ([4f0c19b](https://github.com/resola-ai/deca-apps/commit/4f0c19b4d82b6934d4cfdf19dbe47a93703fe0ac))
- **chatbox:** CB-647 Correct issue spacing in chat header description ([ed22b90](https://github.com/resola-ai/deca-apps/commit/ed22b908f57d10b58051b5353c26267ff9db78dd))
- **chatbox:** CB-647 Correct issue with Tooltip children rendering ([6e2577a](https://github.com/resola-ai/deca-apps/commit/6e2577a09f58ef6e0df797300530370b5b4b4389))
- **chatbox:** CB-647 Improve UI for Chat Header with long text and add generalSettings props for demo ([2c568a7](https://github.com/resola-ai/deca-apps/commit/2c568a7e849d33d095d5b5f0e150039976443dac))
- **chatbox:** CB-647 Integrate tooltip to Chat Header title ([976772c](https://github.com/resola-ai/deca-apps/commit/976772c35f5753067aed89c5df551ee6e95437fd))
- **chatbox:** CB-647 Remove unused Tooltip import ([f26c786](https://github.com/resola-ai/deca-apps/commit/f26c7864f46899e61d53c8ae393878bdfa732851))
- **chatbox:** CB-651 remove chatbox user icon on widget, fix scroll issue on integration page ([1f68bad](https://github.com/resola-ai/deca-apps/commit/1f68bad87927096036bb6c6d872050b0ada4cc39))
- **chatbox:** CB-659 - Remove title and subTitle when adding widgets ([89e7f05](https://github.com/resola-ai/deca-apps/commit/89e7f055eea81d8c00454208b63b795683fb2693))
- **chatbox:** cb-663: add image as default ([839342a](https://github.com/resola-ai/deca-apps/commit/839342a106a8c6f231b22457023c88058b1f6830))
- **chatbox:** cb-663: remove backstick ([8b2e9b0](https://github.com/resola-ai/deca-apps/commit/8b2e9b0d1e2994598099fa996c24e8def7f821b4))
- **chatbox:** CB-680 condition render launcher icon ([4bde77e](https://github.com/resola-ai/deca-apps/commit/4bde77e5ca589841adff5d4c4007bd427847b6cd))
- **chatbox:** CB-685 - add loader for chatbox client ([9ac434e](https://github.com/resola-ai/deca-apps/commit/9ac434e3712ff0d37d6a25f702886321223f8500))
- **chatbox:** CB-685 - cleanup ([47353ba](https://github.com/resola-ai/deca-apps/commit/47353baba68520e1ca1f4b4fb771d923738df212))
- **chatbox:** CB-685 - fix build fail ([592efa2](https://github.com/resola-ai/deca-apps/commit/592efa2849e92c68645c60d15ec357ed92c7991c))
- **chatbox:** CB-685 - fix build fail ([e9c7b8e](https://github.com/resola-ai/deca-apps/commit/e9c7b8e015647c821ae8e13391c2775df798124c))
- **chatbox:** CB-685 - update loader flow ([3bb6db1](https://github.com/resola-ai/deca-apps/commit/3bb6db11a3a053d614d777824e01d69928e98a0d))
- **chatbox:** CB-685 - update loader flow ([4f39dee](https://github.com/resola-ai/deca-apps/commit/4f39dee69666a867c45411c7090912cdbf1ac5b5))
- **chatbox:** CB-685 - update loader flow ([dd15b43](https://github.com/resola-ai/deca-apps/commit/dd15b4391f863b9c0c054d21cc9095c45f36dd23))
- **chatbox:** cb-685: define type, create event payload ([b8841e0](https://github.com/resola-ai/deca-apps/commit/b8841e006edd5dbd2d50565fa49b8663a4512ad5))
- **chatbox:** cb-685: format files ([b10427b](https://github.com/resola-ai/deca-apps/commit/b10427b8b24487a64f5b4fc636bcd1d1ff241f0d))
- **chatbox:** cb-685: handle action button ([1a130c8](https://github.com/resola-ai/deca-apps/commit/1a130c858a513c3e1134b696cff8324c5a0c52b7))
- **chatbox:** cb-685: update localStorage key ([5584510](https://github.com/resola-ai/deca-apps/commit/5584510da96ad1ca358bb5fa5631e01f0980b0c6))
- **chatbox:** CB-687 - [Chatbox Client] Connect to livechat UI ([#420](https://github.com/resola-ai/deca-apps/issues/420)) ([82a19d7](https://github.com/resola-ai/deca-apps/commit/82a19d75435fc500651b467920417047f661c4d7))
- **chatbox:** cb-694 rerender chat screen unexpected ([8a95835](https://github.com/resola-ai/deca-apps/commit/8a958350e831979eb450939ba49ff9939aefc2e9))
- **chatbox:** CB-695 - Update button in client chatbox ([a4f6973](https://github.com/resola-ai/deca-apps/commit/a4f6973db0a2ae331a408fd056e022111b48dd1c))
- **chatbox:** CB-696 Update default state for message tab ([f16d725](https://github.com/resola-ai/deca-apps/commit/f16d7255a44ef16303605a8bc1a79a33001f2d7f))
- **chatbox:** CB-706 - [Chatbox Client] Improve rendering text with linebreak in chatbox client ([2e723f1](https://github.com/resola-ai/deca-apps/commit/2e723f1ba53ef86633b0aa545ab18f83620f6457))
- **chatbox:** CB-707 - [Chatbox Client] Improve rendering buttons - change style in chatbox client ([ef5c00b](https://github.com/resola-ai/deca-apps/commit/ef5c00b34dac3c873b62b07b6ac59b76c91558ad))
- **chatbox:** CB-707 - fix build fail ([c80879e](https://github.com/resola-ai/deca-apps/commit/c80879e95dc6573e68dbc6db96dd83af33ea57b0))
- **chatbox:** CB-708 - [Chatbox Client] Show chatbox avatar in each message avatar ([967e375](https://github.com/resola-ai/deca-apps/commit/967e375c2d8b7e6849e28ac72642e88c1a185894))
- **chatbox:** CB-708 - fix build fail ([c65d040](https://github.com/resola-ai/deca-apps/commit/c65d04026985399c78f017091a67d9aa0d9272b3))
- **chatbox:** CB-709 - [Chatbox Client] Improve loader style ([62b8747](https://github.com/resola-ai/deca-apps/commit/62b87473b6cff1fcaadd75c419acea98e62df967))
- **chatbox:** CB-710 - [Chatbox Client] Improve japanese for chatbox client ([56c56c5](https://github.com/resola-ai/deca-apps/commit/56c56c59304c84d969ac8d22b5b9d70f6b16ef32))
- **chatbox:** cb-711: update the way to trigger event ([1415241](https://github.com/resola-ai/deca-apps/commit/14152411b13e310e96ff584b711f8cfa479b64d2))
- **chatbox:** cb-713: update payload for action button ([378c7dc](https://github.com/resola-ai/deca-apps/commit/378c7dc371541c4476f8b1ed8ca1ed49f38f412a))
- **chatbox:** CB-715 - [Chatbox Client] Add loading when trigger the flow ([2550031](https://github.com/resola-ai/deca-apps/commit/2550031241b1ea6f33349051591032aac0364032))
- **chatbox:** CB-716 - [Chatbox Client] If user does not select the buttons but manually type some text and submit, then the buttons list should be hide ([7ba8d14](https://github.com/resola-ai/deca-apps/commit/7ba8d14af48a94a46a385c93c7c8f1c60421111a))
- **chatbox:** CB-717 - [Chatbox Client] Do not encode user input when rendering sent text ([c21c493](https://github.com/resola-ai/deca-apps/commit/c21c493d25627e650a85e19998cd123214ba7f84))
- **chatbox:** CB-718 - [Chatbox Client] Update card styling ([11b841e](https://github.com/resola-ai/deca-apps/commit/11b841e34907416cff93bfc099b573254aace55b))
- **chatbox:** CB-719 - [Chatbox Client] Update card behavior ([f324cd9](https://github.com/resola-ai/deca-apps/commit/f324cd9183b8f64b16ae692bb6e239cd1685b822))
- **chatbox:** CB-719 - [Chatbox Client] Update card behavior ([c7f7eee](https://github.com/resola-ai/deca-apps/commit/c7f7eeefb7e735883ca168fdf501f6e2f0d77afa))
- **chatbox:** CB-720 - [Chatbox Client] Always open new tab/window when open a link in small/large image widget ([c4e3579](https://github.com/resola-ai/deca-apps/commit/c4e357945a928e594e2f776e8625b729bc72aac8))
- **chatbox:** CB-721 - [Chatbox Client] The bot answer should not break the width of the box ([1ff7ab4](https://github.com/resola-ai/deca-apps/commit/1ff7ab408c326c98bfb94652a91d925d21fb3caa))
- **chatbox:** CB-723 - [Chatbox Client] Hide the manual connect/disconnect to livechat ([a41d35f](https://github.com/resola-ai/deca-apps/commit/a41d35f5897658934cbd8b619671b5e0a1f8cdb5))
- **chatbox:** CB-727 - [Chatbox Client] Text on call-to-action button should not break the width, it should go to next line ([a9a9ccb](https://github.com/resola-ai/deca-apps/commit/a9a9ccbdcc2a02045854ad3ac0f4613307a3d700))
- **chatbox:** CB-734 - remove unused vars to pass husky ([cee0a6f](https://github.com/resola-ai/deca-apps/commit/cee0a6f4073b6c552539fea10fa11384c91fe540))
- **chatbox:** CB-734 - Show bot avatar on sidebar ([c1ef49f](https://github.com/resola-ai/deca-apps/commit/c1ef49f288fa6fc21aa5d7fc41bd3407e23b7f5b))
- **chatbox:** CB-739 - [Chatbox Client] When click on call to action button, always open new tab ([da7398d](https://github.com/resola-ai/deca-apps/commit/da7398d5cc6724b7f56673f3f75a6f70afb2b9a5))
- **chatbox:** cb-740 update content setting page ([38e94d3](https://github.com/resola-ai/deca-apps/commit/38e94d3832542c3ef84cf2338d96b84cfa46ec8f))
- **chatbox:** cb-740 update padding widget ([d857347](https://github.com/resola-ai/deca-apps/commit/d857347f0e106fd97f026fee1286126dff804517))
- **chatbox:** cb-742: fix type ([d1c68de](https://github.com/resola-ai/deca-apps/commit/d1c68de84f907179faea4db03014d57a99a817df))
- **chatbox:** cb-742: handle message from livechat ([9878691](https://github.com/resola-ai/deca-apps/commit/98786916bbd977a0760d61065b40ba97ff9d0744))
- **chatbox:** cb-746: create event constant ([ca13767](https://github.com/resola-ai/deca-apps/commit/ca1376752d6cea139083d9d75d9b684499d7bacc))
- **chatbox:** cb-746: create livechat events, binding data to header chat ([84fa7f1](https://github.com/resola-ai/deca-apps/commit/84fa7f13a0169c9cc137eb7882349428a515d5ad))
- **chatbox:** cb-746: create payload when click connect/disconnect button ([ab261c5](https://github.com/resola-ai/deca-apps/commit/ab261c54fdedbe98555cf8661bc20aac7cb05791))
- **chatbox:** cb-746: remove unused code ([4ebd340](https://github.com/resola-ai/deca-apps/commit/4ebd34096683629f49010e5fba0dc54a901453e4))
- **chatbox:** CB-750 update chatbox logo ([9d56040](https://github.com/resola-ai/deca-apps/commit/9d56040bcd2a89c7643c7d6d4d4731df560031ea))
- **chatbox:** cb-766: update hook url ([e7f709a](https://github.com/resola-ai/deca-apps/commit/e7f709a9eac08b586bcca2d808c353ac9ec0ef4d))
- **chatbox:** CB-769 - [Chatbox Client] Validate orgId, integrationId, botId and domain before send event to hook ([dff3923](https://github.com/resola-ai/deca-apps/commit/dff3923488a11aaa3d46b3feb849eeb1dbc65f04))
- **chatbox:** CB-769 - [Chatbox Client] Validate orgId, integrationId, botId and domain before send event to hook ([79e960b](https://github.com/resola-ai/deca-apps/commit/79e960be7c0cae2e8caab808489e59fb70629191))
- **chatbox:** Fix error dashboard chatbox ([f877026](https://github.com/resola-ai/deca-apps/commit/f877026a23ecf226fc1556da67a4b156b3099d40))
- **chatbox:** remove unused code, add id field for response ([4ab295d](https://github.com/resola-ai/deca-apps/commit/4ab295d0c2a0f4e45c2081ae9294908aa69f0370))
- **chatbox:** resolve conflict ([061b1be](https://github.com/resola-ai/deca-apps/commit/061b1be1073bb8a42c6d9236cb9042835e88683d))
- **chatbox:** show link if answer contains link ([9e39e68](https://github.com/resola-ai/deca-apps/commit/9e39e684d93db429328b4920942334a6d512a202))
- **chatbox:** update chatbox amplify yml - create invalidate for client only ([6c4de8a](https://github.com/resola-ai/deca-apps/commit/6c4de8afc849f87a719b9bc7732546e3a2c10f4e))
- **chatbox:** update image background ([66369b5](https://github.com/resola-ai/deca-apps/commit/66369b57f62e2ff6b521e0d5c0737a6aee84fb06))
- **chatbox:** update padding chat message ([a2b1cd7](https://github.com/resola-ai/deca-apps/commit/a2b1cd77918324d7f80ec41ed9138a619ad8f56c))
- **chatbox:** update properties and remove unused codes ([89194d9](https://github.com/resola-ai/deca-apps/commit/89194d9bffafac4bb9d6db0e74f38eb5cbf62c98))
- implement [#933](https://github.com/resola-ai/deca-apps/issues/933) ([13b911e](https://github.com/resola-ai/deca-apps/commit/13b911ec05a046f78f9a9d09b6e5e05482983ab7))
- **kb:** cb-396: add burger and drawer in small screen ([9574465](https://github.com/resola-ai/deca-apps/commit/95744656006841fd395860e7a6289d07c5a8e6a9))
- **kb:** cb-396: init app ([2f4728d](https://github.com/resola-ai/deca-apps/commit/2f4728d508c22c8213368ab00a21d51760f4b811))
- **kb:** cb-396: setup multilanuages, integrate auth0, pages routing ([3789dfd](https://github.com/resola-ai/deca-apps/commit/3789dfd662613eb284e3a6d69c437d3dee17d750))
- **kb:** cb-396: setup testing, tsconfig, vite config, install neccessary packages ([25e2c52](https://github.com/resola-ai/deca-apps/commit/25e2c52dbc7a7b9ca50611fb7c649532ea1b17df))
- **kb:** CB-483 - implement kb document detail page, add upload file component ([28733d9](https://github.com/resola-ai/deca-apps/commit/28733d9d7da1531333cff0b62c8e05272d79ecd8))
- **kb:** CB-483 - update document detail page with mock data, add custom pagination ([46bf330](https://github.com/resola-ai/deca-apps/commit/46bf3303ab396ec616a1a16a647af9a0ad4d416b))
- **kb:** CB-498 - link homepage card to kb detail page ([39e66e4](https://github.com/resola-ai/deca-apps/commit/39e66e4db712421cb19744af08f5528b4d0ed58b))
- **kb:** CB-498 - update qna detail page, update common components ([33fb7a3](https://github.com/resola-ai/deca-apps/commit/33fb7a3b04057e4d53be3519cf327bd95a3e8660))
- **kb:** CB-509 - add qna table ([5d6d9c2](https://github.com/resola-ai/deca-apps/commit/5d6d9c264f4927dc54a9fceb44c272b0c275fe84))
- **kb:** CB-509 - update qna detail page with mock data ([163336d](https://github.com/resola-ai/deca-apps/commit/163336ddc1c9d1186f7d8566a45d63bb0a8d9338))
- **kb:** CB-511 - update kb form and upload component ([716a81e](https://github.com/resola-ai/deca-apps/commit/716a81e5ed746bcc1dca01500e135f9b68dbda10))
- **kb:** CB-518 - update kb model and move form to share component ([769b857](https://github.com/resola-ai/deca-apps/commit/769b8577cb2c459f378ec70e02f2ab61875a06ce))
- **kb:** fix build fail ([4dd0d90](https://github.com/resola-ai/deca-apps/commit/4dd0d90098352da9ec1fb14587bfbbf309d7d81e))
- **kb:** fix build fail ([0c29895](https://github.com/resola-ai/deca-apps/commit/0c2989544031b12a5e038ee3818d714e4aa8aaf2))
- lc-215: add files for chatbot app ([9312f15](https://github.com/resola-ai/deca-apps/commit/9312f158248e7f1f6c005585bd32d98f7e79dbd8))
- lc-215: init file structure in chatbox ([b9331f0](https://github.com/resola-ai/deca-apps/commit/b9331f0264241a4ce43d6899500e38c6708ca79a))
- lc-215: init files for chatbox app ([9042271](https://github.com/resola-ai/deca-apps/commit/9042271c267af484dd2542982596418840ab6812))
- lc-300: add i18n ([21005e9](https://github.com/resola-ai/deca-apps/commit/21005e999ec3b0a92e401b3682eb40f767a38089))
- LC-741 - Add todo show warning ([cb1e66f](https://github.com/resola-ai/deca-apps/commit/cb1e66fb34627ce6ea0058cf332cf9d7091627c3))
- LC-741 - Fix feedback UI ([3c783ed](https://github.com/resola-ai/deca-apps/commit/3c783ed9b77e29f85779cbd49f8c4d30176a3a88))
- LC-741 - LC-932 - widget userinfo and history ([5d49157](https://github.com/resola-ai/deca-apps/commit/5d49157485453ee8aec6d79784cc4aa92cc47cda))
- LC-841 - Add chatbot widget ([1e9cabf](https://github.com/resola-ai/deca-apps/commit/1e9cabfea2ee1c7a1b7e41bfd9ad99fb33a51f76))
- LC-841 Extend feature to limit install conditions ([752e7b0](https://github.com/resola-ai/deca-apps/commit/752e7b0f5b26f0f71a18b1f39cab4dec3adcc546))
- LC-903 - delay the user.workspace.unassigned_team.updated event for 5 seconds if there is a user.workspace.teams.updated before it ([b0fc3ef](https://github.com/resola-ai/deca-apps/commit/b0fc3ef8b3c4d4ad264d2edb8dbbb3f35a844da3))
- LC-903 - Inquiries appear in inbox before auto-assignment via automation ([37880e9](https://github.com/resola-ai/deca-apps/commit/37880e959f228145af643a0e1fcda5ddae90cffb))
- LC-925 - LC-926 - improve all github action flows ([fb0d661](https://github.com/resola-ai/deca-apps/commit/fb0d661519a61b7ee0699ef9e878e42f986916c9))
- LC-925 - LC-926 - improve ci flow to get commit message ([cd3cb5b](https://github.com/resola-ai/deca-apps/commit/cd3cb5b32b5594934d548b334296f288e8e0a8c0))
- LC-925 - LC-926 - improve ci flow to get commit message ([0376c04](https://github.com/resola-ai/deca-apps/commit/0376c043786e7319868f1c21b1e123032e07c7c6))
- LC-925 - LC-926 - improve ci flow to get commit message ([e8c7be5](https://github.com/resola-ai/deca-apps/commit/e8c7be55c69f917cb72df565b57bb98a998de1c5))
- LC-925 - LC-926 - improve ci flow to get commit message ([c2776a8](https://github.com/resola-ai/deca-apps/commit/c2776a859da716d0246e5ea0eb180a89bdc4c677))
- LC-925 - LC-926 - improve ci for all packages and apps ([0853b2b](https://github.com/resola-ai/deca-apps/commit/0853b2ba04cae4ed5a00aa78589f5f011d7af4b6))
- LC-925 - LC-926 - improve lint-staged for all packages ([ef10b4a](https://github.com/resola-ai/deca-apps/commit/ef10b4af9ddbde8740027d4c72b49e3df262bfff))
- LC-925 - LC-926 - improve linting for all apps and packages ([da9790f](https://github.com/resola-ai/deca-apps/commit/da9790f69dc6200e0f01bbff3c60a002fc042354))
- LC-925 - LC-926 - improve styling, refactor eslint config and typescript config, re-united config logic, fix eslint and typescript bug ([c3248f0](https://github.com/resola-ai/deca-apps/commit/c3248f0250d19a62cb8a5455f9cff03ee59f2fb9))
- LC-925 - LC-926 - improve unit test script and build script for all packages ([6b21594](https://github.com/resola-ai/deca-apps/commit/6b2159437155be2f4a1eefdf1ff84e866c39809c))
- LC-925 - LC-926 - migrate all packages to deca-apps - develop branch ([ffe7880](https://github.com/resola-ai/deca-apps/commit/ffe788040877a4705d1ec750f15f9cbf977476b7))
- LC-925 - LC-926 - migrate livechat and widget-engine from livechat to deca-apps (develop branch) ([10d937c](https://github.com/resola-ai/deca-apps/commit/10d937cf327bed4fc3b75fde086e4c9147b18eb8))
- LC-925 - LC-926 - pure migrate deca-livechat to deca-apps ([95422c7](https://github.com/resola-ai/deca-apps/commit/95422c758e29a67b6b13c353c029473a281d4ce1))
- LC-925 - LC-926 - run unit in local push, and run build on CI ([5fff1ce](https://github.com/resola-ai/deca-apps/commit/5fff1ce766bf7ddb5a7f9d3a0a876844b52e057a))
- LC-925 - LC-926 - update package.json for package.json ([e584f78](https://github.com/resola-ai/deca-apps/commit/e584f783a62c12d588b2cf7f8566eceb9a6ebe37))
- LC-925 - LC-926 - update to be able to build chatbot and chatbox; update to use 4.9.5 for all packages and apps ([79c4807](https://github.com/resola-ai/deca-apps/commit/79c4807b18252ba267be49d0ea2dcbd66db37eb2))
- LC-925 - LC-926 - update tsconfig for all packages, update eslint ignore for chatbot and chatbox, simplify pre-commit, update eslint rule for livechat ([abf7b55](https://github.com/resola-ai/deca-apps/commit/abf7b55b2f824504fe63de5b19db840ecc761c7a))
- LC-925 - LC-926 - use typescript 4 instead ([fb8ae39](https://github.com/resola-ai/deca-apps/commit/fb8ae3984b81b0e2c9dbaacb959899f8cec7006c))
- LC-925 - migrate from deca-livechat develop branch to deca-apps develop branch ([86a8f6e](https://github.com/resola-ai/deca-apps/commit/86a8f6e39be6e0152fd1ce065375894770ffcc86))
- LC-932 - Fix get next message ([2efe0ea](https://github.com/resola-ai/deca-apps/commit/2efe0ea790d6a8e4d51aa03065b821d26ec5d830))
- lc-933-organize-widget-list-order ([88a92ea](https://github.com/resola-ai/deca-apps/commit/88a92ea39927fabd8610e7ebd447335351c56d48))
- lc-933-organize-widget-list-order ([4b0f3e9](https://github.com/resola-ai/deca-apps/commit/4b0f3e94cf8ee96e71af6feb175d73091e970cbb))
- lc-933-organize-widget-list-order ([979cdcf](https://github.com/resola-ai/deca-apps/commit/979cdcf2446f5968e11b4169895d31d16c853458))
- lc-934-show-rebot-info-message-in-larger-screen ([67e47e9](https://github.com/resola-ai/deca-apps/commit/67e47e9545031258af0cfc96628cfe5928d2a864))
- lc-934-show-rebot-info-message-in-larger-screen ([d55eef0](https://github.com/resola-ai/deca-apps/commit/d55eef05b5dfbb4b304327b85fce23458fbcecc5))
- lc-934-show-rebot-info-message-in-larger-screen ([d2bd264](https://github.com/resola-ai/deca-apps/commit/d2bd2647876a6d6817d4cd617e8f530f502d4616))
- lc-934-show-rebot-info-message-in-larger-screen ([9d6c5fa](https://github.com/resola-ai/deca-apps/commit/9d6c5fa219e60717fa8d35c8667b86a96245d6d6))
- lc-934-show-rebot-info-message-in-larger-screen ([6ff679c](https://github.com/resola-ai/deca-apps/commit/6ff679ca68059ac05fe1e86da16414c1856b30ec))
- lc-934-show-rebot-info-message-in-larger-screen ([806129e](https://github.com/resola-ai/deca-apps/commit/806129e047759508046854b18cadd7589c6209ee))
- lc-934-show-rebot-info-message-in-larger-screen, hide for fix later ([a6d2895](https://github.com/resola-ai/deca-apps/commit/a6d2895142693028e922580c7dc47970854b6c36))
- lc-934-show-rebot-info-message-in-larger-screen, use fuzzy search in the main UI thread ([532cbaf](https://github.com/resola-ai/deca-apps/commit/532cbaf5c37dcded86c81a006aae5c98a1216e64))
- LC-943 - allow any in eslint - temp fix ([90fbeb9](https://github.com/resola-ai/deca-apps/commit/90fbeb952514e2a747987f72d9d2d48342334e62))
- LC-943 - change schema name ([de36de4](https://github.com/resola-ai/deca-apps/commit/de36de47390d80004e4395a8a5c43b8d679ba21b))
- LC-943 - copy missing files from account app ([a21c2da](https://github.com/resola-ai/deca-apps/commit/a21c2da13990ca9cd665bc8ec042d0f5127507cf))
- LC-943 - fix account linting issue ([1ee59df](https://github.com/resola-ai/deca-apps/commit/1ee59dfe7a9120ebab9aad8a5da6ee95d86a6d4a))
- LC-943 - fix amplify yml file for account, apex, demo, management app ([4161d27](https://github.com/resola-ai/deca-apps/commit/4161d273b28501ca3c417337b3bf6e42dbd4f195))
- LC-943 - fix build issue for management app ([c3471a0](https://github.com/resola-ai/deca-apps/commit/c3471a0b1ab810804f039c019b9925ac81fa39fa))
- LC-943 - fix build issue in demo app ([9292a6c](https://github.com/resola-ai/deca-apps/commit/9292a6c3c57923f88279c7e2f8d4e62249e75590))
- LC-943 - fix building issue for account ([c513aba](https://github.com/resola-ai/deca-apps/commit/c513aba48cc03b7361b327e477073b7fac6ed9bd))
- LC-943 - fix building issue for apex ([19d821f](https://github.com/resola-ai/deca-apps/commit/19d821f729d317c08329bb256c4e479ab2bd116d))
- LC-943 - fix check-branch-name ([cca9fc9](https://github.com/resola-ai/deca-apps/commit/cca9fc9b60e1174eb26b225a5730ed0b5d8cf592))
- LC-943 - fix linting issue in account app ([c0d1884](https://github.com/resola-ai/deca-apps/commit/c0d188452d689b32cf06ba02d9a88582450a0790))
- LC-943 - fix tsup missing in schema package ([bd88265](https://github.com/resola-ai/deca-apps/commit/bd88265155494365f05b85bcc4eecdfc2096851c))
- LC-943 - migrate account app and realted packages ([db93545](https://github.com/resola-ai/deca-apps/commit/db93545710f03db8f424d197e74cfd38029374c7))
- LC-943 - migrate apex app to deca-apps ([b1ad423](https://github.com/resola-ai/deca-apps/commit/b1ad423472ae743e2db5290bcb17ba136acaa4c4))
- LC-943 - migrate demo app to deca-apps ([36b0b2b](https://github.com/resola-ai/deca-apps/commit/36b0b2bb51519f4caf63a7e6b60d74836ed0da3d))
- LC-943 - migrate management app to deca-apps ([f2e8185](https://github.com/resola-ai/deca-apps/commit/f2e818573c0fc087476f2af1d984f9a9770d4345))
- LC-943 - update account and apex library order in package.json ([c1bc18a](https://github.com/resola-ai/deca-apps/commit/c1bc18a10f21ac40e4a0d7768330bf869deb37b4))
- LC-943 - update account app from develop-branch ([fd5e7d8](https://github.com/resola-ai/deca-apps/commit/fd5e7d8fec8c1de7d927b52650ab7a4b922ad5a7))
- LC-956 - Show message when chanel is not from chatbot ([1a39387](https://github.com/resola-ai/deca-apps/commit/1a3938730c0dbd57ef70eec826d418c32643dcf6))
- lc-962-show-message-for-an-activity ([56b52e9](https://github.com/resola-ai/deca-apps/commit/56b52e988728f6a91c909614256c5457cc0921e3))
- LC-965 - missing style ([052d378](https://github.com/resola-ai/deca-apps/commit/052d37807093e076af1908f7d80fc17b81eb804f))
- LC-966-warning on exit ([7b40740](https://github.com/resola-ai/deca-apps/commit/7b40740bb63dd38d57471a7fa1c4b202e29d00d9))
- **shared:** [PR Preview] Improve PR Preview for chatbox app ([#105](https://github.com/resola-ai/deca-apps/issues/105)) ([81c2330](https://github.com/resola-ai/deca-apps/commit/81c2330c7c242c2a3af6f09e77f26038e7a32397))
- **shared:** cb-339: move reusable logic to package-sharing ([b3af3e7](https://github.com/resola-ai/deca-apps/commit/b3af3e790ed095313adb76670397a4a6bc20e544))
- **shared:** CB-644 - update new header ([6848ae3](https://github.com/resola-ai/deca-apps/commit/6848ae3aeb47d1ab2758ab6088b22642dbd894ea))
- **workshop:** cb-513 - add build file for story book ([a3aeb6f](https://github.com/resola-ai/deca-apps/commit/a3aeb6f0f34f8bcfee210f855f62db099110267b))
- **workshop:** cb-513 - add postbuild script ([4f2d567](https://github.com/resola-ai/deca-apps/commit/4f2d5671479557552158aab6ca4e3685d1329fc4))
- **workshop:** cb-513 - add postbuild script ([ee63369](https://github.com/resola-ai/deca-apps/commit/ee633694eb8664b8e0cabcd639091925417206f0))
- **workshop:** CB-513 - do not include mdx file in stories ([d1f07cb](https://github.com/resola-ai/deca-apps/commit/d1f07cb55462cc206d817e8b2c19ca7b04387fc6))
- **workshop:** CB-513 - do not include mdx file in stories ([d53ab12](https://github.com/resola-ai/deca-apps/commit/d53ab128c938bb45d60018e03be1e7b877172b3d))
- **workshop:** CB-513 - do not include mdx file in stories ([dd7bdec](https://github.com/resola-ai/deca-apps/commit/dd7bdecba10b23d2268a8a87464fc1ac7e14de1c))
- **workshop:** CB-513 - env not found handle ([b34b652](https://github.com/resola-ai/deca-apps/commit/b34b652fc993bd039022ccbee4259ca5d5aae4a3))
- **workshop:** CB-513 - env not found handle ([9f16af5](https://github.com/resola-ai/deca-apps/commit/9f16af5a7ba4d290e6ca10a89a72fad63e20f3ef))
- **workshop:** CB-513 - load all story on building ([cff7569](https://github.com/resola-ai/deca-apps/commit/cff7569db253afcf07a72c730e3e3aa94e27f3d2))
- **workshop:** CB-513 - update amplify - add env ([678c237](https://github.com/resola-ai/deca-apps/commit/678c237f524c35b589b449a3c037ae8d4c3ccd22))
- **workshop:** CB-513 - update header ([0b2b591](https://github.com/resola-ai/deca-apps/commit/0b2b591e867c4982070ce92f8d74b75770be243f))
- **workshop:** CB-513 - update preview url script ([18d2402](https://github.com/resola-ai/deca-apps/commit/18d24021a0b9b7c8f2a310b3afe34cbc601ed567))
- **workshop:** init storybook app - workshop - add custom button ([bb09892](https://github.com/resola-ai/deca-apps/commit/bb0989265e4335092a385bc178b94893010afc0b))
- **workshop:** init storybook app - workshop - add custom button ([79cac8c](https://github.com/resola-ai/deca-apps/commit/79cac8c52db694b82f855256677305d17b367a25))

### Bug Fixes

- **chatbox:** CB - 549 - fix cannot delete chatbox ([7aaf3d9](https://github.com/resola-ai/deca-apps/commit/7aaf3d99493de2388cda0645de8ce4fb9ad90f97))
- **chatbox:** CB-459 - should navigate to home when display 2 tabs ([92ed368](https://github.com/resola-ai/deca-apps/commit/92ed368a2bf0fad09d1c9abe3c2735efbf75f8ef))
- **chatbox:** CB-493 - Add, update, remove widget for content setting… ([#202](https://github.com/resola-ai/deca-apps/issues/202)) ([00b5655](https://github.com/resola-ai/deca-apps/commit/00b56552e697a423ddb07b8f6f3ec918fc124de1))
- **chatbox:** CB-506 CB-507 - Fix Chatbox name is overflowing and update translation ([#209](https://github.com/resola-ai/deca-apps/issues/209)) ([64801e2](https://github.com/resola-ai/deca-apps/commit/64801e2df07207f8b0c5812ded70325cf0bfba7f))
- **chatbox:** cb-550: create form edit for smallImageLink widget ([02ce8a0](https://github.com/resola-ai/deca-apps/commit/02ce8a025f4eeb9a56f2c96833de99d3585ab88b))
- **chatbox:** cb-552: fix missing properties props ([aa26880](https://github.com/resola-ai/deca-apps/commit/aa2688068dfb56f96630198fde25b1bcf22082ea))
- **chatbox:** cb-554: fix icon issue ([1de04a6](https://github.com/resola-ai/deca-apps/commit/1de04a657bafa906712671d3833e443f2d2e6c4a))
- **chatbox:** cb-554: fix icon issue color ([65049fd](https://github.com/resola-ai/deca-apps/commit/65049fd6bac4ccd094d6c65106432fe29c1d4930))
- **chatbox:** cb-554: fix issue color ([ff81da5](https://github.com/resola-ai/deca-apps/commit/ff81da5677f67f6a7157b0e632c4bd01ac2babb0))
- **chatbox:** CB-568 - Fix number input ([9ed977d](https://github.com/resola-ai/deca-apps/commit/9ed977d6029a4478d0931906a45c195a36f16bdf))
- **chatbox:** CB-601 - Validation for chatbox name and description ([#310](https://github.com/resola-ai/deca-apps/issues/310)) ([7662b33](https://github.com/resola-ai/deca-apps/commit/7662b33b9bb3cb4766689d5aa7400eff4ecde6be))
- **chatbox:** CB-614 - Fix disabled button when creating ([a075c40](https://github.com/resola-ai/deca-apps/commit/a075c4025f595fb2ab71ce079ea722e831ce3b89))
- **chatbox:** CB-625 - debug add event not working ([227c896](https://github.com/resola-ai/deca-apps/commit/227c896211f0690a8f9a4dbbc5ee00222b9e1b85))
- **chatbox:** cb-631: fix reload page when submit welcome message ([bc4f325](https://github.com/resola-ai/deca-apps/commit/bc4f32525fb586940b1b8f9737ce67b6c50b6a74))
- **chatbox:** cb-631: improve stylnig for CallToAction Button ([57aeb32](https://github.com/resola-ai/deca-apps/commit/57aeb32ac9b27b34b2b2e9a668612686ce81d697))
- **chatbox:** CB-635 - improve widget render and refactor chatbox ui components ([a709e2f](https://github.com/resola-ai/deca-apps/commit/a709e2fb822831c9f280b6d3fc24f1f3a0e7ebf2))
- **chatbox:** CB-661 update ws init token api header ([af3fc6b](https://github.com/resola-ai/deca-apps/commit/af3fc6b29766d1ab93a7136db6ee36d112fba621))
- **chatbox:** CB-703 update text resources ([8e78823](https://github.com/resola-ai/deca-apps/commit/8e78823948441a5b235b3ca20c4e7bb9e39ba913))
- **chatbox:** CB-743 - Integration page - Item height should be the same ([517f170](https://github.com/resola-ai/deca-apps/commit/517f17030c2c3c15b453ecc6cac643777edf1b1b))
- **chatbox:** CB-763 update layout chatbox and logo ([29f72da](https://github.com/resola-ai/deca-apps/commit/29f72da932d00004592c5b300e6010410ed7dc4f))
- **chatbox:** fix minor type and convention updates ([4b79376](https://github.com/resola-ai/deca-apps/commit/4b793762554e689452ae1e630ce032e710657591))
- fix automation saving ([b10a1f6](https://github.com/resola-ai/deca-apps/commit/b10a1f6cba769bb602878dece6806d0e018b82de))
- fix/lc-928-fix-automation-saving ([738862f](https://github.com/resola-ai/deca-apps/commit/738862f00a83480bde225fa697d6a905419bb214))
- Implement feedback from victor ([b202500](https://github.com/resola-ai/deca-apps/commit/b20250031663330ed61e3eaff4e4866e99e72034))
- lc-928-fix-automation-saving ([f35c8da](https://github.com/resola-ai/deca-apps/commit/f35c8da69b7db028137731b67edff137539d3050))
- LC-936 - AI summary spilled on the new chat page ([28fbb75](https://github.com/resola-ai/deca-apps/commit/28fbb75256486513e3159e0b31e141b499c04e0c))
- LC-955 - Fix chatbot edit is too near text ([f9aa8c2](https://github.com/resola-ai/deca-apps/commit/f9aa8c2016f749116e0b38f26f05bc69705946f7))
- LC-956 - Fix save chatbot is slow ([5c00862](https://github.com/resola-ai/deca-apps/commit/5c00862606af247abfdb7f5210a398a8a6ae9e1c))
- LC-956 - Fix saving a bit slow ([18411f1](https://github.com/resola-ai/deca-apps/commit/18411f1afdbf492dd24a900956409a0ca0792156))
- LC-958 - Fix label displaying ([d9025fc](https://github.com/resola-ai/deca-apps/commit/d9025fc6226c20bd9af1034429406da020d027c1))
- LC-959 - Fix css history messages ([f37e270](https://github.com/resola-ai/deca-apps/commit/f37e2701ab7a37b691703de9540e78b74b657728))
- LC-960 - Fix spacing widget ([6062d6e](https://github.com/resola-ai/deca-apps/commit/6062d6eeb51adbda01ddb254374e31561927a7ad))
- Remove images ([fe572c2](https://github.com/resola-ai/deca-apps/commit/fe572c248053ad0da88c16e64b2aa4a58ff9518f))
- **shared:** CB-644 - open new tab when switch apps ([ac95a4a](https://github.com/resola-ai/deca-apps/commit/ac95a4a0bb6c61370f942be1ea4f5a156692b346))
- **shared:** CB-644 force reload when switch apps ([015ddf4](https://github.com/resola-ai/deca-apps/commit/015ddf45986240cb9b760cfd0682e5703d7fb851))
- **shared:** CB-644 update account setting navigation ([f464958](https://github.com/resola-ai/deca-apps/commit/f46495811a114a78556e7600a33a2b00dbc1a0db))
- **shared:** CB-656 update i18n multiple instance ([94c3f92](https://github.com/resola-ai/deca-apps/commit/94c3f924b0aa0ae15ba034e249e1fef3af3dd57e))
- **shared:** CB-667 missing key while iterating through header item list ([c9756ea](https://github.com/resola-ai/deca-apps/commit/c9756ea403b15135cd1bee8c7fd28e11984ee7d3))
- **shared:** CB-751 update custom button export ([8f3fbde](https://github.com/resola-ai/deca-apps/commit/8f3fbde88e4da40b807794f60cde33e55b2bd3fa))
- **shared:** CB-751 update logo height and add custom button ([eb669d6](https://github.com/resola-ai/deca-apps/commit/eb669d6efbaac38ca1c0f99807dcf496a010158e))
- **share:** fix typescript build fail in chatbox ([1f77dab](https://github.com/resola-ai/deca-apps/commit/1f77dabaa2232a466cb2de47417e33045d877aaf))
- update code with constant ([6bc05ee](https://github.com/resola-ai/deca-apps/commit/6bc05ee129ed75720ea7f42859b9dddef1c5d193))
