{"cancel": "Cancel", "chatbot": "<PERSON><PERSON><PERSON>", "chatbotDesc": "Select a chatbot to integrate.\nAfter integration, the chatbot will be available in Message tab.", "chatbotName": "ChatBot name", "chatbotSettings": {"incidentMessageDesc": "This message show up when <PERSON><PERSON><PERSON> is disconnected due to an incident", "title": "<PERSON><PERSON><PERSON>"}, "chatboxPerChatbotOverLimited": {"close": "OK", "message": "You've reached the integration limit of chatboxes per a chatbox. Consider removing some.", "title": "Can't integrate with this bot"}, "error": "Error", "incidentMessageTitle": "Incident Announcement Message", "internalServerError": "Internal server error", "livechat": "Livechat", "livechatDesc": "Integration with Livechat will be created automatically.\nCurrently, Chatwindow allows users to connect to Livechat only from Chatbot flows.", "livechatSettings": {"incidentMessageDesc": "This message show up when <PERSON><PERSON><PERSON> is disconnected due to an incident", "title": "Livechat Settings"}, "notFound": "Not found", "pageTitle": "Integrations", "save": "Save"}