{"appearanceTimingLabel": "出現タイミング", "autoOpenCardTitle": "自動オープン", "autoOpenOptionTitle": "自動オープン", "avatar": "表示画像", "botImage": "ボット画像", "botName": "表示名", "botSetting": "チャットボット表示", "botTrigger": "出現設定", "copied": "コピーしました", "copy": "コピー", "description": "説明", "displayName": "表示名", "enableChatwindow": "チャットウィンドウを有効にする", "exampleText": "例", "header": "ヘッダー", "ifOnClickEventNotSetText": "このスクリプトが設定されていない場合、チャットウィンドウはウェブサイト上で表示されません。", "integration": "設置用スクリプト", "integrationDesc": "下記スクリプトをコピーして、チャットウィンドウを設置したい全てのページの <span class='highlight'> &lt;/body&gt; </span> タグより前に貼り付けます  \n連携するチャットボットが公開されているかを確認してください", "launcherOnly": "ランチャーを表示", "onClickEventLabel": "Onclickイベント", "openChatText": "チャットを開く", "pageTitle": "全般設定", "triggerUserClickAButtonOption": "ページ内のボタンをクリックすると出現", "triggerUserOpenPageOption": "ページを開いたタイミングで出現", "userClickAButtonDescription": "ボタンやバナーなどのHTML要素をクリックした際に、チャットウィンドウを表示するスクリプトを指定します。<br />対象のHTML要素に以下のスクリプトを追加してください。"}