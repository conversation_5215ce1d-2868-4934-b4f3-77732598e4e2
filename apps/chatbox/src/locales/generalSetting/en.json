{"appearanceTimingLabel": "Appearance timing", "autoOpenCardTitle": "Auto Open", "autoOpenOptionTitle": "Auto open", "avatar": "Avatar", "botImage": "Bot image", "botName": "Bot name", "botSetting": "Bot setting", "botTrigger": "<PERSON><PERSON><PERSON><PERSON>", "copied": "<PERSON>pied", "copy": "Copy", "description": "Description", "displayName": "Display name", "enableChatwindow": "Enable chatwindow", "exampleText": "Example", "header": "Header", "ifOnClickEventNotSetText": "If the `onclick` attribute is not set, the chatwindow will not be displayed on the website.", "integration": "Integration", "integrationDesc": "Paste this code snippet before the closing <span class='highlight'> &lt;/body&gt; </span>tag on all pages you want the widget to appear. Remember to publish a chatbot.", "launcherOnly": "Launcher only", "onClickEventLabel": "Onclick Event", "openChatText": "Open Chat", "pageTitle": "General Setting", "triggerUserClickAButtonOption": "When user click a button", "triggerUserOpenPageOption": "When user opens a page", "userClickAButtonDescription": "Specify the script to display the chat window when clicking an HTML element, such as a button or banner.<br />Add the following script to the 'onclick' attribute of the target HTML element."}