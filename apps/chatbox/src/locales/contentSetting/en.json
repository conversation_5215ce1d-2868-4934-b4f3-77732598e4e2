{"addDescriptionPlaceholder": "Add description", "addTitlePlaceholder": "Add title", "addWidgetTitle": "Add Widgets", "button": "<PERSON><PERSON>", "buttonLabel": "Button Label", "buttonLabelPlaceholder": "Enter button label", "callToActionButtonSubTitle": "Setup call to action for the chatbot", "callToActionButtonTitle": "Call to action button", "callToActionDefaultLabel": "Call to action", "cancel": "Cancel", "chatWithBot": "Chat with a bot", "decaVirtualStoreSubTitle": "Setup Virtual Store for chatbot", "decaVirtualStoreTitle": "DECA Virtual Store", "description": "Description", "dropzoneIdleText": "Drag images here or click to select files", "enterLinkUrlPlaceholder": "Enter link URL", "enterWelcomeMessagePlacholder": "Enter welcome message", "largeImageLinksSubTitle": "Feature the news or external link", "largeImageLinksTitle": "Large image links", "link": "URL Link", "message": "Type a message...", "messageTitle": "Message", "moreWidget": "More Widgets", "moveToUrl": "Move to URL", "pageTitle": "Content Setting", "placeholder": "Placeholder", "placeholderDesc": "Set the message to be displayed in the input field", "remove": "Remove", "removeWidget": "<PERSON><PERSON><PERSON>t", "removeWidgetDescription": "Are you sure you want to remove this widget?", "removeWidgetTitle": "Remove widget confirmation", "required": "This field is required", "resetButton": "Reset button", "save": "Save", "selectVirtualStoreTitle": "Select Virtual Store", "smallImageLinksSubTitle": "Feature the news or external link", "smallImageLinksTitle": "Small image link", "smallLargeImageDefaultDescription": "Description link", "smallLargeImageDefaultTitle": "Article link title", "title": "Title", "welcome": "Welcome", "welcomeMessageDefault": "Hi there !", "welcomeMessageSubTitle": "You can set a welcome message to be displayed at the top of home", "welcomeMessageTitle": "Welcome Message", "widgetsLimitation": {"close": "OK", "message": "You've reached the limit of widgets per a chatbox. Consider removing some.", "title": "Can't add new widget"}, "wrong_url_format": "wrong url format"}