import { ActionIcon, Box, Image, Modal, Text, Tooltip, alpha, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconMaximize, IconMinimize, IconX } from '@tabler/icons-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { MediaType } from '../constants/media';
import { getMediaTypeFromUrl } from '../utils/media';

// Props interface for the MediaViewer component
export interface MediaViewerProps {
  /** URL of the media to display */
  src: string;
  /** Whether the modal is open */
  opened: boolean;
  /** Function to call when the modal is closed */
  onClose: () => void;
  /** Optional title to display above the media */
  title?: string;
  /** Optional alt text for images */
  alt?: string;
  /** Optional width for the media content */
  width?: number | string;
  /** Optional height for the media content */
  height?: number | string;
  /** Optional additional class names */
  className?: string;
  /** Optional styles to apply to the container */
  style?: React.CSSProperties;
}

// Styles for the component
const useStyles = createStyles((theme) => ({
  modal: {
    '.mantine-Modal-header': {
      padding: `${rem(8)} ${rem(16)}`,
      minHeight: rem(48),
    },
    '.mantine-Modal-body': {
      padding: 0,
    },
    '.mantine-Modal-content': {
      position: 'relative',
    },
  },
  fullscreenModal: {
    '.mantine-Modal-body': {
      padding: 0,
    },
    '.mantine-Modal-content': {
      maxWidth: '100vw !important',
      width: '100vw',
      height: '100vh',
      margin: 0,
      borderRadius: 0,
    },
  },
  title: {
    marginBottom: rem(16),
    fontWeight: 500,
    color: theme.colors.gray[8],
    textAlign: 'center',
    padding: `0 ${rem(16)}`,
  },
  mediaContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    padding: `${rem(8)} ${rem(24)} ${rem(24)}`,
  },
  fullscreenMediaContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    padding: 0,
    backgroundColor: theme.colors.gray[0],
  },
  video: {
    maxWidth: '100%',
    maxHeight: '80vh',
    borderRadius: theme.radius.sm,
  },
  fullscreenVideo: {
    width: '100%',
    height: '100%',
    maxHeight: '100vh',
    borderRadius: 0,
    objectFit: 'contain',
  },
  image: {
    borderRadius: theme.radius.sm,
  },
  fullscreenImage: {
    width: '100vw',
    height: '100vh',
    borderRadius: 0,
  },
  fullscreenButton: {
    position: 'absolute',
    bottom: rem(24),
    right: rem(24),
    backgroundColor: alpha(theme.colors.dark[9], 0.6),
    color: alpha(theme.colors.gray[0], 0.85),
    '& svg': {
      transition: 'all 0.2s ease',
    },
    '&:hover': {
      backgroundColor: alpha(theme.colors.dark[8], 0.8),
      '& svg': {
        transform: 'scale(1.2)',
        color: theme.colors.gray[0],
      },
    },
    zIndex: 1000,
  },
  closeButton: {
    position: 'absolute',
    top: rem(16),
    right: rem(16),
    zIndex: 1000,
  },
}));

/**
 * MediaViewer component for displaying images and videos in a modal
 * This component detects the type of media based on the file extension
 * and renders the appropriate viewer (image or video).
 */
const EditorMediaViewer = memo(
  ({ src, opened, onClose, title, alt, width, height, className, style }: MediaViewerProps) => {
    const { classes, cx } = useStyles();
    const [mediaType, setMediaType] = useState<MediaType>(MediaType.IMAGE);
    const [isFullscreen, setIsFullscreen] = useState(false);

    const caption = useMemo(() => {
      if (mediaType === MediaType.VIDEO) {
        return src;
      }
      return undefined;
    }, [mediaType, src]);

    const altText = useMemo(() => {
      if (mediaType === MediaType.VIDEO) {
        return alt ?? title ?? 'Video';
      }
      return alt ?? title ?? 'Image';
    }, [alt, title, mediaType]);

    // Determine media type when src changes
    useEffect(() => {
      if (src) {
        setMediaType(getMediaTypeFromUrl(src));
      }
    }, [src]);

    // Handle escape key to exit fullscreen or close modal
    const handleEscapeKey = useCallback(
      (event: KeyboardEvent) => {
        if (event.key === 'Escape' && opened) {
          if (isFullscreen) {
            setIsFullscreen(false);
          } else {
            onClose();
          }
        }
      },
      [opened, onClose, isFullscreen]
    );

    // Toggle fullscreen mode
    const toggleFullscreen = useCallback(() => {
      setIsFullscreen((prev) => !prev);
    }, []);

    // Render media content based on type
    const renderMediaContent = useCallback(() => {
      const isVideoContent = mediaType === MediaType.VIDEO;

      if (isVideoContent) {
        return (
          <video
            src={src}
            controls
            autoPlay
            className={isFullscreen ? classes.fullscreenVideo : classes.video}
            style={!isFullscreen ? { width: width ?? 'auto', height: height ?? 'auto' } : undefined}
          >
            <track kind='captions' src={caption} label={altText} srcLang='en' />
          </video>
        );
      }

      return (
        <Image
          src={src}
          alt={altText}
          fit='contain'
          width={isFullscreen ? '100%' : (width ?? '100%')}
          height={isFullscreen ? '100%' : (height ?? 'auto')}
          className={isFullscreen ? classes.fullscreenImage : classes.image}
        />
      );
    }, [src, altText, caption, width, height, isFullscreen, mediaType, classes]);

    useEffect(() => {
      window.addEventListener('keydown', handleEscapeKey);
      return () => {
        window.removeEventListener('keydown', handleEscapeKey);
      };
    }, [handleEscapeKey]);

    return (
      <Modal
        opened={opened}
        onClose={onClose}
        size={isFullscreen ? 'full' : 'xl'}
        className={cx(isFullscreen ? classes.fullscreenModal : classes.modal, className)}
        withCloseButton={!isFullscreen}
        centered={!isFullscreen}
        fullScreen={isFullscreen}
        transitionProps={{
          transition: 'fade',
          duration: 300,
          timingFunction: 'ease-in-out',
        }}
      >
        {title && !isFullscreen && <Text className={classes.title}>{title}</Text>}
        <Box
          className={isFullscreen ? classes.fullscreenMediaContainer : classes.mediaContainer}
          style={!isFullscreen ? style : undefined}
        >
          {renderMediaContent()}
          {/* Custom close button to ensure accessibility and testing expectations */}
          {!isFullscreen && (
            <ActionIcon
              aria-label='Close modal'
              size='lg'
              className={classes.closeButton}
              onClick={onClose}
            >
              <IconX size={18} />
            </ActionIcon>
          )}
          <Tooltip label={isFullscreen ? 'Exit full screen' : 'Full screen'}>
            <ActionIcon size='lg' className={classes.fullscreenButton} onClick={toggleFullscreen}>
              {isFullscreen ? <IconMinimize size={18} /> : <IconMaximize size={18} />}
            </ActionIcon>
          </Tooltip>
        </Box>
      </Modal>
    );
  }
);

EditorMediaViewer.displayName = 'EditorMediaViewer';

export default EditorMediaViewer;
