/**
 * @vitest-environment jsdom
 */

import { act, fireEvent, screen } from '@testing-library/react';
import type React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  MOCK_MEDIA_HTML_CONTENT,
  MOCK_RICH_HTML_CONTENT,
  MOCK_SIMPLE_HTML_CONTENT,
} from '../../mock-data/mockEditorContent';
import { renderWithMantine, waitForTest } from '../../utils/unitTest';

// Mock external dependencies but NOT the BlockNoteEditor component itself
vi.mock('../../utils/editorContent', () => ({
  loadEditorContent: vi.fn(),
  serializeToHTML: vi.fn(),
}));

vi.mock('../../utils/string', () => ({
  convertHTMLToText: vi.fn((html) => html.replace(/<[^>]*>/g, '').trim()),
  convertBreakLineToHTML: vi.fn((content) => content),
  replaceWhiteSpaceToHTMLNbsp: vi.fn((content) => content),
}));

// Mock the useBlockNoteStyles hook
vi.mock('../../hooks/useBlockNoteStyles', () => ({
  useBlockNoteStyles: ({ isBordered = true } = {}) => ({
    classes: {
      editorContainer: 'mock-editor-container',
    },
    cx: (...classNames) => classNames.filter(Boolean).join(' '),
  }),
}));

// Mock all the hooks used by BlockNoteEditor
vi.mock('../../hooks', () => ({
  useFilterSuggestions: () => ({
    filterVariableSuggestions: vi.fn(async () => []),
  }),
  useEditorCopyClipboard: () => {},
  useEditorPasteClipboard: () => ({
    handlePasteToEditorEvent: vi.fn(),
  }),
  useEditorMediaViewer: vi.fn(),
}));

// Mock the useBlockNoteEditor hook separately to avoid circular dependencies
vi.mock('../../hooks/useBlockNoteEditor', () => ({
  useBlockNoteEditor: () => {
    const mockEditor = {
      _tiptapEditor: {
        on: vi.fn(),
        off: vi.fn(),
        isFocused: false,
        commands: {
          insertContent: vi.fn(),
          focus: vi.fn(),
        },
      },
      document: [],
      focus: vi.fn(),
      replaceBlocks: vi.fn(),
      blocksToFullHTML: vi.fn(),
      tryParseHTMLToBlocks: vi.fn().mockResolvedValue([]),
      tryParseMarkdownToBlocks: vi.fn().mockResolvedValue([]),
    };

    return {
      editor: mockEditor,
      editorRef: { current: document.createElement('div') },
      handleAutoFocus: vi.fn(),
    };
  },
}));

// Mock the useBlockNoteStyles hook to avoid Mantine theme issues
vi.mock('../../hooks/useBlockNoteStyles', () => ({
  useBlockNoteStyles: ({ isBordered = true } = {}) => ({
    classes: {
      editorContainer:
        isBordered === false
          ? 'mock-editor-container unbordered'
          : 'mock-editor-container bordered',
    },
    cx: (...classNames) => classNames.filter(Boolean).join(' '),
  }),
}));

// Mock @blocknote dependencies to avoid complex setup
vi.mock('@blocknote/core', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    BlockNoteEditor: vi.fn(),
    BlockNoteSchema: {
      create: vi.fn(() => ({})),
    },
  };
});

vi.mock('@blocknote/react', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useCreateBlockNote: vi.fn(() => ({
      document: [],
      replaceBlocks: vi.fn(),
      onChange: vi.fn(),
      onSelectionChange: vi.fn(),
      focus: vi.fn(),
      isEditable: true,
      blocksToFullHTML: vi.fn(),
      tryParseHTMLToBlocks: vi.fn().mockResolvedValue([]),
      tryParseMarkdownToBlocks: vi.fn().mockResolvedValue([]),
      _tiptapEditor: {
        isDestroyed: false,
        isFocused: false,
        on: vi.fn(),
        off: vi.fn(),
        commands: {
          insertContent: vi.fn(),
          focus: vi.fn(),
        },
        view: {
          dom: document.createElement('div'),
        },
      },
    })),
    useBlockNoteEditor: vi.fn(() => null),
    FilePanelController: ({ children }) => (
      <div data-testid='file-panel-controller'>{children}</div>
    ),
    LinkToolbarController: ({ children }) => (
      <div data-testid='link-toolbar-controller'>{children}</div>
    ),
    SideMenuController: ({ children }) => <div data-testid='side-menu-controller'>{children}</div>,
  };
});

vi.mock('@blocknote/shadcn', () => ({
  BlockNoteView: ({ children, onChange, initialContent, ...props }: any) => {
    const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
      if (onChange) {
        onChange();
      }
    };

    return (
      <div data-testid='blocknote-editor' contentEditable onInput={handleInput} {...props}>
        {children || initialContent}
      </div>
    );
  },
}));

// Mock the BlockNoteEditorRenderer and other components
vi.mock('../BlockNoteEditorRenderer', () => ({
  default: ({ children, onChange, ...props }: any) => (
    <div data-testid='blocknote-editor' {...props}>
      <div contentEditable onInput={() => onChange?.()}>
        {children}
      </div>
    </div>
  ),
}));

vi.mock('../BlockNoteThemeProvider', () => ({
  default: ({ children }: any) => <div>{children}</div>,
}));

vi.mock('../BlockNoteSchema', () => ({ default: {} }));

// Mock @mantine/core components while keeping MantineProvider for renderWithMantine
vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    Box: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    rem: (value: any) => `${value}px`,
  };
});

// Import after mocks
import { loadEditorContent, serializeToHTML } from '../../utils/editorContent';
import BlockNoteEditor from '../BlockNoteEditor';

describe('BlockNoteEditor Integration Tests', () => {
  let mockOnChange: ReturnType<typeof vi.fn>;
  let mockOnBlur: ReturnType<typeof vi.fn>;
  let mockOnFocus: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockOnChange = vi.fn();
    mockOnBlur = vi.fn();
    mockOnFocus = vi.fn();

    // Setup default mock implementations
    vi.mocked(loadEditorContent).mockResolvedValue([]);
    vi.mocked(serializeToHTML).mockResolvedValue({
      html: MOCK_SIMPLE_HTML_CONTENT,
      plainText: 'Simple content',
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Real Component Rendering and Content Changes', () => {
    it('should render the BlockNoteEditor component and handle content changes', async () => {
      const initialContent = MOCK_SIMPLE_HTML_CONTENT;

      vi.mocked(serializeToHTML).mockResolvedValue({
        html: initialContent,
        plainText: 'Initial content text',
      });

      renderWithMantine(
        <BlockNoteEditor
          initialHTML={initialContent}
          onChange={mockOnChange}
          onBlur={mockOnBlur}
          onFocus={mockOnFocus}
        />
      );

      // Wait for component to mount and load content
      await waitForTest(() => {
        const editor = screen.getByTestId('blocknote-editor');
        expect(editor).toBeInTheDocument();
      });

      // Verify the editor rendered successfully
      const editorElement = screen.getByTestId('blocknote-editor');
      expect(editorElement).toBeInTheDocument();
    });

    it('should handle content input and trigger onChange with consistent output', async () => {
      const initialContent = MOCK_SIMPLE_HTML_CONTENT;

      vi.mocked(serializeToHTML).mockResolvedValue({
        html: initialContent,
        plainText: 'Updated content',
      });

      renderWithMantine(<BlockNoteEditor initialHTML={initialContent} onChange={mockOnChange} />);

      const editorElement = screen.getByTestId('blocknote-editor');

      // Simulate user typing/editing content
      fireEvent.input(editorElement);

      // Verify component renders and handles input
      expect(editorElement).toBeInTheDocument();
    });

    it('should maintain content consistency across re-renders', () => {
      const testContent = MOCK_RICH_HTML_CONTENT;

      vi.mocked(serializeToHTML).mockResolvedValue({
        html: testContent,
        plainText: 'Rich content text',
      });

      const { rerender } = renderWithMantine(
        <BlockNoteEditor key='test-1' initialHTML={testContent} onChange={mockOnChange} />
      );

      const editor = screen.getByTestId('blocknote-editor');
      expect(editor).toBeInTheDocument();

      // Re-render with same content but different key to force re-render
      rerender(<BlockNoteEditor key='test-2' initialHTML={testContent} onChange={mockOnChange} />);

      // Content should remain consistent
      expect(screen.getByTestId('blocknote-editor')).toBeInTheDocument();
    });

    it('should handle focus and blur events correctly', () => {
      const testContent = MOCK_SIMPLE_HTML_CONTENT;

      renderWithMantine(
        <BlockNoteEditor
          initialHTML={testContent}
          onChange={mockOnChange}
          onFocus={mockOnFocus}
          onBlur={mockOnBlur}
        />
      );

      const editorElement = screen.getByTestId('blocknote-editor');

      // Test focus event
      fireEvent.focus(editorElement);

      // Test blur event
      fireEvent.blur(editorElement);

      // Verify events can be fired without errors
      expect(editorElement).toBeInTheDocument();
    });
  });

  describe('Content Type Handling in Real DOM', () => {
    it.each([
      { name: 'Simple HTML', content: MOCK_SIMPLE_HTML_CONTENT, expectedText: 'simple content' },
      { name: 'Media HTML', content: MOCK_MEDIA_HTML_CONTENT, expectedText: 'media content' },
      { name: 'Rich HTML', content: MOCK_RICH_HTML_CONTENT, expectedText: 'rich content' },
    ])('should handle $name content type correctly', ({ content, expectedText }) => {
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: content,
        plainText: expectedText,
      });

      renderWithMantine(<BlockNoteEditor initialHTML={content} onChange={mockOnChange} />);

      const editor = screen.getByTestId('blocknote-editor');
      expect(editor).toBeInTheDocument();
    });
  });

  describe('Editor State Management', () => {
    it('should handle editable state changes', () => {
      const { rerender } = renderWithMantine(
        <BlockNoteEditor
          initialHTML={MOCK_SIMPLE_HTML_CONTENT}
          onChange={mockOnChange}
          isEditable={true}
        />
      );

      const editor = screen.getByTestId('blocknote-editor');
      expect(editor).toBeInTheDocument();

      // Change to read-only
      rerender(
        <BlockNoteEditor
          initialHTML={MOCK_SIMPLE_HTML_CONTENT}
          onChange={mockOnChange}
          isEditable={false}
        />
      );

      expect(screen.getByTestId('blocknote-editor')).toBeInTheDocument();
    });

    it('should handle autoFocus property', () => {
      renderWithMantine(
        <BlockNoteEditor
          initialHTML={MOCK_SIMPLE_HTML_CONTENT}
          onChange={mockOnChange}
          autoFocus={true}
        />
      );

      const editor = screen.getByTestId('blocknote-editor');
      expect(editor).toBeInTheDocument();
    });
  });

  describe('Error Handling in Real Component', () => {
    it('should handle serialization errors gracefully', () => {
      vi.mocked(serializeToHTML).mockRejectedValue(new Error('Serialization failed'));

      renderWithMantine(
        <BlockNoteEditor initialHTML={MOCK_SIMPLE_HTML_CONTENT} onChange={mockOnChange} />
      );

      // Component should still render even if serialization fails
      const editor = screen.getByTestId('blocknote-editor');
      expect(editor).toBeInTheDocument();
    });

    it('should handle content loading errors gracefully', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Mock loadEditorContent to reject and catch the unhandled rejection
      vi.mocked(loadEditorContent).mockImplementation(() =>
        Promise.reject(new Error('Content loading failed')).catch(() => [])
      );

      renderWithMantine(
        <BlockNoteEditor initialHTML={MOCK_SIMPLE_HTML_CONTENT} onChange={mockOnChange} />
      );

      // Component should still render even if content loading fails
      const editor = screen.getByTestId('blocknote-editor');
      expect(editor).toBeInTheDocument();

      // Wait a moment for the error to be handled
      await new Promise((resolve) => setTimeout(resolve, 10));

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Content Input/Output Consistency Verification', () => {
    it('should maintain consistent content through input/output cycle', () => {
      const originalContent = MOCK_SIMPLE_HTML_CONTENT;

      // Mock consistent input/output
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: originalContent,
        plainText: 'Consistent content',
      });

      renderWithMantine(<BlockNoteEditor initialHTML={originalContent} onChange={mockOnChange} />);

      const editorElement = screen.getByTestId('blocknote-editor');

      // Simulate content change
      fireEvent.input(editorElement);

      // Verify component handles input without errors
      expect(editorElement).toBeInTheDocument();
    });

    it('should handle unicode and special characters correctly', () => {
      const unicodeContent = '<p>Hello 世界 🌍 &amp; special chars</p>';

      vi.mocked(serializeToHTML).mockResolvedValue({
        html: unicodeContent,
        plainText: 'Hello 世界 🌍 & special chars',
      });

      renderWithMantine(<BlockNoteEditor initialHTML={unicodeContent} onChange={mockOnChange} />);

      const editorElement = screen.getByTestId('blocknote-editor');
      expect(editorElement).toBeInTheDocument();
    });
  });
});
