import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import BlockNoteToolbar from '../BlockNoteToolbar';

// Mock the components used in BlockNoteToolbar
vi.mock('@blocknote/react', () => ({
  BasicTextStyleButton: ({ basicTextStyle }) => (
    <button type='button' data-testid={`basic-text-style-${basicTextStyle}`}>
      {basicTextStyle}
    </button>
  ),
  FormattingToolbar: ({ children }) => <div data-testid='formatting-toolbar'>{children}</div>,
  FormattingToolbarController: ({ formattingToolbar }) => (
    <div data-testid='formatting-toolbar-controller'>{formattingToolbar()}</div>
  ),
  NestBlockButton: () => (
    <button type='button' data-testid='nest-block-button'>
      Nest
    </button>
  ),
  TextAlignButton: ({ textAlignment }) => (
    <button type='button' data-testid={`text-align-${textAlignment}`}>
      {textAlignment}
    </button>
  ),
  UnnestBlockButton: () => (
    <button type='button' data-testid='unnest-block-button'>
      Unnest
    </button>
  ),
}));

// Mock the custom buttons
vi.mock('../CustomButtons', () => ({
  ColorStyleButton: () => (
    <button type='button' data-testid='color-style-button'>
      Color
    </button>
  ),
  FileReplaceButton: () => (
    <button type='button' data-testid='file-replace-button'>
      Replace
    </button>
  ),
  FileCaptionButton: () => (
    <button type='button' data-testid='file-caption-button'>
      Caption
    </button>
  ),
  CreateLinkButton: () => (
    <button type='button' data-testid='create-link-button'>
      Link
    </button>
  ),
  BlockTypeSelect: () => <div data-testid='block-type-select'>Block Type</div>,
}));

describe('BlockNoteToolbar', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the toolbar with all components by default', () => {
    render(<BlockNoteToolbar />);

    // Check if the toolbar containers are rendered
    expect(screen.getByTestId('formatting-toolbar')).toBeInTheDocument();
    expect(screen.getByTestId('formatting-toolbar-controller')).toBeInTheDocument();

    // Check if custom buttons are rendered
    expect(screen.getByTestId('block-type-select')).toBeInTheDocument();
    expect(screen.getByTestId('file-caption-button')).toBeInTheDocument();
    expect(screen.getByTestId('file-replace-button')).toBeInTheDocument();
    expect(screen.getByTestId('color-style-button')).toBeInTheDocument();
    expect(screen.getByTestId('create-link-button')).toBeInTheDocument();

    // Check if basic text style buttons are rendered
    expect(screen.getByTestId('basic-text-style-bold')).toBeInTheDocument();
    expect(screen.getByTestId('basic-text-style-italic')).toBeInTheDocument();
    expect(screen.getByTestId('basic-text-style-underline')).toBeInTheDocument();
    expect(screen.getByTestId('basic-text-style-strike')).toBeInTheDocument();
    expect(screen.getByTestId('basic-text-style-code')).toBeInTheDocument();

    // Check if text alignment buttons are rendered
    expect(screen.getByTestId('text-align-left')).toBeInTheDocument();
    expect(screen.getByTestId('text-align-center')).toBeInTheDocument();
    expect(screen.getByTestId('text-align-right')).toBeInTheDocument();

    // Check if nest/unnest buttons are rendered
    expect(screen.getByTestId('nest-block-button')).toBeInTheDocument();
    expect(screen.getByTestId('unnest-block-button')).toBeInTheDocument();
  });

  it('does not render text alignment buttons when enabledTextAlignment is false', () => {
    render(<BlockNoteToolbar enabledTextAlignment={false} />);

    // Text alignment buttons should not be rendered
    expect(screen.queryByTestId('text-align-left')).not.toBeInTheDocument();
    expect(screen.queryByTestId('text-align-center')).not.toBeInTheDocument();
    expect(screen.queryByTestId('text-align-right')).not.toBeInTheDocument();

    // Other buttons should still be rendered
    expect(screen.getByTestId('basic-text-style-bold')).toBeInTheDocument();
    expect(screen.getByTestId('color-style-button')).toBeInTheDocument();
  });

  it('does not render nest/unnest buttons when enabledNestedBlock is false', () => {
    render(<BlockNoteToolbar enabledNestedBlock={false} />);

    // Nest/unnest buttons should not be rendered
    expect(screen.queryByTestId('nest-block-button')).not.toBeInTheDocument();
    expect(screen.queryByTestId('unnest-block-button')).not.toBeInTheDocument();

    // Other buttons should still be rendered
    expect(screen.getByTestId('basic-text-style-bold')).toBeInTheDocument();
    expect(screen.getByTestId('text-align-left')).toBeInTheDocument();
  });

  it('disables both text alignment and nested block buttons when both props are false', () => {
    render(<BlockNoteToolbar enabledTextAlignment={false} enabledNestedBlock={false} />);

    // Text alignment buttons should not be rendered
    expect(screen.queryByTestId('text-align-left')).not.toBeInTheDocument();
    expect(screen.queryByTestId('text-align-center')).not.toBeInTheDocument();
    expect(screen.queryByTestId('text-align-right')).not.toBeInTheDocument();

    // Nest/unnest buttons should not be rendered
    expect(screen.queryByTestId('nest-block-button')).not.toBeInTheDocument();
    expect(screen.queryByTestId('unnest-block-button')).not.toBeInTheDocument();

    // Other buttons should still be rendered
    expect(screen.getByTestId('basic-text-style-bold')).toBeInTheDocument();
    expect(screen.getByTestId('color-style-button')).toBeInTheDocument();
    expect(screen.getByTestId('create-link-button')).toBeInTheDocument();
  });
});
