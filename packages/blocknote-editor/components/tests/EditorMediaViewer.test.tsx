import { fireEvent, screen } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithMantine } from '../../utils/unitTest';
import EditorMediaViewer, { type MediaViewerProps } from '../EditorMediaViewer';

// -----------------------------------------------------------------------------
// Test constants
// -----------------------------------------------------------------------------
const TEST_IMAGE_SRC = 'https://example.com/image.png';
const TEST_VIDEO_SRC = 'https://example.com/video.mp4';
const TEST_ALT_TEXT = 'Test media';
const TEST_TITLE = 'Media title';

// Helper to render with required props
const setup = (overrideProps: Partial<MediaViewerProps> = {}) => {
  const defaultProps: MediaViewerProps = {
    src: TEST_IMAGE_SRC,
    opened: true,
    onClose: vi.fn(),
    alt: TEST_ALT_TEXT,
  } as MediaViewerProps;

  const utils = renderWithMantine(<EditorMediaViewer {...defaultProps} {...overrideProps} />);
  return { ...utils, onClose: defaultProps.onClose };
};

// Stub matchMedia for Mantine Modal internals
beforeEach(() => {
  window.matchMedia = vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }));
});

afterEach(() => {
  vi.restoreAllMocks();
});

// -----------------------------------------------------------------------------
// Test suites
// -----------------------------------------------------------------------------

describe('EditorMediaViewer – Rendering', () => {
  it('renders an image when src is an image URL', () => {
    setup();
    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', TEST_IMAGE_SRC);
    expect(img).toHaveAttribute('alt', TEST_ALT_TEXT);
  });

  it('renders a video when src is a video URL', () => {
    setup({ src: TEST_VIDEO_SRC });
    const videoEl = document.querySelector('video');
    expect(videoEl).toBeInTheDocument();
    expect(videoEl?.getAttribute('src')).toBe(TEST_VIDEO_SRC);
  });
});

describe('EditorMediaViewer – Interaction', () => {
  it('calls onClose when Escape key is pressed', () => {
    const { onClose } = setup();

    // Dispatch the key event on document.body instead of window.
    // In real browsers, the event target is usually an element (e.g., <body>),
    // which has getAttribute; jsdom sets the target to `window`, causing an
    // error inside Mantine's useWindowEvent handler. Using document.body
    // better mimics the browser behaviour and avoids the error.
    fireEvent.keyDown(document.body, { key: 'Escape', code: 'Escape' });

    expect(onClose).toHaveBeenCalled();
  });

  it('toggles fullscreen mode when fullscreen button is clicked', () => {
    setup({ src: TEST_VIDEO_SRC });

    // There are two buttons: close modal (aria-label="Close modal") and fullscreen
    // Identify fullscreen button as the second button inside modal content
    const buttons = document.querySelectorAll('button');
    const fullscreenButton = buttons[buttons.length - 1] as HTMLButtonElement;

    // Sanity check
    expect(fullscreenButton).toBeInTheDocument();

    // Initial state – close button should exist
    const closeBtnBefore = document.querySelector('button[aria-label="Close modal"]');
    expect(closeBtnBefore).toBeInTheDocument();

    // Click to enter fullscreen
    fireEvent.click(fullscreenButton);

    // Close button should disappear in fullscreen
    const closeBtnAfter = document.querySelector('button[aria-label="Close modal"]');
    expect(closeBtnAfter).not.toBeInTheDocument();

    // Click again to exit fullscreen
    fireEvent.click(fullscreenButton);

    const closeBtnAfterExit = document.querySelector('button[aria-label="Close modal"]');
    expect(closeBtnAfterExit).toBeInTheDocument();
  });
});
