import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { createRef } from 'react';
import { act } from 'react-dom/test-utils';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import * as useEditorCopyClipboardModule from '../../hooks/useEditorCopyClipboard';
import * as useEditorMediaViewerModule from '../../hooks/useEditorMediaViewer';
import * as suppressWarningsModule from '../../utils/suppressWarnings';
import { renderWithMantine } from '../../utils/unitTest';
import { inlineEditorViewerCSS } from '../BlockNote.styles';
import BlockNoteViewer, { type BlockNoteViewerProps } from '../BlockNoteViewer';

// Constants for test data
const TEST_HTML_CONTENT = '<p>Test content</p>';
const TEST_MARKDOWN_CONTENT = '# Test markdown';
const TEST_CLASS_NAME = 'test-class';
const TEST_ID = 'test-id';
const TEST_ACTION_COLOR = '#ff0000';
const TEST_TEXT_COLOR = '#333333';
const TEST_IMAGE_SRC = 'https://example.com/image.jpg';
const TEST_IMAGE_ALT = 'Test image alt';

// Mock EditorMediaViewer component
vi.mock('../EditorMediaViewer', () => ({
  default: ({ opened, onClose, src, alt }) =>
    opened ? (
      <div data-testid='editor-media-viewer'>
        <span>Media Viewer - {src}</span>
        <button type='button' onClick={onClose}>
          Close
        </button>
      </div>
    ) : null,
}));

// Mock BlockNoteThemeProvider
vi.mock('../BlockNoteThemeProvider', () => ({
  default: ({ children, shadowRootId }) => (
    <div data-testid='blocknote-theme-provider' data-shadow-root-id={shadowRootId}>
      {children}
    </div>
  ),
}));

// Mock the hooks
vi.mock('../../hooks/useEditorMediaViewer', () => ({
  default: vi.fn(() => ({
    isOpen: false,
    closeViewer: vi.fn(),
    currentSrc: null,
    currentAlt: null,
    openViewer: vi.fn(),
  })),
}));

vi.mock('../../hooks/useEditorCopyClipboard', () => ({
  useEditorCopyClipboard: vi.fn(),
}));

vi.mock('../../hooks/useBlockNoteStyles', () => ({
  useBlockNoteStyles: vi.fn(() => ({
    classes: {
      blockNoteViewer: 'mock-blocknote-viewer',
      editorContainer: 'mock-editor-container',
    },
    cx: (...args) => args.filter(Boolean).join(' '),
  })),
}));

// Mock warning suppression utilities
vi.mock('../../utils/suppressWarnings', () => ({
  enableBlockNoteWarningSuppression: vi.fn(),
  disableBlockNoteWarningSuppression: vi.fn(),
}));

// Mock string utilities
vi.mock('../../utils/string', () => ({
  convertBreakLineToHTML: vi.fn((content) => content),
  replaceWhiteSpaceToHTMLNbsp: vi.fn((content) => content),
}));

// Mock constants
vi.mock('../../constants', () => ({
  themeConfigurations: {
    colors: {
      decaBlue: ['', '', '', '', '', '', '#1F84F4'],
    },
  },
}));

// Mock CSS styles
vi.mock('../BlockNote.styles', () => ({
  shadcnStyleCSS: 'mock-shadcn-css',
  interStyleCSS: 'mock-inter-css',
  inlineEditorViewerCSS: vi.fn(() => 'mock-inline-css'),
}));

// Mock the shadow DOM
vi.mock('react-shadow', () => ({
  default: {
    div: ({ children, ref, ...props }) => {
      const setRef = (node: HTMLDivElement | null) => {
        if (node) {
          // Assign a mock shadowRoot to mimic real Shadow DOM behaviour
          // Define it via Object.defineProperty to bypass the read-only setter in JSDOM
          if (!Object.prototype.hasOwnProperty.call(node, 'shadowRoot')) {
            Object.defineProperty(node, 'shadowRoot', {
              value: node,
              writable: false,
              configurable: true,
            });
          }

          if (typeof ref === 'function') {
            ref(node);
          } else if (ref && 'current' in ref) {
            // eslint-disable-next-line no-param-reassign
            (ref as any).current = node;
          }
        }
      };

      return (
        <div data-testid='shadow-root' ref={setRef} {...props}>
          {children}
        </div>
      );
    },
  },
}));

// Mock the BlockNote libraries
vi.mock('@blocknote/shadcn', () => ({
  BlockNoteView: ({ editor, className, editable }) => (
    <div data-testid='blocknote-view' className={className} data-editable={editable}>
      BlockNote View Mock
    </div>
  ),
}));

vi.mock('@blocknote/react', () => ({
  useCreateBlockNote: vi.fn(() => ({
    insertBlocks: vi.fn(),
    replaceBlocks: vi.fn(),
    setEditorContent: vi.fn(),
    document: { id: 'mock-document-id' },
    tryParseHTMLToBlocks: vi.fn().mockResolvedValue([{ type: 'paragraph', content: 'Test' }]),
    tryParseMarkdownToBlocks: vi.fn().mockResolvedValue([{ type: 'paragraph', content: 'Test' }]),
    domElement: document.createElement('div'),
  })),
}));

// Mock BlockNote components
vi.mock('../BlockNoteSchema', () => ({
  default: { paragraph: { type: 'paragraph' } },
}));

vi.mock('../BlockNoteExtensions', () => ({
  default: {},
}));

describe('BlockNoteViewer Component', () => {
  // Default props for most tests
  const defaultProps: BlockNoteViewerProps = {
    initialHTML: TEST_HTML_CONTENT,
    className: TEST_CLASS_NAME,
    id: TEST_ID,
  };

  // Mock for media viewer hook
  const mockMediaViewerHook = {
    isOpen: false,
    currentSrc: null,
    currentAlt: null,
    openViewer: vi.fn(),
    closeViewer: vi.fn(),
  };

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();

    // Setup default mock implementations
    useEditorMediaViewerModule.default.mockImplementation(() => mockMediaViewerHook);
    useEditorCopyClipboardModule.useEditorCopyClipboard.mockImplementation(() => {});
    suppressWarningsModule.enableBlockNoteWarningSuppression.mockImplementation(() => {});
    suppressWarningsModule.disableBlockNoteWarningSuppression.mockImplementation(() => {});

    // Mock window.matchMedia
    window.matchMedia = vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('renders with custom className', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(screen.getByTestId('blocknote-view')).toHaveClass(TEST_CLASS_NAME);
    });

    it('renders non-editable BlockNoteView', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(screen.getByTestId('blocknote-view')).toHaveAttribute('data-editable', 'false');
    });

    it('renders with shadow DOM', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(screen.getByTestId('shadow-root')).toBeInTheDocument();
    });

    it('renders with theme provider', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(screen.getByTestId('blocknote-theme-provider')).toBeInTheDocument();
    });
  });

  describe('Props Handling', () => {
    it('handles isBordered prop', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} isBordered={true} />);
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('handles textColor prop', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} textColor={TEST_TEXT_COLOR} />);
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('handles actionColor prop', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} actionColor={TEST_ACTION_COLOR} />);
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('handles useActionColorAsHyperlink prop', () => {
      renderWithMantine(
        <BlockNoteViewer
          {...defaultProps}
          useActionColorAsHyperlink={true}
          actionColor={TEST_ACTION_COLOR}
        />
      );
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('handles isUsingInlineCSS prop set to false', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} isUsingInlineCSS={false} />);
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
      expect(screen.queryByTestId('inline-styles')).not.toBeInTheDocument();
    });

    it('handles enableMediaViewer prop', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} enableMediaViewer={false} />);
      expect(useEditorMediaViewerModule.default).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false,
        })
      );
    });
  });

  describe('ForwardRef', () => {
    it('forwards ref correctly', () => {
      const ref = createRef<HTMLDivElement>();
      renderWithMantine(<BlockNoteViewer {...defaultProps} ref={ref} />);

      // The ref should be attached to the Box element with data-blocknote-viewer
      expect(ref.current).toBeTruthy();
    });
  });

  describe('Hook Integration', () => {
    it('initializes copy clipboard functionality', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(useEditorCopyClipboardModule.useEditorCopyClipboard).toHaveBeenCalled();
    });

    it('calls the media viewer hook with correct parameters', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} enableMediaViewer={true} />);
      expect(useEditorMediaViewerModule.default).toHaveBeenCalledWith({
        id: TEST_ID,
        editorSelector: '[data-blocknote-viewer] .bn-editor',
        enabled: true,
        shadowRoot: null,
        rootElement: null,
      });
    });

    it('enables warning suppression on mount', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(suppressWarningsModule.enableBlockNoteWarningSuppression).toHaveBeenCalled();
    });
  });

  describe('Media Viewer Integration', () => {
    it('does not render media viewer when no current source', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(screen.queryByTestId('editor-media-viewer')).not.toBeInTheDocument();
    });

    it('renders media viewer when media is open', () => {
      useEditorMediaViewerModule.default.mockImplementation(() => ({
        ...mockMediaViewerHook,
        isOpen: true,
        currentSrc: TEST_IMAGE_SRC,
        currentAlt: TEST_IMAGE_ALT,
      }));

      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(screen.getByTestId('editor-media-viewer')).toBeInTheDocument();
      expect(screen.getByText(`Media Viewer - ${TEST_IMAGE_SRC}`)).toBeInTheDocument();
    });

    it('calls closeViewer when media viewer is closed', () => {
      const mockCloseViewer = vi.fn();
      useEditorMediaViewerModule.default.mockImplementation(() => ({
        ...mockMediaViewerHook,
        isOpen: true,
        currentSrc: TEST_IMAGE_SRC,
        closeViewer: mockCloseViewer,
      }));

      renderWithMantine(<BlockNoteViewer {...defaultProps} />);

      const closeButton = screen.getByText('Close');
      fireEvent.click(closeButton);

      expect(mockCloseViewer).toHaveBeenCalled();
    });
  });

  describe('Content Handling', () => {
    it('handles markdown content', () => {
      renderWithMantine(
        <BlockNoteViewer {...defaultProps} initialHTML={TEST_MARKDOWN_CONTENT} isMarkdown={true} />
      );
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('handles HTML content', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} />);
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('handles empty content', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} initialHTML='' />);
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });
  });

  describe('CSS and Styling', () => {
    it('renders inline CSS when isUsingInlineCSS is true', () => {
      const { container } = renderWithMantine(
        <BlockNoteViewer {...defaultProps} isUsingInlineCSS={true} />
      );

      // Check if style element exists in the shadow root
      const shadowRoot = container.querySelector('[data-testid="shadow-root"]');
      expect(shadowRoot).toBeInTheDocument();

      // New assertion: style tag should exist when inline CSS is enabled
      expect(shadowRoot?.querySelector('style')).toBeInTheDocument();
    });

    it('does not inject inline CSS when isUsingInlineCSS is false', () => {
      const { container } = renderWithMantine(
        <BlockNoteViewer {...defaultProps} isUsingInlineCSS={false} />
      );

      const shadowRoot = container.querySelector('[data-testid="shadow-root"]');
      expect(shadowRoot).toBeInTheDocument();

      // There should be no style tag when inline CSS is disabled
      expect(shadowRoot?.querySelector('style')).not.toBeInTheDocument();
    });

    it('calls inlineEditorViewerCSS with correct arguments when hyperlinks use action color', () => {
      renderWithMantine(
        <BlockNoteViewer
          {...defaultProps}
          useActionColorAsHyperlink
          actionColor={TEST_ACTION_COLOR}
        />
      );

      expect(inlineEditorViewerCSS).toHaveBeenCalledWith(
        expect.objectContaining({
          actionColorLink: TEST_ACTION_COLOR,
        })
      );
    });

    it('applies shadow root ID correctly', () => {
      renderWithMantine(<BlockNoteViewer {...defaultProps} id={TEST_ID} />);

      expect(screen.getByTestId('blocknote-theme-provider')).toHaveAttribute(
        'data-shadow-root-id',
        `block-note-shadow-root${TEST_ID}`
      );
    });
  });

  // New tests targeting callback behaviour
  describe('onContentLoaded Callback', () => {
    it('invokes onContentLoaded immediately when there is no initial content', () => {
      const onContentLoaded = vi.fn();
      renderWithMantine(
        <BlockNoteViewer {...defaultProps} initialHTML='' onContentLoaded={onContentLoaded} />
      );
      // onContentLoaded should be called because there is no media to load
      expect(onContentLoaded).toHaveBeenCalled();
    });

    it('invokes onContentLoaded when provided and initialHTML is present', async () => {
      // Use fake timers to fast-forward the DOM_UPDATE_DELAY and fallback timeout
      vi.useFakeTimers();
      const onContentLoaded = vi.fn();

      renderWithMantine(<BlockNoteViewer {...defaultProps} onContentLoaded={onContentLoaded} />);

      // Fast forward timers to allow component side-effects to run
      vi.advanceTimersByTime(5000); // longer than CONTENT_LOADING_TIMEOUT

      expect(onContentLoaded).toHaveBeenCalled();

      vi.useRealTimers();
    });
  });

  describe('Edge Cases', () => {
    it('handles component unmounting cleanly', () => {
      const { unmount } = renderWithMantine(<BlockNoteViewer {...defaultProps} />);

      expect(() => unmount()).not.toThrow();
      expect(suppressWarningsModule.disableBlockNoteWarningSuppression).toHaveBeenCalled();
    });

    it('handles prop changes correctly', () => {
      const { unmount } = renderWithMantine(<BlockNoteViewer {...defaultProps} />);

      // Verify initial render
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();

      // Unmount and render with new props
      unmount();

      renderWithMantine(
        <BlockNoteViewer
          {...defaultProps}
          initialHTML='<p>Updated content</p>'
          textColor={TEST_TEXT_COLOR}
        />
      );

      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });
  });
});
