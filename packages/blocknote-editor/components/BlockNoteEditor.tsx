import { Box } from '@mantine/core';
import isNil from 'lodash/isNil';
import type React from 'react';
import { memo, useCallback, useEffect } from 'react';
import type { HTMLEditorProps } from '../types/blocknote';
import BlockNoteEditorRenderer from './BlockNoteEditorRenderer';
import BlockNoteThemeProvider from './BlockNoteThemeProvider';

import { useFilterSuggestions } from '../hooks';
import { useBlockNoteEditor } from '../hooks/useBlockNoteEditor';
import { useBlockNoteStyles } from '../hooks/useBlockNoteStyles';
import { loadEditorContent, serializeToHTML } from '../utils/editorContent';
import BlockNoteSchema from './BlockNoteSchema';

import '@blocknote/core/fonts/inter.css';
import '@blocknote/shadcn/style.css';

/**
 * BlockNoteEditor - A rich text editor component built on top of BlockNote
 *
 * @important CSS Import Required: You must import the CSS file in your main application file:
 * ```tsx
 * import '@resola-ai/blocknote-editor/styles.css';
 * ```
 *
 * @param props - HTMLEditorProps containing editor configuration
 * @returns React component for the BlockNote editor
 */
const BlockNoteEditor: React.FC<HTMLEditorProps> = (props) => {
  const {
    className,
    initialHTML = '',
    isBordered = true,
    isEditable = true,
    autoFocus = true,
    isMarkdown = false,
    usingCustomFormattingToolbar = true,
    usingCustomLinkToolbar = true,
    usingCustomFilePanel = true,
    usingCustomSuggestionVariable = false,
    variableSuggestions = [],
    language = 'en',
    uploadFile,
    onChange,
    onBlur,
    onFocus,
  } = props;

  const { filterVariableSuggestions } = useFilterSuggestions({
    variableSuggestions,
  });

  // Use the shared editor hook
  const { editor, editorRef, handleAutoFocus } = useBlockNoteEditor({
    schema: BlockNoteSchema,
    language,
    autoFocus,
    uploadFile,
    onBlur,
    onFocus,
  });

  const { classes, cx } = useBlockNoteStyles({ isBordered });

  /**
   * Handles the change event of the editor
   * Converts the editor's content to HTML and passes it to the onChange callback
   * @returns {void}
   */
  const handleEditorChange = useCallback(async () => {
    if (!onChange) return;

    // Use shared serialization utility
    const { html, plainText } = await serializeToHTML(editor);
    onChange(html, plainText);
  }, [editor, onChange]);

  /**
   * Load initial HTML content to the editor
   * @param htmlContent HTML content to load
   * @returns void
   */
  const loadInitialHTML = useCallback(
    async (htmlContent: string) => {
      // Use shared content loading utility
      const blocks = await loadEditorContent(editor, htmlContent, isMarkdown);
      editor.replaceBlocks(editor.document, blocks);

      if (autoFocus) {
        handleAutoFocus();
      }
    },
    [editor, autoFocus, isMarkdown, handleAutoFocus]
  );

  /**
   * Load initial HTML content on mount
   * @returns void
   */
  useEffect(() => {
    if (!isNil(initialHTML)) {
      loadInitialHTML(initialHTML);
    }
  }, [initialHTML, loadInitialHTML]);

  // ARIA attributes for accessibility
  const ariaAttributes = {
    'aria-multiline': 'true',
    'aria-hidden': 'false',
    'aria-disabled': !isEditable ? 'true' : 'false',
  };

  // Data attributes for the editor
  const dataAttributes = {
    'data-blocknote-editor': '',
  };

  return (
    <BlockNoteThemeProvider>
      <Box data-blocknote-container>
        <BlockNoteEditorRenderer
          ref={editorRef}
          editor={editor}
          className={cx(classes.editorContainer, className)}
          editable={isEditable}
          onChange={handleEditorChange}
          theme='light'
          formattingToolbarEnabled={usingCustomFormattingToolbar}
          linkToolbarEnabled={usingCustomLinkToolbar}
          filePanelEnabled={usingCustomFilePanel}
          sideMenuEnabled={false}
          suggestionVariableEnabled={usingCustomSuggestionVariable}
          variableSuggestionHandler={async (query) => filterVariableSuggestions(editor, query)}
          ariaAttributes={ariaAttributes}
          dataAttributes={dataAttributes}
          enabledNestedBlock={true}
          enabledTextAlignment={true}
        />
      </Box>
    </BlockNoteThemeProvider>
  );
};

export default memo(BlockNoteEditor);
