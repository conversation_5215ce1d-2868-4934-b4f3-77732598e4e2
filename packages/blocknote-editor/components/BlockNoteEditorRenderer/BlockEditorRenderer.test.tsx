import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithMantine } from '../../utils/unitTest';
import { BlockNoteEditorRenderer } from './BlockEditorRenderer';
import type { BlockNoteEditorRendererProps } from './BlockEditorRenderer';

// Mock all dependencies
vi.mock('@blocknote/react', () => ({
  FilePanelController: ({ filePanel: FilePanelComponent }) => (
    <div data-testid='file-panel-controller'>{FilePanelComponent && <FilePanelComponent />}</div>
  ),
  LinkToolbarController: ({ linkToolbar }) => (
    <div data-testid='link-toolbar-controller'>
      {linkToolbar &&
        React.createElement(linkToolbar, { title: 'test', url: 'https://example.com' })}
    </div>
  ),
  SideMenuController: ({ sideMenu }) => (
    <div data-testid='side-menu-controller'>
      {sideMenu &&
        React.createElement(sideMenu, {
          editor: { schema: { blockSchema: {} } },
          block: { type: 'paragraph', props: {} },
        })}
    </div>
  ),
  RemoveBlockItem: ({ children }) => <div data-testid='remove-block-item'>{children}</div>,
  DragHandleMenu: ({ children }) => <div data-testid='drag-handle-menu'>{children}</div>,
  SuggestionMenuController: ({ triggerCharacter, getItems }) => (
    <div
      data-testid='suggestion-menu-controller'
      data-trigger-character={triggerCharacter}
      onClick={() => getItems?.('test')}
      onKeyUp={(event) => event.key === 'Enter' && getItems?.('test')} // accessibility
    >
      Suggestion Menu
    </div>
  ),
}));

vi.mock('@blocknote/shadcn', () => ({
  BlockNoteView: (props) => {
    const {
      editor,
      className,
      editable,
      onChange,
      theme,
      formattingToolbar,
      linkToolbar,
      filePanel,
      sideMenu,
      children,
      ...restProps
    } = props;

    return (
      <div
        data-testid='blocknote-view'
        className={className}
        data-theme={theme}
        data-editable={editable ? 'true' : 'false'}
        data-formatting-toolbar={formattingToolbar ? 'true' : 'false'}
        data-link-toolbar={linkToolbar ? 'true' : 'false'}
        data-file-panel={filePanel ? 'true' : 'false'}
        data-side-menu={sideMenu ? 'true' : 'false'}
        onClick={onChange}
        {...restProps}
      >
        {children}
      </div>
    );
  },
}));

vi.mock('../CustomComponents', () => ({
  FilePanel: () => <div data-testid='file-panel'>File Panel</div>,
  LinkToolbar: () => <div data-testid='link-toolbar'>Link Toolbar</div>,
  SideMenu: vi.fn(({ dragHandleMenu }) => (
    <div data-testid='side-menu'>
      Side Menu
      {dragHandleMenu && React.createElement(dragHandleMenu, {})}
    </div>
  )),
}));

// Import the mocked SideMenu for test assertions
import { SideMenu } from '../CustomComponents';

vi.mock('../BlockNoteToolbar', () => ({
  default: ({ enabledNestedBlock, enabledTextAlignment }) => (
    <div
      data-testid='block-note-toolbar'
      data-nested-block={enabledNestedBlock ? 'true' : 'false'}
      data-text-alignment={enabledTextAlignment ? 'true' : 'false'}
    >
      Toolbar
    </div>
  ),
}));

// Mock editor
const mockEditor = {
  _tiptapEditor: {
    on: vi.fn(),
    off: vi.fn(),
    isFocused: false,
  },
  document: [],
  focus: vi.fn(),
  replaceBlocks: vi.fn(),
};

describe('BlockNoteEditorRenderer', () => {
  const defaultProps: BlockNoteEditorRendererProps = {
    editor: mockEditor as any,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render with default props', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toBeInTheDocument();
      expect(editorElement).toHaveAttribute('data-theme', 'light');
      expect(editorElement).toHaveAttribute('data-editable', 'true');
    });

    it('should render with custom className', () => {
      const customClass = 'custom-editor-class';
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} className={customClass} />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveClass(customClass);
    });

    it('should respect editable prop', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} editable={false} />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveAttribute('data-editable', 'false');
    });

    it('should apply custom theme', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} theme='dark' />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveAttribute('data-theme', 'dark');
    });

    it('should apply data attributes', () => {
      const dataAttributes = { 'data-test': 'value', 'data-custom': 'attribute' };
      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} dataAttributes={dataAttributes} />
      );

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveAttribute('data-test', 'value');
      expect(editorElement).toHaveAttribute('data-custom', 'attribute');
    });

    it('should apply aria attributes', () => {
      const ariaAttributes = { 'aria-label': 'Editor', 'aria-describedby': 'description' };
      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} ariaAttributes={ariaAttributes} />
      );

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveAttribute('aria-label', 'Editor');
      expect(editorElement).toHaveAttribute('aria-describedby', 'description');
    });
  });

  describe('Control Components', () => {
    it('should render formatting toolbar when enabled', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} formattingToolbarEnabled={true} />
      );

      expect(screen.getByTestId('block-note-toolbar')).toBeInTheDocument();
      expect(screen.getByTestId('blocknote-view')).toHaveAttribute(
        'data-formatting-toolbar',
        'false'
      );
    });

    it('should not render formatting toolbar when disabled', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} formattingToolbarEnabled={false} />
      );

      expect(screen.queryByTestId('block-note-toolbar')).not.toBeInTheDocument();
      expect(screen.getByTestId('blocknote-view')).toHaveAttribute(
        'data-formatting-toolbar',
        'true'
      );
    });

    it('should render link toolbar when enabled', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} linkToolbarEnabled={true} />);

      expect(screen.getByTestId('link-toolbar-controller')).toBeInTheDocument();
      expect(screen.getByTestId('blocknote-view')).toHaveAttribute('data-link-toolbar', 'false');
    });

    it('should not render link toolbar when disabled', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} linkToolbarEnabled={false} />);

      expect(screen.queryByTestId('link-toolbar-controller')).not.toBeInTheDocument();
      expect(screen.getByTestId('blocknote-view')).toHaveAttribute('data-link-toolbar', 'true');
    });

    it('should render file panel when enabled', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} filePanelEnabled={true} />);

      expect(screen.getByTestId('file-panel-controller')).toBeInTheDocument();
      expect(screen.getByTestId('blocknote-view')).toHaveAttribute('data-file-panel', 'false');
    });

    it('should not render file panel when disabled', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} filePanelEnabled={false} />);

      expect(screen.queryByTestId('file-panel-controller')).not.toBeInTheDocument();
      expect(screen.getByTestId('blocknote-view')).toHaveAttribute('data-file-panel', 'true');
    });

    it('should render side menu when enabled', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} sideMenuEnabled={true} />);

      expect(screen.getByTestId('side-menu-controller')).toBeInTheDocument();
      expect(screen.getByTestId('blocknote-view')).toHaveAttribute('data-side-menu', 'false');
    });

    it('should not render side menu when disabled', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} sideMenuEnabled={false} />);

      expect(screen.queryByTestId('side-menu-controller')).not.toBeInTheDocument();
      expect(screen.getByTestId('blocknote-view')).toHaveAttribute('data-side-menu', 'true');
    });
  });

  describe('Custom Components', () => {
    it('should use custom link toolbar when provided', () => {
      const CustomLinkToolbar = () => (
        <div data-testid='custom-link-toolbar'>Custom Link Toolbar</div>
      );

      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          linkToolbarEnabled={true}
          customLinkToolbar={CustomLinkToolbar}
        />
      );

      expect(screen.getByTestId('custom-link-toolbar')).toBeInTheDocument();
      expect(screen.queryByTestId('link-toolbar')).not.toBeInTheDocument();
    });

    it('should use custom file panel when provided', () => {
      const CustomFilePanel = () => <div data-testid='custom-file-panel'>Custom File Panel</div>;

      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          filePanelEnabled={true}
          customFilePanel={CustomFilePanel}
        />
      );

      expect(screen.getByTestId('custom-file-panel')).toBeInTheDocument();
      expect(screen.queryByTestId('file-panel')).not.toBeInTheDocument();
    });

    it('should use custom side menu when provided', () => {
      const CustomSideMenu = () => <div data-testid='custom-side-menu'>Custom Side Menu</div>;

      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          sideMenuEnabled={true}
          customSideMenu={CustomSideMenu}
        />
      );

      expect(screen.getByTestId('side-menu-controller')).toBeInTheDocument();
    });
  });

  describe('Toolbar Configuration', () => {
    it('should pass nested block configuration to toolbar', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          formattingToolbarEnabled={true}
          enabledNestedBlock={false}
        />
      );

      const toolbar = screen.getByTestId('block-note-toolbar');
      expect(toolbar).toHaveAttribute('data-nested-block', 'false');
    });

    it('should pass text alignment configuration to toolbar', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          formattingToolbarEnabled={true}
          enabledTextAlignment={false}
        />
      );

      const toolbar = screen.getByTestId('block-note-toolbar');
      expect(toolbar).toHaveAttribute('data-text-alignment', 'false');
    });

    it('should enable nested block by default', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} formattingToolbarEnabled={true} />
      );

      const toolbar = screen.getByTestId('block-note-toolbar');
      expect(toolbar).toHaveAttribute('data-nested-block', 'true');
    });

    it('should enable text alignment by default', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} formattingToolbarEnabled={true} />
      );

      const toolbar = screen.getByTestId('block-note-toolbar');
      expect(toolbar).toHaveAttribute('data-text-alignment', 'true');
    });
  });

  describe('Variable Suggestions', () => {
    it('should use custom trigger character when suggestions enabled', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          suggestionVariableEnabled={true}
          variableTriggerChar='@'
        />
      );

      const suggestionMenu = screen.getByTestId('suggestion-menu-controller');
      expect(suggestionMenu).toHaveAttribute('data-trigger-character', '@');
    });

    it('should use default trigger character when suggestions enabled and not provided', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} suggestionVariableEnabled={true} />
      );

      const suggestionMenu = screen.getByTestId('suggestion-menu-controller');
      expect(suggestionMenu).toHaveAttribute('data-trigger-character', '{');
    });

    it('should not render suggestion menu when suggestions disabled', () => {
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} />);

      expect(screen.queryByTestId('suggestion-menu-controller')).not.toBeInTheDocument();
    });

    it('should call variable suggestion handler when suggestions enabled', async () => {
      const mockSuggestionHandler = vi.fn().mockResolvedValue([
        { id: 1, text: 'variable1' },
        { id: 2, text: 'variable2' },
      ]);

      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          suggestionVariableEnabled={true}
          variableSuggestionHandler={mockSuggestionHandler}
        />
      );

      const suggestionMenu = screen.getByTestId('suggestion-menu-controller');
      fireEvent.click(suggestionMenu);

      expect(mockSuggestionHandler).toHaveBeenCalledWith('test');
    });

    it('should return empty array when suggestions disabled', async () => {
      const mockSuggestionHandler = vi.fn().mockResolvedValue([{ id: 1, text: 'variable1' }]);

      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          suggestionVariableEnabled={false}
          variableSuggestionHandler={mockSuggestionHandler}
        />
      );

      expect(screen.queryByTestId('suggestion-menu-controller')).not.toBeInTheDocument();
      expect(mockSuggestionHandler).not.toHaveBeenCalled();
    });

    it('should handle suggestion handler errors gracefully', async () => {
      const mockSuggestionHandler = vi.fn().mockRejectedValue(new Error('Suggestion error'));
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          suggestionVariableEnabled={true}
          variableSuggestionHandler={mockSuggestionHandler}
        />
      );

      const suggestionMenu = screen.getByTestId('suggestion-menu-controller');
      fireEvent.click(suggestionMenu);

      // Wait for async operation
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(consoleSpy).toHaveBeenCalledWith(
        'Error in variable suggestion handler:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Event Handling', () => {
    it('should handle onChange event', () => {
      const mockOnChange = vi.fn();
      renderWithMantine(<BlockNoteEditorRenderer {...defaultProps} onChange={mockOnChange} />);

      const editorElement = screen.getByTestId('blocknote-view');
      fireEvent.click(editorElement);

      expect(mockOnChange).toHaveBeenCalled();
    });
  });

  describe('Dictionary Support', () => {
    it('should pass dictionary to side menu wrapper when provided', () => {
      const dictionary = {
        drag_handle: {
          delete_menuitem: 'Delete Block',
        },
      };

      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} sideMenuEnabled={true} dictionary={dictionary} />
      );

      // The side menu controller should be rendered
      expect(screen.getByTestId('side-menu-controller')).toBeInTheDocument();

      // The SideMenu should be called and the dictionary should be used for the delete menu item text
      expect(SideMenu).toHaveBeenCalledWith(
        expect.objectContaining({
          dragHandleMenu: expect.any(Function),
        }),
        expect.anything()
      );

      // Verify that the dragHandleMenu function uses the dictionary text
      expect(screen.getByTestId('remove-block-item')).toHaveTextContent('Delete Block');
    });

    it('should handle empty dictionary gracefully', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} sideMenuEnabled={true} dictionary={{}} />
      );

      expect(screen.getByTestId('side-menu-controller')).toBeInTheDocument();

      // The SideMenu should be called
      expect(SideMenu).toHaveBeenCalledWith(
        expect.objectContaining({
          dragHandleMenu: expect.any(Function),
        }),
        expect.anything()
      );

      // With empty dictionary, the remove block item should have empty text
      expect(screen.getByTestId('remove-block-item')).toHaveTextContent('');
    });
  });

  describe('Control Element Wrapping', () => {
    it('should wrap formatting toolbar with specific data attribute', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer {...defaultProps} formattingToolbarEnabled={true} />
      );

      const formattingToolbar = document.querySelector('[data-editor-control-formatting-toolbar]');
      expect(formattingToolbar).toBeInTheDocument();
      expect(formattingToolbar).toContainElement(screen.getByTestId('block-note-toolbar'));
    });

    it('should render control components when enabled', () => {
      renderWithMantine(
        <BlockNoteEditorRenderer
          {...defaultProps}
          formattingToolbarEnabled={true}
          linkToolbarEnabled={true}
          filePanelEnabled={true}
          sideMenuEnabled={true}
        />
      );

      // Check that all control components are rendered
      expect(screen.getByTestId('block-note-toolbar')).toBeInTheDocument();
      expect(screen.getByTestId('link-toolbar-controller')).toBeInTheDocument();
      expect(screen.getByTestId('file-panel-controller')).toBeInTheDocument();
      expect(screen.getByTestId('side-menu-controller')).toBeInTheDocument();
    });
  });
});
