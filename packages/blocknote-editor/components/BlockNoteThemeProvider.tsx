import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import type { FC, ReactNode } from 'react';
import { themeConfigurations } from '../constants/theme';

interface BlockNoteThemeProviderProps {
  children: ReactNode;
  shadowRootId?: string;
}

/**
 * A wrapper component that provides Mantine theming context to its children.
 * This is used internally by BlockNote components and can be used by consumers
 * to ensure consistent theming if they're already using Mantine in their app.
 */
const BlockNoteThemeProvider: FC<BlockNoteThemeProviderProps> = ({ children, shadowRootId }) => {
  return (
    <MantineProvider
      stylesTransform={emotionTransform}
      theme={themeConfigurations}
      forceColorScheme='light'
      withCssVariables={false}
      cssVariablesSelector={shadowRootId ? `#${shadowRootId}` : undefined}
    >
      <MantineEmotionProvider>{children}</MantineEmotionProvider>
    </MantineProvider>
  );
};

export default BlockNoteThemeProvider;
