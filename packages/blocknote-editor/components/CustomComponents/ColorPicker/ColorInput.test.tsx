import { fireEvent, render, screen } from '@testing-library/react';
import { beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
// userEvent is avoided here to prevent navigator.clipboard redefinition errors

// -----------------------------------------------------------------------------
// Ensure matchMedia is available for Mantine internals before any component is imported
// -----------------------------------------------------------------------------

beforeAll(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: (query: string) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: () => {},
      removeListener: () => {},
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => false,
    }),
  });
});

// -----------------------------------------------------------------------------
// Mocks
// -----------------------------------------------------------------------------

vi.mock('@blocknote/react', () => ({
  useDictionary: () => ({
    custom: {
      pickColor: 'Pick a color',
    },
  }),
}));

vi.mock('@mantine/hooks', async () => {
  const actual = await vi.importActual<any>('@mantine/hooks');
  return {
    ...actual,
    useMediaQuery: () => false,
  };
});

// -----------------------------------------------------------------------------
// Utilities
// -----------------------------------------------------------------------------

import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import type React from 'react';

// Helper renderer with custom theme including required Deca colors
const getRenderWithMantine = () => {
  const customColors = {
    decaNavy: Array(10).fill('#001f3f'),
    decaGrey: Array(10).fill('#aaaaaa'),
  };

  return (ui: React.ReactNode) =>
    render(
      <MantineProvider theme={{ colors: customColors }}>
        <MantineEmotionProvider>{ui}</MantineEmotionProvider>
      </MantineProvider>
    );
};

// Dynamic import helper so that mocks & matchMedia are applied first
const getColorInput = async () => {
  const [{ default: ColorInput }] = await Promise.all([import('./ColorInput')]);
  return ColorInput as React.FC<{ value: string; onChange: (c: string) => void }>;
};

// Common setup for each test case
const INITIAL_COLOR = '#ffffff';
const NEW_COLOR = '#123456';
const setup = async () => {
  const onChange = vi.fn();
  const ColorInput = await getColorInput();
  const renderWithMantine = getRenderWithMantine();
  renderWithMantine(<ColorInput value={INITIAL_COLOR} onChange={onChange} />);
  const input = screen.getByRole('textbox') as HTMLInputElement;
  return { input, onChange };
};

// -----------------------------------------------------------------------------
// Tests
// -----------------------------------------------------------------------------

describe('ColorInput', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('renders with placeholder from dictionary and initial value', async () => {
    const { input } = await setup();
    expect(input).toBeInTheDocument();
    expect(input.placeholder).toBe('Pick a color');
    expect(input.value).toBe(INITIAL_COLOR);
  });

  it('updates internal value when user types and calls onChange at the end', async () => {
    const { input, onChange } = await setup();

    // Update input value directly
    fireEvent.change(input, { target: { value: NEW_COLOR } });

    // Blur to trigger onChangeEnd inside Mantine ColorInput
    fireEvent.blur(input);

    expect(input.value).toBe(NEW_COLOR);
    expect(onChange).toHaveBeenCalledWith(NEW_COLOR);
  });
});
