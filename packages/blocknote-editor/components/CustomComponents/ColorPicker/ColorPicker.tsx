import { useDictionary } from '@blocknote/react';
import { Box, Flex, Text, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck } from '@tabler/icons-react';
/**
 * This file is a custom Color Picker Component for BlockNote Editor.
 * Cloned from BlockNote repo:
 * `packages/react/src/components/ColorPicker/ColorPicker.tsx`
 */
import { useCallback, useMemo, useState } from 'react';

import { DEFAULT_COLORS } from '../../../constants/color';
import SettingActions from '../SettingActions';
import ColorIcon from './ColorIcon';
import ColorInput from './ColorInput';

const useStyles = createStyles((theme) => ({
  backgroundSection: {
    paddingLeft: theme.spacing.xs,
  },
  title: {
    '&.mantine-Title-root': {
      fontWeight: 600,
      lineHeight: rem(theme.fontSizes.md),
      fontSize: `${theme.fontSizes.md}`,
      paddingLeft: theme.spacing.xxs,
      marginBottom: `${theme.spacing.xs}`,
    },
  },
  colorInputBox: {
    padding: rem(0),
    margin: `${theme.spacing.xs} 0`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  colorItem: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: theme.spacing.xxs,
    cursor: 'pointer',
    fontSize: theme.fontSizes.md,
  },
  colorIcon: {
    display: 'flex',
    marginRight: theme.spacing.xs,
  },
  checkedIcon: {
    width: rem(16),
    marginRight: rem(2),
    svg: {
      color: theme.colors.decaBlue[6],
    },
  },
}));

const ColorPicker = (props: {
  onClick?: () => void;
  iconSize?: number;
  text?: {
    color: string;
    setColor: (color: string) => void;
  };
  background?: {
    color: string;
    setColor: (color: string) => void;
  };
}) => {
  const dict = useDictionary();

  const [textColor, setTextColor] = useState<string>(props.text?.color || '');
  const [backgroundColor, setBackgroundColor] = useState<string>(props.background?.color || '');
  const { classes } = useStyles();

  /**
   * Apply colors to selection text
   * @returns void
   */
  const applyColor = useCallback(() => {
    props.text!.setColor(textColor);
    props.background!.setColor(backgroundColor);
    props.onClick?.();
  }, [props, textColor, backgroundColor]);

  /**
   * Cancel color selection
   * @returns void
   */
  const cancelColor = useCallback(() => {
    props.onClick?.();
  }, [props]);

  /**
   * Check if color is default
   * @param color - color to check
   * @returns boolean
   */
  const isDefaultColor = useCallback((color: string) => DEFAULT_COLORS.includes(color), []);

  /**
   * Default text color section
   * @returns JSX.Element
   */
  const TextColorSection = useMemo(
    () =>
      props.text ? (
        <Box>
          <Title className={classes.title} order={5}>
            {dict.color_picker.text_title}
          </Title>
          <Box className={classes.colorInputBox}>
            <span className={classes.checkedIcon}>
              {textColor && !isDefaultColor(textColor) && <IconCheck size={14} />}
            </span>
            <ColorInput
              value={isDefaultColor(textColor) ? '' : textColor}
              onChange={(color: string) => {
                setTextColor(color);
              }}
            />
          </Box>
          <Box>
            {DEFAULT_COLORS.map((color) => (
              <Text
                key={`text-color-${color}`}
                className={classes.colorItem}
                onClick={() => {
                  setTextColor(color);
                }}
              >
                <span className={classes.checkedIcon}>
                  {textColor === color && <IconCheck size={14} />}
                </span>
                <ColorIcon className={classes.colorIcon} textColor={color} size={props.iconSize} />
                {dict.color_picker.colors[color]}
              </Text>
            ))}
          </Box>
        </Box>
      ) : null,
    [
      textColor,
      props.text,
      props.iconSize,
      dict.color_picker.colors,
      dict.color_picker.text_title,
      classes,
      isDefaultColor,
    ]
  );

  /**
   * Default background color section
   * @returns JSX.Element
   */
  const BackgroundColorSection = useMemo(
    () =>
      props.background ? (
        <Box className={classes.backgroundSection}>
          <Title className={classes.title} order={5}>
            {dict.color_picker.background_title}
          </Title>
          <Box className={classes.colorInputBox}>
            <span className={classes.checkedIcon}>
              {backgroundColor && !isDefaultColor(backgroundColor) && <IconCheck size={14} />}
            </span>
            <ColorInput
              value={isDefaultColor(backgroundColor) ? '' : backgroundColor}
              onChange={(color: string) => {
                setBackgroundColor(color);
              }}
            />
          </Box>

          <Box>
            {DEFAULT_COLORS.map((color) => (
              <Text
                key={`background-color-${color}`}
                className={classes.colorItem}
                onClick={() => {
                  setBackgroundColor(color);
                }}
              >
                <span className={classes.checkedIcon}>
                  {backgroundColor === color && <IconCheck size={14} />}
                </span>
                <ColorIcon
                  className={classes.colorIcon}
                  backgroundColor={color}
                  size={props.iconSize}
                />
                {dict.color_picker.colors[color]}
              </Text>
            ))}
          </Box>
        </Box>
      ) : null,
    [
      backgroundColor,
      props.background,
      props.iconSize,
      dict.color_picker.colors,
      dict.color_picker.background_title,
      classes,
      isDefaultColor,
    ]
  );

  return (
    <Box>
      <Flex gap={rem(4)}>
        {TextColorSection}
        {BackgroundColorSection}
      </Flex>
      <SettingActions onApply={applyColor} onCancel={cancelColor} />
    </Box>
  );
};

export default ColorPicker;
