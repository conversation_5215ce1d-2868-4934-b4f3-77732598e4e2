// Ensure matchMedia is defined for <PERSON><PERSON>'s useMediaQuery
import { fireEvent, screen } from '@testing-library/react';
import type React from 'react';
// Add comprehensive unit tests for ColorPicker component
import { beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';

// Test utilities (will be imported dynamically to ensure matchMedia is defined first)
import { render } from '@testing-library/react';

// -----------------------------------------------------------------------------
// Ensure matchMedia is always defined before <PERSON><PERSON> or other libraries access it
// -----------------------------------------------------------------------------

beforeAll(() => {
  // Unconditionally define window.matchMedia stub compatible with Mantine hooks
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: (query: string) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: () => {},
      removeListener: () => {},
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => false,
    }),
  });
});

// Dynamic imports helper
const getRenderWithMantine = async () => {
  const [{ MantineProvider }, { MantineEmotionProvider }] = await Promise.all([
    import('@mantine/core'),
    import('@mantine/emotion'),
  ]);

  const customColors = {
    decaBlue: Array(10).fill('#1e90ff'),
    decaNavy: Array(10).fill('#001f3f'),
    decaGrey: Array(10).fill('#aaaaaa'),
    decaMono: Array(10).fill('#ffffff'),
  };

  return (ui: React.ReactNode) =>
    render(
      <MantineProvider theme={{ colors: customColors }}>
        <MantineEmotionProvider>{ui}</MantineEmotionProvider>
      </MantineProvider>
    );
};

// -----------------------------------------------------------------------------
// Mock dictionary returned by `useDictionary` from @blocknote/react
// -----------------------------------------------------------------------------

// Create a dictionary covering all keys used in ColorPicker & SettingActions
const MOCK_DICT = {
  color_picker: {
    text_title: 'Text',
    background_title: 'Background',
    // Map every DEFAULT_COLORS entry to a readable label
    colors: {
      default: 'Default',
      gray: 'Gray',
      brown: 'Brown',
      red: 'Red',
      orange: 'Orange',
      yellow: 'Yellow',
      green: 'Green',
      blue: 'Blue',
      purple: 'Purple',
      pink: 'Pink',
    },
  },
  custom: {
    actions: {
      cancel: 'Cancel',
      apply: 'Apply',
    },
  },
};

vi.mock('@blocknote/react', () => ({
  useDictionary: () => MOCK_DICT,
}));

vi.mock('@mantine/hooks', async () => {
  const actual = await vi.importActual<any>('@mantine/hooks');
  return {
    ...actual,
    useMediaQuery: () => false,
  };
});

// -----------------------------------------------------------------------------
// Helper to render the component with default props
// -----------------------------------------------------------------------------
const setup = async (props?: Record<string, unknown>) => {
  const [{ default: ColorPicker }] = await Promise.all([import('./ColorPicker')]);
  const renderWithMantine = await getRenderWithMantine();

  const defaultProps = {
    onClick: vi.fn(),
    text: {
      color: 'red',
      setColor: vi.fn(),
    },
    background: {
      color: 'blue',
      setColor: vi.fn(),
    },
    iconSize: 16,
  } as const;

  const utils = renderWithMantine(<ColorPicker {...defaultProps} {...props} />);
  return {
    ...utils,
    defaultProps,
  };
};

// -----------------------------------------------------------------------------
// Tests
// -----------------------------------------------------------------------------

describe('ColorPicker', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('renders text and background sections when both props are provided', async () => {
    await setup();

    // Titles should be rendered according to dictionary
    expect(screen.getByText('Text')).toBeInTheDocument();
    expect(screen.getByText('Background')).toBeInTheDocument();
  });

  it('updates color selections and applies them when the Apply button is clicked', async () => {
    const { defaultProps } = await setup();

    // Select new colors
    // Click on the first "Green" option (Text section)
    fireEvent.click(screen.getAllByText('Green')[0]);
    // Click on the second "Yellow" option (Background section)
    fireEvent.click(screen.getAllByText('Yellow')[1]);

    // Apply changes
    fireEvent.click(screen.getByRole('button', { name: 'Apply' }));

    // Text color should be updated to "green"
    expect(defaultProps.text.setColor).toHaveBeenCalledWith('green');
    // Background color should be updated to "yellow"
    expect(defaultProps.background.setColor).toHaveBeenCalledWith('yellow');
    // onClick callback should be invoked after applying
    expect(defaultProps.onClick).toHaveBeenCalled();
  });

  it('does not update colors when Cancel is clicked, but triggers onClick', async () => {
    const { defaultProps } = await setup();

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    // Color setters should NOT be called
    expect(defaultProps.text.setColor).not.toHaveBeenCalled();
    expect(defaultProps.background.setColor).not.toHaveBeenCalled();
    // onClick should be called once
    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  it('renders only text section when background prop is not provided', async () => {
    await setup({ background: undefined });

    expect(screen.getByText('Text')).toBeInTheDocument();
    // Background section should not exist
    expect(screen.queryByText('Background')).not.toBeInTheDocument();
  });

  it('renders only background section when text prop is not provided', async () => {
    await setup({ text: undefined });

    expect(screen.getByText('Background')).toBeInTheDocument();
    // Text section should not exist
    expect(screen.queryByText('Text')).not.toBeInTheDocument();
  });
});
