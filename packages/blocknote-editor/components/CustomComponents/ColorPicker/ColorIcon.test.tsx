import { render, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it } from 'vitest';

import { MantineWrapper } from '../../../utils/unitTest';
import ColorIcon from './ColorIcon';

// -----------------------------------------------------------------------------
// Test Constants
// -----------------------------------------------------------------------------
const DEFAULT_DATA_TEXT_COLOR = 'default';
const DEFAULT_DATA_BACKGROUND_COLOR = 'default';
const CUSTOM_TEXT_COLOR = '#FF0000';
const CUSTOM_BACKGROUND_COLOR = '#00FF00';
const CUSTOM_CLASS_NAME = 'custom-class-name';

// -----------------------------------------------------------------------------
// Test Suite
// -----------------------------------------------------------------------------

describe('ColorIcon component', () => {
  beforeEach(() => {
    // Ensure a clean DOM for every test
    document.body.innerHTML = '';
  });

  describe('Default Rendering', () => {
    it('renders the component with default props', () => {
      render(<ColorIcon />, { wrapper: MantineWrapper });

      const iconElement = screen.getByText('A');

      expect(iconElement).toBeInTheDocument();
      expect(iconElement).toHaveClass('bn-color-icon');
      expect(iconElement).toHaveAttribute('data-text-color', DEFAULT_DATA_TEXT_COLOR);
      expect(iconElement).toHaveAttribute('data-background-color', DEFAULT_DATA_BACKGROUND_COLOR);
    });
  });

  describe('Props Handling', () => {
    it('applies custom textColor and backgroundColor props correctly', () => {
      render(
        <ColorIcon textColor={CUSTOM_TEXT_COLOR} backgroundColor={CUSTOM_BACKGROUND_COLOR} />,
        { wrapper: MantineWrapper }
      );

      const iconElement = screen.getByText('A');
      expect(iconElement).toHaveAttribute('data-text-color', CUSTOM_TEXT_COLOR);
      expect(iconElement).toHaveAttribute('data-background-color', CUSTOM_BACKGROUND_COLOR);
    });

    it('merges provided className with default classes', () => {
      render(<ColorIcon className={CUSTOM_CLASS_NAME} />, { wrapper: MantineWrapper });

      const iconElement = screen.getByText('A');

      // Should contain both default and custom class names
      expect(iconElement.classList.contains('bn-color-icon')).toBe(true);
      expect(iconElement.classList.contains(CUSTOM_CLASS_NAME)).toBe(true);
    });

    it('handles undefined optional props gracefully', () => {
      render(<ColorIcon textColor={undefined} backgroundColor={undefined} size={undefined} />, {
        wrapper: MantineWrapper,
      });

      const iconElement = screen.getByText('A');

      // Should fall back to default dataset values
      expect(iconElement).toHaveAttribute('data-text-color', DEFAULT_DATA_TEXT_COLOR);
      expect(iconElement).toHaveAttribute('data-background-color', DEFAULT_DATA_BACKGROUND_COLOR);
    });
  });
});
