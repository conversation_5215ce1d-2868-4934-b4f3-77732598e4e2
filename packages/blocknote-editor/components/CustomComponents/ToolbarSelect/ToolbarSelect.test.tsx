import { IconH1, IconH2, IconLetterT } from '@tabler/icons-react';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithMantine } from '../../../utils/unitTest';
import { ToolbarSelect, type ToolbarSelectItem } from './ToolbarSelect';

// Mock Mantine components to prevent provider errors
vi.mock('@mantine/core', () => ({
  MantineProvider: ({ children }) => <div data-testid='mantine-provider'>{children}</div>,
  Box: ({ children, className, ...props }) => (
    <div className={className} data-testid='mantine-box' {...props}>
      {children}
    </div>
  ),
  useMantineTheme: () => ({
    colors: {},
    primaryColor: 'blue',
    colorScheme: 'light',
  }),
}));

vi.mock('@mantine/emotion', () => ({
  MantineEmotionProvider: ({ children }) => (
    <div data-testid='mantine-emotion-provider'>{children}</div>
  ),
  emotionTransform: { type: 'selector-transform' },
  createStyles: () => () => ({
    classes: {
      selectButton: 'mock-select-button',
      buttonContent: 'mock-button-content',
      menuItem: 'mock-menu-item',
    },
    cx: (...classNames) => classNames.filter(Boolean).join(' '),
  }),
}));

// Constants for test data
const TEST_PLACEHOLDER = 'Select an option';
const TEST_ITEMS: ToolbarSelectItem[] = [
  {
    key: 'paragraph',
    text: 'Paragraph',
    icon: <IconLetterT size={16} />,
    onClick: vi.fn(),
    isSelected: false,
  },
  {
    key: 'heading1',
    text: 'Heading 1',
    icon: <IconH1 size={16} />,
    onClick: vi.fn(),
    isSelected: true,
  },
  {
    key: 'heading2',
    text: 'Heading 2',
    icon: <IconH2 size={16} />,
    onClick: vi.fn(),
    isSelected: false,
  },
];

// Mock Mantine components
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');

  const MenuMock = (props: { children: React.ReactNode }) => {
    return <div data-testid='mantine-menu'>{props.children}</div>;
  };

  MenuMock.Target = (props: { children: React.ReactNode }) => (
    <div data-testid='menu-target'>{props.children}</div>
  );

  MenuMock.Dropdown = (props: { children: React.ReactNode }) => (
    <div data-testid='menu-dropdown'>
      {/* Wrap menu items to make them visible in tests */}
      <div data-testid='menu-items-container'>{props.children}</div>
    </div>
  );

  // Each Menu.Item will render with text content for testing
  MenuMock.Item = (props: {
    children: React.ReactNode;
    onClick?: () => void;
    leftSection?: React.ReactNode;
    fw?: number;
  }) => {
    return (
      <div
        data-testid='menu-item'
        onClick={props.onClick}
        onKeyUp={(e) => e.key === 'Enter' && props.onClick()}
        className='menu-item'
      >
        {props.leftSection && <span className='left-section'>{props.leftSection}</span>}
        <span className='item-text'>{props.children}</span>
      </div>
    );
  };

  return {
    ...actual,
    MantineProvider: ({ children }) => <div data-testid='mantine-provider'>{children}</div>,
    Box: ({ children, className, ...props }) => (
      <div className={className} data-testid='mantine-box' {...props}>
        {children}
      </div>
    ),
    useMantineTheme: () => ({
      colors: {},
      primaryColor: 'blue',
      colorScheme: 'light',
    }),
    Menu: MenuMock,
    Button: (props: {
      children: React.ReactNode;
      className?: string;
      rightSection?: React.ReactNode;
    }) => (
      <button data-testid='toolbar-button' type='button' className={props.className || ''}>
        {props.rightSection && <span className='right-section'>{props.rightSection}</span>}
        <span className='button-content'>{props.children}</span>
      </button>
    ),
  };
});

describe('ToolbarSelect', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with placeholder when no item is selected', () => {
    const items = TEST_ITEMS.map((item) => ({ ...item, isSelected: false }));

    renderWithMantine(<ToolbarSelect placeholder={TEST_PLACEHOLDER} items={items} />);

    const button = screen.getByTestId('toolbar-button');
    expect(button).toHaveTextContent(TEST_PLACEHOLDER);
  });

  it('renders with selected item text when an item is selected', () => {
    renderWithMantine(<ToolbarSelect placeholder={TEST_PLACEHOLDER} items={TEST_ITEMS} />);

    const button = screen.getByTestId('toolbar-button');
    expect(button).toHaveTextContent('Heading 1');
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-select';

    renderWithMantine(
      <ToolbarSelect placeholder={TEST_PLACEHOLDER} items={TEST_ITEMS} className={customClass} />
    );

    const button = screen.getByTestId('toolbar-button');
    expect(button.className).toContain(customClass);
  });

  it('renders all menu items', () => {
    renderWithMantine(<ToolbarSelect placeholder={TEST_PLACEHOLDER} items={TEST_ITEMS} />);

    // Get the dropdown to check it exists
    const dropdown = screen.getByTestId('menu-dropdown');
    expect(dropdown).toBeInTheDocument();

    // Check the container exists
    const container = screen.getByTestId('menu-items-container');
    expect(container).toBeInTheDocument();

    // Check it has the expected number of items
    const menuItems = screen.getAllByTestId('menu-item');
    expect(menuItems).toHaveLength(TEST_ITEMS.length);

    // Verify the content of items
    TEST_ITEMS.forEach((item) => {
      const hasItemWithText = menuItems.some((menuItem) =>
        menuItem.textContent?.includes(item.text)
      );
      expect(hasItemWithText).toBe(true);
    });
  });

  it('calls onClick handler when menu item is clicked', () => {
    renderWithMantine(<ToolbarSelect placeholder={TEST_PLACEHOLDER} items={TEST_ITEMS} />);

    // Get all menu items
    const menuItems = screen.getAllByTestId('menu-item');

    // Click the first one
    fireEvent.click(menuItems[0]);

    // Verify the first item's onClick was called
    expect(TEST_ITEMS[0].onClick).toHaveBeenCalledTimes(1);
  });
});
