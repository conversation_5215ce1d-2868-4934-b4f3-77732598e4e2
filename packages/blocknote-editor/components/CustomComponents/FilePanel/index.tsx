import type {
  BlockSchema,
  DefaultBlockSchema,
  DefaultInlineContentSchema,
  DefaultStyleSchema,
  InlineContentSchema,
  StyleSchema,
} from '@blocknote/core';
import { useBlockNoteEditor, useComponentsContext, useDictionary } from '@blocknote/react';
import type { ComponentProps, FilePanelProps } from '@blocknote/react';
/**
 * Override the default FilePanel component from BlockNoteEditor
 * the original component is located at @blocknote/react/src/components/FilePanel/FilePanel.tsx
 */
import { useMemo, useState } from 'react';
import { SUPPORTED_UPLOAD_BLOCKS } from '../../../constants/file';
import { EmbedTab } from './EmbedTab';
import { UploadTab } from './UploadTab';

type PanelProps = ComponentProps['FilePanel']['Root'];

/**
 * By default, the FilePanel component will render with default tabs. However,
 * you can override the tabs to render by passing the `tabs` prop. You can use
 * the default tab panels in the `DefaultTabPanels` directory or make your own
 * using the `FilePanelPanel` component.
 */
export const FilePanel = <
  B extends BlockSchema = DefaultBlockSchema,
  I extends InlineContentSchema = DefaultInlineContentSchema,
  S extends StyleSchema = DefaultStyleSchema,
>(
  props: FilePanelProps<I, S> & Partial<Pick<PanelProps, 'defaultOpenTab' | 'tabs'>>
) => {
  const Components = useComponentsContext()!;
  const dict = useDictionary();
  const editor = useBlockNoteEditor<B, I, S>();
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * The tabs to render in the FilePanel component. By default, the FilePanel
   * component will render the UploadTab and EmbedTab. You can override the tabs via props
   */
  const tabs: PanelProps['tabs'] = useMemo(() => {
    const isShowUploadTab =
      editor.uploadFile !== undefined && SUPPORTED_UPLOAD_BLOCKS.includes(props.block?.type);

    const UploadTabs = isShowUploadTab
      ? [
          {
            name: dict.file_panel.upload.title,
            tabPanel: <UploadTab block={props.block} setLoading={setLoading} />,
          },
        ]
      : [];

    return (
      props.tabs ?? [
        ...UploadTabs,
        {
          name: dict.file_panel.embed.title,
          tabPanel: <EmbedTab block={props.block} />,
        },
      ]
    );
  }, [
    props.tabs,
    editor.uploadFile,
    props.block,
    dict.file_panel.upload.title,
    dict.file_panel.embed.title,
  ]);

  const [openTab, setOpenTab] = useState<string>(props.defaultOpenTab || tabs[0]?.name);

  return (
    <Components.FilePanel.Root
      className={'bn-panel'}
      defaultOpenTab={openTab}
      openTab={openTab}
      setOpenTab={setOpenTab}
      tabs={tabs}
      loading={loading}
    />
  );
};
