import { screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithMantine } from '../../../utils/unitTest';

// -----------------------------------------------------------------------------
// Mocks
// -----------------------------------------------------------------------------

// Variable that controls whether the mocked editor exposes an `uploadFile` fn
let isUploadFileAvailable = true;

// Mock @blocknote/react hooks & Components context used by FilePanel
vi.mock('@blocknote/react', () => {
  const React = require('react');
  return {
    // Expose an editor object with (or without) the `uploadFile` method
    useBlockNoteEditor: () => (isUploadFileAvailable ? { uploadFile: vi.fn() } : {}),

    // Provide minimal Components context – only the pieces FilePanel needs
    useComponentsContext: () => ({
      FilePanel: {
        Root: ({ tabs }: any) => (
          <div data-testid='file-panel-root'>
            {tabs.map((tab: any) => (
              <div key={tab.name} data-testid='tab-name'>
                {tab.name}
              </div>
            ))}
          </div>
        ),
      },
    }),

    // Simplified i18n dictionary with just the keys FilePanel reads
    useDictionary: () => ({
      file_panel: {
        upload: { title: 'Upload' },
        embed: { title: 'Embed' },
      },
    }),
  };
});

// Stub out child tab components – we only care that FilePanel includes them
vi.mock('./UploadTab', () => ({
  UploadTab: () => <div data-testid='upload-tab-render' />,
}));
vi.mock('./EmbedTab', () => ({
  EmbedTab: () => <div data-testid='embed-tab-render' />,
}));

// -----------------------------------------------------------------------------
// Test constants
// -----------------------------------------------------------------------------

const MOCK_IMAGE_BLOCK = { id: 'block-image', type: 'image' } as any;
const MOCK_FILE_BLOCK = { id: 'block-file', type: 'file' } as any;

// -----------------------------------------------------------------------------
// Tests
// -----------------------------------------------------------------------------

describe('FilePanel component', () => {
  // Reset mocks and default state before each test
  beforeEach(() => {
    vi.resetAllMocks();
    isUploadFileAvailable = true;
  });

  it('renders both Upload and Embed tabs when uploads are supported', async () => {
    // Ensure the editor has an uploadFile function and the block is upload-supported
    isUploadFileAvailable = true;

    // Dynamic import AFTER mocks are configured
    const { FilePanel } = await import('./index.tsx');

    renderWithMantine(<FilePanel /* @ts-ignore */ block={MOCK_IMAGE_BLOCK} />);

    // Expect both tab names to be present
    const tabNames = screen.getAllByTestId('tab-name').map((el) => el.textContent);
    expect(tabNames).toEqual(['Upload', 'Embed']);
  });

  it('renders only Embed tab when uploads are not supported', async () => {
    // Editor without uploadFile OR unsupported block type
    isUploadFileAvailable = false;

    const { FilePanel } = await import('./index.tsx');

    renderWithMantine(<FilePanel /* @ts-ignore */ block={MOCK_FILE_BLOCK} />);

    const tabNames = screen.getAllByTestId('tab-name').map((el) => el.textContent);
    expect(tabNames).toEqual(['Embed']);
  });

  it('uses custom tabs prop when provided', async () => {
    const { FilePanel } = await import('./index.tsx');

    const customTabs = [{ name: 'Custom', tabPanel: <div>Custom Panel</div> }];

    renderWithMantine(<FilePanel /* @ts-ignore */ block={MOCK_IMAGE_BLOCK} tabs={customTabs} />);

    const tabNames = screen.getAllByTestId('tab-name').map((el) => el.textContent);
    expect(tabNames).toEqual(['Custom']);
  });
});
