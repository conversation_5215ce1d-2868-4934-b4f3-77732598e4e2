import type {
  BlockSchema,
  DefaultBlockSchema,
  DefaultInlineContentSchema,
  DefaultStyleSchema,
  InlineContentSchema,
  StyleSchema,
} from '@blocknote/core';
import { useBlockNoteEditor, useComponentsContext, useDictionary } from '@blocknote/react';
import type { FilePanelProps } from '@blocknote/react';
import { Box, Button, FileButton, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconUpload } from '@tabler/icons-react';
/**
 * Override the default UploadTab component from BlockNoteEditor
 * the original component is located at @blocknote/react/src/components/FilePanel/DefaultTabs/UploadTab.tsx
 */
import { useCallback, useEffect, useState } from 'react';
import { IMAGE_FILE_TYPES } from '../../../constants/file';

const useStyles = createStyles(() => ({
  fileInputWrapper: {
    width: '100%',
    minWidth: rem(200),
    display: 'flex',
    justifyContent: 'center',
  },
  fileButton: {
    paddingLeft: `${rem(12)} !important`,
    paddingRight: `${rem(12)} !important`,
  },
  icon: {
    width: rem(14),
    height: rem(14),
  },
}));

const UPLOADING_FAILED_TIMEOUT = 3000;

export const UploadTab = <
  B extends BlockSchema = DefaultBlockSchema,
  I extends InlineContentSchema = DefaultInlineContentSchema,
  S extends StyleSchema = DefaultStyleSchema,
>(
  props: FilePanelProps<I, S> & {
    setLoading: (loading: boolean) => void;
  }
) => {
  const { block, setLoading } = props;
  const { cx, classes } = useStyles();

  const Components = useComponentsContext()!;
  const dict = useDictionary();
  const editor = useBlockNoteEditor<B, I, S>();

  const [uploadFailed, setUploadFailed] = useState<boolean>(false);

  const config = editor.schema.blockSchema[block.type];

  // Access accept mime types safely with type assertion
  const accept =
    block.type === 'image'
      ? IMAGE_FILE_TYPES.join(',')
      : config.isFileBlock && (config as any).fileBlockAcceptMimeTypes?.length
        ? (config as any).fileBlockAcceptMimeTypes.join(',')
        : '*/*';

  /**
   * Upload file to the server
   * @param {File} file
   * @returns {void}
   */
  const uploadFile = useCallback(
    async (file: File) => {
      setLoading(true);

      if (editor.uploadFile !== undefined) {
        try {
          let updateData = await editor.uploadFile(file);
          if (typeof updateData === 'string') {
            updateData = {
              props: {
                name: file.name,
                url: updateData,
              },
            };
          }
          // Ensure the update data has the correct props for image blocks
          if (block.type === 'image' && typeof updateData === 'object') {
            // Make sure we have the required properties for images
            if (!updateData.props) {
              updateData.props = {};
            }
            if (!updateData.props.url && updateData.url) {
              updateData.props.url = updateData.url;
            }
            if (!updateData.props.caption) {
              updateData.props.caption = file.name;
            }
          }
          editor.updateBlock(block, updateData);
        } catch (e) {
          console.error('File upload failed:', e);
          setUploadFailed(true);
        } finally {
          setLoading(false);
        }
      }
    },
    [block, editor, setLoading]
  );

  /**
   * Handle file change event
   * @param {File | null} file
   * @returns {void}
   */
  const handleFileChange = useCallback(
    (file: File | null) => {
      if (file === null) {
        return;
      }

      uploadFile(file);
    },
    [uploadFile]
  );

  useEffect(() => {
    if (uploadFailed) {
      setTimeout(() => {
        setUploadFailed(false);
      }, UPLOADING_FAILED_TIMEOUT);
    }
  }, [uploadFailed]);

  return (
    <Components.FilePanel.TabPanel className={'bn-tab-panel'}>
      <Box className={cx(classes.fileInputWrapper, 'bn-file-input')}>
        <FileButton accept={accept} onChange={handleFileChange}>
          {(props) => (
            <Button
              className={classes.fileButton}
              leftSection={<IconUpload className={classes.icon} />}
              {...props}
            >
              {dict.file_panel.upload.file_placeholder[block.type] ||
                dict.file_panel.upload.file_placeholder.file}
            </Button>
          )}
        </FileButton>
      </Box>
      {uploadFailed && <div className='bn-error-text'>{dict.file_panel.upload.upload_error}</div>}
    </Components.FilePanel.TabPanel>
  );
};
