import { fireEvent, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { IMAGE_FILE_TYPES } from '../../../constants/file';
import { renderWithMantine, waitForAsync } from '../../../utils/unitTest';

// -----------------------------------------------------------------------------
// Mocks
// -----------------------------------------------------------------------------

// Will be assigned individually in each test to control behaviour
let mockUploadFile: ReturnType<typeof vi.fn>;
const mockUpdateBlock = vi.fn();

// Mock editor hooks, components context and dictionary used inside UploadTab
vi.mock('@blocknote/react', () => {
  const React = require('react');
  return {
    // Provide the mocked editor instance
    useBlockNoteEditor: () => ({
      uploadFile: (...args: any[]) => mockUploadFile?.(...args),
      updateBlock: mockUpdateBlock,
      schema: {
        blockSchema: {
          image: {
            isFileBlock: true,
            fileBlockAcceptMimeTypes: IMAGE_FILE_TYPES,
          },
          file: {
            isFileBlock: true,
            fileBlockAcceptMimeTypes: ['application/pdf'],
          },
        },
      },
    }),

    // Minimal Components context – only what UploadTab consumes
    useComponentsContext: () => ({
      FilePanel: {
        TabPanel: ({ children, ...props }: any) => (
          <div data-testid='tab-panel' {...props}>
            {children}
          </div>
        ),
      },
    }),

    // Simplified dictionary
    useDictionary: () => ({
      file_panel: {
        upload: {
          file_placeholder: {
            image: 'Upload image',
            file: 'Upload file',
          },
          upload_error: 'Upload failed',
        },
      },
    }),
  };
});

// Partially mock @mantine/core to override FileButton for easier interaction
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual<typeof import('@mantine/core')>('@mantine/core');
  const React = require('react');
  return {
    ...actual,
    // <FileButton> uses a render-prop pattern; expose underlying input for tests
    FileButton: ({ accept, onChange, children }: any) => {
      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] ?? null;
        onChange(file);
      };

      return (
        <div data-testid='file-button'>
          {typeof children === 'function' ? children({}) : null}
          <input type='file' data-testid='file-input' accept={accept} onChange={handleChange} />
        </div>
      );
    },
  };
});

// Mock @mantine/hooks to avoid matchMedia dependency
vi.mock('@mantine/hooks', () => ({
  useMediaQuery: () => false,
}));

// Ensure matchMedia is defined before any Mantine modules are evaluated
if (!window.matchMedia) {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
}

// -----------------------------------------------------------------------------
// Component under test – imported AFTER mocks are in place
// -----------------------------------------------------------------------------
import { UploadTab } from './UploadTab';

// -----------------------------------------------------------------------------
// Test constants
// -----------------------------------------------------------------------------

const MOCK_IMAGE_BLOCK = { id: 'block-img', type: 'image' } as any;
const MOCK_FILE_BLOCK = { id: 'block-file', type: 'file' } as any;
const createMockFile = (name = 'test-image.png', type = 'image/png') =>
  new File(['dummy'], name, { type });

// -----------------------------------------------------------------------------
// Tests
// -----------------------------------------------------------------------------

describe('UploadTab component', () => {
  const setLoading = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Re-establish matchMedia stub after mocks are cleared
    if (!window.matchMedia) {
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation((query) => ({
          matches: false,
          media: query,
          onchange: null,
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      });
    }
    mockUpdateBlock.mockReset();
    setLoading.mockReset();
  });

  // ---------------------------------------------------------------------------
  // Rendering / props
  // ---------------------------------------------------------------------------

  it('sets correct accept attribute for image blocks', () => {
    mockUploadFile = vi.fn();

    renderWithMantine(
      <UploadTab /* @ts-ignore */ block={MOCK_IMAGE_BLOCK} setLoading={setLoading} />
    );

    const fileInput = screen.getByTestId('file-input');
    expect(fileInput).toHaveAttribute('accept', IMAGE_FILE_TYPES.join(','));
  });

  it('sets correct accept attribute for file blocks with custom mime types', () => {
    mockUploadFile = vi.fn();

    renderWithMantine(
      <UploadTab /* @ts-ignore */ block={MOCK_FILE_BLOCK} setLoading={setLoading} />
    );

    const fileInput = screen.getByTestId('file-input');
    expect(fileInput).toHaveAttribute('accept', 'application/pdf');
  });

  // ---------------------------------------------------------------------------
  // Successful upload flow
  // ---------------------------------------------------------------------------

  it('calls updateBlock with generated props after successful upload', async () => {
    const mockUrl = 'https://example.com/test-image.png';
    mockUploadFile = vi.fn().mockResolvedValue(mockUrl);

    renderWithMantine(
      <UploadTab /* @ts-ignore */ block={MOCK_IMAGE_BLOCK} setLoading={setLoading} />
    );

    const fileInput = screen.getByTestId('file-input');
    const file = createMockFile();

    // Trigger change event with the mock file
    fireEvent.change(fileInput, { target: { files: [file] } });

    // Wait for async operations to complete
    await waitForAsync();

    expect(mockUploadFile).toHaveBeenCalledWith(file);
    expect(mockUpdateBlock).toHaveBeenCalledTimes(1);
    expect(mockUpdateBlock).toHaveBeenCalledWith(MOCK_IMAGE_BLOCK, {
      props: {
        name: file.name,
        url: mockUrl,
        caption: file.name,
      },
    });
  });

  // ---------------------------------------------------------------------------
  // Error handling (simplified to avoid long fake-timer sequence)
  // ---------------------------------------------------------------------------

  it('shows error text when upload fails', async () => {
    mockUploadFile = vi.fn().mockRejectedValue(new Error('upload failed'));

    renderWithMantine(
      <UploadTab /* @ts-ignore */ block={MOCK_IMAGE_BLOCK} setLoading={setLoading} />
    );

    const fileInput = screen.getByTestId('file-input');
    const file = createMockFile();

    fireEvent.change(fileInput, { target: { files: [file] } });

    expect(await screen.findByText('Upload failed')).toBeInTheDocument();
  });
});
