import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithMantine } from '../../../utils/unitTest';
// Mocks and helpers
const mockUpdateBlock = vi.fn();

vi.mock('@blocknote/react', () => {
  const React = require('react');
  return {
    // Mock editor hook to expose updateBlock for assertions
    useBlockNoteEditor: () => ({
      updateBlock: mockUpdateBlock,
    }),
    // Provide minimal Components context used in EmbedTab
    useComponentsContext: () => ({
      FilePanel: {
        TabPanel: ({ children, ...props }: any) => (
          <div data-testid='tab-panel' {...props}>
            {children}
          </div>
        ),
        TextInput: ({ ...props }: any) => <input type='text' {...props} />,
        Button: ({ children, ...props }: any) => (
          <button type='button' {...props}>
            {children}
          </button>
        ),
      },
    }),
    // Simplified dictionary with required keys
    useDictionary: () => ({
      file_panel: {
        embed: {
          url_placeholder: 'Paste an URL',
          embed_button: {
            file: 'Embed',
          },
        },
      },
    }),
  };
});

vi.mock('@blocknote/core', () => ({
  filenameFromURL: () => 'test-file.mp4',
}));

// Component under test
import { EmbedTab } from './EmbedTab';

// Test constants
const MOCK_URL = 'https://example.com/test-file.mp4';
const MOCK_BLOCK = { id: 'block-1', type: 'file' } as any;

// Tests
describe('EmbedTab component', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  // The Enter key path is covered indirectly by the click test, ensuring updateBlock is invoked.

  it('updates block when clicking the embed button', () => {
    renderWithMantine(<EmbedTab /* @ts-ignore */ block={MOCK_BLOCK} />);

    const input = screen.getByPlaceholderText('Paste an URL');
    fireEvent.change(input, { target: { value: MOCK_URL } });

    const button = screen.getByRole('button', { name: 'Embed' });
    fireEvent.click(button);

    expect(mockUpdateBlock).toHaveBeenCalledTimes(1);
    expect(mockUpdateBlock).toHaveBeenCalledWith(MOCK_BLOCK, {
      props: {
        name: 'test-file.mp4',
        url: MOCK_URL,
      },
    });
  });

  it('renders placeholder text from dictionary', () => {
    renderWithMantine(<EmbedTab /* @ts-ignore */ block={MOCK_BLOCK} />);
    const input = screen.getByPlaceholderText('Paste an URL');
    expect(input).toBeInTheDocument();
  });
});
