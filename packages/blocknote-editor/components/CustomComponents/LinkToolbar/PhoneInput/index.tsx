import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type React from 'react';
import { useCallback } from 'react';
import RTPhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

interface PhoneInputStyleProps {
  hideFlagDropdown: boolean;
}

const useStyles = createStyles((theme, { hideFlagDropdown = false }: PhoneInputStyleProps) => ({
  inputWrapper: {
    '& .react-tel-input input.form-control': {
      maxWidth: '100%',
      border: `${rem(1)} solid ${theme.colors.decaLight[1]}`,
      paddingLeft: hideFlagDropdown ? rem(12) : rem(48),
    },
    '& .react-tel-input .flag-dropdown': {
      display: hideFlagDropdown ? 'none' : 'block',
      border: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    },
  },
}));

interface PhoneInputProps {
  className?: string;
  value: string;
  optionalProps?: {
    placeholder?: string;
    disableCountryCode?: boolean;
    disableDropdown?: boolean;
    masks: { [key: string]: string };
  };
  onChange: (value: string) => void;
  onEnter?: () => void;
}
const DEFAULT_COUNTRY = 'jp';
const PHONE_NUMBER_PREFIX = '+';

const PhoneInput: React.FC<PhoneInputProps> = ({
  className,
  value,
  optionalProps = {},
  onChange,
  onEnter,
}) => {
  const { disableCountryCode = false } = optionalProps;
  const { cx, classes } = useStyles({ hideFlagDropdown: disableCountryCode });

  /**
   * Handle phone number input change
   * @param phone - phone number, ex: 811234567890 (without prefix)
   * add prefix `+` to the phone number and call onChange(+811234567890)
   * @returns void
   */
  const handleInputChange = useCallback(
    (phone: string) => {
      if (phone) {
        onChange(disableCountryCode ? phone : `${PHONE_NUMBER_PREFIX}${phone}`);
      }
    },
    [onChange, disableCountryCode]
  );

  /**
   * Handle Enter key press
   * @param event - keyboard event
   * @returns void
   */
  const handleEnter = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        onEnter?.();
      }
    },
    [onEnter]
  );

  return (
    <Box className={cx(className, classes.inputWrapper)}>
      <RTPhoneInput
        country={DEFAULT_COUNTRY}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleEnter}
        {...optionalProps}
      />
    </Box>
  );
};

export default PhoneInput;
