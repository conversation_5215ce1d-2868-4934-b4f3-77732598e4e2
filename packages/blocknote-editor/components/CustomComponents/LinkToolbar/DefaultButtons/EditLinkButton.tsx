/**
 * NOTE: This file is a copy of the original file from the blocknote-react package.
 * The original file can be found at: `packages/react/src/components/LinkToolbar/DefaultButtons/EditLinkButton.tsx`.
 */
import { useComponentsContext, useDictionary } from '@blocknote/react';
import { EditLinkMenuItems } from '../EditLinkMenuItems';
import type { LinkToolbarProps } from '../LinkToolbarProps';

export const EditLinkButton = (props: Pick<LinkToolbarProps, 'url' | 'text' | 'editLink'>) => {
  const Components = useComponentsContext()!;
  const dict = useDictionary();

  return (
    <section className={'bn-left-side-popover'}>
      <Components.Generic.Popover.Root>
        <Components.Generic.Popover.Trigger>
          <Components.LinkToolbar.Button
            className={'bn-button'}
            mainTooltip={dict.link_toolbar.edit.tooltip}
            isSelected={false}
          >
            {dict.link_toolbar.edit.text}
          </Components.LinkToolbar.Button>
        </Components.Generic.Popover.Trigger>
        <Components.Generic.Popover.Content
          className={'bn-popover-content bn-form-popover bn-link-setting-popover'}
          variant={'form-popover'}
        >
          <EditLinkMenuItems {...props} />
        </Components.Generic.Popover.Content>
      </Components.Generic.Popover.Root>
    </section>
  );
};
