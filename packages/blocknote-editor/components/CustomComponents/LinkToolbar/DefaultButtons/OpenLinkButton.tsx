/**
 * NOTE: This file is a copy of the original file from the blocknote-react package.
 * The original file can be found at: `packages/react/src/components/LinkToolbar/DefaultButtons/OpenLinkButton.tsx`.
 */
import { useComponentsContext, useDictionary } from '@blocknote/react';
import { IconExternalLink } from '@tabler/icons-react';
import type { LinkToolbarProps } from '../LinkToolbarProps';

export const OpenLinkButton = (props: Pick<LinkToolbarProps, 'url'>) => {
  const Components = useComponentsContext()!;
  const dict = useDictionary();

  return (
    <Components.LinkToolbar.Button
      className={'bn-button'}
      mainTooltip={dict.link_toolbar.open.tooltip}
      label={dict.link_toolbar.open.tooltip}
      isSelected={false}
      onClick={() => {
        window.open(props.url, '_blank');
      }}
      icon={<IconExternalLink strokeWidth={2} size={16} />}
    />
  );
};
