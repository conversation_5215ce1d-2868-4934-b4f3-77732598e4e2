/**
 * NOTE: This file is a copy of the original file from the blocknote-react package.
 * The original file can be found at: `packages/react/src/components/LinkToolbar/DefaultButtons/DeleteLinkButton.tsx`.
 */
import { useComponentsContext, useDictionary } from '@blocknote/react';
import { IconUnlink } from '@tabler/icons-react';
import type { LinkToolbarProps } from '../LinkToolbarProps';

export const DeleteLinkButton = (props: Pick<LinkToolbarProps, 'deleteLink'>) => {
  const Components = useComponentsContext()!;
  const dict = useDictionary();
  return (
    <Components.LinkToolbar.Button
      className={'bn-button'}
      label={dict.link_toolbar.delete.tooltip}
      mainTooltip={dict.link_toolbar.delete.tooltip}
      isSelected={false}
      onClick={props.deleteLink}
      icon={<IconUnlink strokeWidth={2} size={16} />}
    />
  );
};
