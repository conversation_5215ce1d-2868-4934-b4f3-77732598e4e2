import { useComponentsContext, useDictionary } from '@blocknote/react';
import { Box, Checkbox, Code, Flex, Switch, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconDiamonds } from '@tabler/icons-react';
import get from 'lodash/get';
import type React from 'react';
import { useCallback, useState } from 'react';
import type { ChangeEvent } from 'react';
import {
  getPhoneFromLink,
  getVariableFromLink,
  isInternationalPhoneNumber,
  isPhoneLink,
  isPhoneVariableLink,
} from '../../../../utils/phone';
import PhoneInput from '../PhoneInput';

const useStyles = createStyles((theme) => ({
  switchGroup: {
    marginBottom: theme.spacing.xs,
    justifyContent: 'flex-end',
  },
  phoneSwitch: {
    label: {
      cursor: 'pointer',
    },
  },
  formGroup: {
    marginBottom: theme.spacing.xs,
  },
  formCheckbox: {
    marginTop: theme.spacing.xs,
    '& label': {
      paddingLeft: theme.spacing.xs,
      cursor: 'pointer',
    },
    '& .mantine-Checkbox-input': {
      borderWidth: rem(1),
    },
  },
  label: {
    display: 'block',
    marginBottom: theme.spacing.xs,
    fontSize: theme.fontSizes.md,
    fontWeight: 500,
  },
}));

interface PhoneLinkInputProps {
  url: string;
  onChange: (url: string) => void;
  onKeyDown: (event: React.KeyboardEvent) => void;
  onSubmit: () => void;
}

const PhoneLinkInput: React.FC<PhoneLinkInputProps> = ({ url, onChange, onKeyDown, onSubmit }) => {
  const dict = useDictionary();
  const Components = useComponentsContext()!;
  const { cx, classes } = useStyles();

  // Extract phone number and variable from URL
  const [phone, setPhone] = useState<string>(
    !isPhoneVariableLink(url) && isPhoneLink(url) ? getPhoneFromLink(url) : ''
  );

  const [variable, setVariable] = useState<string>(
    isPhoneVariableLink(url) ? getVariableFromLink(url) : ''
  );

  // Check if phone number is in international format
  const [usingInternationalPhoneNumber, setUsingInternationalPhoneNumber] = useState<boolean>(
    isInternationalPhoneNumber(url)
  );

  // State of checkbox to set variable phone number
  const [usingVariablePhone, setUsingVariablePhone] = useState<boolean>(isPhoneVariableLink(url));

  /**
   * Handle variable change
   * @param event - input change event
   * @returns void
   */
  const handleVariableChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      setVariable(event.currentTarget.value);
      onChange(event.currentTarget.value);
    },
    [setVariable, onChange]
  );

  /**
   * Handle phone number change
   * @param value - phone number
   * @returns void
   */
  const handlePhoneNumberChange = useCallback(
    (value: string) => {
      setPhone(value);
      onChange(value);
    },
    [setPhone, onChange]
  );

  return (
    <>
      <Flex className={classes.switchGroup}>
        <Switch
          className={cx('bn-switch', classes.phoneSwitch)}
          label={get(dict, 'custom.usePhoneVariable', 'Use Variable Phone number')}
          labelPosition='left'
          size='xs'
          checked={usingVariablePhone}
          onChange={(event) => setUsingVariablePhone(event.currentTarget.checked)}
        />
      </Flex>
      {usingVariablePhone ? (
        <>
          <Text fz='xs' mb={rem(12)}>
            {get(dict, 'custom.variableFormat', 'Variable with format:')}
            <Code> {'{{variable}}'} </Code>
          </Text>
          <Components.Generic.Form.TextInput
            className={'bn-text-input'}
            name='variable'
            icon={<IconDiamonds />}
            placeholder={get(dict, 'custom.variablePlaceholder', 'Enter Variable')}
            value={variable}
            onChange={handleVariableChange}
            onKeyDown={onKeyDown}
            onSubmit={onSubmit}
          />
        </>
      ) : (
        <>
          <PhoneInput
            className={'bn-phone-input'}
            value={phone}
            optionalProps={{
              placeholder: get(dict, 'custom.phoneNumberPlaceholder', 'Enter Phone number'),
              disableCountryCode: !usingInternationalPhoneNumber,
              disableDropdown: !usingInternationalPhoneNumber,
              masks: { jp: '............' },
            }}
            onChange={handlePhoneNumberChange}
            onEnter={onSubmit}
          />
          <Box className={classes.formCheckbox}>
            <Checkbox
              checked={usingInternationalPhoneNumber}
              size='sm'
              label={get(
                dict,
                'custom.useInternationalPhoneNumber',
                'Use International Phone number format'
              )}
              onChange={(event) => setUsingInternationalPhoneNumber(event.currentTarget.checked)}
            />
          </Box>
        </>
      )}
    </>
  );
};

export default PhoneLinkInput;
