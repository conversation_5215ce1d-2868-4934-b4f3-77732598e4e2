import { useComponentsContext, useDictionary } from '@blocknote/react';
import { IconLink } from '@tabler/icons-react';
import type React from 'react';

interface TextLinkInputProps {
  url: string;
  onUrlChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onSubmit: () => void;
}

const TextLinkInput: React.FC<TextLinkInputProps> = ({ url, onUrlChange, onKeyDown, onSubmit }) => {
  const Components = useComponentsContext()!;
  const dict = useDictionary();

  return (
    <Components.Generic.Form.TextInput
      className={'bn-text-input'}
      name='url'
      icon={<IconLink />}
      autoFocus={true}
      placeholder={dict.link_toolbar.form.url_placeholder}
      value={url}
      onKeyDown={onKeyDown}
      onChange={onUrlChange}
      onSubmit={onSubmit}
    />
  );
};

export default TextLinkInput;
