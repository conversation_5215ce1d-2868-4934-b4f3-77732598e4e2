import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// -----------------------------------------------------------------------------
// Mocks
// -----------------------------------------------------------------------------

// Create a shared mock for the TextInput so we can inspect its props later
const TextInputMock = vi.fn().mockImplementation((props: any) => {
  // Extract icon to render it separately (avoids React warnings about unknown props)
  const { icon, ...inputProps } = props;
  return (
    <div data-testid='text-input-wrapper'>
      {icon}
      {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
      <input data-testid='text-input' {...inputProps} />
    </div>
  );
});

// Mock @blocknote/react hooks and Components context
vi.mock('@blocknote/react', () => ({
  useComponentsContext: () => ({
    Generic: {
      Form: {
        TextInput: TextInputMock,
      },
    },
  }),
  useDictionary: () => ({
    link_toolbar: {
      form: {
        url_placeholder: 'Enter URL',
      },
    },
  }),
}));

// Mock icon to avoid importing real SVG
vi.mock('@tabler/icons-react', () => ({
  IconLink: () => <svg data-testid='icon-link' />,
}));

// Import the component *after* mocks are set up
import TextLinkInput from './TextLinkInput';

// -----------------------------------------------------------------------------
// Test constants
// -----------------------------------------------------------------------------

const MOCK_URL = 'https://example.com';

// -----------------------------------------------------------------------------
// Tests
// -----------------------------------------------------------------------------

describe('TextLinkInput', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders input with correct value and placeholder', () => {
    render(
      <TextLinkInput url={MOCK_URL} onUrlChange={vi.fn()} onKeyDown={vi.fn()} onSubmit={vi.fn()} />
    );

    const input = screen.getByTestId('text-input') as HTMLInputElement;
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute('placeholder', 'Enter URL');
    expect(input.value).toBe(MOCK_URL);
  });

  it('invokes callback props on change and keydown events', () => {
    const handleChange = vi.fn();
    const handleKeyDown = vi.fn();

    render(
      <TextLinkInput
        url={MOCK_URL}
        onUrlChange={handleChange}
        onKeyDown={handleKeyDown}
        onSubmit={vi.fn()}
      />
    );

    const input = screen.getByTestId('text-input');

    // Trigger change event
    fireEvent.change(input, { target: { value: 'https://changed.com' } });
    expect(handleChange).toHaveBeenCalledTimes(1);

    // Trigger keydown event
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter', charCode: 13 });
    expect(handleKeyDown).toHaveBeenCalledTimes(1);
  });

  it('passes onSubmit prop down to TextInput component', () => {
    const handleSubmit = vi.fn();

    render(
      <TextLinkInput
        url={MOCK_URL}
        onUrlChange={vi.fn()}
        onKeyDown={vi.fn()}
        onSubmit={handleSubmit}
      />
    );

    // TextInputMock should have been called exactly once
    expect(TextInputMock).toHaveBeenCalledTimes(1);

    // Extract the props passed to the mock
    const passedProps = TextInputMock.mock.calls[0][0];

    // Ensure the onSubmit prop is forwarded correctly
    expect(passedProps.onSubmit).toBe(handleSubmit);

    // Calling the forwarded onSubmit should trigger the original callback
    passedProps.onSubmit();
    expect(handleSubmit).toHaveBeenCalledTimes(1);
  });
});
