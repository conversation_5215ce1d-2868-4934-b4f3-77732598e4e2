import { fireEvent, screen } from '@testing-library/react';
import type React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithMantine } from '../../../../utils/unitTest';
import PhoneLinkInput from './PhoneLinkInput';

// -----------------------------------------------------------------------------
// Constants & Helpers
// -----------------------------------------------------------------------------

const MOCK_DICTIONARY = {
  custom: {
    usePhoneVariable: 'Use Variable Phone number',
    variableFormat: 'Variable with format:',
    variablePlaceholder: 'Enter Variable',
    phoneNumberPlaceholder: 'Enter Phone number',
    useInternationalPhoneNumber: 'Use International Phone number format',
  },
};

// -----------------------------------------------------------------------------
// Mocks
// -----------------------------------------------------------------------------

// Mock @blocknote/react hooks utilised inside component
vi.mock('@blocknote/react', () => ({
  __esModule: true,
  useDictionary: vi.fn(() => MOCK_DICTIONARY),
  useComponentsContext: vi.fn(() => ({
    Generic: {
      Form: {
        // Minimal mock of TextInput used by component
        TextInput: ({ value, onChange, onKeyDown, ...rest }: any) => (
          <input
            data-testid='variable-input'
            value={value}
            onChange={onChange}
            onKeyDown={onKeyDown}
            {...rest}
          />
        ),
      },
    },
  })),
}));

// Mock internal PhoneInput to simplify testing interactions
vi.mock('../PhoneInput', () => ({
  __esModule: true,
  default: ({ onChange, onEnter, value = '', ...rest }: any) => (
    <input
      data-testid='phone-input'
      value={value}
      onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
      onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          onEnter?.();
        }
      }}
      {...rest}
    />
  ),
}));

// -----------------------------------------------------------------------------
// Tests
// -----------------------------------------------------------------------------

describe('PhoneLinkInput component', () => {
  const INITIAL_PHONE_URL = 'tel:1234567890';
  const INITIAL_VARIABLE_URL = 'tel:{{phone}}';

  let onChangeSpy: ReturnType<typeof vi.fn>;
  let onSubmitSpy: ReturnType<typeof vi.fn>;
  let onKeyDownSpy: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    onChangeSpy = vi.fn();
    onSubmitSpy = vi.fn();
    onKeyDownSpy = vi.fn();
  });

  // ---------------------------------------------------------------------------
  // Rendering behaviour
  // ---------------------------------------------------------------------------

  it('renders phone input by default when provided with plain phone link', () => {
    renderWithMantine(
      <PhoneLinkInput
        url={INITIAL_PHONE_URL}
        onChange={onChangeSpy}
        onKeyDown={onKeyDownSpy}
        onSubmit={onSubmitSpy}
      />
    );

    // Switch present
    expect(screen.getByRole('switch', { name: /use variable phone number/i })).toBeInTheDocument();

    // Phone input is displayed, variable input is not
    expect(screen.getByTestId('phone-input')).toBeInTheDocument();
    expect(screen.queryByTestId('variable-input')).not.toBeInTheDocument();
  });

  it('renders variable input when switch is toggled', () => {
    renderWithMantine(
      <PhoneLinkInput
        url={INITIAL_PHONE_URL}
        onChange={onChangeSpy}
        onKeyDown={onKeyDownSpy}
        onSubmit={onSubmitSpy}
      />
    );

    const switchCheckbox = screen.getByRole('switch', {
      name: /use variable phone number/i,
    });

    fireEvent.click(switchCheckbox);

    // Variable input should now be visible and phone input hidden
    expect(screen.getByTestId('variable-input')).toBeInTheDocument();
    expect(screen.queryByTestId('phone-input')).not.toBeInTheDocument();
  });

  it('renders variable input by default when provided with variable phone url', () => {
    renderWithMantine(
      <PhoneLinkInput
        url={INITIAL_VARIABLE_URL}
        onChange={onChangeSpy}
        onKeyDown={onKeyDownSpy}
        onSubmit={onSubmitSpy}
      />
    );

    expect(screen.getByTestId('variable-input')).toBeInTheDocument();
    expect(screen.queryByTestId('phone-input')).not.toBeInTheDocument();
  });

  // ---------------------------------------------------------------------------
  // Interaction behaviour
  // ---------------------------------------------------------------------------

  it('calls onChange when phone number changes', () => {
    renderWithMantine(
      <PhoneLinkInput
        url={INITIAL_PHONE_URL}
        onChange={onChangeSpy}
        onKeyDown={onKeyDownSpy}
        onSubmit={onSubmitSpy}
      />
    );

    const phoneInput = screen.getByTestId('phone-input') as HTMLInputElement;
    fireEvent.change(phoneInput, { target: { value: '+811234567890' } });

    expect(onChangeSpy).toHaveBeenCalledWith('+811234567890');
  });

  it('calls onChange when variable value changes', () => {
    renderWithMantine(
      <PhoneLinkInput
        url={INITIAL_VARIABLE_URL}
        onChange={onChangeSpy}
        onKeyDown={onKeyDownSpy}
        onSubmit={onSubmitSpy}
      />
    );

    const variableInput = screen.getByTestId('variable-input') as HTMLInputElement;
    fireEvent.change(variableInput, { target: { value: '{{myPhone}}' } });

    expect(onChangeSpy).toHaveBeenCalledWith('{{myPhone}}');
  });

  it('calls onSubmit when Enter is pressed in phone input', () => {
    renderWithMantine(
      <PhoneLinkInput
        url={INITIAL_PHONE_URL}
        onChange={onChangeSpy}
        onKeyDown={onKeyDownSpy}
        onSubmit={onSubmitSpy}
      />
    );

    const phoneInput = screen.getByTestId('phone-input');
    fireEvent.keyDown(phoneInput, { key: 'Enter', code: 'Enter' });

    expect(onSubmitSpy).toHaveBeenCalled();
  });
});
