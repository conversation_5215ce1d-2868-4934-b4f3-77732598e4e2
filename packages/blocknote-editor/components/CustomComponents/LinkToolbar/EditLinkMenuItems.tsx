import { useComponentsContext, useDictionary } from '@blocknote/react';
import { Box, Checkbox, Flex, SegmentedControl, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconTypography } from '@tabler/icons-react';
import get from 'lodash/get';
/**
 * Custom implementation of the BlockNote link editor toolbar.
 * Based on the original from @blocknote/react.
 */
import { useCallback, useEffect, useMemo, useState } from 'react';
import type { ChangeEvent, KeyboardEvent } from 'react';

import { EditorLinkType, LinkTarget } from '../../../constants/link';
import { createPhoneLink, isPhoneLink } from '../../../utils';
import {
  addTargetToLinkQuery,
  getTargetFromLinkQuery,
  removeTargetFromLinkQuery,
} from '../../../utils/link';
import SettingActions from '../SettingActions';
import PhoneLinkInput from './LinkInput/PhoneLinkInput';
import TextLinkInput from './LinkInput/TextLinkInput';
import type { LinkToolbarProps } from './LinkToolbarProps';

// Styles with descriptive names
const useStyles = createStyles((theme) => ({
  linkTypeSelector: {
    marginBottom: theme.spacing.xxs,
    justifyContent: 'flex-start',
  },
  formSection: {
    marginBottom: theme.spacing.xs,
  },
  sectionLabel: {
    display: 'block',
    marginBottom: theme.spacing.xs,
    fontSize: theme.fontSizes.md,
    fontWeight: 500,
  },
  targetOptionCheckbox: {
    marginBottom: theme.spacing.xs,
    marginTop: rem(-4),
    '& label': {
      paddingLeft: theme.spacing.xs,
      cursor: 'pointer',
    },
    '& .mantine-Checkbox-input': {
      borderWidth: rem(1),
    },
  },
}));

interface EditLinkMenuItemsProps extends Pick<LinkToolbarProps, 'url' | 'text' | 'editLink'> {}

/**
 * Component for editing link properties including URL, text, and target.
 */
export const EditLinkMenuItems = ({ url, text, editLink }: EditLinkMenuItemsProps) => {
  const components = useComponentsContext()!;
  const dictionary = useDictionary();
  const { classes } = useStyles();

  // Extract base URL without target parameter
  const urlWithoutTarget = useMemo(() => removeTargetFromLinkQuery(decodeURI(url)), [url]);

  // Extract target from URL
  const initialTarget = useMemo(() => getTargetFromLinkQuery(url), [url]);

  // Form state
  const [linkUrl, setLinkUrl] = useState<string>(urlWithoutTarget);
  const [linkText, setLinkText] = useState<string>(text);
  const [openInNewTab, setOpenInNewTab] = useState<boolean>(initialTarget === LinkTarget.BLANK);
  const [linkTarget, setLinkTarget] = useState<LinkTarget>(initialTarget);
  const [linkType, setLinkType] = useState<EditorLinkType>(
    isPhoneLink(urlWithoutTarget) ? EditorLinkType.PHONE : EditorLinkType.TEXT
  );

  /**
   * Handles Enter key press to submit the form
   */
  const handleKeyboardSubmit = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === 'Enter') {
        event.preventDefault();

        const finalUrl = isPhoneLink(linkUrl) ? linkUrl : addTargetToLinkQuery(linkUrl, linkTarget);

        editLink(finalUrl, linkText);
      }
    },
    [editLink, linkUrl, linkText, linkTarget]
  );

  /**
   * Updates URL for text links
   */
  const handleTextUrlChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => setLinkUrl(event.currentTarget.value),
    []
  );

  /**
   * Updates URL for phone links
   */
  const handlePhoneNumberChange = useCallback(
    (phoneNumber: string) => setLinkUrl(createPhoneLink(phoneNumber)),
    []
  );

  /**
   * Updates link text
   */
  const handleLinkTextChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => setLinkText(event.currentTarget.value),
    []
  );

  /**
   * Submits the form with current values
   */
  const handleFormSubmit = useCallback(() => {
    const finalUrl = isPhoneLink(linkUrl) ? linkUrl : addTargetToLinkQuery(linkUrl, linkTarget);

    editLink(finalUrl, linkText);
  }, [editLink, linkUrl, linkText, linkTarget]);

  /**
   * Toggles whether link opens in new tab
   */
  const handleTargetToggle = useCallback((event: ChangeEvent<HTMLInputElement>) => {
    const shouldOpenInNewTab = event.currentTarget.checked;
    setOpenInNewTab(shouldOpenInNewTab);
    setLinkTarget(shouldOpenInNewTab ? LinkTarget.BLANK : LinkTarget.SELF);
  }, []);

  /**
   * Cancels editing and restores original values
   */
  const handleCancel = useCallback(() => {
    editLink(url, text);
  }, [editLink, url, text]);

  // Sync state with props when they change
  useEffect(() => {
    setLinkText(text);
  }, [text]);

  useEffect(() => {
    setLinkUrl(urlWithoutTarget);
  }, [urlWithoutTarget]);

  return (
    <components.Generic.Form.Root>
      {/* Link Type Selector */}
      <Flex className={classes.linkTypeSelector}>
        <SegmentedControl
          value={linkType}
          data={[
            {
              label: get(dictionary, 'custom.useTextLink', 'Text link'),
              value: EditorLinkType.TEXT,
            },
            {
              label: get(dictionary, 'custom.usePhoneNumber', 'Phone number'),
              value: EditorLinkType.PHONE,
            },
          ]}
          onChange={(value: string) => setLinkType(value as EditorLinkType)}
        />
      </Flex>

      {/* Phone Link Input */}
      {linkType === EditorLinkType.PHONE && (
        <Box className={classes.formSection}>
          <PhoneLinkInput
            url={linkUrl}
            onChange={handlePhoneNumberChange}
            onKeyDown={handleKeyboardSubmit}
            onSubmit={handleFormSubmit}
          />
        </Box>
      )}

      {/* Text Link Input */}
      {linkType === EditorLinkType.TEXT && (
        <Box className={classes.formSection}>
          <Text className={classes.sectionLabel} component='label'>
            {get(dictionary, 'custom.linkURL', 'Link URL')}
          </Text>
          <TextLinkInput
            url={!isPhoneLink(linkUrl) ? linkUrl : ''}
            onUrlChange={handleTextUrlChange}
            onKeyDown={handleKeyboardSubmit}
            onSubmit={handleFormSubmit}
          />
        </Box>
      )}

      {/* Link Text Input */}
      <Box className={classes.formSection}>
        <Text className={classes.sectionLabel} component='label'>
          {get(dictionary, 'custom.linkText', 'Link Text')}
        </Text>
        <components.Generic.Form.TextInput
          className={'bn-text-input'}
          name='title'
          icon={<IconTypography />}
          placeholder={dictionary.link_toolbar.form.title_placeholder}
          value={linkText}
          onKeyDown={handleKeyboardSubmit}
          onChange={handleLinkTextChange}
          onSubmit={handleFormSubmit}
        />
      </Box>

      {/* Text Link Input */}
      {linkType === EditorLinkType.TEXT && (
        <Box className={classes.targetOptionCheckbox}>
          <Checkbox
            checked={openInNewTab}
            onChange={handleTargetToggle}
            label={get(dictionary, 'custom.openInNewTab', 'Open in new tab')}
            size='sm'
          />
        </Box>
      )}

      {/* Action Buttons */}
      <SettingActions onApply={handleFormSubmit} onCancel={handleCancel} />
    </components.Generic.Form.Root>
  );
};
