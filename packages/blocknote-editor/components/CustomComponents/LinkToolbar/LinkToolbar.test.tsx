import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// -----------------------------------------------------------------------------
// Mocks
// -----------------------------------------------------------------------------

// Minimal mock for Components context returned by useComponentsContext
const mockComponents = {
  LinkToolbar: {
    Root: ({ children, ...rest }: any) => (
      <div data-testid='link-toolbar-root' {...rest}>
        {children}
      </div>
    ),
    Button: ({ children, ...rest }: any) => (
      <button type='button' {...rest}>
        {children}
      </button>
    ),
  },
};

// Mock @blocknote/react hook to provide Components context
vi.mock('@blocknote/react', () => ({
  useComponentsContext: () => mockComponents,
}));

// Mock default button components rendered by LinkToolbar when no custom children are provided
vi.mock('./DefaultButtons/EditLinkButton', () => ({
  EditLinkButton: ({ editLink }: { editLink?: () => void }) => (
    <button data-testid='edit-link-button' type='button' onClick={editLink}>
      Edit
    </button>
  ),
}));

vi.mock('./DefaultButtons/OpenLinkButton', () => ({
  OpenLinkButton: () => (
    <button data-testid='open-link-button' type='button'>
      Open
    </button>
  ),
}));

vi.mock('./DefaultButtons/DeleteLinkButton', () => ({
  DeleteLinkButton: ({ deleteLink }: { deleteLink?: () => void }) => (
    <button data-testid='delete-link-button' type='button' onClick={deleteLink}>
      Delete
    </button>
  ),
}));

// Import the component *after* setting up mocks
import { LinkToolbar } from './index';

// -----------------------------------------------------------------------------
// Test constants
// -----------------------------------------------------------------------------

const MOCK_URL = 'https://example.com';
const MOCK_TEXT = 'Example';

// -----------------------------------------------------------------------------
// Tests
// -----------------------------------------------------------------------------

describe('LinkToolbar', () => {
  let startHideTimer: ReturnType<typeof vi.fn>;
  let stopHideTimer: ReturnType<typeof vi.fn>;
  let editLink: ReturnType<typeof vi.fn>;
  let deleteLink: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.resetAllMocks();
    startHideTimer = vi.fn();
    stopHideTimer = vi.fn();
    editLink = vi.fn();
    deleteLink = vi.fn();
  });

  // ---------------------------------------------------------------------------
  // Rendering behaviour
  // ---------------------------------------------------------------------------

  it('renders default buttons when no custom children are provided', () => {
    render(
      <LinkToolbar
        url={MOCK_URL}
        text={MOCK_TEXT}
        editLink={editLink}
        deleteLink={deleteLink}
        startHideTimer={startHideTimer}
        stopHideTimer={stopHideTimer}
      />
    );

    expect(screen.getByTestId('edit-link-button')).toBeInTheDocument();
    expect(screen.getByTestId('open-link-button')).toBeInTheDocument();
    expect(screen.getByTestId('delete-link-button')).toBeInTheDocument();
  });

  it('renders custom children instead of default buttons when provided', () => {
    render(
      <LinkToolbar
        url={MOCK_URL}
        text={MOCK_TEXT}
        editLink={editLink}
        deleteLink={deleteLink}
        startHideTimer={startHideTimer}
        stopHideTimer={stopHideTimer}
      >
        <div data-testid='custom-child'>Custom Child</div>
      </LinkToolbar>
    );

    expect(screen.getByTestId('custom-child')).toBeInTheDocument();
    expect(screen.queryByTestId('edit-link-button')).not.toBeInTheDocument();
    expect(screen.queryByTestId('open-link-button')).not.toBeInTheDocument();
    expect(screen.queryByTestId('delete-link-button')).not.toBeInTheDocument();
  });

  // ---------------------------------------------------------------------------
  // Interaction behaviour
  // ---------------------------------------------------------------------------

  it('invokes callback props when corresponding buttons are clicked', () => {
    render(
      <LinkToolbar
        url={MOCK_URL}
        text={MOCK_TEXT}
        editLink={editLink}
        deleteLink={deleteLink}
        startHideTimer={startHideTimer}
        stopHideTimer={stopHideTimer}
      />
    );

    fireEvent.click(screen.getByTestId('edit-link-button'));
    expect(editLink).toHaveBeenCalledTimes(1);

    fireEvent.click(screen.getByTestId('delete-link-button'));
    expect(deleteLink).toHaveBeenCalledTimes(1);
  });

  it('calls startHideTimer and stopHideTimer on mouse leave/enter events', () => {
    render(
      <LinkToolbar
        url={MOCK_URL}
        text={MOCK_TEXT}
        editLink={editLink}
        deleteLink={deleteLink}
        startHideTimer={startHideTimer}
        stopHideTimer={stopHideTimer}
      />
    );

    const root = screen.getByTestId('link-toolbar-root');

    fireEvent.mouseEnter(root);
    expect(stopHideTimer).toHaveBeenCalledTimes(1);

    fireEvent.mouseLeave(root);
    expect(startHideTimer).toHaveBeenCalledTimes(1);
  });
});
