/**
 * NOTE: This file is a copy of the original file from the blocknote-react package.
 * The original file can be found at: `packages/react/src/components/LinkToolbar/LinkToolbarProps.ts`.
 */
import type {
  BlockNoteEditor,
  BlockSchema,
  InlineContentSchema,
  LinkToolbarState,
  StyleSchema,
  UiElementPosition,
} from '@blocknote/core';

export type LinkToolbarProps = Omit<LinkToolbarState, keyof UiElementPosition> &
  Pick<
    BlockNoteEditor<BlockSchema, InlineContentSchema, StyleSchema>['linkToolbar'],
    'deleteLink' | 'editLink' | 'startHideTimer' | 'stopHideTimer'
  >;
