import type {
  BlockNoteEditor,
  BlockSchema,
  DefaultBlockSchema,
  DefaultInlineContentSchema,
  DefaultStyleSchema,
  InlineContentSchema,
  SideMenuState,
  StyleSchema,
  UiElementPosition,
} from '@blocknote/core';
import type { FC } from 'react';
import type * as React from 'react';

import type { DragHandleMenuProps } from '@blocknote/react';

export type SideMenuProps<
  BSchema extends BlockSchema = DefaultBlockSchema,
  I extends InlineContentSchema = DefaultInlineContentSchema,
  S extends StyleSchema = DefaultStyleSchema,
> = {
  editor: BlockNoteEditor<BSchema, I, S>;
  dragHandleMenu?: FC<DragHandleMenuProps<BSchema, I, S>>;
  addBlock: (e?: React.MouseEvent) => void;
  blockDragStart: () => void;
  blockDragEnd: () => void;
  freezeMenu: () => void;
  unfreezeMenu: () => void;
} & Omit<SideMenuState<BSchema, I, S>, keyof UiElementPosition>;
