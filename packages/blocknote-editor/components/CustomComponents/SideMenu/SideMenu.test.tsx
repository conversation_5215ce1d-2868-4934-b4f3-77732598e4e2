import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
/**
 * Comprehensive unit tests for SideMenu component
 *
 * This test suite covers:
 * - Default rendering with default buttons (AddBlockButton and DragHandleButton)
 * - Custom children rendering
 * - Data attributes generation based on block properties
 * - Component integration with useComponentsContext
 * - Edge cases for different block types (paragraph, heading, file blocks)
 * - Props handling and component composition
 */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { MantineWrapper } from '../../../utils/unitTest';

// Mock external dependencies
vi.mock('@blocknote/react', () => {
  const mockComponents = {
    SideMenu: {
      Root: ({ children, className, ...dataAttributes }) => (
        <div data-testid='side-menu-root' className={className} {...dataAttributes}>
          {children}
        </div>
      ),
    },
  };

  return {
    useComponentsContext: () => mockComponents,
  };
});

// Mock the default button components
vi.mock('./DefaultButtons/AddBlockButton', () => ({
  AddBlockButton: ({ addBlock }) => (
    <button type='button' data-testid='add-block-button' onClick={addBlock}>
      Add Block
    </button>
  ),
}));

vi.mock('./DefaultButtons/DragHandleButton', () => ({
  DragHandleButton: (props) => (
    <button
      type='button'
      data-testid='drag-handle-button'
      onClick={() => {
        if (props.blockDragStart) props.blockDragStart();
        if (props.blockDragEnd) props.blockDragEnd();
      }}
    >
      Drag Handle
    </button>
  ),
}));

// Import the component after mocking
import { SideMenu } from './SideMenu';

// Test constants
const MOCK_EDITOR = {
  schema: {
    blockSchema: {
      paragraph: { isFileBlock: false },
      heading: { isFileBlock: false },
      image: { isFileBlock: true },
      video: { isFileBlock: true },
    },
  },
};

const MOCK_PARAGRAPH_BLOCK = {
  id: 'paragraph-123',
  type: 'paragraph',
  props: {},
  content: [],
};

const MOCK_HEADING_BLOCK = {
  id: 'heading-123',
  type: 'heading',
  props: {
    level: 2,
  },
  content: [],
};

const MOCK_IMAGE_BLOCK_WITH_URL = {
  id: 'image-123',
  type: 'image',
  props: {
    url: 'https://example.com/image.jpg',
  },
  content: [],
};

const MOCK_IMAGE_BLOCK_WITHOUT_URL = {
  id: 'image-456',
  type: 'image',
  props: {
    url: '',
  },
  content: [],
};

const MOCK_BASE_PROPS = {
  editor: MOCK_EDITOR,
  addBlock: vi.fn(),
  blockDragStart: vi.fn(),
  blockDragEnd: vi.fn(),
  freezeMenu: vi.fn(),
  unfreezeMenu: vi.fn(),
};

const MOCK_PARAGRAPH_PROPS = {
  ...MOCK_BASE_PROPS,
  block: MOCK_PARAGRAPH_BLOCK,
};

const MOCK_HEADING_PROPS = {
  ...MOCK_BASE_PROPS,
  block: MOCK_HEADING_BLOCK,
};

const MOCK_IMAGE_WITH_URL_PROPS = {
  ...MOCK_BASE_PROPS,
  block: MOCK_IMAGE_BLOCK_WITH_URL,
};

const MOCK_IMAGE_WITHOUT_URL_PROPS = {
  ...MOCK_BASE_PROPS,
  block: MOCK_IMAGE_BLOCK_WITHOUT_URL,
};

// Custom components for testing children prop
const CUSTOM_BUTTON = () => (
  <button type='button' data-testid='custom-button'>
    Custom Button
  </button>
);

const MULTIPLE_CUSTOM_CHILDREN = () => (
  <>
    <button type='button' data-testid='custom-button-1'>
      Custom Button 1
    </button>
    <button type='button' data-testid='custom-button-2'>
      Custom Button 2
    </button>
  </>
);

describe('SideMenu component', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders the SideMenu root with correct className', () => {
      render(<SideMenu {...MOCK_PARAGRAPH_PROPS} />, { wrapper: MantineWrapper });

      const sideMenuRoot = screen.getByTestId('side-menu-root');
      expect(sideMenuRoot).toBeInTheDocument();
      expect(sideMenuRoot).toHaveClass('bn-side-menu');
    });

    it('renders default buttons when no children are provided', () => {
      render(<SideMenu {...MOCK_PARAGRAPH_PROPS} />, { wrapper: MantineWrapper });

      // Should render both default buttons
      expect(screen.getByTestId('add-block-button')).toBeInTheDocument();
      expect(screen.getByTestId('drag-handle-button')).toBeInTheDocument();

      // Check button content
      expect(screen.getByText('Add Block')).toBeInTheDocument();
      expect(screen.getByText('Drag Handle')).toBeInTheDocument();
    });

    it('renders custom children when provided', () => {
      render(
        <SideMenu {...MOCK_PARAGRAPH_PROPS}>
          <CUSTOM_BUTTON />
        </SideMenu>,
        { wrapper: MantineWrapper }
      );

      // Should render custom button
      expect(screen.getByTestId('custom-button')).toBeInTheDocument();
      expect(screen.getByText('Custom Button')).toBeInTheDocument();

      // Should not render default buttons
      expect(screen.queryByTestId('add-block-button')).not.toBeInTheDocument();
      expect(screen.queryByTestId('drag-handle-button')).not.toBeInTheDocument();
    });

    it('renders multiple custom children correctly', () => {
      render(
        <SideMenu {...MOCK_PARAGRAPH_PROPS}>
          <MULTIPLE_CUSTOM_CHILDREN />
        </SideMenu>,
        { wrapper: MantineWrapper }
      );

      // Should render both custom buttons
      expect(screen.getByTestId('custom-button-1')).toBeInTheDocument();
      expect(screen.getByTestId('custom-button-2')).toBeInTheDocument();
      expect(screen.getByText('Custom Button 1')).toBeInTheDocument();
      expect(screen.getByText('Custom Button 2')).toBeInTheDocument();

      // Should not render default buttons
      expect(screen.queryByTestId('add-block-button')).not.toBeInTheDocument();
      expect(screen.queryByTestId('drag-handle-button')).not.toBeInTheDocument();
    });
  });

  describe('Data Attributes Generation', () => {
    it('generates correct data attributes for paragraph block', () => {
      render(<SideMenu {...MOCK_PARAGRAPH_PROPS} />, { wrapper: MantineWrapper });

      const sideMenuRoot = screen.getByTestId('side-menu-root');
      expect(sideMenuRoot).toHaveAttribute('data-block-type', 'paragraph');

      // Should not have heading-specific attributes
      expect(sideMenuRoot).not.toHaveAttribute('data-level');
      expect(sideMenuRoot).not.toHaveAttribute('data-url');
    });

    it('generates correct data attributes for heading block with level', () => {
      render(<SideMenu {...MOCK_HEADING_PROPS} />, { wrapper: MantineWrapper });

      const sideMenuRoot = screen.getByTestId('side-menu-root');
      expect(sideMenuRoot).toHaveAttribute('data-block-type', 'heading');
      expect(sideMenuRoot).toHaveAttribute('data-level', '2');

      // Should not have file block attributes
      expect(sideMenuRoot).not.toHaveAttribute('data-url');
    });

    it('generates correct data attributes for file block with URL', () => {
      render(<SideMenu {...MOCK_IMAGE_WITH_URL_PROPS} />, { wrapper: MantineWrapper });

      const sideMenuRoot = screen.getByTestId('side-menu-root');
      expect(sideMenuRoot).toHaveAttribute('data-block-type', 'image');
      expect(sideMenuRoot).toHaveAttribute('data-url', 'true');

      // Should not have heading-specific attributes
      expect(sideMenuRoot).not.toHaveAttribute('data-level');
    });

    it('generates correct data attributes for file block without URL', () => {
      render(<SideMenu {...MOCK_IMAGE_WITHOUT_URL_PROPS} />, { wrapper: MantineWrapper });

      const sideMenuRoot = screen.getByTestId('side-menu-root');
      expect(sideMenuRoot).toHaveAttribute('data-block-type', 'image');
      expect(sideMenuRoot).toHaveAttribute('data-url', 'false');

      // Should not have heading-specific attributes
      expect(sideMenuRoot).not.toHaveAttribute('data-level');
    });
  });

  describe('Props Handling', () => {
    it('passes addBlock prop to AddBlockButton correctly', () => {
      render(<SideMenu {...MOCK_PARAGRAPH_PROPS} />, { wrapper: MantineWrapper });

      const addBlockButton = screen.getByTestId('add-block-button');
      fireEvent.click(addBlockButton);

      expect(MOCK_PARAGRAPH_PROPS.addBlock).toHaveBeenCalledTimes(1);
    });

    it('passes remaining props to DragHandleButton correctly', () => {
      render(<SideMenu {...MOCK_PARAGRAPH_PROPS} />, { wrapper: MantineWrapper });

      const dragHandleButton = screen.getByTestId('drag-handle-button');
      fireEvent.click(dragHandleButton);

      // Note: DragHandleButton mock calls both functions for testing
      expect(MOCK_PARAGRAPH_PROPS.blockDragStart).toHaveBeenCalledTimes(1);
      expect(MOCK_PARAGRAPH_PROPS.blockDragEnd).toHaveBeenCalledTimes(1);
    });

    it('handles props destructuring correctly without affecting functionality', () => {
      const propsWithExtraFields = {
        ...MOCK_PARAGRAPH_PROPS,
        extraProp: 'should not affect rendering',
      };

      render(<SideMenu {...propsWithExtraFields} />, { wrapper: MantineWrapper });

      // Should still render normally
      expect(screen.getByTestId('side-menu-root')).toBeInTheDocument();
      expect(screen.getByTestId('add-block-button')).toBeInTheDocument();
      expect(screen.getByTestId('drag-handle-button')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('integrates correctly with useComponentsContext', () => {
      render(<SideMenu {...MOCK_PARAGRAPH_PROPS} />, { wrapper: MantineWrapper });

      // Should use the mocked components from useComponentsContext
      expect(screen.getByTestId('side-menu-root')).toBeInTheDocument();
    });

    it('handles missing or undefined props gracefully', () => {
      const minimalProps = {
        ...MOCK_BASE_PROPS,
        block: {
          id: 'minimal-block',
          type: 'paragraph',
          props: {},
          content: [],
        },
      };

      expect(() => {
        render(<SideMenu {...minimalProps} />, { wrapper: MantineWrapper });
      }).not.toThrow();

      expect(screen.getByTestId('side-menu-root')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles block with undefined props', () => {
      const blockWithUndefinedProps = {
        ...MOCK_BASE_PROPS,
        block: {
          id: 'undefined-props-block',
          type: 'paragraph',
          props: undefined,
          content: [],
        },
      };

      expect(() => {
        render(<SideMenu {...blockWithUndefinedProps} />, { wrapper: MantineWrapper });
      }).not.toThrow();

      const sideMenuRoot = screen.getByTestId('side-menu-root');
      expect(sideMenuRoot).toHaveAttribute('data-block-type', 'paragraph');
    });

    it('handles unknown block types gracefully', () => {
      const editorWithUnknownBlock = {
        schema: {
          blockSchema: {
            unknownBlock: { isFileBlock: false },
          },
        },
      };

      const unknownBlockProps = {
        ...MOCK_BASE_PROPS,
        editor: editorWithUnknownBlock,
        block: {
          id: 'unknown-block',
          type: 'unknownBlock',
          props: {},
          content: [],
        },
      };

      expect(() => {
        render(<SideMenu {...unknownBlockProps} />, { wrapper: MantineWrapper });
      }).not.toThrow();

      const sideMenuRoot = screen.getByTestId('side-menu-root');
      expect(sideMenuRoot).toHaveAttribute('data-block-type', 'unknownBlock');
    });

    it('handles null or empty children gracefully', () => {
      render(<SideMenu {...MOCK_PARAGRAPH_PROPS}>{null}</SideMenu>, { wrapper: MantineWrapper });

      // Should render the SideMenu root
      expect(screen.getByTestId('side-menu-root')).toBeInTheDocument();
    });
  });

  describe('Component Memoization', () => {
    it('recalculates data attributes when block props change', () => {
      const { rerender } = render(<SideMenu {...MOCK_PARAGRAPH_PROPS} />, {
        wrapper: MantineWrapper,
      });

      let sideMenuRoot = screen.getByTestId('side-menu-root');
      expect(sideMenuRoot).toHaveAttribute('data-block-type', 'paragraph');

      // Change to heading block
      rerender(
        <MantineWrapper>
          <SideMenu {...MOCK_HEADING_PROPS} />
        </MantineWrapper>
      );

      sideMenuRoot = screen.getByTestId('side-menu-root');
      expect(sideMenuRoot).toHaveAttribute('data-block-type', 'heading');
      expect(sideMenuRoot).toHaveAttribute('data-level', '2');
    });
  });
});
