import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
/**
 * Comprehensive unit tests for DragHandleButton component
 *
 * This test suite covers:
 * - Component rendering with default and custom props
 * - Menu open/close handlers
 * - Drag start/end event handlers
 * - Component dependencies and integrations
 * - Edge cases and prop variations
 */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { MantineWrapper } from '../../../../utils/unitTest';

// Mock external dependencies
vi.mock('@blocknote/react', () => {
  const mockComponents = {
    Generic: {
      Menu: {
        Root: ({ children, onOpenChange, position, ...props }) => (
          <div data-testid='menu-root' data-position={position} {...props}>
            <button
              type='button'
              data-testid='menu-trigger-test'
              onClick={() => onOpenChange?.(true)}
              onBlur={() => onOpenChange?.(false)}
            >
              Open Menu
            </button>
            {children}
          </div>
        ),
        Trigger: ({ children }) => <div data-testid='menu-trigger'>{children}</div>,
      },
    },
    SideMenu: {
      Button: ({ label, draggable, onDragStart, onDragEnd, className, icon, type, ...props }) => (
        <button
          data-testid='side-menu-button'
          draggable={draggable}
          onDragStart={onDragStart}
          onDragEnd={onDragEnd}
          className={className}
          type={type}
          aria-label={label}
          {...props}
        >
          {icon}
          {label}
        </button>
      ),
    },
  };

  return {
    useComponentsContext: () => mockComponents,
    useDictionary: () => ({
      side_menu: {
        drag_handle_label: 'Drag Handle',
      },
    }),
    DragHandleMenu: ({ block }) => (
      <div data-testid='default-drag-handle-menu'>
        Default Menu for block: {block?.id || 'unknown'}
      </div>
    ),
  };
});

vi.mock('@tabler/icons-react', () => ({
  IconGripVertical: ({ size, 'data-test': dataTest }) => (
    <span data-testid='icon-grip-vertical' data-size={size} data-test={dataTest}>
      Grip Icon
    </span>
  ),
}));

// Import the component after mocking
import { DragHandleButton } from './DragHandleButton';

// Test constants
const MOCK_BLOCK = {
  id: 'test-block-123',
  type: 'paragraph',
  props: {},
  content: [],
};

const MOCK_PROPS = {
  block: MOCK_BLOCK,
  freezeMenu: vi.fn(),
  unfreezeMenu: vi.fn(),
  blockDragStart: vi.fn(),
  blockDragEnd: vi.fn(),
};

const CUSTOM_DRAG_HANDLE_MENU = ({ block }) => (
  <div data-testid='custom-drag-handle-menu'>Custom Menu for block: {block?.id || 'unknown'}</div>
);

const MOCK_PROPS_WITH_CUSTOM_MENU = {
  ...MOCK_PROPS,
  dragHandleMenu: CUSTOM_DRAG_HANDLE_MENU,
};

describe('DragHandleButton component', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders the component with all required elements', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      // Check if menu root is rendered
      expect(screen.getByTestId('menu-root')).toBeInTheDocument();
      expect(screen.getByTestId('menu-root')).toHaveAttribute('data-position', 'left');

      // Check if menu trigger is rendered
      expect(screen.getByTestId('menu-trigger')).toBeInTheDocument();

      // Check if side menu button is rendered
      expect(screen.getByTestId('side-menu-button')).toBeInTheDocument();

      // Check if icon is rendered
      expect(screen.getByTestId('icon-grip-vertical')).toBeInTheDocument();
      expect(screen.getByTestId('icon-grip-vertical')).toHaveAttribute('data-size', '24');
      expect(screen.getByTestId('icon-grip-vertical')).toHaveAttribute('data-test', 'dragHandle');
    });

    it('renders with correct button properties', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      const button = screen.getByTestId('side-menu-button');

      // Check button attributes
      expect(button).toHaveAttribute('draggable', 'true');
      expect(button).toHaveAttribute('type', 'button');
      expect(button).toHaveAttribute('aria-label', 'Drag Handle');
      expect(button).toHaveClass('bn-button');
    });

    it('renders with default DragHandleMenu when no custom menu provided', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      // Should render default menu
      expect(screen.getByTestId('default-drag-handle-menu')).toBeInTheDocument();
      expect(screen.getByText('Default Menu for block: test-block-123')).toBeInTheDocument();

      // Should not render custom menu
      expect(screen.queryByTestId('custom-drag-handle-menu')).not.toBeInTheDocument();
    });

    it('renders with custom DragHandleMenu when provided via props', () => {
      render(<DragHandleButton {...MOCK_PROPS_WITH_CUSTOM_MENU} />, { wrapper: MantineWrapper });

      // Should render custom menu
      expect(screen.getByTestId('custom-drag-handle-menu')).toBeInTheDocument();
      expect(screen.getByText('Custom Menu for block: test-block-123')).toBeInTheDocument();

      // Should not render default menu
      expect(screen.queryByTestId('default-drag-handle-menu')).not.toBeInTheDocument();

      // Custom menu should be rendered with the correct block data
      expect(screen.getByText('Custom Menu for block: test-block-123')).toBeInTheDocument();
    });
  });

  describe('Menu Open/Close Handlers', () => {
    it('calls freezeMenu when menu opens', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      const menuTrigger = screen.getByTestId('menu-trigger-test');
      fireEvent.click(menuTrigger);

      expect(MOCK_PROPS.freezeMenu).toHaveBeenCalledTimes(1);
    });

    it('calls unfreezeMenu when menu closes', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      const menuTrigger = screen.getByTestId('menu-trigger-test');

      // First open the menu
      fireEvent.click(menuTrigger);

      // Then close it
      fireEvent.blur(menuTrigger);

      expect(MOCK_PROPS.unfreezeMenu).toHaveBeenCalledTimes(1);
    });

    it('handles multiple menu open/close cycles correctly', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      const menuTrigger = screen.getByTestId('menu-trigger-test');

      // Multiple open/close cycles
      fireEvent.click(menuTrigger); // Open
      fireEvent.blur(menuTrigger); // Close
      fireEvent.click(menuTrigger); // Open again
      fireEvent.blur(menuTrigger); // Close again

      expect(MOCK_PROPS.freezeMenu).toHaveBeenCalledTimes(2);
      expect(MOCK_PROPS.unfreezeMenu).toHaveBeenCalledTimes(2);
    });
  });

  describe('Drag Event Handlers', () => {
    it('calls blockDragStart when drag starts', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      const button = screen.getByTestId('side-menu-button');
      fireEvent.dragStart(button);

      expect(MOCK_PROPS.blockDragStart).toHaveBeenCalledTimes(1);
    });

    it('calls blockDragEnd when drag ends', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      const button = screen.getByTestId('side-menu-button');
      fireEvent.dragEnd(button);

      expect(MOCK_PROPS.blockDragEnd).toHaveBeenCalledTimes(1);
    });

    it('handles multiple drag events correctly', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      const button = screen.getByTestId('side-menu-button');

      // Multiple drag cycles
      fireEvent.dragStart(button);
      fireEvent.dragEnd(button);
      fireEvent.dragStart(button);
      fireEvent.dragEnd(button);

      expect(MOCK_PROPS.blockDragStart).toHaveBeenCalledTimes(2);
      expect(MOCK_PROPS.blockDragEnd).toHaveBeenCalledTimes(2);
    });
  });

  describe('Edge Cases and Props Validation', () => {
    it('handles undefined block gracefully', () => {
      const propsWithUndefinedBlock = {
        ...MOCK_PROPS,
        block: undefined,
      };

      expect(() => {
        render(<DragHandleButton {...propsWithUndefinedBlock} />, { wrapper: MantineWrapper });
      }).not.toThrow();

      // Should still render the component
      expect(screen.getByTestId('side-menu-button')).toBeInTheDocument();
    });

    it('handles null dragHandleMenu prop correctly', () => {
      const propsWithNullMenu = {
        ...MOCK_PROPS,
        dragHandleMenu: null,
      };

      render(<DragHandleButton {...propsWithNullMenu} />, { wrapper: MantineWrapper });

      // Should use default menu when dragHandleMenu is null
      expect(screen.getByTestId('default-drag-handle-menu')).toBeInTheDocument();
    });

    it('handles undefined dragHandleMenu prop correctly', () => {
      const propsWithUndefinedMenu = {
        ...MOCK_PROPS,
        dragHandleMenu: undefined,
      };

      render(<DragHandleButton {...propsWithUndefinedMenu} />, { wrapper: MantineWrapper });

      // Should use default menu when dragHandleMenu is undefined
      expect(screen.getByTestId('default-drag-handle-menu')).toBeInTheDocument();
    });

    it('maintains component stability with different block types', () => {
      const differentBlock = {
        id: 'different-block-456',
        type: 'heading',
        props: { level: 1 },
        content: [],
      };

      const propsWithDifferentBlock = {
        ...MOCK_PROPS,
        block: differentBlock,
      };

      render(<DragHandleButton {...propsWithDifferentBlock} />, { wrapper: MantineWrapper });

      expect(screen.getByTestId('side-menu-button')).toBeInTheDocument();
      expect(screen.getByText('Default Menu for block: different-block-456')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('integrates correctly with useComponentsContext hook', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      // Verify that the mocked components are used
      expect(screen.getByTestId('menu-root')).toBeInTheDocument();
      expect(screen.getByTestId('menu-trigger')).toBeInTheDocument();
      expect(screen.getByTestId('side-menu-button')).toBeInTheDocument();
    });

    it('integrates correctly with useDictionary hook', () => {
      render(<DragHandleButton {...MOCK_PROPS} />, { wrapper: MantineWrapper });

      const button = screen.getByTestId('side-menu-button');
      expect(button).toHaveAttribute('aria-label', 'Drag Handle');
    });

    it('passes block prop correctly to drag handle menu component', () => {
      render(<DragHandleButton {...MOCK_PROPS_WITH_CUSTOM_MENU} />, { wrapper: MantineWrapper });

      // Verify that the menu component receives the block prop correctly
      expect(screen.getByText('Custom Menu for block: test-block-123')).toBeInTheDocument();
    });
  });
});
