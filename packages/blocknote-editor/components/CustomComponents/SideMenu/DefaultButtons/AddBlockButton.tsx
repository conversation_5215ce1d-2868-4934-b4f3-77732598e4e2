import type {
  BlockSchema,
  DefaultBlockSchema,
  DefaultInlineContentSchema,
  DefaultStyleSchema,
  InlineContentSchema,
  StyleSchema,
} from '@blocknote/core';
import { useComponentsContext, useDictionary } from '@blocknote/react';
import { IconPlus } from '@tabler/icons-react';

import type { SideMenuProps } from '../SideMenuProps';

export const AddBlockButton = <
  BSchema extends BlockSchema = DefaultBlockSchema,
  I extends InlineContentSchema = DefaultInlineContentSchema,
  S extends StyleSchema = DefaultStyleSchema,
>(
  props: Pick<SideMenuProps<BSchema, I, S>, 'addBlock'>
) => {
  const Components = useComponentsContext()!;
  const dict = useDictionary();
  /**
   * Extended button props
   * NOTE: The `ExtendedButtonProps` is used to prevent the button from submitting a form
   * when it is inside a form element.
   */
  const extendedButtonProps = { type: 'button' };

  return (
    <Components.SideMenu.Button
      className={'bn-button'}
      label={dict.side_menu.add_block_label}
      icon={<IconPlus size={24} data-test='dragHandleAdd' />}
      onClick={props.addBlock}
      {...extendedButtonProps}
    />
  );
};
