import type {
  BlockSchema,
  DefaultBlockSchema,
  DefaultInlineContentSchema,
  DefaultStyleSchema,
  InlineContentSchema,
  StyleSchema,
} from '@blocknote/core';
import { DragHandleMenu, useComponentsContext, useDictionary } from '@blocknote/react';
import { IconGripVertical } from '@tabler/icons-react';

import type { SideMenuProps } from '../SideMenuProps';

export const DragHandleButton = <
  BSchema extends BlockSchema = DefaultBlockSchema,
  I extends InlineContentSchema = DefaultInlineContentSchema,
  S extends StyleSchema = DefaultStyleSchema,
>(
  props: Omit<SideMenuProps<BSchema, I, S>, 'addBlock'>
) => {
  const Components = useComponentsContext()!;
  const dict = useDictionary();

  const Component = props.dragHandleMenu ?? DragHandleMenu;
  /**
   * Extended button props
   * NOTE: The `ExtendedButtonProps` is used to prevent the button from submitting a form
   * when it is inside a form element.
   */
  const extendedButtonProps = { type: 'button' };

  /**
   * Handler for when the menu opens
   */
  const handleMenuOpen = () => {
    props.freezeMenu();
  };

  /**
   * Handler for when the menu closes
   */
  const handleMenuClose = () => {
    props.unfreezeMenu();
  };

  /**
   * Handler for menu open/close changes
   */
  const handleOpenChange = (open: boolean) => {
    if (open) {
      handleMenuOpen();
    } else {
      handleMenuClose();
    }
  };

  return (
    <Components.Generic.Menu.Root onOpenChange={handleOpenChange} position={'left'}>
      <Components.Generic.Menu.Trigger>
        <Components.SideMenu.Button
          label={dict.side_menu.drag_handle_label}
          draggable={true}
          onDragStart={() => props.blockDragStart()}
          onDragEnd={() => props.blockDragEnd()}
          className={'bn-button'}
          icon={<IconGripVertical size={24} data-test='dragHandle' />}
          {...extendedButtonProps}
        />
      </Components.Generic.Menu.Trigger>
      <Component block={props.block} />
    </Components.Generic.Menu.Root>
  );
};
