import { useDictionary } from '@blocknote/react';
import { Box, Button, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import get from 'lodash/get';
import type React from 'react';

const useStyles = createStyles((theme) => ({
  actionsBox: {
    display: 'flex',
    justifyContent: 'flex-end',
    marginTop: theme.spacing.md,
    gap: theme.spacing.xs,
    button: {
      minWidth: rem(80),
    },
    'button .mantine-Button-label': {
      fontSize: theme.fontSizes.md,
      fontWeight: 500,
    },
  },
  cancelButton: {
    color: `${theme.colors.decaNavy?.[5]} !important`,
    '&:hover': {
      backgroundColor: `${theme.colors.decaNavy?.[0]} !important`,
    },
  },
  applyButton: {
    backgroundColor: `${theme.colors.decaNavy?.[5]} !important`,
    color: `${theme.colors.decaMono?.[9]} !important`,
    '&:hover': {
      backgroundColor: `${theme.colors.decaMono?.[9]} !important`,
      color: `${theme.colors.decaNavy?.[5]} !important`,
      borderColor: `${theme.colors.decaNavy?.[5]} !important`,
    },
  },
}));

interface SettingActionsProps {
  onApply: () => void;
  onCancel: () => void;
}

const SettingActions: React.FC<SettingActionsProps> = ({ onApply, onCancel }) => {
  const dict = useDictionary();
  const { classes } = useStyles();

  return (
    <Box className={classes.actionsBox}>
      <Button className={classes.cancelButton} size='sm' variant='outline' onClick={onCancel}>
        {get(dict, 'custom.actions.cancel', 'Cancel')}
      </Button>
      <Button className={classes.applyButton} size='sm' variant='filled' onClick={onApply}>
        {get(dict, 'custom.actions.apply', 'Apply')}
      </Button>
    </Box>
  );
};

export default SettingActions;
