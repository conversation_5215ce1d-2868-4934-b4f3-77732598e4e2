import { DragHandleMenu, RemoveBlockItem } from '@blocknote/react';
import type React from 'react';
import {
  type ControlComponentType,
  renderConditionalComponent,
} from '../../utils/componentWrappers';
import { SideMenu } from '../CustomComponents';

/**
 * Side Menu Wrapper Component Props
 */
export interface SideMenuWrapperProps {
  customComponent?: ControlComponentType;
  dictionary?: Record<string, any>;
  deleteMenuItemText?: string;
  [key: string]: any; // Allow any additional props to be passed through
}

/**
 * Side Menu Wrapper Component
 */
export const SideMenuWrapper: React.FC<SideMenuWrapperProps> = ({
  customComponent,
  dictionary,
  deleteMenuItemText,
  ...props
}) => {
  const menuItemText = deleteMenuItemText ?? dictionary?.drag_handle?.delete_menuitem ?? '';

  if (!props.editor || !props.block) {
    return null;
  }

  // Use shared utility for conditional component rendering
  return renderConditionalComponent(customComponent, SideMenu, props, {
    dragHandleMenu: (dragProps: any) => (
      <DragHandleMenu {...dragProps}>
        <RemoveBlockItem {...dragProps}>{menuItemText}</RemoveBlockItem>
      </DragHandleMenu>
    ),
  });
};

export default SideMenuWrapper;
