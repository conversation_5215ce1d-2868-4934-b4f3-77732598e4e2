// -----------------------------------------------------------------------------
// FileReplaceButton.test.tsx
// -----------------------------------------------------------------------------
import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Component under test
import { FileReplaceButton } from './FileReplaceButton';

// -----------------------------------------------------------------------------
// Mocks
// -----------------------------------------------------------------------------

// Mock for @blocknote/core utilities — we create the mock function inside the
// factory (hoisted) and later access it through the imported module to set
// return values.
vi.mock('@blocknote/core', () => ({
  checkBlockIsFileBlock: vi.fn(),
}));

// After the mock is declared (but still before tests run), import the module so
// we can easily reference the mock function.
import * as BlocknoteCore from '@blocknote/core';

// Dynamic variables that we need to tweak inside test cases
let mockSelectedBlocks: any[] = [];
const mockEditor = { isEditable: true } as any;

// Simplified dictionary just for this component
const mockDictionary = {
  formatting_toolbar: {
    file_replace: {
      tooltip: {
        file: 'Replace file',
      },
    },
  },
};

// A very lightweight set of components that emulate the shape expected by
// FileReplaceButton (Generic.Popover + FormattingToolbar.Button). We don’t rely
// on Mantine here to avoid unnecessary complexity — these stubs are more than
// enough for behavioural testing.
const mockComponentsContext = {
  Generic: {
    Popover: {
      Root: ({ opened, children }: { opened: boolean; children: React.ReactNode }) => {
        const childArray = React.Children.toArray(children);
        const trigger = childArray[0];
        const content = childArray.slice(1);
        return (
          <div data-testid='popover-root'>
            {trigger}
            {opened ? content : null}
          </div>
        );
      },
      Trigger: ({ children }: { children: React.ReactNode }) => (
        <div data-testid='popover-trigger'>{children}</div>
      ),
      Content: ({ children }: { children: React.ReactNode }) => (
        <div data-testid='popover-content'>{children}</div>
      ),
    },
  },
  FormattingToolbar: {
    Button: ({ onClick, icon }: { onClick: () => void; icon: React.ReactNode }) => (
      <button type='button' data-testid='formatting-button' onClick={onClick}>
        {icon}
      </button>
    ),
  },
} as any;

// Mock hooks from @blocknote/react
vi.mock('@blocknote/react', () => ({
  useBlockNoteEditor: () => mockEditor,
  useComponentsContext: () => mockComponentsContext,
  useDictionary: () => mockDictionary,
  useSelectedBlocks: () => mockSelectedBlocks,
}));

// Mock Mantine’s useClickOutside — it returns a ref but its implementation is
// irrelevant for these unit tests.
vi.mock('@mantine/hooks', () => ({
  useClickOutside: () => React.useRef(null),
}));

// Mock FilePanel so we can easily assert its presence without pulling all of
// its dependencies in.
vi.mock('../CustomComponents', () => ({
  FilePanel: () => <div data-testid='file-panel'>File Panel</div>,
}));

// -----------------------------------------------------------------------------
// Test helpers
// -----------------------------------------------------------------------------
import { renderWithMantine } from '../../utils/unitTest';

const renderComponent = () => renderWithMantine(<FileReplaceButton />);

// -----------------------------------------------------------------------------
// Tests
// -----------------------------------------------------------------------------

describe('FileReplaceButton', () => {
  beforeEach(() => {
    // Reset all runtime mocks to a clean state before each test
    vi.clearAllMocks();
    mockSelectedBlocks = [];
    mockEditor.isEditable = true;
    (BlocknoteCore.checkBlockIsFileBlock as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      true
    );
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('does not render when no block is selected', () => {
    mockSelectedBlocks = [];
    renderComponent();
    expect(screen.queryByTestId('formatting-button')).toBeNull();
  });

  it('does not render when the selected block is not a file block', () => {
    mockSelectedBlocks = [{ id: '1', type: 'paragraph' }];
    (BlocknoteCore.checkBlockIsFileBlock as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      false
    );

    renderComponent();
    expect(screen.queryByTestId('formatting-button')).toBeNull();
  });

  it('does not render when the editor is not editable', () => {
    mockSelectedBlocks = [{ id: '1', type: 'file' }];
    mockEditor.isEditable = false;

    renderComponent();
    expect(screen.queryByTestId('formatting-button')).toBeNull();
  });

  it('renders button and toggles popover with FilePanel', () => {
    mockSelectedBlocks = [{ id: '1', type: 'file' }];
    mockEditor.isEditable = true;
    (BlocknoteCore.checkBlockIsFileBlock as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      true
    );

    renderComponent();

    // Button should be visible
    const button = screen.getByTestId('formatting-button');
    expect(button).toBeInTheDocument();

    // FilePanel should not be in the document initially
    expect(screen.queryByTestId('file-panel')).not.toBeInTheDocument();

    // Click the button to open popover
    fireEvent.click(button);
    expect(screen.getByTestId('file-panel')).toBeInTheDocument();

    // Click again to close popover
    fireEvent.click(button);
    expect(screen.queryByTestId('file-panel')).not.toBeInTheDocument();
  });
});

// -----------------------------------------------------------------------------
// End of FileReplaceButton.test.tsx
// -----------------------------------------------------------------------------
