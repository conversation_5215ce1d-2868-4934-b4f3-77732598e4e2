import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// -----------------------------------------------------------------------------
// Component under test – imported AFTER mocks are in place
// -----------------------------------------------------------------------------

// -----------------------------------------------------------------------------
// Mocks
// -----------------------------------------------------------------------------

// Mock @blocknote/core helpers
vi.mock('@blocknote/core', () => ({
  checkBlockIsFileBlock: vi.fn(),
  checkBlockIsFileBlockWithPlaceholder: vi.fn(),
}));

// Import the mocked module to access the spy functions later
import * as BlocknoteCore from '@blocknote/core';

// Dynamic vars controlled per-test
let mockSelectedBlocks: any[] = [];
const mockEditor: any = {
  isEditable: true,
  updateBlock: vi.fn(),
};

// Simplified dictionary used by the component
const mockDictionary = {
  formatting_toolbar: {
    file_caption: {
      tooltip: 'Edit caption',
      input_placeholder: 'Enter caption',
    },
  },
};

// Minimal components context stub (Popover / FormattingToolbar / Form)
const mockComponentsContext = {
  Generic: {
    Popover: {
      Root: ({ opened, children }: { opened: boolean; children: React.ReactNode }) => {
        const childs = React.Children.toArray(children);
        const trigger = childs[0];
        const content = childs.slice(1);
        return (
          <div data-testid='popover-root'>
            {trigger}
            {opened ? content : null}
          </div>
        );
      },
      Trigger: ({ children }: { children: React.ReactNode }) => (
        <div data-testid='popover-trigger'>{children}</div>
      ),
      Content: ({ children }: { children: React.ReactNode }) => (
        <div data-testid='popover-content'>{children}</div>
      ),
    },
    Form: {
      Root: ({ children }: { children: React.ReactNode }) => (
        <form data-testid='form-root'>{children}</form>
      ),
      TextInput: ({ value, onChange, onKeyDown }: any) => (
        <input
          data-testid='caption-input'
          value={value}
          onChange={onChange}
          onKeyDown={onKeyDown}
        />
      ),
    },
  },
  FormattingToolbar: {
    Button: ({ onClick, icon }: { onClick: () => void; icon: React.ReactNode }) => (
      <button type='button' data-testid='formatting-button' onClick={onClick}>
        {icon}
      </button>
    ),
  },
} as any;

// Mock hooks from @blocknote/react
vi.mock('@blocknote/react', () => ({
  useBlockNoteEditor: () => mockEditor,
  useComponentsContext: () => mockComponentsContext,
  useDictionary: () => mockDictionary,
  useSelectedBlocks: () => mockSelectedBlocks,
}));

// Mock Mantine hook
vi.mock('@mantine/hooks', () => ({
  useClickOutside: () => React.useRef(null),
}));

import { renderWithMantine } from '../../utils/unitTest';
// -----------------------------------------------------------------------------
// AFTER mocks – now import the component and test utils
// -----------------------------------------------------------------------------
import { FileCaptionButton } from './FileCaptionButton';

const renderComponent = () => renderWithMantine(<FileCaptionButton />);

// -----------------------------------------------------------------------------
// Tests
// -----------------------------------------------------------------------------

describe('FileCaptionButton', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockSelectedBlocks = [];
    mockEditor.isEditable = true;
    mockEditor.updateBlock.mockReset();
    (BlocknoteCore.checkBlockIsFileBlock as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      true
    );
    (
      BlocknoteCore.checkBlockIsFileBlockWithPlaceholder as unknown as ReturnType<typeof vi.fn>
    ).mockReturnValue(false);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('does not render when no block is selected', () => {
    mockSelectedBlocks = [];
    renderComponent();
    expect(screen.queryByTestId('formatting-button')).toBeNull();
  });

  it('does not render when selected block is not a file block', () => {
    mockSelectedBlocks = [{ id: '1', type: 'paragraph' }];
    (BlocknoteCore.checkBlockIsFileBlock as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      false
    );

    renderComponent();
    expect(screen.queryByTestId('formatting-button')).toBeNull();
  });

  it('does not render when editor is not editable', () => {
    mockSelectedBlocks = [{ id: '1', type: 'file', props: { caption: '' } }];
    mockEditor.isEditable = false;

    renderComponent();
    expect(screen.queryByTestId('formatting-button')).toBeNull();
  });

  it('does not render when block is a placeholder', () => {
    mockSelectedBlocks = [{ id: '1', type: 'file', props: { caption: '' } }];
    (
      BlocknoteCore.checkBlockIsFileBlockWithPlaceholder as unknown as ReturnType<typeof vi.fn>
    ).mockReturnValue(true);

    renderComponent();
    expect(screen.queryByTestId('formatting-button')).toBeNull();
  });

  it('renders button, opens popover, edits caption and calls updateBlock on Enter', () => {
    const initialCaption = 'Old caption';
    const newCaption = 'New caption';
    const mockBlock = { id: '1', type: 'file', props: { caption: initialCaption } };

    mockSelectedBlocks = [mockBlock];

    renderComponent();

    // Button should render
    const button = screen.getByTestId('formatting-button');
    expect(button).toBeInTheDocument();

    // Popover content should be hidden initially
    expect(screen.queryByTestId('caption-input')).not.toBeInTheDocument();

    // Open popover
    fireEvent.click(button);
    const input = screen.getByTestId('caption-input') as HTMLInputElement;
    expect(input).toBeInTheDocument();
    expect(input.value).toBe(initialCaption);

    // Change value
    fireEvent.change(input, { target: { value: newCaption } });
    expect(input.value).toBe(newCaption);

    // Press Enter -> updateBlock should be invoked
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter', charCode: 13 });
    expect(mockEditor.updateBlock).toHaveBeenCalledTimes(1);
    expect(mockEditor.updateBlock).toHaveBeenCalledWith(mockBlock, {
      props: { caption: newCaption },
    });
  });
});
