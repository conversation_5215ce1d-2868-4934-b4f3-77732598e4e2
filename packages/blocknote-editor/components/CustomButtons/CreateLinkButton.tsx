import {
  type BlockNoteEditor,
  type BlockSchema,
  type InlineContentSchema,
  type StyleSchema,
  formatKeyboardShortcut,
} from '@blocknote/core';
import {
  useBlockNoteEditor,
  useComponentsContext,
  useDictionary,
  useEditorContentOrSelectionChange,
  useSelectedBlocks,
} from '@blocknote/react';
import { IconLink } from '@tabler/icons-react';
/**
 * NOTE: This file is a copy of the original file from the blocknote-react package.
 * The original file can be found at: `packages/react/src/components/FormattingToolbar/DefaultButtons/CreateLinkButton.tsx`.
 */
import { useCallback, useMemo, useState } from 'react';
import { EditLinkMenuItems } from '../CustomComponents/LinkToolbar/EditLinkMenuItems';

function checkLinkInSchema(
  editor: BlockNoteEditor<BlockSchema, any, StyleSchema>
): editor is BlockNoteEditor<
  BlockSchema,
  {
    link: {
      type: 'link';
      propSchema: any;
      content: 'styled';
    };
  },
  StyleSchema
> {
  return (
    'link' in editor.schema.inlineContentSchema && editor.schema.inlineContentSchema.link === 'link'
  );
}

export const CreateLinkButton = () => {
  const editor = useBlockNoteEditor<BlockSchema, InlineContentSchema, StyleSchema>();
  const Components = useComponentsContext()!;
  const dict = useDictionary();

  const linkInSchema = checkLinkInSchema(editor);

  const selectedBlocks = useSelectedBlocks(editor);

  const [url, setUrl] = useState<string>(editor.getSelectedLinkUrl() || '');
  const [text, setText] = useState<string>(editor.getSelectedText());

  useEditorContentOrSelectionChange(() => {
    setText(editor.getSelectedText() || '');
    setUrl(editor.getSelectedLinkUrl() || '');
  }, editor);

  const update = useCallback(
    (url: string, text: string) => {
      editor.createLink(url, text);
      editor.focus();
    },
    [editor]
  );

  const show = useMemo(() => {
    if (!linkInSchema) {
      return false;
    }

    for (const block of selectedBlocks) {
      if (block.content === undefined) {
        return false;
      }
    }

    return true;
  }, [linkInSchema, selectedBlocks]);

  if (!show || !('link' in editor.schema.inlineContentSchema) || !editor.isEditable) {
    return null;
  }

  return (
    <section className={'bn-right-side-popover'}>
      <Components.Generic.Popover.Root>
        <Components.Generic.Popover.Trigger>
          <Components.FormattingToolbar.Button
            className={'bn-button'}
            data-test='createLink'
            label={dict.formatting_toolbar.link.tooltip}
            mainTooltip={dict.formatting_toolbar.link.tooltip}
            secondaryTooltip={formatKeyboardShortcut(
              dict.formatting_toolbar.link.secondary_tooltip,
              dict.generic.ctrl_shortcut
            )}
            icon={<IconLink strokeWidth={2} size={16} />}
          />
        </Components.Generic.Popover.Trigger>
        <Components.Generic.Popover.Content
          className={'bn-popover-content bn-form-popover'}
          variant={'form-popover'}
        >
          <EditLinkMenuItems url={url} text={text} editLink={update} />
        </Components.Generic.Popover.Content>
      </Components.Generic.Popover.Root>
    </section>
  );
};
