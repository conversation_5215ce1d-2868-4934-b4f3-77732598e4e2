import {
  type BlockSchema,
  type InlineContentSchema,
  type StyleSchema,
  checkBlockIsFileBlock,
  checkBlockIsFileBlockWithPlaceholder,
} from '@blocknote/core';
import {
  useBlockNoteEditor,
  useComponentsContext,
  useDictionary,
  useSelectedBlocks,
} from '@blocknote/react';
import { useClickOutside } from '@mantine/hooks';
import { IconForms } from '@tabler/icons-react';
/**
 * Override the default FileReplaceButton component from BlockNoteEditor
 * the original component is located at @blocknote/react/src/components/FormattingToolbar/DefaultButtons/FileCaptionButton.tsx
 */
import { useCallback, useMemo, useState } from 'react';
import type { ChangeEvent, KeyboardEvent } from 'react';

export const FileCaptionButton = () => {
  const dict = useDictionary();
  const Components = useComponentsContext()!;
  const editor = useBlockNoteEditor<BlockSchema, InlineContentSchema, StyleSchema>();
  const [currentEditingCaption, setCurrentEditingCaption] = useState<string>();
  const selectedBlocks = useSelectedBlocks(editor);
  const [opened, setOpened] = useState<boolean>(false);
  const contentRef = useClickOutside(() => setOpened(false));

  /**
   * Get the current block note context
   * This is used to get the current block note tool
   * and to check if the current block note tool is the same as the file caption tool
   */
  const fileBlock = useMemo(() => {
    // Checks if only one block is selected.
    if (selectedBlocks.length !== 1) {
      return undefined;
    }

    const block = selectedBlocks[0];
    if (checkBlockIsFileBlock(block, editor)) {
      setCurrentEditingCaption(block.props.caption);
      return block;
    }

    return undefined;
  }, [editor, selectedBlocks]);

  /**
   * Get the current block note tool
   * This is used to check if the current block note tool is the same as the file caption tool
   * @params event: KeyboardEvent
   * @returns void
   */
  const handleEnter = useCallback(
    (event: KeyboardEvent) => {
      if (fileBlock && event.key === 'Enter') {
        event.preventDefault();
        editor.updateBlock(fileBlock, {
          props: {
            caption: currentEditingCaption as never,
          },
        });
      }
    },
    [currentEditingCaption, editor, fileBlock]
  );

  /**
   * Handle the change event of the input field
   * @params event: ChangeEvent<HTMLInputElement>
   * @returns void
   */
  const handleChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => setCurrentEditingCaption(event.currentTarget?.value),
    []
  );

  if (!fileBlock || checkBlockIsFileBlockWithPlaceholder(fileBlock, editor) || !editor.isEditable) {
    return null;
  }

  return (
    <Components.Generic.Popover.Root opened={opened} position={'bottom'}>
      <Components.Generic.Popover.Trigger>
        <Components.FormattingToolbar.Button
          className={'bn-button'}
          label={dict.formatting_toolbar.file_caption.tooltip}
          mainTooltip={dict.formatting_toolbar.file_caption.tooltip}
          icon={<IconForms strokeWidth={2} size={16} />}
          isSelected={fileBlock && fileBlock.props?.caption !== ''}
          onClick={() => setOpened((prev) => !prev)}
        />
      </Components.Generic.Popover.Trigger>
      <div ref={contentRef}>
        <Components.Generic.Popover.Content
          className={'bn-popover-content bn-form-popover'}
          variant={'form-popover'}
        >
          <Components.Generic.Form.Root>
            <Components.Generic.Form.TextInput
              name={'file-caption'}
              icon={<IconForms strokeWidth={2} size={16} />}
              value={currentEditingCaption || ''}
              autoFocus={true}
              placeholder={dict.formatting_toolbar.file_caption.input_placeholder}
              onKeyDown={handleEnter}
              onChange={handleChange}
            />
          </Components.Generic.Form.Root>
        </Components.Generic.Popover.Content>
      </div>
    </Components.Generic.Popover.Root>
  );
};
