import type {
  BlockNoteEditor,
  BlockSchema,
  InlineContentSchema,
  StyleSchema,
} from '@blocknote/core';
import {
  useBlockNoteEditor,
  useComponentsContext,
  useDictionary,
  useEditorContentOrSelectionChange,
  useSelectedBlocks,
} from '@blocknote/react';
import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useClickOutside } from '@mantine/hooks';
/**
 * This file is a custom Color Style Button for BlockNote Editor.
 * Cloned from BlockNote repo:
 * `packages/react/src/components/FormattingToolbar/DefaultButtons/ColorStyleButton.tsx`
 */
import { useCallback, useMemo, useState } from 'react';
import { ColorIcon, ColorPicker } from '../CustomComponents';

const useStyles = createStyles(() => ({
  popoverContent: {
    minWidth: rem(340),
  },
}));

function checkColorInSchema<Color extends 'text' | 'background'>(
  color: Color,
  editor: BlockNoteEditor<BlockSchema, InlineContentSchema, StyleSchema>
): editor is BlockNoteEditor<
  BlockSchema,
  InlineContentSchema,
  Color extends 'text'
    ? {
        textColor: {
          type: 'textColor';
          propSchema: 'string';
        };
      }
    : {
        backgroundColor: {
          type: 'backgroundColor';
          propSchema: 'string';
        };
      }
> {
  return (
    `${color}Color` in editor.schema.styleSchema &&
    editor.schema.styleSchema[`${color}Color`].type === `${color}Color` &&
    editor.schema.styleSchema[`${color}Color`].propSchema === 'string'
  );
}

export const ColorStyleButton = () => {
  const Components = useComponentsContext()!;
  const dict = useDictionary();
  const editor = useBlockNoteEditor<BlockSchema, InlineContentSchema, StyleSchema>();

  // This is our custom state to handle click outside of BlockNote component popover
  const [opened, setOpened] = useState<boolean>(false);
  const contentRef = useClickOutside(() => {});
  const { classes, cx } = useStyles();

  const textColorInSchema = checkColorInSchema('text', editor);
  const backgroundColorInSchema = checkColorInSchema('background', editor);

  const selectedBlocks = useSelectedBlocks(editor);
  const [currentTextColor, setCurrentTextColor] = useState<string>(
    textColorInSchema ? editor.getActiveStyles().textColor || 'default' : 'default'
  );
  const [currentBackgroundColor, setCurrentBackgroundColor] = useState<string>(
    backgroundColorInSchema ? editor.getActiveStyles().backgroundColor || 'default' : 'default'
  );

  useEditorContentOrSelectionChange(() => {
    if (textColorInSchema) {
      setCurrentTextColor(editor.getActiveStyles().textColor || 'default');
    }
    if (backgroundColorInSchema) {
      setCurrentBackgroundColor(editor.getActiveStyles().backgroundColor || 'default');
    }
  }, editor);

  /**
   * Set text color to selected blocks
   * @param color - color to set
   * @returns void
   */
  const setTextColor = useCallback(
    (color: string) => {
      if (!textColorInSchema) {
        throw Error('Tried to set text color, but style does not exist in editor schema.');
      }

      if (editor.getActiveStyles().textColor === color) return;

      color === 'default'
        ? editor.removeStyles({ textColor: color })
        : editor.addStyles({ textColor: color });

      editor.focus();
    },
    [editor, textColorInSchema]
  );

  /**
   * Set background color to selected blocks
   * @param color - color to set
   * @returns void
   */
  const setBackgroundColor = useCallback(
    (color: string) => {
      if (!backgroundColorInSchema) {
        throw Error('Tried to set background color, but style does not exist in editor schema.');
      }

      if (editor.getActiveStyles().backgroundColor === color) return;

      color === 'default'
        ? editor.removeStyles({ backgroundColor: color })
        : editor.addStyles({ backgroundColor: color });

      editor.focus();
    },
    [backgroundColorInSchema, editor]
  );

  const show = useMemo(() => {
    if (!textColorInSchema && !backgroundColorInSchema) {
      return false;
    }

    for (const block of selectedBlocks) {
      if (block.content !== undefined) {
        return true;
      }
    }

    return false;
  }, [backgroundColorInSchema, selectedBlocks, textColorInSchema]);

  if (!show) {
    return null;
  }

  return (
    <Components.Generic.Popover.Root opened={opened} position={'bottom'}>
      <Components.Generic.Popover.Trigger>
        <Components.FormattingToolbar.Button
          className={'bn-button'}
          data-test='colors'
          label={dict.formatting_toolbar.colors.tooltip}
          mainTooltip={dict.formatting_toolbar.colors.tooltip}
          icon={
            <ColorIcon
              textColor={currentTextColor}
              backgroundColor={currentBackgroundColor}
              size={20}
            />
          }
          onClick={() => setOpened((prev) => !prev)}
        />
      </Components.Generic.Popover.Trigger>

      <div ref={contentRef}>
        <Components.Generic.Popover.Content
          className={cx(
            classes.popoverContent,
            'bn-popover-content bn-form-popover bn-color-picker-dropdown'
          )}
          variant='form-popover'
        >
          <ColorPicker
            text={
              textColorInSchema
                ? {
                    color: currentTextColor,
                    setColor: setTextColor,
                  }
                : undefined
            }
            background={
              backgroundColorInSchema
                ? {
                    color: currentBackgroundColor,
                    setColor: setBackgroundColor,
                  }
                : undefined
            }
            onClick={() => setOpened(false)}
          />
        </Components.Generic.Popover.Content>
      </div>
    </Components.Generic.Popover.Root>
  );
};
