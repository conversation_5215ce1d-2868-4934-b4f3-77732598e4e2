import {
  type BlockSchema,
  type InlineContentSchema,
  type StyleSchema,
  checkBlockIsFileBlock,
} from '@blocknote/core';
import {
  useBlockNoteEditor,
  useComponentsContext,
  useDictionary,
  useSelectedBlocks,
} from '@blocknote/react';
import { useClickOutside } from '@mantine/hooks';
import { IconPhotoEdit } from '@tabler/icons-react';
/**
 * Override the default FileReplaceButton component from BlockNoteEditor
 * the original component is located at @blocknote/react/src/components/FormattingToolbar/DefaultButtons/FileReplaceButton.tsx
 */
import { useState } from 'react';
import { FilePanel } from '../CustomComponents';

export const FileReplaceButton = () => {
  const dict = useDictionary();
  const Components = useComponentsContext()!;
  const editor = useBlockNoteEditor<BlockSchema, InlineContentSchema, StyleSchema>();
  const selectedBlocks = useSelectedBlocks(editor);
  const [opened, setOpened] = useState<boolean>(false);
  const contentRef = useClickOutside(() => setOpened(false));

  const block = selectedBlocks.length === 1 ? selectedBlocks[0] : undefined;
  if (block === undefined || !checkBlockIsFileBlock(block, editor) || !editor.isEditable) {
    return null;
  }

  return (
    <Components.Generic.Popover.Root opened={opened} position={'bottom'}>
      <Components.Generic.Popover.Trigger>
        <Components.FormattingToolbar.Button
          className={'bn-button'}
          isSelected={opened}
          mainTooltip={
            dict.formatting_toolbar.file_replace.tooltip[block.type] ||
            dict.formatting_toolbar.file_replace.tooltip.file
          }
          label={
            dict.formatting_toolbar.file_replace.tooltip[block.type] ||
            dict.formatting_toolbar.file_replace.tooltip.file
          }
          icon={<IconPhotoEdit strokeWidth={2} size={16} />}
          onClick={() => setOpened((prev) => !prev)}
        />
      </Components.Generic.Popover.Trigger>
      <div ref={contentRef}>
        <Components.Generic.Popover.Content
          className={'bn-popover-content bn-panel-popover'}
          variant={'panel-popover'}
        >
          <FilePanel block={block as any} />
        </Components.Generic.Popover.Content>
      </div>
    </Components.Generic.Popover.Root>
  );
};
