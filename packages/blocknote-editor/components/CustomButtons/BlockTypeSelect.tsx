import type {
  Block,
  BlockSchema,
  Dictionary,
  InlineContentSchema,
  StyleSchema,
} from '@blocknote/core';
import {
  useBlockNoteEditor,
  useDictionary,
  useEditorContentOrSelectionChange,
  useSelectedBlocks,
} from '@blocknote/react';
import {
  IconH1,
  IconH2,
  IconH3,
  IconLetterT,
  IconList,
  IconListCheck,
  IconListNumbers,
  type IconProps,
} from '@tabler/icons-react';
import get from 'lodash/get';
import { useMemo, useState } from 'react';
import { ToolbarSelect, type ToolbarSelectItem } from '../CustomComponents';

/**
 * Interface defining the structure of items in the block type selector dropdown
 */
export interface BlockTypeSelectItem {
  /** Display name of the block type */
  name: string;
  /** Block type identifier */
  type: string;
  /** Additional properties for the block type */
  props?: Record<string, boolean | number | string>;
  /** Icon component to display */
  icon: React.ComponentType<IconProps>;
  /** Function to determine if this block type is currently selected */
  isSelected: (block: Block<BlockSchema, InlineContentSchema, StyleSchema>) => boolean;
}

/**
 * Creates a standard list of block type items for the selector
 * @param dict Dictionary for internationalization
 * @returns Array of block type items
 */
export const blockTypeSelectItems = (dict: Dictionary): BlockTypeSelectItem[] => [
  {
    name: dict.slash_menu.paragraph.title,
    type: 'paragraph',
    icon: IconLetterT,
    isSelected: (block) => block.type === 'paragraph',
  },
  {
    name: dict.slash_menu.heading.title,
    type: 'heading',
    props: { level: 1 },
    icon: IconH1,
    isSelected: (block) =>
      block.type === 'heading' && 'level' in block.props && block.props.level === 1,
  },
  {
    name: dict.slash_menu.heading_2.title,
    type: 'heading',
    props: { level: 2 },
    icon: IconH2,
    isSelected: (block) =>
      block.type === 'heading' && 'level' in block.props && block.props.level === 2,
  },
  {
    name: dict.slash_menu.heading_3.title,
    type: 'heading',
    props: { level: 3 },
    icon: IconH3,
    isSelected: (block) =>
      block.type === 'heading' && 'level' in block.props && block.props.level === 3,
  },
  {
    name: dict.slash_menu.bullet_list.title,
    type: 'bulletListItem',
    icon: IconList,
    isSelected: (block) => block.type === 'bulletListItem',
  },
  {
    name: dict.slash_menu.numbered_list.title,
    type: 'numberedListItem',
    icon: IconListNumbers,
    isSelected: (block) => block.type === 'numberedListItem',
  },
  {
    name: dict.slash_menu.check_list.title,
    type: 'checkListItem',
    icon: IconListCheck,
    isSelected: (block) => block.type === 'checkListItem',
  },
];

/**
 * Component that handles generating items for the block type selector
 */
const BlockTypeItems = ({
  filteredItems,
  block,
  editor,
  selectedBlocks,
}: {
  filteredItems: BlockTypeSelectItem[];
  block: Block<BlockSchema, InlineContentSchema, StyleSchema>;
  editor: ReturnType<typeof useBlockNoteEditor<BlockSchema, InlineContentSchema, StyleSchema>>;
  selectedBlocks: Block<BlockSchema, InlineContentSchema, StyleSchema>[];
}): ToolbarSelectItem[] => {
  return useMemo(() => {
    const onClick = (item: BlockTypeSelectItem) => {
      if (typeof editor.focus === 'function') {
        editor.focus();
      }

      // Apply the selected block type to all selected blocks
      for (const block of selectedBlocks) {
        editor.updateBlock(block, {
          type: item.type,
          // Use type assertion to avoid complex typing issues
          props: item.props || ({} as any),
        });
      }
    };

    return filteredItems.map((item) => {
      const Icon = item.icon;

      return {
        key: item.type,
        text: item.name,
        icon: <Icon size={16} />,
        onClick: () => onClick(item),
        isSelected: item.isSelected(block),
      };
    });
  }, [block, filteredItems, editor, selectedBlocks]);
};

interface BlockTypeSelectProps {
  /** Optional custom items to use instead of the default block types */
  items?: BlockTypeSelectItem[];
}

/**
 * Component that displays a dropdown for selecting block types in the editor
 * Allows users to change the current block type (paragraph, heading, list, etc.)
 */
export const BlockTypeSelect = (props: BlockTypeSelectProps): JSX.Element | null => {
  const dict = useDictionary();
  const editor = useBlockNoteEditor<BlockSchema, InlineContentSchema, StyleSchema>();
  const selectedBlocks = useSelectedBlocks(editor);

  // Track the current block to determine selected state
  const [block, setBlock] = useState(editor.getTextCursorPosition().block);

  // Filter out items that aren't supported by the editor schema
  const filteredItems: BlockTypeSelectItem[] = useMemo(() => {
    return (props.items || blockTypeSelectItems(dict)).filter(
      (item) => item.type in editor.schema.blockSchema
    );
  }, [editor.schema.blockSchema, dict, props.items]);

  // Only show the selector if the current block type is in our list of options
  const shouldShow: boolean = useMemo(
    () => filteredItems.some((item) => item.type === block.type),
    [block.type, filteredItems]
  );

  // Get the items for the dropdown
  const fullItems = BlockTypeItems({ filteredItems, block, editor, selectedBlocks });

  // Update the current block whenever the cursor position or content changes
  useEditorContentOrSelectionChange(() => {
    setBlock(editor.getTextCursorPosition().block);
  }, editor);

  // Don't render if we shouldn't show or if editor is not editable
  if (!shouldShow || !editor.isEditable) {
    return null;
  }

  return (
    <div className='block-type-select'>
      <ToolbarSelect
        className='bn-select'
        placeholder={get(dict, 'custom.blockTypeSelect.placeholder', 'Select Block Type')}
        items={fullItems}
        data-testid='toolbar-select'
      />
    </div>
  );
};
