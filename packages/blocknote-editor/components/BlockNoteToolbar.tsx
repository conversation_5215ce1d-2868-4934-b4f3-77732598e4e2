import {
  BasicTextStyleButton,
  FormattingToolbar,
  FormattingToolbarController,
  NestBlockButton,
  TextAlignButton,
  UnnestBlockButton,
} from '@blocknote/react';
import type { FC } from 'react';
import {
  BlockTypeSelect,
  ColorStyleButton,
  CreateLinkButton,
  FileCaptionButton,
  FileReplaceButton,
} from './CustomButtons';

interface BlockNoteToolbarProps {
  enabledNestedBlock?: boolean;
  enabledTextAlignment?: boolean;
}

/**
 * Custom Formatting Toolbar content component
 */
const CustomFormattingToolbar: FC<BlockNoteToolbarProps> = ({
  enabledNestedBlock = true,
  enabledTextAlignment = true,
}) => (
  <FormattingToolbar>
    <BlockTypeSelect key={'blockTypeSelect'} />
    <FileCaptionButton key={'fileCaptionButton'} />
    <FileReplaceButton key={'fileReplaceButton'} />

    <BasicTextStyleButton basicTextStyle={'bold'} key={'boldStyleButton'} />
    <BasicTextStyleButton basicTextStyle={'italic'} key={'italicStyleButton'} />
    <BasicTextStyleButton basicTextStyle={'underline'} key={'underlineStyleButton'} />
    <BasicTextStyleButton basicTextStyle={'strike'} key={'strikeStyleButton'} />
    <BasicTextStyleButton key={'codeStyleButton'} basicTextStyle={'code'} />
    {enabledTextAlignment && (
      <>
        <TextAlignButton textAlignment={'left'} key={'textAlignLeftButton'} />
        <TextAlignButton textAlignment={'center'} key={'textAlignCenterButton'} />
        <TextAlignButton textAlignment={'right'} key={'textAlignRightButton'} />
      </>
    )}
    <ColorStyleButton key={'colorStyleButton'} />

    {enabledNestedBlock && (
      <>
        <NestBlockButton key={'nestBlockButton'} />
        <UnnestBlockButton key={'unnestBlockButton'} />
      </>
    )}

    <CreateLinkButton key={'createLinkButton'} />
  </FormattingToolbar>
);

/**
 * BlockNote Toolbar for BlockNoteEditor
 * @returns {React.FC}
 */
const BlockNoteToolbar: FC<BlockNoteToolbarProps> = ({
  enabledNestedBlock = true,
  enabledTextAlignment = true,
}) => {
  return (
    <FormattingToolbarController
      formattingToolbar={() => (
        <CustomFormattingToolbar
          enabledNestedBlock={enabledNestedBlock}
          enabledTextAlignment={enabledTextAlignment}
        />
      )}
    />
  );
};

export default BlockNoteToolbar;
