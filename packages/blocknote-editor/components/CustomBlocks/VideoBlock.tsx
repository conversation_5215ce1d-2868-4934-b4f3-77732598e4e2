/**
 * This file is a custom block for BlockNote editor.
 * It allows to add a video block to the editor.
 * Cloned from BlockNote repo -> `packages/react/src/blocks/VideoBlockContent/VideoBlockContent.tsx`
 */
import { type FileBlockConfig, videoBlockConfig, videoParse } from '@blocknote/core';
import { IconBrandYoutube } from '@tabler/icons-react';

import {
  FigureWithCaption,
  LinkWithCaption,
  type ReactCustomBlockRenderProps,
  ResizableFileBlockWrapper,
  createReactBlockSpec,
  useResolveUrl,
} from '@blocknote/react';
import { YOUTUBE_REGEX, isYoutubeURL } from '../../utils/video';

export const VideoPreview = (
  props: Omit<ReactCustomBlockRenderProps<FileBlockConfig, any, any>, 'contentRef'>
) => {
  const resolved = useResolveUrl(props.block.props.url!);

  if (resolved?.downloadUrl && isYoutubeURL(resolved.downloadUrl)) {
    const videoId = resolved.downloadUrl.match(YOUTUBE_REGEX)?.[1];
    if (!videoId) return null;

    return (
      <iframe
        title='YouTube video preview'
        className='bn-visual-media block-note-youtube-iframe'
        width={560}
        height={315}
        src={`https://www.youtube.com/embed/${videoId}`}
        frameBorder={0}
        allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share'
        referrerPolicy='strict-origin-when-cross-origin'
        allowFullScreen
      />
    );
  }

  return (
    <video
      className={'bn-visual-media'}
      src={
        resolved?.loadingState === 'loading'
          ? props.block.props.url
          : (resolved?.downloadUrl ?? props.block.props.url)
      }
      controls={true}
      contentEditable={false}
      draggable={false}
    >
      <track kind='captions' srcLang='en' label='English' />
    </video>
  );
};

export const VideoToExternalHTML = (
  props: Omit<ReactCustomBlockRenderProps<typeof videoBlockConfig, any, any>, 'contentRef'>
) => {
  if (!props.block.props.url) {
    return <p>Add video</p>;
  }

  const video = props.block.props.showPreview ? (
    <video src={props.block.props.url}>
      <track kind='captions' srcLang='en' label='English' />
    </video>
  ) : (
    <a href={props.block.props.url}>{props.block.props.name || props.block.props.url}</a>
  );

  if (props.block.props.caption) {
    return props.block.props.showPreview ? (
      <FigureWithCaption caption={props.block.props.caption}>{video}</FigureWithCaption>
    ) : (
      <LinkWithCaption caption={props.block.props.caption}>{video}</LinkWithCaption>
    );
  }

  return video;
};

export const VideoBlock = (
  props: ReactCustomBlockRenderProps<typeof videoBlockConfig, any, any>
) => {
  return (
    <ResizableFileBlockWrapper
      {...(props as any)}
      buttonText={props.editor.dictionary.file_blocks.video.add_button_text}
      buttonIcon={<IconBrandYoutube size={24} />}
    >
      <VideoPreview {...(props as any)} />
    </ResizableFileBlockWrapper>
  );
};

export const ReactVideoBlock = createReactBlockSpec(videoBlockConfig, {
  render: VideoBlock,
  parse: videoParse,
  toExternalHTML: VideoToExternalHTML,
});
