import { useResolveUrl } from '@blocknote/react';
import { render, screen } from '@testing-library/react';
import React from 'react';
/**
 * Comprehensive unit tests for VideoBlock component
 *
 * This test suite covers:
 * - VideoBlock component rendering with and without URLs
 * - VideoPreview component for regular videos and YouTube URLs
 * - VideoToExternalHTML component for different rendering modes
 * - Edge cases and error handling
 * - Component exports and integration
 */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { MantineWrapper } from '../../utils/unitTest';
// Helper to access and update the mocked implementation inside tests
const mockUseResolveUrl = vi.mocked(useResolveUrl);

// Mock the modules before importing our components
vi.mock('@blocknote/react', () => {
  const mockUseResolveUrl = vi.fn().mockImplementation((url) => ({
    loadingState: 'success',
    downloadUrl: url,
    error: null,
  }));

  return {
    createReactBlockSpec: vi.fn().mockImplementation((config, options) => {
      return options;
    }),
    useResolveUrl: mockUseResolveUrl,
    ResizableFileBlockWrapper: ({ buttonText, buttonIcon, children, block, ...props }) => {
      // If no URL, render the add button
      if (!block?.props?.url) {
        return (
          <button data-testid='add-file-button' type='button'>
            {buttonIcon}
            {buttonText}
          </button>
        );
      }
      // If URL exists, render the wrapper with children
      return <div data-testid='resize-handles-wrapper'>{children}</div>;
    },
    FigureWithCaption: ({ caption, children }) => (
      <figure data-testid='figure-with-caption'>
        {children}
        <figcaption>{caption}</figcaption>
      </figure>
    ),
    LinkWithCaption: ({ caption, children }) => (
      <div data-testid='link-with-caption'>
        {children}
        <div>{caption}</div>
      </div>
    ),
  };
});

vi.mock('@tabler/icons-react', () => ({
  IconBrandYoutube: vi
    .fn()
    .mockImplementation(() => <span data-testid='icon-brand-youtube'>YouTube Icon</span>),
}));

vi.mock('../../utils/video', () => {
  const YOUTUBE_REGEX =
    /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^/]+\/[^/]+\/|(?:v|e(?:mbed)?)\/|[^/]+[?&]v=)|youtu\.be\/)([^"&?/ ]{11})/;
  return {
    // Provide functional default so tests relying on real detection work out-of-the-box.
    isYoutubeURL: vi.fn((url: string) => YOUTUBE_REGEX.test(url)),
    getEmbededHTML: vi.fn(),
    YOUTUBE_REGEX,
  };
});

vi.mock('@blocknote/core', () => ({
  videoBlockConfig: {
    type: 'video',
    propSchema: {
      url: { default: '' },
      name: { default: '' },
      caption: { default: '' },
      showPreview: { default: true },
    },
  },
  videoParse: vi.fn(),
}));

import { getEmbededHTML, isYoutubeURL } from '../../utils/video';
// Import the actual implementation
import * as originalModule from './VideoBlock';

// Mock functions with proper typing
const mockIsYoutubeURL = vi.mocked(isYoutubeURL);
const mockGetEmbededHTML = vi.mocked(getEmbededHTML);

// Constants and mock data
const MOCK_EDITOR = {
  dictionary: {
    file_blocks: {
      video: {
        add_button_text: 'Add Video',
      },
    },
  },
  domElement: {
    firstElementChild: {
      clientWidth: 800,
    },
  },
};

const MOCK_YOUTUBE_URL = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
const MOCK_REGULAR_VIDEO_URL = 'https://example.com/video.mp4';
const MOCK_YOUTUBE_EMBED_HTML =
  '<iframe class="block-note-youtube-iframe" width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>';

const MOCK_BLOCK_WITH_YOUTUBE_URL = {
  props: {
    url: MOCK_YOUTUBE_URL,
    name: 'test-youtube-video',
    caption: '',
    showPreview: true,
  },
};

const MOCK_BLOCK_WITH_VIDEO_URL = {
  props: {
    url: MOCK_REGULAR_VIDEO_URL,
    name: 'test-video',
    caption: '',
    showPreview: true,
  },
};

const MOCK_BLOCK_WITH_CAPTION = {
  props: {
    url: MOCK_REGULAR_VIDEO_URL,
    name: 'test-video',
    caption: 'Test Video Caption',
    showPreview: true,
  },
};

const MOCK_BLOCK_WITHOUT_URL = {
  props: {
    url: '',
    name: '',
    caption: '',
    showPreview: false,
  },
};

const MOCK_BLOCK_WITHOUT_PREVIEW = {
  props: {
    url: MOCK_REGULAR_VIDEO_URL,
    name: 'test-video',
    caption: '',
    showPreview: false,
  },
};

const MOCK_BLOCK_YOUTUBE_WITH_CAPTION = {
  props: {
    url: MOCK_YOUTUBE_URL,
    name: 'test-youtube-video',
    caption: 'YouTube Video Caption',
    showPreview: true,
  },
};

const MOCK_BLOCK_LOADING = {
  props: {
    url: MOCK_REGULAR_VIDEO_URL,
    name: 'test-video',
    caption: '',
    showPreview: true,
  },
};

describe('VideoBlock component', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('renders ResizableFileBlockWrapper when no URL is provided', () => {
    render(
      <originalModule.VideoBlock
        block={MOCK_BLOCK_WITHOUT_URL}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // The ResizableFileBlockWrapper should render the add button when no URL
    expect(screen.getByTestId('add-file-button')).toBeInTheDocument();
    expect(screen.getByText('Add Video')).toBeInTheDocument();
    // Note: Icon is rendered as a React component, not as DOM element with testid
  });

  it('renders ResizableFileBlockWrapper with VideoPreview when URL is provided', () => {
    mockIsYoutubeURL.mockReturnValue(false);

    render(
      <originalModule.VideoBlock
        block={MOCK_BLOCK_WITH_VIDEO_URL}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // When URL is provided, it should render the video preview
    expect(screen.getByTestId('resize-handles-wrapper')).toBeInTheDocument();
    // The VideoPreview should render a video element
    const video = document.querySelector('video');
    expect(video).toBeTruthy();
  });

  it('renders YouTube video when YouTube URL is provided', () => {
    mockIsYoutubeURL.mockReturnValue(true);
    mockGetEmbededHTML.mockReturnValue(MOCK_YOUTUBE_EMBED_HTML);

    render(
      <originalModule.VideoBlock
        block={MOCK_BLOCK_WITH_YOUTUBE_URL}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // Should render the resize wrapper when URL is provided
    expect(screen.getByTestId('resize-handles-wrapper')).toBeInTheDocument();
    // Should render some form of video content (either video element or YouTube embed)
    const mediaContent =
      document.querySelector('.bn-visual-media') || document.querySelector('video');
    expect(mediaContent).toBeTruthy();
  });
});

describe('VideoPreview component', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    // Restore default implementation for useResolveUrl after mocks reset
    mockUseResolveUrl.mockImplementation((url) => ({
      loadingState: 'success',
      downloadUrl: url,
      error: null,
    }));
  });

  it('renders regular video element when non-YouTube URL is provided', () => {
    mockIsYoutubeURL.mockReturnValue(false);

    render(<originalModule.VideoPreview block={MOCK_BLOCK_WITH_VIDEO_URL} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const video = document.querySelector('video');
    expect(video).toBeTruthy();
    expect(video).toHaveAttribute('src', MOCK_REGULAR_VIDEO_URL);
    expect(video).toHaveAttribute('controls');
    expect(video).toHaveAttribute('contentEditable', 'false');
    expect(video).toHaveAttribute('draggable', 'false');
    expect(video).toHaveClass('bn-visual-media');

    // Check for accessibility track element
    const track = video.querySelector('track');
    expect(track).toBeTruthy();
    expect(track).toHaveAttribute('kind', 'captions');
    expect(track).toHaveAttribute('srcLang', 'en');
    expect(track).toHaveAttribute('label', 'English');
  });

  it('renders embedded YouTube video when YouTube URL is provided', () => {
    mockIsYoutubeURL.mockReturnValue(true);
    mockGetEmbededHTML.mockReturnValue(MOCK_YOUTUBE_EMBED_HTML);

    render(
      <originalModule.VideoPreview block={MOCK_BLOCK_WITH_YOUTUBE_URL} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    // Check if some form of video content is rendered (either YouTube embed or fallback video)
    const mediaContent =
      document.querySelector('.bn-visual-media') || document.querySelector('video');
    expect(mediaContent).toBeTruthy();
    expect(mediaContent).toHaveClass('bn-visual-media');
  });

  it('renders video with original URL while resolved download is still loading', () => {
    mockIsYoutubeURL.mockReturnValue(false);
    // Simulate the resolver still loading (no downloadUrl yet)
    mockUseResolveUrl.mockImplementationOnce((url) => ({
      loadingState: 'loading',
      downloadUrl: undefined,
      error: null,
    }));

    render(<originalModule.VideoPreview block={MOCK_BLOCK_WITH_VIDEO_URL} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    // Should fall back to the original URL while loading
    const video = document.querySelector('video');
    expect(video).toBeTruthy();
    expect(video).toHaveAttribute('src', MOCK_REGULAR_VIDEO_URL);
  });

  it('renders video with fallback URL when resolved is undefined', () => {
    mockIsYoutubeURL.mockReturnValue(false);

    render(<originalModule.VideoPreview block={MOCK_BLOCK_LOADING} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const video = document.querySelector('video');
    expect(video).toBeTruthy();
    expect(video).toHaveClass('bn-visual-media');
  });

  it('renders iframe with correct attributes for YouTube preview', () => {
    mockIsYoutubeURL.mockReturnValue(true);

    render(
      <originalModule.VideoPreview block={MOCK_BLOCK_WITH_YOUTUBE_URL} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    const iframe = screen.getByTitle('YouTube video preview');
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveClass('bn-visual-media');
    expect(iframe).toHaveAttribute('width', '560');
    expect(iframe).toHaveAttribute('height', '315');
    expect(iframe).toHaveAttribute('src', 'https://www.youtube.com/embed/dQw4w9WgXcQ');
    expect(iframe).toHaveAttribute('allowfullscreen');
  });

  it('falls back to rendering a <video> element when videoId extraction fails', () => {
    const INVALID_YOUTUBE_URL = 'https://www.youtube.com/watch?v=shortid'; // id shorter than 11 chars

    // Do NOT mock isYoutubeURL here – rely on real implementation which returns false

    const blockWithInvalidYouTube = {
      props: {
        url: INVALID_YOUTUBE_URL,
        name: 'invalid-youtube-video',
        caption: '',
        showPreview: true,
      },
    };

    render(<originalModule.VideoPreview block={blockWithInvalidYouTube} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    // Should render a regular video element with the original URL
    const video = document.querySelector('video');
    expect(video).toBeTruthy();
    expect(video).toHaveAttribute('src', INVALID_YOUTUBE_URL);
  });
});

describe('VideoToExternalHTML', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('renders placeholder when no URL is provided', () => {
    render(
      <originalModule.VideoToExternalHTML block={MOCK_BLOCK_WITHOUT_URL} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    expect(screen.getByText('Add video')).toBeInTheDocument();
  });

  it('renders video element when showPreview is true', () => {
    render(
      <originalModule.VideoToExternalHTML block={MOCK_BLOCK_WITH_VIDEO_URL} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    const video = document.querySelector('video');
    expect(video).toBeTruthy();
    expect(video).toHaveAttribute('src', MOCK_REGULAR_VIDEO_URL);

    // Check for accessibility track element
    const track = video.querySelector('track');
    expect(track).toBeTruthy();
    expect(track).toHaveAttribute('kind', 'captions');
    expect(track).toHaveAttribute('srcLang', 'en');
    expect(track).toHaveAttribute('label', 'English');
  });

  it('renders link when showPreview is false', () => {
    render(
      <originalModule.VideoToExternalHTML
        block={MOCK_BLOCK_WITHOUT_PREVIEW}
        editor={MOCK_EDITOR}
      />,
      { wrapper: MantineWrapper }
    );

    const link = screen.getByRole('link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', MOCK_REGULAR_VIDEO_URL);
    expect(link).toHaveTextContent('test-video');
  });

  it('renders link with URL text when no name is provided and showPreview is false', () => {
    const blockWithoutName = {
      props: {
        url: MOCK_REGULAR_VIDEO_URL,
        name: '',
        caption: '',
        showPreview: false,
      },
    };

    render(<originalModule.VideoToExternalHTML block={blockWithoutName} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const link = screen.getByRole('link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveTextContent(MOCK_REGULAR_VIDEO_URL);
  });

  it('renders figure with caption when caption is provided and showPreview is true', () => {
    render(
      <originalModule.VideoToExternalHTML block={MOCK_BLOCK_WITH_CAPTION} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    expect(screen.getByTestId('figure-with-caption')).toBeInTheDocument();
    const video = document.querySelector('video');
    expect(video).toBeTruthy(); // video element
    expect(screen.getByText('Test Video Caption')).toBeInTheDocument();

    // Check for accessibility track element
    const track = video.querySelector('track');
    expect(track).toBeTruthy();
    expect(track).toHaveAttribute('kind', 'captions');
    expect(track).toHaveAttribute('srcLang', 'en');
    expect(track).toHaveAttribute('label', 'English');
  });

  it('renders link with caption when caption is provided and showPreview is false', () => {
    const blockWithCaptionNoPreview = {
      ...MOCK_BLOCK_WITH_CAPTION,
      props: {
        ...MOCK_BLOCK_WITH_CAPTION.props,
        showPreview: false,
      },
    };

    render(
      <originalModule.VideoToExternalHTML block={blockWithCaptionNoPreview} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    expect(screen.getByTestId('link-with-caption')).toBeInTheDocument();
    expect(screen.getByRole('link')).toBeInTheDocument();
    expect(screen.getByText('Test Video Caption')).toBeInTheDocument();
  });

  it('renders YouTube video figure with caption when YouTube URL and caption are provided', () => {
    render(
      <originalModule.VideoToExternalHTML
        block={MOCK_BLOCK_YOUTUBE_WITH_CAPTION}
        editor={MOCK_EDITOR}
      />,
      { wrapper: MantineWrapper }
    );

    expect(screen.getByTestId('figure-with-caption')).toBeInTheDocument();
    const video = document.querySelector('video');
    expect(video).toBeTruthy(); // video element
    expect(screen.getByText('YouTube Video Caption')).toBeInTheDocument();

    // Check for accessibility track element
    const track = video.querySelector('track');
    expect(track).toBeTruthy();
    expect(track).toHaveAttribute('kind', 'captions');
    expect(track).toHaveAttribute('srcLang', 'en');
    expect(track).toHaveAttribute('label', 'English');
  });
});

describe('ReactVideoBlock', () => {
  it('exports the ReactVideoBlock component', () => {
    expect(originalModule.ReactVideoBlock).toBeDefined();
    expect(typeof originalModule.ReactVideoBlock).toBe('object');
  });
});

describe('Edge cases and error handling', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('handles missing props gracefully', () => {
    const blockWithNullProps = {
      props: {
        url: null,
        name: null,
        caption: null,
        showPreview: null,
      },
    };

    render(<originalModule.VideoToExternalHTML block={blockWithNullProps} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    // Should render the placeholder when URL is null/falsy
    expect(screen.getByText('Add video')).toBeInTheDocument();
  });

  it('handles empty strings in props', () => {
    const blockWithEmptyStrings = {
      props: {
        url: '',
        name: '',
        caption: '',
        showPreview: false,
      },
    };

    render(
      <originalModule.VideoToExternalHTML block={blockWithEmptyStrings} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    expect(screen.getByText('Add video')).toBeInTheDocument();
  });

  it('renders video component without crashing when given valid props', () => {
    mockIsYoutubeURL.mockReturnValue(false);

    // Should not crash when rendering with valid props
    expect(() => {
      render(
        <originalModule.VideoPreview block={MOCK_BLOCK_WITH_VIDEO_URL} editor={MOCK_EDITOR} />,
        { wrapper: MantineWrapper }
      );
    }).not.toThrow();

    const video = document.querySelector('video');
    expect(video).toBeTruthy();
  });
});
