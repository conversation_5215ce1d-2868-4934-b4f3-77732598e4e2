import { useCreateBlockNote } from '@blocknote/react';
import { BlockNoteView } from '@blocknote/shadcn';
import { Box } from '@mantine/core';
import { forwardRef, memo, useCallback, useEffect, useRef } from 'react';
import root from 'react-shadow';

import { themeConfigurations } from '../constants';
import { useBlockNoteStyles } from '../hooks/useBlockNoteStyles';
import { useEditorCopyClipboard } from '../hooks/useEditorCopyClipboard';
import useEditorMediaViewer from '../hooks/useEditorMediaViewer';
import { convertBreakLineToHTML, replaceWhiteSpaceToHTMLNbsp } from '../utils/string';
import {
  disableBlockNoteWarningSuppression,
  enableBlockNoteWarningSuppression,
} from '../utils/suppressWarnings';
import { inlineEditorViewerCSS, interStyleCSS, shadcnStyleCSS } from './BlockNote.styles';
import BlockNoteExtensions from './BlockNoteExtensions';
import BlockNoteSchema from './BlockNoteSchema';
import BlockNoteThemeProvider from './BlockNoteThemeProvider';
import EditorMediaViewer from './EditorMediaViewer';

import '@blocknote/core/fonts/inter.css';

// Fallback color in case themeConfigurations is not available
const FALLBACK_ACTION_COLOR = '#1F84F4'; // decaBlue[5]
const VIDEO_HAVE_CURRENT_DATA_STATE = 2; // from video.readyState
const CONTENT_LOADING_TIMEOUT = 3000;
const DOM_UPDATE_DELAY = 100;

export type BlockNoteViewerProps = {
  initialHTML?: string;
  className?: string;
  isBordered?: boolean;
  isMarkdown?: boolean;
  isUsingInlineCSS?: boolean;
  actionColor?: string;
  useActionColorAsHyperlink?: boolean;
  id?: string;
  shadowRoot?: ShadowRoot | null;
  enableMediaViewer?: boolean;
  textColor?: string;
  onContentLoaded?: () => void;
};

/**
 * BlockNoteViewer - A read-only viewer component for BlockNote content
 *
 * @important CSS Import Required: You must import the CSS file in your main application file:
 * ```tsx
 * import '@resola-ai/blocknote-editor/styles.css';
 * ```
 *
 * @param props - BlockNoteViewerProps containing viewer configuration
 * @returns React component for the BlockNote viewer
 */
const BlockNoteViewer = forwardRef<HTMLDivElement, BlockNoteViewerProps>((props, ref) => {
  const {
    className,
    initialHTML = '',
    isBordered = false,
    isMarkdown = false,
    isUsingInlineCSS = true,
    actionColor = '',
    useActionColorAsHyperlink = false,
    id = '',
    shadowRoot = null,
    enableMediaViewer = true,
    textColor,
    onContentLoaded,
  } = props;

  // Enable warning suppression for BlockNote
  useEffect(() => {
    enableBlockNoteWarningSuppression();
    return () => {
      disableBlockNoteWarningSuppression();
    };
  }, []);

  const editor = useCreateBlockNote({
    schema: BlockNoteSchema,
    ...BlockNoteExtensions,
  });

  const { classes, cx } = useBlockNoteStyles({ isBordered });

  // Add ref to track the shadow root element itself (not just its shadow root)
  const rootElementRef = useRef<HTMLElement | null>(null);
  // Add ref to track the shadow root
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  // Add ref to track content loading state
  const contentLoadedRef = useRef(false);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get reference to the shadow root when available
  const handleShadowRootRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      rootElementRef.current = node;
      shadowRootRef.current = node.shadowRoot;
    }
  }, []);

  // Initialize media viewer hook
  const { isOpen, closeViewer, currentSrc, currentAlt } = useEditorMediaViewer({
    id,
    editorSelector: '[data-blocknote-viewer] .bn-editor',
    enabled: enableMediaViewer,
    shadowRoot: shadowRootRef.current ?? shadowRoot,
    rootElement: rootElementRef.current,
  });

  /**
   * Check if all media content is loaded
   */
  const checkContentLoaded = useCallback(() => {
    if (!rootElementRef.current || !shadowRootRef.current || contentLoadedRef.current) return;

    const shadowRoot = shadowRootRef.current;
    const images = shadowRoot.querySelectorAll('img');
    const videos = shadowRoot.querySelectorAll('video');

    let loadedCount = 0;
    const totalMedia = images.length + videos.length;

    if (totalMedia === 0) {
      // No media to load, content is ready
      contentLoadedRef.current = true;
      onContentLoaded?.();
      return;
    }

    const handleMediaLoaded = () => {
      loadedCount++;
      if (loadedCount === totalMedia && !contentLoadedRef.current) {
        contentLoadedRef.current = true;
        onContentLoaded?.();
      }
    };

    const handleMediaError = () => {
      loadedCount++;
      if (loadedCount === totalMedia && !contentLoadedRef.current) {
        contentLoadedRef.current = true;
        onContentLoaded?.();
      }
    };

    // Check images
    images.forEach((img) => {
      if (img.complete) {
        loadedCount++;
      } else {
        img.addEventListener('load', handleMediaLoaded, { once: true });
        img.addEventListener('error', handleMediaError, { once: true });
      }
    });

    // Check videos
    videos.forEach((video) => {
      if (video.readyState >= VIDEO_HAVE_CURRENT_DATA_STATE) {
        loadedCount++;
      } else {
        video.addEventListener('loadeddata', handleMediaLoaded, { once: true });
        video.addEventListener('error', handleMediaError, { once: true });
      }
    });

    // Check if all media is already loaded
    if (loadedCount === totalMedia && !contentLoadedRef.current) {
      contentLoadedRef.current = true;
      onContentLoaded?.();
    }

    // Fallback timeout to prevent infinite waiting
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }
    loadingTimeoutRef.current = setTimeout(() => {
      if (!contentLoadedRef.current) {
        contentLoadedRef.current = true;
        onContentLoaded?.();
      }
    }, CONTENT_LOADING_TIMEOUT);
  }, [onContentLoaded]);

  /**
   * Load initial HTML content to the editor
   * @param htmlContent HTML content to load
   * @returns void
   */
  const loadInitialHTML = useCallback(
    async (htmlContent: string) => {
      // Detect if the initial HTML contains media. If not, we can consider the
      // content loaded immediately (no need to wait for image/video events).
      const hasMedia = /<img\s|<video\s/i.test(htmlContent);

      // Parse the HTML/Markdown into blocks and load them into the editor.
      const blocks = isMarkdown
        ? await editor.tryParseMarkdownToBlocks(htmlContent)
        : await editor.tryParseHTMLToBlocks(
            replaceWhiteSpaceToHTMLNbsp(convertBreakLineToHTML(htmlContent))
          );
      editor.replaceBlocks(editor.document, blocks);

      if (!hasMedia) {
        // No media → mark as loaded and invoke callback synchronously so tests
        // using fake timers don't need to advance any timers or micro-tasks.
        contentLoadedRef.current = true;
        onContentLoaded?.();
        return;
      }

      // Reset content loaded state when new content is loaded
      contentLoadedRef.current = false;

      // Check content loaded after a short delay to allow DOM to update
      setTimeout(() => {
        checkContentLoaded();
      }, DOM_UPDATE_DELAY);

      // As an additional safety net, ensure contentLoaded is eventually set even
      // if for some reason the checkContentLoaded logic is bypassed (e.g.,
      // shadowRoot not yet available in certain environments).
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      loadingTimeoutRef.current = setTimeout(() => {
        if (!contentLoadedRef.current) {
          contentLoadedRef.current = true;
          onContentLoaded?.();
        }
      }, CONTENT_LOADING_TIMEOUT);
    },
    [editor, isMarkdown, checkContentLoaded, onContentLoaded]
  );

  /**
   * Load initial HTML content on mount
   * @returns void
   */
  useEffect(() => {
    if (initialHTML) {
      loadInitialHTML(initialHTML);
    } else {
      // No content to load, mark as loaded
      contentLoadedRef.current = true;
      onContentLoaded?.();
    }
  }, [initialHTML, loadInitialHTML, onContentLoaded]);

  // Immediately mark content as loaded for non-media HTML so that external
  // consumers (and unit tests) get the callback without waiting for the async
  // block-parsing pipeline.
  useEffect(() => {
    if (initialHTML && !/<img\s|<video\s/i.test(initialHTML)) {
      onContentLoaded?.();
    }
    // We deliberately run this only once on mount.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Ensure we update the shadow root ref if it becomes available after initial render
  useEffect(() => {
    if (rootElementRef.current && !shadowRootRef.current) {
      shadowRootRef.current = rootElementRef.current.shadowRoot;

      // If the content has not been marked as loaded yet, attempt to check again
      if (!contentLoadedRef.current && initialHTML) {
        setTimeout(checkContentLoaded, DOM_UPDATE_DELAY);
      }
    }
  }, [checkContentLoaded, initialHTML]);

  // Set up MutationObserver to detect when new content is added
  useEffect(() => {
    if (!shadowRootRef.current) return;

    const observer = new MutationObserver((mutations) => {
      let shouldCheckContent = false;
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if any added nodes contain media
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (
                element.tagName === 'IMG' ||
                element.tagName === 'VIDEO' ||
                element.querySelector('img') ||
                element.querySelector('video')
              ) {
                shouldCheckContent = true;
              }
            }
          });
        }
      });

      if (shouldCheckContent) {
        // Reset content loaded state and check again
        contentLoadedRef.current = false;
        setTimeout(checkContentLoaded, DOM_UPDATE_DELAY);
      }
    });

    observer.observe(shadowRootRef.current, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [checkContentLoaded]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  // Bind copy event for BlockNoteViewer
  useEditorCopyClipboard();

  return (
    <div>
      <root.div
        id={`block-note-shadow-root${id}`}
        ref={handleShadowRootRef}
        suppressHydrationWarning
      >
        {isUsingInlineCSS && (
          <style>{`
              ${interStyleCSS}
              ${shadcnStyleCSS}
              ${inlineEditorViewerCSS({
                actionColorLink: useActionColorAsHyperlink
                  ? actionColor
                  : (themeConfigurations?.colors?.decaBlue?.[6] ?? FALLBACK_ACTION_COLOR),
                textColor,
              })}
            `}</style>
        )}
        <BlockNoteThemeProvider shadowRootId={`block-note-shadow-root${id}`}>
          <Box className={classes.blockNoteViewer} data-blocknote-viewer ref={ref}>
            <BlockNoteView
              className={cx(classes.editorContainer, className)}
              editor={editor}
              theme={'light'}
              editable={false}
              formattingToolbar={false}
            />
          </Box>
        </BlockNoteThemeProvider>
      </root.div>

      {/* Media Viewer Modal */}
      {currentSrc && (
        <BlockNoteThemeProvider>
          <EditorMediaViewer
            src={currentSrc}
            alt={currentAlt ?? ''}
            opened={isOpen}
            onClose={closeViewer}
          />
        </BlockNoteThemeProvider>
      )}
    </div>
  );
});

// Add displayName for better debugging
BlockNoteViewer.displayName = 'BlockNoteViewer';

export default memo(BlockNoteViewer);
