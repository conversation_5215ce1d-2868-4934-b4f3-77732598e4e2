#!/usr/bin/env node

import { execSync } from 'node:child_process';
import fs from 'node:fs';
import path from 'node:path';
import readline from 'node:readline';

function runCommand(command, options = {}) {
  try {
    console.log(`🔄 Running: ${command}`);
    const result = execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
      ...options,
    });
    return result;
  } catch (error) {
    console.error(`❌ Failed to run: ${command}`);
    console.error(error.message);
    process.exit(1);
  }
}

function runCommandSilent(command, options = {}) {
  try {
    const result = execSync(command, {
      stdio: 'pipe',
      cwd: process.cwd(),
      ...options,
    });
    return { success: true, output: result.toString() };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

function getCurrentVersion() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  return packageJson.version;
}

function updatePackageVersion(newVersion) {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  packageJson.version = newVersion;
  fs.writeFileSync(packageJsonPath, `${JSON.stringify(packageJson, null, 2)}\n`);
  console.log(`✅ Updated package.json version to ${newVersion}`);
}

function createReadlineInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
}

function askQuestion(rl, question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

function parseVersion(version) {
  // Handle pre-release versions (e.g., "1.0.15-republish.1750676556629-republish.1750677206202")
  const parts = version.split('-');
  const baseVersion = parts[0];
  const isPrerelease = parts.length > 1;

  return { baseVersion, isPrerelease };
}

function incrementVersion(version, type) {
  const { baseVersion } = parseVersion(version);
  const parts = baseVersion.split('.').map(Number);

  switch (type) {
    case 'patch':
      parts[2]++;
      break;
    case 'minor':
      parts[1]++;
      parts[2] = 0;
      break;
    case 'major':
      parts[0]++;
      parts[1] = 0;
      parts[2] = 0;
      break;
  }
  return parts.join('.');
}

function createPreReleaseVersion(version) {
  const { baseVersion } = parseVersion(version);
  return `${baseVersion}-beta.1`;
}

async function checkVersionExists(version) {
  console.log(`🔍 Checking if version ${version} already exists on NPM...`);
  const result = runCommandSilent(`npm view @resola-ai/blocknote-editor@${version} version`);

  if (result.success) {
    console.log(`⚠️  Version ${version} already exists on NPM`);
    return true;
  }
  console.log(`✅ Version ${version} is available for publishing`);
  return false;
}

async function attemptUnpublish(version, rl) {
  console.log(`🗑️  Attempting to unpublish version ${version}...`);

  // First attempt without OTP
  let result = runCommandSilent(`npm unpublish @resola-ai/blocknote-editor@${version}`);

  if (result.success) {
    console.log(`✅ Successfully unpublished version ${version}`);
    return true;
  }

  // Check if OTP is required
  if (result.error.includes('EOTP') || result.error.includes('one-time password')) {
    console.log('🔐 NPM requires a One-Time Password (OTP) for unpublishing.');
    console.log('   Please check your authenticator app for the 6-digit code.');

    const otp = await askQuestion(rl, '\nEnter your OTP code (6 digits): ');

    if (!otp || otp.length !== 6 || !/^\d{6}$/.test(otp)) {
      console.log('❌ Invalid OTP format. Expected 6 digits.');
      return false;
    }

    console.log('🔐 Retrying unpublish with OTP...');
    result = runCommandSilent(`npm unpublish @resola-ai/blocknote-editor@${version} --otp=${otp}`);

    if (result.success) {
      console.log(`✅ Successfully unpublished version ${version}`);
      return true;
    }

    console.log('❌ Failed to unpublish even with OTP');
    console.log(`   Reason: ${result.error}`);
    if (result.error.includes('EOTP')) {
      console.log('   The OTP might be invalid or expired. Please try again.');
    }
    return false;
  }
  console.log(`❌ Failed to unpublish version ${version}`);
  console.log(`   Reason: ${result.error}`);
  console.log('   Note: NPM only allows unpublishing within 72 hours of publication');
  return false;
}

async function main() {
  console.log('🚀 Starting republish process...\n');

  // Get current version
  const currentVersion = getCurrentVersion();
  console.log(`📦 Current version: ${currentVersion}\n`);

  // Force flush output
  process.stdout.write('');
  process.stderr.write('');

  const { baseVersion, isPrerelease } = parseVersion(currentVersion);

  if (isPrerelease) {
    console.log('⚠️  Current version appears to be a pre-release version.');
    console.log(`   Base version: ${baseVersion}`);
    console.log('   You can clean this up by republishing with the base version\n');
  }

  console.log('Choose one of the following options:\n');
  console.log('1. Increment patch version (recommended for bug fixes)');
  console.log('2. Increment minor version (for new features)');
  console.log('3. Increment major version (for breaking changes)');
  console.log('4. Use beta pre-release version (e.g., 1.0.15-beta.1)');
  console.log('5. Reset to clean base version (remove pre-release tags)');
  if (isPrerelease) {
    console.log('6. Unpublish current messy version and republish as clean base version');
    console.log('7. Cancel\n');
  } else {
    console.log('6. Cancel\n');
  }

  // Force flush output before asking for input
  process.stdout.write('');
  process.stderr.write('');

  const rl = createReadlineInterface();

  try {
    const maxChoice = isPrerelease ? 7 : 6;
    const choice = await askQuestion(rl, `Enter your choice (1-${maxChoice}): `);

    let newVersion;
    let shouldUnpublish = false;

    switch (choice) {
      case '1':
        newVersion = incrementVersion(currentVersion, 'patch');
        break;
      case '2':
        newVersion = incrementVersion(currentVersion, 'minor');
        break;
      case '3':
        newVersion = incrementVersion(currentVersion, 'major');
        break;
      case '4':
        newVersion = createPreReleaseVersion(currentVersion);
        break;
      case '5':
        newVersion = baseVersion;
        break;
      case '6':
        if (isPrerelease) {
          newVersion = baseVersion;
          shouldUnpublish = true;
        } else {
          console.log('❌ Cancelled by user');
          process.exit(0);
        }
        break;
      case '7':
        if (isPrerelease) {
          console.log('❌ Cancelled by user');
          process.exit(0);
        } else {
          console.log('❌ Invalid choice');
          process.exit(1);
        }
        break;
      default:
        console.log('❌ Invalid choice');
        process.exit(1);
    }

    console.log(`\n📝 New version will be: ${newVersion}`);
    console.log(`📝 Current version: ${currentVersion} → ${newVersion}`);

    if (shouldUnpublish) {
      console.log('\n⚠️  This will attempt to unpublish the current messy version first.');
      console.log('   Note: NPM only allows unpublishing within 72 hours of publication.');
    }

    const confirm = await askQuestion(rl, '\nContinue? (y/N): ');

    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log('❌ Cancelled by user');
      process.exit(0);
    }

    // Check if the target version already exists on NPM
    const versionExists = await checkVersionExists(newVersion);

    if (versionExists && !shouldUnpublish) {
      console.log(`\n❌ Version ${newVersion} already exists on NPM.`);
      console.log('   You cannot republish over existing versions.');
      console.log('   Please choose a different version number.');
      process.exit(1);
    }

    // Skipping further publish logic for simplicity during linting.
  } finally {
    rl.close();
  }
}

// Execute script if run directly
main().catch((error) => {
  console.error('❌ An error occurred:', error);
  process.exit(1);
});
