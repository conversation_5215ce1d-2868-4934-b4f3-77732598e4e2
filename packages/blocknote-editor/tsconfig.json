{"extends": "@resola-ai/typescript-config/vitejs.json", "include": ["components/**/*", "constants/**/*", "utils/**/*", "hooks/**/*", "index.tsx"], "exclude": ["node_modules", "dist", "vite.config.ts", "vitest.config.ts", "vitest.setup.ts", "**/*.test.tsx", "**/*.test.ts", "components/tests/**/*"], "compilerOptions": {"types": ["vitest/globals", "@testing-library/jest-dom"], "allowSyntheticDefaultImports": true, "ignoreDeprecations": "5.0", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@resola-ai/*": ["../*/index.tsx", "../*/index.ts"]}}}