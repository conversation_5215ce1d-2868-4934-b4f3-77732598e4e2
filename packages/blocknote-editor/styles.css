/* BlockNote Editor Styles */

/* Import BlockNote core styles */
@import '@blocknote/core/fonts/inter.css';
@import '@blocknote/shadcn/style.css';

/* Import phone input styles */
@import 'react-phone-input-2/lib/style.css';

/* Core BlockNote ShadCN Styles */
.bn-block-outer {
  line-height: 1.5;
  transition: margin 0.2s;
}
.bn-block {
  display: flex;
  flex-direction: column;
}
.bn-block-content {
  display: flex;
  padding: 3px 0;
  transition: font-size 0.2s;
  width: 100%;
}
.bn-block-content:before {
  transition: all 0.2s;
}
.bn-block-content.ProseMirror-selectednode > *,
.ProseMirror-selectednode > .bn-block-content > * {
  border-radius: 4px;
  outline: 4px solid rgb(100, 160, 255);
}
.bn-block-group .bn-block-group {
  margin-left: 1.5em;
}
.bn-block-group .bn-block-group > .bn-block-outer {
  position: relative;
}
.bn-block-group .bn-block-group > .bn-block-outer:not([data-prev-depth-changed]):before {
  content: ' ';
  display: inline;
  position: absolute;
  left: -20px;
  height: 100%;
  transition: all 0.2s 0.1s;
}
.bn-block-group .bn-block-group > .bn-block-outer[data-prev-depth-change='-2']:before {
  height: 0;
}
.bn-inline-content code {
  font-family: monospace;
}
[data-prev-depth-change='1'] {
  --x: 1;
}
[data-prev-depth-change='2'] {
  --x: 2;
}
[data-prev-depth-change='3'] {
  --x: 3;
}
[data-prev-depth-change='4'] {
  --x: 4;
}
[data-prev-depth-change='5'] {
  --x: 5;
}
[data-prev-depth-change='-1'] {
  --x: -1;
}
[data-prev-depth-change='-2'] {
  --x: -2;
}
[data-prev-depth-change='-3'] {
  --x: -3;
}
[data-prev-depth-change='-4'] {
  --x: -4;
}
[data-prev-depth-change='-5'] {
  --x: -5;
}
.bn-block-outer[data-prev-depth-change] {
  margin-left: calc(10px * var(--x));
}
.bn-block-outer[data-prev-depth-change] .bn-block-outer[data-prev-depth-change] {
  margin-left: 0;
}
[data-level='1'] {
  --level: 3em;
}
[data-level='2'] {
  --level: 2em;
}
[data-level='3'] {
  --level: 1.3em;
}
[data-prev-level='1'] {
  --prev-level: 3em;
}
[data-prev-level='2'] {
  --prev-level: 2em;
}
[data-prev-level='3'] {
  --prev-level: 1.3em;
}
.bn-block-outer[data-prev-type='heading'] > .bn-block > .bn-block-content {
  font-size: var(--prev-level);
  font-weight: 700;
}
.bn-block-outer:not([data-prev-type]) > .bn-block > .bn-block-content[data-content-type='heading'] {
  font-size: var(--level);
  font-weight: 700;
}
.bn-block-content:before {
  margin-right: 0;
  content: '';
}
.bn-block-content[data-content-type='numberedListItem'] {
  display: flex;
  gap: 1.2em;
}
[data-content-type='numberedListItem'] {
  --index: attr(data-index);
}
[data-prev-type='numberedListItem'] {
  --prev-index: attr(data-prev-index);
}
.bn-block-outer[data-prev-type='numberedListItem']:not([data-prev-index='none'])
  > .bn-block
  > .bn-block-content:before {
  content: var(--prev-index) '.';
}
.bn-block-outer:not([data-prev-type])
  > .bn-block
  > .bn-block-content[data-content-type='numberedListItem']:before {
  content: var(--index) '.';
}
.bn-block-content[data-content-type='bulletListItem'] {
  display: flex;
  gap: 1.2em;
}
.bn-block-content[data-content-type='checkListItem'] > div {
  display: flex;
}
.bn-block-content[data-content-type='checkListItem'] > div > div > input {
  margin: 0 1.2em 0 0;
  cursor: pointer;
}
.bn-block-content[data-content-type='checkListItem'][data-checked='true'] .bn-inline-content {
  text-decoration: line-through;
}
.bn-block-content[data-text-alignment='center'] {
  justify-content: center;
}
.bn-block-content[data-text-alignment='right'] {
  justify-content: flex-end;
}
.bn-block-outer[data-prev-type='bulletListItem'] > .bn-block > .bn-block-content:before {
  content: '•';
}
.bn-block-outer:not([data-prev-type])
  > .bn-block
  > .bn-block-content[data-content-type='bulletListItem']:before {
  content: '•';
}
[data-content-type='bulletListItem']
  ~ .bn-block-group
  > .bn-block-outer[data-prev-type='bulletListItem']
  > .bn-block
  > .bn-block-content:before {
  content: '◦';
}
[data-content-type='bulletListItem']
  ~ .bn-block-group
  > .bn-block-outer:not([data-prev-type])
  > .bn-block
  > .bn-block-content[data-content-type='bulletListItem']:before {
  content: '◦';
}
[data-content-type='bulletListItem']
  ~ .bn-block-group
  [data-content-type='bulletListItem']
  ~ .bn-block-group
  > .bn-block-outer[data-prev-type='bulletListItem']
  > .bn-block
  > .bn-block-content:before {
  content: '▪';
}
[data-content-type='bulletListItem']
  ~ .bn-block-group
  [data-content-type='bulletListItem']
  ~ .bn-block-group
  > .bn-block-outer:not([data-prev-type])
  > .bn-block
  > .bn-block-content[data-content-type='bulletListItem']:before {
  content: '▪';
}

/* File Block Styles */
[data-file-block] .bn-file-block-content-wrapper:has(.bn-add-file-button),
[data-file-block] .bn-file-block-content-wrapper:has(.bn-file-default-preview) {
  width: 100%;
}
[data-file-block] .bn-file-block-content-wrapper {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
[data-file-block] .bn-add-file-button {
  align-items: center;
  background-color: #f2f1ee;
  border-radius: 4px;
  color: #7d797a;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  gap: 10px;
  padding: 12px;
  width: 100%;
}
[data-file-block] .bn-add-file-button:hover {
  background-color: #e1e1e1;
}
[data-file-block] .bn-add-file-button-icon {
  width: 24px;
  height: 24px;
}
[data-file-block] .bn-add-file-button .bn-add-file-button-text {
  font-size: 0.9rem;
}
[data-file-block] .bn-file-and-caption-wrapper {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
}
[data-file-block] .bn-file-default-preview {
  align-items: center;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  gap: 4px;
  padding: 4px;
  width: 100%;
}
[data-file-block] .bn-file-default-preview:hover,
.ProseMirror-selectednode .bn-file-default-preview {
  background-color: #e1e1e1;
}
[data-file-block] .bn-file-default-preview-icon {
  width: 24px;
  height: 24px;
}
[data-file-block] .bn-visual-media-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  width: -moz-fit-content;
  width: fit-content;
}
[data-file-block] .bn-visual-media {
  border-radius: 4px;
  max-width: 100%;
}
[data-file-block] img.bn-visual-media {
  width: 512px;
}
[data-file-block] .bn-visual-media-resize-handle {
  position: absolute;
  width: 8px;
  height: 30px;
  background-color: #000;
  border: 1px solid white;
  border-radius: 4px;
  cursor: ew-resize;
}
[data-content-type='audio'] > .bn-file-block-content-wrapper,
.bn-audio {
  width: 100%;
}
[data-file-block] .bn-file-caption {
  font-size: 0.8em;
  padding-block: 4px;
}
[data-file-block] .bn-file-caption:empty {
  padding-block: 0;
}

/* Color and Alignment Styles */
[data-text-color='gray'] {
  color: #9b9a97;
}
[data-text-color='brown'] {
  color: #64473a;
}
[data-text-color='red'] {
  color: #e03e3e;
}
[data-text-color='orange'] {
  color: #d9730d;
}
[data-text-color='yellow'] {
  color: #dfab01;
}
[data-text-color='green'] {
  color: #4d6461;
}
[data-text-color='blue'] {
  color: #0b6e99;
}
[data-text-color='purple'] {
  color: #6940a5;
}
[data-text-color='pink'] {
  color: #ad1a72;
}
[data-background-color='gray'] {
  background-color: #ebeced;
}
[data-background-color='brown'] {
  background-color: #e9e5e3;
}
[data-background-color='red'] {
  background-color: #fbe4e4;
}
[data-background-color='orange'] {
  background-color: #faebdd;
}
[data-background-color='yellow'] {
  background-color: #fbf3db;
}
[data-background-color='green'] {
  background-color: #ddedea;
}
[data-background-color='blue'] {
  background-color: #ddebf1;
}
[data-background-color='purple'] {
  background-color: #eae4f2;
}
[data-background-color='pink'] {
  background-color: #f4dfeb;
}
[data-text-alignment='left'] {
  justify-content: flex-start;
  text-align: left;
}
[data-text-alignment='center'] {
  justify-content: center;
  text-align: center;
}
[data-text-alignment='right'] {
  justify-content: flex-end;
  text-align: right;
}
[data-text-alignment='justify'] {
  justify-content: flex-start;
  text-align: justify;
}

/* Table Styles */
.ProseMirror .tableWrapper {
  overflow-x: auto;
}
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  overflow: hidden;
}
.ProseMirror td,
.ProseMirror th {
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}
.ProseMirror .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 4px;
  z-index: 20;
  background-color: #adf;
  pointer-events: none;
}
.ProseMirror.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}
.ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: '';
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

/* Core Editor Styles */
.bn-editor {
  outline: none;
  padding-inline: 54px;
  --N800: #172b4d;
  --N40: #dfe1e6;
}
.bn-root {
  box-sizing: border-box;
}
.bn-root *,
.bn-root *:before,
.bn-root *:after {
  box-sizing: inherit;
}
.bn-default-styles p,
.bn-default-styles h1,
.bn-default-styles h2,
.bn-default-styles h3,
.bn-default-styles li {
  margin: 0;
  padding: 0;
  font-size: inherit;
  min-width: 2px !important;
}
.bn-default-styles {
  font-size: 16px;
  font-weight: 400;
  font-family:
    Inter,
    SF Pro Display,
    -apple-system,
    BlinkMacSystemFont,
    Open Sans,
    Segoe UI,
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    Helvetica Neue,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container and Theme Variables */
.bn-container {
  --bn-colors-editor-text: #3f3f3f;
  --bn-colors-editor-background: #ffffff;
  --bn-colors-menu-text: #3f3f3f;
  --bn-colors-menu-background: #ffffff;
  --bn-colors-tooltip-text: #3f3f3f;
  --bn-colors-tooltip-background: #efefef;
  --bn-colors-hovered-text: #3f3f3f;
  --bn-colors-hovered-background: #efefef;
  --bn-colors-selected-text: #ffffff;
  --bn-colors-selected-background: #3f3f3f;
  --bn-colors-disabled-text: #afafaf;
  --bn-colors-disabled-background: #efefef;
  --bn-colors-shadow: #cfcfcf;
  --bn-colors-border: #efefef;
  --bn-colors-side-menu: #cfcfcf;
  --bn-colors-highlights-gray-text: #9b9a97;
  --bn-colors-highlights-gray-background: #ebeced;
  --bn-colors-highlights-brown-text: #64473a;
  --bn-colors-highlights-brown-background: #e9e5e3;
  --bn-colors-highlights-red-text: #e03e3e;
  --bn-colors-highlights-red-background: #fbe4e4;
  --bn-colors-highlights-orange-text: #d9730d;
  --bn-colors-highlights-orange-background: #f6e9d9;
  --bn-colors-highlights-yellow-text: #dfab01;
  --bn-colors-highlights-yellow-background: #fbf3db;
  --bn-colors-highlights-green-text: #4d6461;
  --bn-colors-highlights-green-background: #ddedea;
  --bn-colors-highlights-blue-text: #0b6e99;
  --bn-colors-highlights-blue-background: #ddebf1;
  --bn-colors-highlights-purple-text: #6940a5;
  --bn-colors-highlights-purple-background: #eae4f2;
  --bn-colors-highlights-pink-text: #ad1a72;
  --bn-colors-highlights-pink-background: #f4dfeb;
  --bn-font-family:
    'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Open Sans', 'Segoe UI', 'Roboto',
    'Oxygen', 'Ubuntu', 'Cantarell', 'Helvetica Neue', sans-serif;
  --bn-border-radius: 6px;
  --bn-shadow-medium: 0 4px 12px var(--bn-colors-shadow);
  --bn-shadow-light: 0 2px 6px var(--bn-colors-border);
  --bn-border: 1px solid var(--bn-colors-border);
  --bn-border-radius-small: max(var(--bn-border-radius) - 2px, 1px);
  --bn-border-radius-medium: var(--bn-border-radius);
  --bn-border-radius-large: max(var(--bn-border-radius) + 2px, 1px);
}

/* Dark Theme */
.bn-container[data-color-scheme='dark'] {
  --bn-colors-editor-text: #cfcfcf;
  --bn-colors-editor-background: #1f1f1f;
  --bn-colors-menu-text: #cfcfcf;
  --bn-colors-menu-background: #1f1f1f;
  --bn-colors-tooltip-text: #cfcfcf;
  --bn-colors-tooltip-background: #161616;
  --bn-colors-hovered-text: #cfcfcf;
  --bn-colors-hovered-background: #161616;
  --bn-colors-selected-text: #cfcfcf;
  --bn-colors-selected-background: #0f0f0f;
  --bn-colors-disabled-text: #3f3f3f;
  --bn-colors-disabled-background: #161616;
  --bn-colors-shadow: #0f0f0f;
  --bn-colors-border: #161616;
  --bn-colors-side-menu: #7f7f7f;
  --bn-colors-highlights-gray-text: #bebdb8;
  --bn-colors-highlights-gray-background: #9b9a97;
  --bn-colors-highlights-brown-text: #8e6552;
  --bn-colors-highlights-brown-background: #64473a;
  --bn-colors-highlights-red-text: #ec4040;
  --bn-colors-highlights-red-background: #be3434;
  --bn-colors-highlights-orange-text: #e3790d;
  --bn-colors-highlights-orange-background: #b7600a;
  --bn-colors-highlights-yellow-text: #dfab01;
  --bn-colors-highlights-yellow-background: #b58b00;
  --bn-colors-highlights-green-text: #6b8b87;
  --bn-colors-highlights-green-background: #4d6461;
  --bn-colors-highlights-blue-text: #0e87bc;
  --bn-colors-highlights-blue-background: #0b6e99;
  --bn-colors-highlights-purple-text: #8552d7;
  --bn-colors-highlights-purple-background: #6940a5;
  --bn-colors-highlights-pink-text: #da208f;
  --bn-colors-highlights-pink-background: #ad1a72;
}

/* Base Styling */
.bn-container * {
  font-family: var(--bn-font-family);
}
.bn-editor {
  background-color: var(--bn-colors-editor-background);
  border-radius: var(--bn-border-radius-large);
  color: var(--bn-colors-editor-text);
  font-family: var(--bn-font-family);
}
.bn-react-node-view-renderer {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.bn-block-group .bn-block-group .bn-block-outer:not([data-prev-depth-changed]):before {
  border-left: 1px solid var(--bn-colors-side-menu);
}
.bn-inline-content:has(> .ProseMirror-trailingBreak):before {
  color: var(--bn-colors-side-menu);
}
.bn-container .bn-color-icon {
  align-items: center;
  border: var(--bn-border);
  border-radius: var(--bn-border-radius-small);
  display: flex;
  justify-content: center;
}
.bn-error-text {
  color: red;
  font-size: 12px;
}

/* Tailwind/ShadCN Reset and Utility Classes */
*,
:before,
:after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}
:before,
:after {
  --tw-content: '';
}
html,
:host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-family:
    ui-sans-serif,
    system-ui,
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    Segoe UI Symbol,
    'Noto Color Emoji';
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
}
body {
  margin: 0;
  line-height: inherit;
}

/* Component-specific Styles */
.bn-side-menu {
  display: flex;
  justify-content: center;
}
.bn-side-menu .bn-button {
  padding: 0;
  height: 24px;
}
.bn-select {
  max-height: var(--radix-select-content-available-height);
}
.bn-menu-dropdown {
  max-height: var(--radix-dropdown-menu-content-available-height);
}
.bn-color-picker-dropdown {
  overflow: auto;
}
.bn-suggestion-menu-item[aria-selected='true'],
.bn-suggestion-menu-item:hover {
  background-color: hsl(var(--accent));
}

/* Viewer Styles */
[data-blocknote-viewer] {
  border-radius: 6px;
  border: none;
  padding: 12px 12px 12px 0;
}

[data-blocknote-viewer] .bn-container {
  padding: 0;
}

[data-blocknote-viewer] .bn-container .bn-inline-content {
  word-wrap: break-word;
}

[data-blocknote-viewer] .bn-container a {
  text-decoration: underline;
  cursor: pointer;
}

[data-blocknote-viewer] .bn-container a:visited {
  opacity: 0.8;
}

[data-blocknote-viewer] .bn-editor {
  padding: 0;
  font-size: 14px;
  writing-mode: horizontal-tb;
  background-color: transparent;
}

[data-blocknote-viewer] .bn-editor .block-note-youtube-iframe {
  max-width: 100%;
  min-width: 280px;
  min-height: 315px;
}

[data-blocknote-viewer]
  .bn-editor
  .bn-block
  .bn-block-content[data-content-type]
  .bn-inline-content:has(> .ProseMirror-trailingBreak),
[data-blocknote-viewer] .bn-editor .bn-block-content.ProseMirror-selectednode > *,
.ProseMirror-selectednode > .bn-block-content > * {
  outline: none;
}

[data-blocknote-viewer] [data-file-block] .bn-file-caption {
  font-style: italic;
  text-align: center;
}

[data-blocknote-viewer] [data-file-block] .bn-visual-media {
  min-height: 100px;
}
