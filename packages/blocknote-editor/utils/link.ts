import { LinkTarget } from '../constants/link';
import { isPhoneLink } from './phone';

/**
 * Adds a target parameter to a URL's query string.
 *
 * @param url - The URL to modify
 * @param targetValue - The target value to add (_blank, _self, etc.)
 * @param shouldPreservePhoneLinks - Whether to leave telephone links unchanged
 * @returns The URL with target parameter added (or original URL for phone links)
 */
export const addTargetToLinkQuery = (
  url: string,
  targetValue: LinkTarget,
  shouldPreservePhoneLinks = false
): string => {
  // Skip processing for telephone links when preservation is enabled
  if (shouldPreservePhoneLinks && isPhoneLink(url)) {
    return url;
  }

  try {
    const urlObject = new URL(url);

    // Don't modify if target already exists
    if (urlObject.searchParams.has('target')) {
      return url;
    }

    // Add the target parameter
    urlObject.searchParams.set('target', targetValue);
    return urlObject.toString();
  } catch (error) {
    // Return original URL for invalid URLs or relative paths
    return url;
  }
};

/**
 * Extracts the target parameter from a URL's query string.
 *
 * @param url - The URL to extract target from
 * @param shouldPreservePhoneLinks - Whether to handle telephone links specially
 * @returns The target value from the URL or default value
 */
export const getTargetFromLinkQuery = (
  url: string,
  shouldPreservePhoneLinks = false
): LinkTarget => {
  // Phone links should open in same window when preservation is enabled
  if (shouldPreservePhoneLinks && isPhoneLink(url)) {
    return LinkTarget.SELF;
  }

  try {
    const urlObject = new URL(url);
    const extractedTarget = urlObject.searchParams.get('target');

    // Return the extracted target or default to BLANK
    return (extractedTarget as LinkTarget) || LinkTarget.BLANK;
  } catch (error) {
    // Default to BLANK for invalid URLs
    return LinkTarget.BLANK;
  }
};

/**
 * Removes the target parameter from a URL's query string.
 *
 * @param url - The URL to modify
 * @param shouldPreservePhoneLinks - Whether to leave telephone links unchanged
 * @returns The URL with target parameter removed (or original URL for phone links)
 */
export const removeTargetFromLinkQuery = (
  url: string,
  shouldPreservePhoneLinks = false
): string => {
  // Skip processing for telephone links when preservation is enabled
  if (shouldPreservePhoneLinks && isPhoneLink(url)) {
    return url;
  }

  try {
    const urlObject = new URL(url);

    // Remove the target parameter
    urlObject.searchParams.delete('target');
    return urlObject.toString();
  } catch (error) {
    // Return original URL for invalid URLs or relative paths
    return url;
  }
};
