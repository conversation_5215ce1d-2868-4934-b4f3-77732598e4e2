import { render, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { type ControlComponentType, createComponentWrapper } from './componentWrappers';

// Mock components for testing
const MockDefaultComponent: ControlComponentType = (props) => (
  <div data-testid='default-component' {...props}>
    Default Component
  </div>
);

const MockCustomComponent: ControlComponentType = (props) => (
  <div data-testid='custom-component' {...props}>
    Custom Component
  </div>
);

// Set display names for better testing
MockDefaultComponent.displayName = 'MockDefaultComponent';
MockCustomComponent.displayName = 'MockCustomComponent';

describe('componentWrappers', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('createComponentWrapper', () => {
    it('returns default component when no custom component is provided', () => {
      const WrappedComponent = createComponentWrapper(undefined, MockDefaultComponent);

      render(<WrappedComponent />);

      expect(screen.getByTestId('default-component')).toBeInTheDocument();
      expect(screen.getByText('Default Component')).toBeInTheDocument();
    });

    it('returns custom component when provided', () => {
      const WrappedComponent = createComponentWrapper(MockCustomComponent, MockDefaultComponent);

      render(<WrappedComponent />);

      expect(screen.getByTestId('custom-component')).toBeInTheDocument();
      expect(screen.getByText('Custom Component')).toBeInTheDocument();
    });

    it('passes props to the wrapped component', () => {
      const testProps = {
        'data-custom-prop': 'test-value',
        className: 'test-class',
      };

      const WrappedComponent = createComponentWrapper(undefined, MockDefaultComponent);

      render(<WrappedComponent {...testProps} />);

      const component = screen.getByTestId('default-component');
      expect(component).toHaveAttribute('data-custom-prop', 'test-value');
      expect(component).toHaveClass('test-class');
    });

    it('merges additional props with component props', () => {
      const additionalProps = { 'data-additional': 'additional-value' };
      const componentProps = { 'data-component': 'component-value' };

      const WrappedComponent = createComponentWrapper(
        undefined,
        MockDefaultComponent,
        additionalProps
      );

      render(<WrappedComponent {...componentProps} />);

      const component = screen.getByTestId('default-component');
      expect(component).toHaveAttribute('data-additional', 'additional-value');
      expect(component).toHaveAttribute('data-component', 'component-value');
    });

    it('sets correct display name for default component', () => {
      const WrappedComponent = createComponentWrapper(undefined, MockDefaultComponent);

      expect(WrappedComponent.displayName).toBe('DefaultMockDefaultComponent');
    });

    it('sets correct display name for custom component', () => {
      const WrappedComponent = createComponentWrapper(MockCustomComponent, MockDefaultComponent);

      expect(WrappedComponent.displayName).toBe('CustomMockCustomComponent');
    });

    it('handles component without display name', () => {
      const ComponentWithoutName = (props: any) => <div {...props}>No Name</div>;

      const WrappedComponent = createComponentWrapper(ComponentWithoutName, MockDefaultComponent);

      // Should fall back to function name or 'Component'
      expect(WrappedComponent.displayName).toMatch(/^Custom(ComponentWithoutName|Component)$/);
    });

    it('additional props override component props when there are conflicts', () => {
      const additionalProps = { className: 'additional-class' };
      const componentProps = { className: 'component-class' };

      const WrappedComponent = createComponentWrapper(
        undefined,
        MockDefaultComponent,
        additionalProps
      );

      render(<WrappedComponent {...componentProps} />);

      const component = screen.getByTestId('default-component');
      // Additional props should override component props
      expect(component).toHaveClass('additional-class');
      expect(component).not.toHaveClass('component-class');
    });
  });
});
