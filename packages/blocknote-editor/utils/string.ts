import { cleanHTMLToMarkdown } from '@blocknote/core';
import { STRING_UTIL_REGEX_PATTERNS } from '../constants/regex';

/**
 * Converts HTML to plain text.
 * @param html HTML to convert
 * @returns Plain text
 */
export const convertHTMLToText = (html: string) => {
  return html ? replaceNbspWithEmpty(html.replace(STRING_UTIL_REGEX_PATTERNS.HTML_TAGS, '')) : '';
};

/**
 * Converts Break Line `\n` string to `<br />` in HTML.
 * @param text Plain text to convert
 * @returns HTML
 */
export const convertBreakLineToHTML = (text: string) => {
  return text ? text.replace(STRING_UTIL_REGEX_PATTERNS.BREAK_LINE, '<br />') : '';
};

/**
 * Converts Text Markdown to HTML Break Line.
 * @param markdownText Markdown text to convert
 * @returns HTML
 */
export const converMarkdownTextToHTMLBreakline = (markdownText: string) => {
  return markdownText
    .replace(STRING_UTIL_REGEX_PATTERNS.MARKDOWN_BREAK_LINE, '<br />')
    .replace(STRING_UTIL_REGEX_PATTERNS.MARKDOWN_ESCAPED_BREAK_LINE, '<br />');
};

/**
 * Checks if the HTML includes media tags.
 * @param html HTML to check
 * @returns Boolean
 */
export const isIncludedMediaHTML = (html: string) => {
  return html.includes('<img') || html.includes('<video') || html.includes('<audio');
};

/**
 * Checks if the HTML includes media tags.
 * @param html HTML to check
 * @returns Boolean
 */
export const isIncludedBlockNoteMediaHTML = (html: string) => {
  return (
    html.includes('<img') ||
    html.includes('<video') ||
    html.includes('<audio') ||
    html.includes('data-content-type="image"') ||
    html.includes('data-content-type="video"') ||
    html.includes('data-content-type="audio"') ||
    html.includes('data-content-type="file"')
  );
};

/**
 * Replaces URLs in the content with anchor tags.
 * @param input Content to replace URLs
 * @returns Content with replaced URLs
 */
export const replaceUrlsWithAnchorTags = (input: string) => {
  if (!input) return input;

  // Skip if the text is already a markdown link
  if (STRING_UTIL_REGEX_PATTERNS.MARKDOWN_LINK.exec(input)) {
    return input;
  }

  // Process the input through a series of steps
  const processedInput = extractExistingAnchorTags(input);
  const result = processUrls(processedInput.text);

  // Restore original anchor tags
  return restoreAnchorTags(result, processedInput.anchors);
};

/**
 * Extracts existing anchor tags and replaces them with placeholders
 */
const extractExistingAnchorTags = (text: string) => {
  const anchors: string[] = [];

  const processedText = text.replace(STRING_UTIL_REGEX_PATTERNS.ANCHOR_TAGS, (match) => {
    const placeholder = `__ANCHOR_${anchors.length}__`;
    anchors.push(match);
    return placeholder;
  });

  return { text: processedText, anchors };
};

/**
 * Restores original anchor tags from placeholders
 */
const restoreAnchorTags = (text: string, anchors: string[]) => {
  let result = text;
  anchors.forEach((original, index) => {
    result = result.replace(`__ANCHOR_${index}__`, original);
  });
  return result;
};

/**
 * Processes all URLs in text, replacing them with anchor tags
 */
const processUrls = (text: string) => {
  // Process email addresses first
  let result = text.replace(
    STRING_UTIL_REGEX_PATTERNS.EMAIL,
    (email) => `<a href="mailto:${email}">${email}</a>`
  );

  // Find all URLs in the text
  const matches = Array.from(result.matchAll(STRING_UTIL_REGEX_PATTERNS.URL));

  // Process URLs from end to beginning to avoid index shifts
  for (let i = matches.length - 1; i >= 0; i--) {
    const match = matches[i];
    if (!match.index && match.index !== 0) continue;

    const url = match[0];
    const start = match.index;
    const end = start + url.length;

    result = replaceUrlWithAnchor(result, url, start, end);
  }

  return result;
};

/**
 * Determines how to format a URL and replace it with an anchor tag
 */
const replaceUrlWithAnchor = (text: string, url: string, start: number, end: number) => {
  // Check if this is a www. URL (need to add protocol for href)
  const href = getHrefFromUrl(url);

  // Handle special cases
  if (hasTrailingPunctuation(url)) {
    return handleUrlWithTrailingPunctuation(text, url, start, end);
  }

  if (hasJapaneseOrChineseTextBoundary(url)) {
    return handleUrlWithAsianTextBoundary(text, url, start, end);
  }

  // Regular URL without special handling needed
  return `${text.substring(0, start)}<a target="_blank" rel="noreferrer" href="${href}">${url}</a>${text.substring(end)}`;
};

/**
 * Converts a URL to an href attribute value
 */
const getHrefFromUrl = (url: string): string => {
  return url.startsWith('www.') ? `https://${url}` : url;
};

/**
 * Checks if a URL has trailing punctuation
 */
const hasTrailingPunctuation = (url: string) => {
  return (
    STRING_UTIL_REGEX_PATTERNS.TRAILING_PUNCTUATION.test(url) &&
    !STRING_UTIL_REGEX_PATTERNS.TRAILING_URL_CHARS.test(url.slice(-2, -1))
  );
};

/**
 * Handles URLs with trailing punctuation
 */
const handleUrlWithTrailingPunctuation = (
  text: string,
  url: string,
  start: number,
  end: number
) => {
  const cleanUrl = url.slice(0, -1);
  const punct = url.slice(-1);
  const cleanHref = cleanUrl.startsWith('www.') ? `https://${cleanUrl}` : cleanUrl;

  return `${text.substring(0, start)}<a target="_blank" rel="noreferrer" href="${cleanHref}">${cleanUrl}</a>${punct}${text.substring(end)}`;
};

/**
 * Checks if a URL ends with Japanese or Chinese characters
 */
const hasJapaneseOrChineseTextBoundary = (url: string) => {
  return STRING_UTIL_REGEX_PATTERNS.JAPANESE_CHINESE_CHARS.test(url.slice(-1));
};

/**
 * Handles URLs that end with Japanese or Chinese text
 */
const handleUrlWithAsianTextBoundary = (text: string, url: string, start: number, end: number) => {
  const jpMatch = STRING_UTIL_REGEX_PATTERNS.URL_WITH_ASIAN_TEXT.exec(url);

  if (!jpMatch) return text;

  // Store the matched groups immediately to prevent any potential changes
  const [_, cleanUrl, jpText] = jpMatch;
  const cleanHref = cleanUrl.startsWith('www.') ? `https://${cleanUrl}` : cleanUrl;

  return `${text.substring(0, start)}<a target="_blank" rel="noreferrer" href="${cleanHref}">${cleanUrl}</a>${jpText}${text.substring(end)}`;
};

/**
 * Decodes HTML entities in the content.
 * @param content Content to decode
 * @returns Decoded content
 */
export const decodeSpecialCharacters = (content: string | null | undefined) => {
  // Guard against null or undefined inputs to prevent runtime errors
  if (typeof content !== 'string' || content.length === 0) {
    return '';
  }

  // First, decode any HTML entities
  let decodedText = content;

  // Replace &lt; and &gt; with actual characters
  decodedText = decodedText
    .replace(STRING_UTIL_REGEX_PATTERNS.HTML_LT, '<')
    .replace(STRING_UTIL_REGEX_PATTERNS.HTML_GT, '>');

  // Replace &#x3D; with =
  decodedText = decodedText
    .replace(STRING_UTIL_REGEX_PATTERNS.HTML_EQUALS, '=')
    .replace(STRING_UTIL_REGEX_PATTERNS.HTML_EQUALS_AMP, '=');

  // Replace &amp; with &
  decodedText = decodedText
    .replace(STRING_UTIL_REGEX_PATTERNS.HTML_AMP, '&')
    .replace(STRING_UTIL_REGEX_PATTERNS.HTML_AMP_ESCAPED, '&');

  // Replace %20 with %
  decodedText = decodedText.replace(STRING_UTIL_REGEX_PATTERNS.HTML_PERCENT, '%');

  // Replace HTML link tags with Markdown link syntax [text](url)
  decodedText = decodedText.replace(STRING_UTIL_REGEX_PATTERNS.HTML_LINK, '[$2]($1)');

  // Replace %7B with {
  decodedText = decodedText.replace(STRING_UTIL_REGEX_PATTERNS.HTML_LEFT_BRACE, '{');

  // Replace %7D with }
  decodedText = decodedText.replace(STRING_UTIL_REGEX_PATTERNS.HTML_RIGHT_BRACE, '}');

  // Replace %2E with .
  decodedText = decodedText.replace(STRING_UTIL_REGEX_PATTERNS.HTML_DOT, '.');

  return decodedText;
};

/**
 * Decode Japanese text and special charaters in Markdown link with variables.
 * @param markdownContent Markdown content to customize
 * @example detectMarkdownLinkWithVariable('[{{varable_text}}]({{variable_link}})', (match, text, url) => {});
 * @example detectMarkdownLinkWithVariable('[{{varable_text}}](tel: {{variable_link}})', (match, text, url) => {});
 */
export const detectMarkdownLinkWithVariable = (
  markdownContent: string,
  matchedHandler: (match: string) => string
) => {
  // Replace each match using a callback function
  return markdownContent
    .replace(STRING_UTIL_REGEX_PATTERNS.MARKDOWN_VARIABLE_PHONE_LINK, matchedHandler)
    .replace(STRING_UTIL_REGEX_PATTERNS.MARKDOWN_VARIABLE_LINK, matchedHandler);
};

/**
 * Encodes HTML entities in the content.
 * @param content Content to encode
 * @returns Encoded content
 */
export const encodeMarkdownContent = (content: string) => {
  // First, decode any HTML entities
  let encodedText = content;

  // Remove breaklines characters at the beginning and end with a fixed maximum length
  encodedText = encodedText
    .replace(STRING_UTIL_REGEX_PATTERNS.LEADING_WHITESPACE, '') // Remove leading characters
    .replace(STRING_UTIL_REGEX_PATTERNS.TRAILING_WHITESPACE, ''); // Remove trailing characters

  // Replace &lt; and &gt; with actual characters
  encodedText = encodedText.replace(STRING_UTIL_REGEX_PATTERNS.URL_GENERAL, (match) => {
    return match.replace(/\\/g, '');
  });

  // Replace multiple new lines with a single new line in BlockNote
  encodedText = encodedText.replace(STRING_UTIL_REGEX_PATTERNS.MULTIPLE_NEWLINES, '\n');

  encodedText = decodeSpecialCharacters(encodedText);

  // Preserve styled variables by not modifying them
  const styledVariables: string[] = [];
  encodedText = encodedText.replace(STRING_UTIL_REGEX_PATTERNS.STYLED_VARIABLE, (match) => {
    const placeholder = `__STYLED_VAR_${styledVariables.length}__`;
    styledVariables.push(match);
    return placeholder;
  });

  // Preserve variable links by not modifying them
  const variableLinks: string[] = [];
  encodedText = encodedText.replace(STRING_UTIL_REGEX_PATTERNS.VARIABLE_LINK, (match) => {
    const placeholder = `__VAR_LINK_${variableLinks.length}__`;
    variableLinks.push(match);
    return placeholder;
  });

  encodedText = detectMarkdownLinkWithVariable(encodedText, (match) => {
    // Return the updated Markdown link
    return decodeURIComponent(match);
  });

  // Restore styled variables
  styledVariables.forEach((varText, index) => {
    encodedText = encodedText.replace(`__STYLED_VAR_${index}__`, varText);
  });

  // Restore variable links
  variableLinks.forEach((varLink, index) => {
    encodedText = encodedText.replace(`__VAR_LINK_${index}__`, varLink);
  });

  return encodedText;
};

/**
 * Replaces phone links in markdown.
 * @param markdown Markdown to replace phone links
 * @returns Markdown with replaced phone links
 */
export const replacePhoneLinks = (markdown: string): string => {
  // Replace [Phone variable](tel:<number>) or [Phone Number](tel:<number>) with <a> tags
  return markdown.replace(
    STRING_UTIL_REGEX_PATTERNS.PHONE_LINK,
    (_match, linkText, phoneNumber) => {
      return `<phone href="tel:${phoneNumber}" target='_blank' rel='noreferrer'>${linkText}</phone>`;
    }
  );
};

/**
 * Replace &nbsp; with two new line characters for a break.
 * @param markdown Markdown to replace
 * @returns Markdown with replaced &nbsp;
 */
export const replaceNbspBreaksWithBreaks = (markdown) => {
  // Replace &nbsp; with two new line characters for a break
  return markdown
    .replace(STRING_UTIL_REGEX_PATTERNS.NBSP_BREAKS, '<br />')
    .replace(STRING_UTIL_REGEX_PATTERNS.AMP_NBSP_BREAKS, '<br />')
    .replace(/\n&nbsp;\n/g, '<br /><br />') // Handle pattern with &nbsp; between two newlines
    .replace(/\n&amp;nbsp;\n/g, '<br /><br />'); // Handle pattern with &amp;nbsp; between two newlines
};

/**
 * Replace &nbsp; with empty string.
 * @param content Markdown to replace
 * @returns Content with replaced &nbsp;
 */
export const replaceNbspWithEmpty = (content) => {
  return content
    .replace(STRING_UTIL_REGEX_PATTERNS.NBSP, '')
    .replace(STRING_UTIL_REGEX_PATTERNS.AMP_NBSP, '');
};

/**
 * Decode &nbsp; to HTML space.
 * @param content Content to decode
 * @returns Content with decoded &nbsp;
 */
export const decodeNbsp = (content: string) => {
  return content.replace(STRING_UTIL_REGEX_PATTERNS.AMP_NBSP, '&nbsp;');
};

/**
 * Clean bad whitespace character from markdown content
 * @param content: string
 * @returns string
 */
export const cleanBadMarkdownContent = (content: string) => {
  // Replace non-breaking spaces (Unicode \u00A0) and other bad whitespace characters with regular spaces
  return content.replace(STRING_UTIL_REGEX_PATTERNS.BAD_WHITESPACE, ' ').trim();
};

/**
 * Normalizes markdown after purified.
 * @param markdown Markdown to normalize
 * @returns Normalized markdown
 */
export const normalizeMarkdownToBlockNote = (markdown: string) => {
  // Preserve styled variables by not modifying them
  const styledVariables: string[] = [];
  let normalizedMarkdown = markdown.replace(STRING_UTIL_REGEX_PATTERNS.STYLED_VARIABLE, (match) => {
    const placeholder = `__STYLED_VAR_${styledVariables.length}__`;
    styledVariables.push(match);
    return placeholder;
  });

  // Preserve variable links by not modifying them
  const variableLinks: string[] = [];
  normalizedMarkdown = normalizedMarkdown.replace(
    STRING_UTIL_REGEX_PATTERNS.VARIABLE_LINK,
    (match) => {
      const placeholder = `__VAR_LINK_${variableLinks.length}__`;
      variableLinks.push(match);
      return placeholder;
    }
  );

  // Preserve pure variables as a fallback
  const pureVariables: string[] = [];
  normalizedMarkdown = normalizedMarkdown.replace(
    STRING_UTIL_REGEX_PATTERNS.PURE_VARIABLE,
    (match) => {
      // Only replace if it looks like a standalone variable, not part of another pattern
      if (normalizedMarkdown.includes(`[${match}]`) || normalizedMarkdown.includes(`](${match})`)) {
        return match; // Don't replace if it's part of a link
      }
      const placeholder = `__PURE_VAR_${pureVariables.length}__`;
      pureVariables.push(match);
      return placeholder;
    }
  );

  // Revert single new lines to double new lines to import to BlockNote
  normalizedMarkdown = normalizedMarkdown.replace(STRING_UTIL_REGEX_PATTERNS.LINE_BREAK, '\n\n');
  normalizedMarkdown = decodeSpecialCharacters(normalizedMarkdown);

  // Restore styled variables
  styledVariables.forEach((varText, index) => {
    normalizedMarkdown = normalizedMarkdown.replace(`__STYLED_VAR_${index}__`, varText);
  });

  // Restore variable links
  variableLinks.forEach((varLink, index) => {
    normalizedMarkdown = normalizedMarkdown.replace(`__VAR_LINK_${index}__`, varLink);
  });

  // Restore pure variables
  pureVariables.forEach((varText, index) => {
    normalizedMarkdown = normalizedMarkdown.replace(`__PURE_VAR_${index}__`, varText);
  });

  return normalizedMarkdown;
};

/**
 * Checks if markdown content is empty after removing whitespace and &nbsp; characters.
 * @param content Markdown content to check
 * @returns True if content is empty, false otherwise
 */
export const isEmptyMarkdownContent = (content: string) => {
  return (
    content
      .replace(STRING_UTIL_REGEX_PATTERNS.NBSP, '')
      .replace(STRING_UTIL_REGEX_PATTERNS.NBSP_BREAKS, '')
      .replace(/\n/g, '')
      .replace(/\s+/g, '')
      .trim() === ''
  );
};

/**
 * Finalizes markdown after purified.
 * @param markdown Markdown to finalize
 * @returns Finalized markdown
 */
export const finalizeMarkdownFromBlockNote = (markdown: string) => {
  return isEmptyMarkdownContent(markdown) ? '' : encodeMarkdownContent(markdown);
};

/**
 * Normalizes markdown for React Markdown.
 * @param markdown Markdown to normalize
 * @returns Normalized markdown
 */
export const normalizeMarkdownForReactMarkdown = (markdown: string) => {
  return cleanBadMarkdownContent(
    replacePhoneLinks(replaceNbspBreaksWithBreaks(decodeSpecialCharacters(markdown)))
  );
};

/**
 * Normalizes markdown for text message.
 * @param textWithMarkdown Text with markdown to normalize
 * @returns Normalized text with markdown
 */
export const normalizeMarkdownForTextMessage = (textWithMarkdown: string) => {
  return replaceUrlsWithAnchorTags(replaceNbspWithEmpty(decodeSpecialCharacters(textWithMarkdown)));
};

/**
 * Normalizes text message.
 * @param text Text to normalize
 * @returns Normalized text
 */
export const normalizeTextMessage = (text: string) => {
  return replaceUrlsWithAnchorTags(text);
};

/**
 * Replace white space to HTML nbsp.
 * @param text Text to replace
 * @returns Text with replaced white space
 */
export const replaceWhiteSpaceToHTMLNbsp = (text: string) => {
  return text.replace(STRING_UTIL_REGEX_PATTERNS.MULTIPLE_SPACES, (match) =>
    match.replace(/ /g, '&nbsp;')
  );
};

/**
 * Normalizes different types of line breaks to Unix-style (\n)
 * Handles:
 * - \n: Unix/macOS line break
 * - \r: Classic Mac line break
 * - \r\n: Windows line break
 * - Unicode line separators
 */
export const normalizeLineBreaks = (text: string): string => {
  return text
    .replace(STRING_UTIL_REGEX_PATTERNS.WINDOWS_LINE_BREAK, '\n') // Convert Windows line breaks
    .replace(STRING_UTIL_REGEX_PATTERNS.CLASSIC_MAC_LINE_BREAK, '\n') // Convert Classic Mac line breaks
    .replace(STRING_UTIL_REGEX_PATTERNS.UNICODE_LINE_SEPARATOR, '\n') // Convert Unicode line separator
    .replace(STRING_UTIL_REGEX_PATTERNS.UNICODE_PARAGRAPH_SEPARATOR, '\n'); // Convert Unicode paragraph separator
};

/**
 * Checks if text contains multiple consecutive line breaks
 * Uses normalized line breaks for consistent checking across platforms
 */
export const hasMultipleNewLines = (text: string): boolean => {
  const normalizedText = normalizeLineBreaks(text);
  return STRING_UTIL_REGEX_PATTERNS.NEWLINE_TEST.test(normalizedText);
};

/**
 * Normalize markdown when pasting from clipboard.
 * @param markdown Markdown to normalize
 * @returns Normalized markdown
 */
export const normalizeMarkdownWhenPastingToEditor = (markdown: string) => {
  let customMarkdown = detectMarkdownLinkWithVariable(markdown, (match) => {
    // Return the updated Markdown link
    return match.replace(/\./g, '%2E');
  });

  // Replace multiple new lines to HTML nbsp
  if (hasMultipleNewLines(customMarkdown)) {
    // Normalize line breaks to Unix-style (\n)
    customMarkdown = normalizeLineBreaks(customMarkdown);

    // Filter out empty lines and join with \n&nbsp;\n
    customMarkdown = customMarkdown.replace(
      STRING_UTIL_REGEX_PATTERNS.DOUBLE_NEWLINES,
      '\n&nbsp;\n&nbsp;'
    );

    // Remove &nbsp; from the start of headings and list items
    customMarkdown = customMarkdown.replace(STRING_UTIL_REGEX_PATTERNS.HEADING_WITH_NBSP, '$2');
  }

  return replaceWhiteSpaceToHTMLNbsp(
    normalizeMarkdownToBlockNote(cleanBadMarkdownContent(customMarkdown))
  );
};

/**
 * Normalize markdown when copying from editor.
 * @param markdown Markdown to normalize
 * @returns Normalized markdown
 */
export const normalizeMarkdownWhenCopyingFromEditor = (markdown: string) => {
  // Replace white space to HTML nbsp
  let normalizedMarkdown = markdown.replace(STRING_UTIL_REGEX_PATTERNS.MULTIPLE_SPACES, (match) =>
    match.replace(/ /g, '&nbsp;')
  );

  // Clean HTML to markdown
  normalizedMarkdown = cleanHTMLToMarkdown(
    normalizeEmptyHTMLBlockFromClipboard(normalizedMarkdown)
  );

  // Replace double new lines to single new line
  normalizedMarkdown = normalizedMarkdown.replace(
    STRING_UTIL_REGEX_PATTERNS.MULTIPLE_NEWLINES_REPLACEMENT,
    (match) => match.slice(1)
  );

  return normalizedMarkdown;
};

/**
 * Normalize empty HTML block from clipboard.
 * @param content Content to normalize
 * @returns Normalized content
 */
export const normalizeEmptyHTMLBlockFromClipboard = (content: string): string => {
  // Define the exact HTML structure you want to replace
  // Replace it with a empty content
  return content.replace(
    STRING_UTIL_REGEX_PATTERNS.EMPTY_HTML_BLOCK,
    '<div class="bn-block-content" data-content-type="paragraph"><p class="bn-inline-content">&nbsp;</p></div>'
  );
};

/**
 * Checks if content is markdown
 * @param content Content to check
 * @returns Boolean indicating if content is markdown
 */
export const isMarkdownContent = (content: string): boolean => {
  return STRING_UTIL_REGEX_PATTERNS.MARKDOWN_SYNTAX.test(content);
};

/**
 * Checks if markdown is complex
 * @param content Markdown content to check
 * @returns Boolean indicating if markdown is complex
 */
export const isComplexMarkdown = (content: string): boolean => {
  return STRING_UTIL_REGEX_PATTERNS.COMPLEX_MARKDOWN.test(content);
};
