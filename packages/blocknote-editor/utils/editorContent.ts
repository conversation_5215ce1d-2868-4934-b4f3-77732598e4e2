import type { BlockNoteEditor } from '@blocknote/core';
import {
  convertEmptyParagraphToSpaceContent,
  convertWhiteSpaceToNbspInBlockNote,
  normalizeBlockNoteHTML,
  parseSpaceContentToEmptyParagraph,
} from './content';
import {
  convertBreakLineToHTML,
  finalizeMarkdownFromBlockNote,
  normalizeMarkdownToBlockNote,
  replaceWhiteSpaceToHTMLNbsp,
} from './string';
import { convertHTMLToText } from './string';

/**
 * Load HTML content into the editor
 * @param editor BlockNote editor instance
 * @param htmlContent HTML content to load
 * @param isMarkdown Whether the content is in Markdown format
 * @returns Promise<void>
 */
export const loadEditorContent = async (
  editor: BlockNoteEditor,
  content: string,
  isMarkdown = false
): Promise<any[]> => {
  let blocks: any[];

  if (isMarkdown) {
    const normalizedMarkdown = normalizeMarkdownToBlockNote(content);
    blocks = await editor.tryParseMarkdownToBlocks(normalizedMarkdown);
  } else {
    const processedHTML = replaceWhiteSpaceToHTMLNbsp(convertBreakLineToHTML(content));
    blocks = await editor.tryParseHTMLToBlocks(processedHTML);
  }

  return blocks;
};

/**
 * Process blocks for Markdown editor (handles empty paragraphs)
 * @param blocks Editor blocks
 * @returns Processed blocks
 */
export const processBlocksForMarkdown = (blocks: any[]): any[] => {
  return parseSpaceContentToEmptyParagraph(blocks);
};

/**
 * Serialize editor content to Markdown
 * @param editor BlockNote editor instance
 * @returns Promise<string> The markdown content
 */
export const serializeToMarkdown = async (editor: BlockNoteEditor): Promise<string> => {
  // Convert empty paragraphs to space content for markdown
  const processedBlocks = convertEmptyParagraphToSpaceContent(editor.document);

  // Convert to markdown
  const markdown = await editor.blocksToMarkdownLossy(processedBlocks);

  // Finalize markdown
  return finalizeMarkdownFromBlockNote(markdown);
};

/**
 * Serialize editor content to HTML
 * @param editor BlockNote editor instance
 * @returns Promise<{html: string, plainText: string}> The HTML content and plain text
 */
export const serializeToHTML = async (
  editor: BlockNoteEditor
): Promise<{ html: string; plainText: string }> => {
  const html = await editor.blocksToFullHTML(convertWhiteSpaceToNbspInBlockNote(editor.document));
  const normalizedHTML = normalizeBlockNoteHTML(html);
  const plainText = convertHTMLToText(html);

  return { html: normalizedHTML, plainText };
};
