import { describe, expect, it, vi } from 'vitest';
import { STRING_UTIL_REGEX_PATTERNS } from '../constants/regex';
import {
  cleanBadMarkdownContent,
  converMarkdownTextToHTMLBreakline,
  convertBreakLineToHTML,
  convertHTMLToText,
  decodeNbsp,
  decodeSpec<PERSON><PERSON>haracter<PERSON>,
  detectMarkdownLinkWithVariable,
  encodeMarkdownContent,
  finalizeMarkdownFromBlockNote,
  hasMultipleNewLines,
  isComplexMarkdown,
  isEmptyMarkdownContent,
  isIncludedBlockNoteMediaHTML,
  isIncludedMediaHTML,
  isMarkdownContent,
  normalizeEmptyHTMLBlockFromClipboard,
  normalizeLineBreaks,
  normalizeMarkdownForReactMarkdown,
  normalizeMarkdownForTextMessage,
  normalizeMarkdownToBlockNote,
  normalizeMarkdownWhenCopyingFromEditor,
  normalizeMarkdownWhenPastingToEditor,
  normalizeTextMessage,
  replaceNbspBreaksWithBreaks,
  replaceNbspWithEmpty,
  replacePhoneLinks,
  replaceUrlsWithAnchorTags,
  replaceWhiteSpaceToHTMLNbsp,
} from './string';

// Mock the external dependency
vi.mock('@blocknote/core', () => ({
  cleanHTMLToMarkdown: (html: string) => html.replace(/<[^>]{0,10000}>/g, ''),
}));

// Constants for test cases
const HTML_WITH_TAGS = '<div>Hello <strong>World</strong></div>';
const HTML_WITH_NBSP = 'Hello&nbsp;World';
const HTML_WITH_MEDIA = '<div>Test <img src="image.jpg" alt="Image"></div>';
const MARKDOWN_WITH_LINK = '[Link Text](https://example.com)';
const TEXT_WITH_URL = 'Visit https://example.com for more info';
const MULTI_LINE_TEXT = 'Line 1\nLine 2\nLine 3';
const COMPLEX_MARKDOWN = '# Heading\n\n> Blockquote\n\n- List item';

describe('STRING_UTIL_REGEX_PATTERNS', () => {
  it('should have HTML_TAGS pattern that matches HTML tags', () => {
    expect(STRING_UTIL_REGEX_PATTERNS.HTML_TAGS.test('<div>')).toBe(true);
    expect(STRING_UTIL_REGEX_PATTERNS.HTML_TAGS.test('<strong>text</strong>')).toBe(true);
    expect(STRING_UTIL_REGEX_PATTERNS.HTML_TAGS.test('plain text')).toBe(false);
  });

  it('should have URL pattern that matches URLs', () => {
    // Create new RegExp instances without the 'g' flag to avoid stateful behavior
    const urlPattern = new RegExp(STRING_UTIL_REGEX_PATTERNS.URL.source);

    expect(urlPattern.test('https://example.com')).toBe(true);
    expect(urlPattern.test('www.example.com')).toBe(true);
    expect(urlPattern.test('plain text')).toBe(false);
  });

  it('should have EMAIL pattern that matches email addresses', () => {
    expect(STRING_UTIL_REGEX_PATTERNS.EMAIL.test('<EMAIL>')).toBe(true);
    expect(STRING_UTIL_REGEX_PATTERNS.EMAIL.test('plain text')).toBe(false);
  });
});

describe('HTML to Text Conversion Functions', () => {
  describe('convertHTMLToText', () => {
    it('should remove HTML tags', () => {
      expect(convertHTMLToText(HTML_WITH_TAGS)).toBe('Hello World');
    });

    it('should return empty string for null/undefined HTML', () => {
      expect(convertHTMLToText('')).toBe('');
      expect(convertHTMLToText(undefined as any)).toBe('');
    });

    it('should remove &nbsp; characters', () => {
      expect(convertHTMLToText('Hello&nbsp;World')).toBe('HelloWorld');
    });
  });

  describe('convertBreakLineToHTML', () => {
    it('should convert newlines to <br /> tags', () => {
      expect(convertBreakLineToHTML('Line 1\nLine 2')).toBe('Line 1<br />Line 2');
    });

    it('should handle Windows line breaks', () => {
      expect(convertBreakLineToHTML('Line 1\r\nLine 2')).toBe('Line 1<br />Line 2');
    });

    it('should handle empty strings', () => {
      expect(convertBreakLineToHTML('')).toBe('');
    });
  });

  describe('converMarkdownTextToHTMLBreakline', () => {
    it('should convert newlines to <br /> tags', () => {
      expect(converMarkdownTextToHTMLBreakline('Line 1\nLine 2')).toBe('Line 1<br />Line 2');
    });

    it('should convert escaped newlines', () => {
      expect(converMarkdownTextToHTMLBreakline('Line 1\\nLine 2')).toBe('Line 1<br />Line 2');
    });
  });
});

describe('HTML Content Detection Functions', () => {
  describe('isIncludedMediaHTML', () => {
    it('should detect img tags', () => {
      expect(isIncludedMediaHTML('<div><img src="test.jpg"></div>')).toBe(true);
    });

    it('should detect video tags', () => {
      expect(isIncludedMediaHTML('<video src="test.mp4"></video>')).toBe(true);
    });

    it('should detect audio tags', () => {
      expect(isIncludedMediaHTML('<audio src="test.mp3"></audio>')).toBe(true);
    });

    it('should return false for HTML without media', () => {
      expect(isIncludedMediaHTML('<div>No media here</div>')).toBe(false);
    });
  });

  describe('isIncludedBlockNoteMediaHTML', () => {
    it('should detect standard media tags', () => {
      expect(isIncludedBlockNoteMediaHTML('<div><img src="test.jpg"></div>')).toBe(true);
    });

    it('should detect data-content-type attributes', () => {
      expect(isIncludedBlockNoteMediaHTML('<div data-content-type="image">Image</div>')).toBe(true);
      expect(isIncludedBlockNoteMediaHTML('<div data-content-type="video">Video</div>')).toBe(true);
      expect(isIncludedBlockNoteMediaHTML('<div data-content-type="audio">Audio</div>')).toBe(true);
      expect(isIncludedBlockNoteMediaHTML('<div data-content-type="file">File</div>')).toBe(true);
    });

    it('should return false for HTML without media', () => {
      expect(isIncludedBlockNoteMediaHTML('<div>No media here</div>')).toBe(false);
    });
  });
});

describe('URL Processing Functions', () => {
  describe('replaceUrlsWithAnchorTags', () => {
    it('should replace http URLs with anchor tags', () => {
      const result = replaceUrlsWithAnchorTags('Visit http://example.com');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="http://example.com">http://example.com</a>'
      );
    });

    it('should replace https URLs with anchor tags', () => {
      const result = replaceUrlsWithAnchorTags('Visit https://example.com');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://example.com">https://example.com</a>'
      );
    });

    it('should replace www URLs with anchor tags and add https://', () => {
      const result = replaceUrlsWithAnchorTags('Visit www.example.com');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://www.example.com">www.example.com</a>'
      );
    });

    it('should handle URLs with trailing punctuation', () => {
      const result = replaceUrlsWithAnchorTags('Visit https://example.com.');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://example.com">https://example.com</a>.'
      );
    });

    it('should handle email addresses', () => {
      const result = replaceUrlsWithAnchorTags('Contact <EMAIL>');
      expect(result).toBe('Contact <a href="mailto:<EMAIL>"><EMAIL></a>');
    });

    it('should not modify markdown links', () => {
      const link = '[Example](https://example.com)';
      expect(replaceUrlsWithAnchorTags(link)).toBe(link);
    });

    it('should handle empty input', () => {
      expect(replaceUrlsWithAnchorTags('')).toBe('');
      expect(replaceUrlsWithAnchorTags(null as any)).toBe(null);
    });

    it('should preserve existing anchor tags', () => {
      const html = 'Check <a href="https://example.com">this link</a> and https://another.com';
      const result = replaceUrlsWithAnchorTags(html);
      expect(result).toBe(
        'Check <a href="https://example.com">this link</a> and <a target="_blank" rel="noreferrer" href="https://another.com">https://another.com</a>'
      );
    });

    it('should handle URLs with Japanese text boundary', () => {
      const result = replaceUrlsWithAnchorTags('Visit https://example.com日本語');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://example.com">https://example.com</a>日本語'
      );
    });

    it('should handle URLs with Chinese text boundary', () => {
      const result = replaceUrlsWithAnchorTags('Visit https://example.com中文');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://example.com">https://example.com</a>中文'
      );
    });

    it('should handle www URLs with Asian text boundary', () => {
      const result = replaceUrlsWithAnchorTags('Visit www.example.com日本語');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://www.example.com">www.example.com</a>日本語'
      );
    });
  });
});

describe('Character and Entity Processing Functions', () => {
  describe('decodeSpecialCharacters', () => {
    it('should decode HTML entities', () => {
      expect(decodeSpecialCharacters('&lt;div&gt;')).toBe('<div>');
      expect(decodeSpecialCharacters('a &amp; b')).toBe('a & b');
      expect(decodeSpecialCharacters('a &#x3D; b')).toBe('a = b');
    });

    it('should decode URL encoded characters', () => {
      expect(decodeSpecialCharacters('%7Btest%7D')).toBe('{test}');
      expect(decodeSpecialCharacters('test%2Ecom')).toBe('test.com');
      expect(decodeSpecialCharacters('25%25')).toBe('25%');
    });

    it('should convert HTML links to markdown links', () => {
      expect(decodeSpecialCharacters('<a href="https://example.com">Example</a>')).toBe(
        '[Example](https://example.com)'
      );
    });

    it('should return empty string for null or undefined input', () => {
      // @ts-expect-error Testing runtime behaviour with null
      expect(decodeSpecialCharacters(null)).toBe('');
      // @ts-expect-error Testing runtime behaviour with undefined
      expect(decodeSpecialCharacters(undefined)).toBe('');
    });
  });

  describe('detectMarkdownLinkWithVariable', () => {
    it('should handle markdown links with variables', () => {
      const input = 'Check [{{variable_text}}]({{variable_link}})';
      const result = detectMarkdownLinkWithVariable(input, (match) => `PROCESSED:${match}`);
      expect(result).toBe('Check PROCESSED:[{{variable_text}}]({{variable_link}})');
    });

    it('should handle phone links with variables', () => {
      const input = 'Call [Contact](tel: {{phone_number}})';
      const result = detectMarkdownLinkWithVariable(input, (match) => `PROCESSED:${match}`);
      expect(result).toBe('Call PROCESSED:[Contact](tel: {{phone_number}})');
    });

    it('should not modify text without markdown links', () => {
      const input = 'Plain text without links';
      const result = detectMarkdownLinkWithVariable(input, (match) => `PROCESSED:${match}`);
      expect(result).toBe(input);
    });
  });

  describe('replaceNbspWithEmpty', () => {
    it('should replace &nbsp; with empty string', () => {
      expect(replaceNbspWithEmpty('Hello&nbsp;World')).toBe('HelloWorld');
    });

    it('should replace &amp;nbsp; with empty string', () => {
      expect(replaceNbspWithEmpty('Hello&amp;nbsp;World')).toBe('HelloWorld');
    });
  });

  describe('decodeNbsp', () => {
    it('should convert &amp;nbsp; to &nbsp;', () => {
      expect(decodeNbsp('Hello&amp;nbsp;World')).toBe('Hello&nbsp;World');
    });
  });

  describe('cleanBadMarkdownContent', () => {
    it('should remove special whitespace characters', () => {
      expect(cleanBadMarkdownContent('Hello\u00A0World')).toBe('Hello World');
      expect(cleanBadMarkdownContent('Hello\u200BWorld')).toBe('Hello World');
    });

    it('should trim the result', () => {
      expect(cleanBadMarkdownContent('  Hello World  ')).toBe('Hello World');
    });
  });
});

describe('Markdown Normalization Functions', () => {
  describe('normalizeMarkdownToBlockNote', () => {
    it('should convert single newlines to double newlines', () => {
      expect(normalizeMarkdownToBlockNote('Line 1\nLine 2')).toBe('Line 1\n\nLine 2');
    });

    it('should decode special characters', () => {
      expect(normalizeMarkdownToBlockNote('&lt;div&gt;')).toBe('<div>');
    });

    it('should preserve styled variables', () => {
      const input = 'Text with {{value::variable}} styled variable';
      const result = normalizeMarkdownToBlockNote(input);
      expect(result).toContain('{{value::variable}}');
    });

    it('should preserve variable links', () => {
      const input = 'Text with [{{variable}}](variable://test) link';
      const result = normalizeMarkdownToBlockNote(input);
      expect(result).toContain('[{{variable}}](variable://test)');
    });

    it('should preserve pure variables when not part of links', () => {
      const input = 'Text with {{standalone_variable}} here';
      const result = normalizeMarkdownToBlockNote(input);
      expect(result).toContain('{{standalone_variable}}');
    });

    it('should not replace pure variables when they are part of links', () => {
      const input = 'Text with [{{variable}}] and ]({{variable}}) patterns';
      const result = normalizeMarkdownToBlockNote(input);
      expect(result).toContain('{{variable}}');
    });
  });

  describe('isEmptyMarkdownContent', () => {
    it('should return true for empty content', () => {
      expect(isEmptyMarkdownContent('')).toBe(true);
      expect(isEmptyMarkdownContent('  ')).toBe(true);
      expect(isEmptyMarkdownContent('&nbsp;')).toBe(true);
      expect(isEmptyMarkdownContent('\n&nbsp;\n')).toBe(true);
    });

    it('should return false for non-empty content', () => {
      expect(isEmptyMarkdownContent('Hello')).toBe(false);
      expect(isEmptyMarkdownContent('&nbsp;Hello')).toBe(false);
    });
  });

  describe('finalizeMarkdownFromBlockNote', () => {
    it('should return empty string for empty content', () => {
      expect(finalizeMarkdownFromBlockNote('&nbsp;')).toBe('');
    });

    it('should encode non-empty content', () => {
      expect(finalizeMarkdownFromBlockNote('Hello\nWorld')).toBe('Hello\nWorld');
    });
  });

  describe('normalizeMarkdownForReactMarkdown', () => {
    it('should process markdown for React Markdown', () => {
      const result = normalizeMarkdownForReactMarkdown('Hello\n&nbsp;\nWorld');
      expect(result).toBe('Hello<br />\nWorld');
    });

    it('should replace phone links', () => {
      const result = normalizeMarkdownForReactMarkdown('[Call Me](tel:123456)');
      expect(result).toBe(
        "<phone href=\"tel:123456\" target='_blank' rel='noreferrer'>Call Me</phone>"
      );
    });

    it('should clean bad markdown content', () => {
      const result = normalizeMarkdownForReactMarkdown('Hello\u00A0World');
      expect(result).toBe('Hello World');
    });
  });

  describe('normalizeMarkdownForTextMessage', () => {
    it('should process markdown for text messages', () => {
      const result = normalizeMarkdownForTextMessage('Visit &lt;https://example.com&gt;');
      expect(result).toContain('Visit <');
      expect(result).toContain('https://example.com');
      expect(result).toContain('</a>');
    });

    it('should replace URLs with anchor tags', () => {
      const result = normalizeMarkdownForTextMessage('Visit https://example.com');
      expect(result).toContain('<a target="_blank" rel="noreferrer" href="https://example.com">');
    });

    it('should remove &nbsp; characters', () => {
      const result = normalizeMarkdownForTextMessage('Hello&nbsp;World');
      expect(result).toBe('HelloWorld');
    });
  });

  describe('normalizeTextMessage', () => {
    it('should replace URLs with anchor tags', () => {
      const result = normalizeTextMessage('Visit https://example.com');
      expect(result).toContain('<a target="_blank" rel="noreferrer" href="https://example.com">');
    });
  });
});

describe('Whitespace and Line Break Functions', () => {
  describe('replaceWhiteSpaceToHTMLNbsp', () => {
    it('should replace multiple spaces with &nbsp;', () => {
      expect(replaceWhiteSpaceToHTMLNbsp('Hello  World')).toBe('Hello&nbsp;&nbsp;World');
    });

    it('should not replace single spaces', () => {
      expect(replaceWhiteSpaceToHTMLNbsp('Hello World')).toBe('Hello World');
    });
  });

  describe('normalizeLineBreaks', () => {
    it('should normalize Windows line breaks', () => {
      expect(normalizeLineBreaks('Line 1\r\nLine 2')).toBe('Line 1\nLine 2');
    });

    it('should normalize classic Mac line breaks', () => {
      expect(normalizeLineBreaks('Line 1\rLine 2')).toBe('Line 1\nLine 2');
    });

    it('should normalize Unicode line separators', () => {
      expect(normalizeLineBreaks('Line 1\u2028Line 2')).toBe('Line 1\nLine 2');
      expect(normalizeLineBreaks('Line 1\u2029Line 2')).toBe('Line 1\nLine 2');
    });
  });

  describe('hasMultipleNewLines', () => {
    it('should detect multiple newlines', () => {
      expect(hasMultipleNewLines('Line 1\n\nLine 2')).toBe(true);
      expect(hasMultipleNewLines('Line 1\n \nLine 2')).toBe(true);
    });

    it('should normalize line breaks before checking', () => {
      expect(hasMultipleNewLines('Line 1\r\n\r\nLine 2')).toBe(true);
    });

    it('should return false for single newlines', () => {
      expect(hasMultipleNewLines('Line 1\nLine 2')).toBe(false);
    });
  });
});

describe('Advanced Markdown Processing Functions', () => {
  describe('normalizeMarkdownWhenPastingToEditor', () => {
    it('should process markdown links with variables', () => {
      const result = normalizeMarkdownWhenPastingToEditor('[Link]({{variable.with.dots}})');
      expect(result).toContain('[Link]({{variable.with.dots}})');
    });

    it('should handle multiple newlines', () => {
      const result = normalizeMarkdownWhenPastingToEditor('Line 1\n\nLine 2');
      expect(result).toContain('Line 1');
      expect(result).toContain('&nbsp;');
      expect(result).toContain('Line 2');
    });

    it('should not add &nbsp; to headings', () => {
      const result = normalizeMarkdownWhenPastingToEditor('\n\n# Heading');
      expect(result).not.toContain('&nbsp;# Heading');
      expect(result).toContain('# Heading');
    });

    it('should replace multiple spaces with &nbsp;', () => {
      const result = normalizeMarkdownWhenPastingToEditor('Text with  double  spaces');
      expect(result).toContain('Text with&nbsp;&nbsp;double&nbsp;&nbsp;spaces');
    });
  });

  describe('normalizeMarkdownWhenCopyingFromEditor', () => {
    it('should replace multiple spaces with &nbsp;', () => {
      const result = normalizeMarkdownWhenCopyingFromEditor('Text with  double  spaces');
      expect(result).toContain('Text with&nbsp;&nbsp;double&nbsp;&nbsp;spaces');
    });

    it('should clean HTML to markdown', () => {
      const result = normalizeMarkdownWhenCopyingFromEditor('<div>HTML content</div>');
      expect(result).toBe('HTML content');
    });

    it('should normalize empty HTML blocks', () => {
      const emptyBlock =
        '<div class="bn-block-content" data-content-type="paragraph"><p class="bn-inline-content"></p></div>';
      const result = normalizeMarkdownWhenCopyingFromEditor(emptyBlock);
      expect(result).not.toContain('<p class="bn-inline-content"></p>');
    });

    it('should reduce multiple newlines', () => {
      const result = normalizeMarkdownWhenCopyingFromEditor('Line 1\n\n\nLine 2');
      expect(result).toBe('Line 1\n\nLine 2');
    });
  });

  describe('normalizeEmptyHTMLBlockFromClipboard', () => {
    it('should replace empty paragraph blocks with &nbsp;', () => {
      const emptyBlock =
        '<div class="bn-block-content" data-content-type="paragraph"><p class="bn-inline-content"></p></div>';
      const expected =
        '<div class="bn-block-content" data-content-type="paragraph"><p class="bn-inline-content">&nbsp;</p></div>';
      expect(normalizeEmptyHTMLBlockFromClipboard(emptyBlock)).toBe(expected);
    });

    it('should not modify non-empty blocks', () => {
      const nonEmptyBlock =
        '<div class="bn-block-content" data-content-type="paragraph"><p class="bn-inline-content">Content</p></div>';
      expect(normalizeEmptyHTMLBlockFromClipboard(nonEmptyBlock)).toBe(nonEmptyBlock);
    });
  });
});

describe('Markdown Detection Functions', () => {
  describe('isMarkdownContent', () => {
    it('should detect basic markdown syntax', () => {
      expect(isMarkdownContent('**Bold text**')).toBe(true);
      expect(isMarkdownContent('*Italic text*')).toBe(true);
      expect(isMarkdownContent('# Heading')).toBe(true);
      expect(isMarkdownContent('[Link](url)')).toBe(true);
      expect(isMarkdownContent('`code`')).toBe(true);
      expect(isMarkdownContent('> Blockquote')).toBe(true);
    });

    it('should return false for plain text', () => {
      expect(isMarkdownContent('Plain text without markdown')).toBe(false);
    });
  });

  describe('isComplexMarkdown', () => {
    it('should detect complex markdown structures', () => {
      expect(isComplexMarkdown('Paragraph\n\nAnother paragraph')).toBe(true);
      expect(isComplexMarkdown('Text\n# Heading')).toBe(true);
      expect(isComplexMarkdown('Text\n> Blockquote')).toBe(true);
      expect(isComplexMarkdown('Text\n- List item')).toBe(true);
      expect(isComplexMarkdown('Text\n1. Numbered item')).toBe(true);
      expect(isComplexMarkdown('Text\n* List item')).toBe(true);
      expect(isComplexMarkdown('Text\n```\nCode block\n```')).toBe(true);
    });

    it('should return false for simple markdown or plain text', () => {
      expect(isComplexMarkdown('**Bold** and *italic*')).toBe(false);
      expect(isComplexMarkdown('Plain text with a [link](url)')).toBe(false);
      expect(isComplexMarkdown('Text with `inline code`')).toBe(false);
    });
  });
});

describe('URL Processing Helper Functions', () => {
  describe('getHrefFromUrl', () => {
    it('should add https:// to www URLs', () => {
      // We can't directly test getHrefFromUrl as it's a private function,
      // but we can test its behavior through replaceUrlsWithAnchorTags
      const result = replaceUrlsWithAnchorTags('Visit www.example.com');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://www.example.com">www.example.com</a>'
      );
    });

    it('should not modify URLs with protocols', () => {
      const result = replaceUrlsWithAnchorTags('Visit https://example.com');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://example.com">https://example.com</a>'
      );
    });
  });

  describe('hasTrailingPunctuation', () => {
    it('should handle URLs with trailing periods', () => {
      const result = replaceUrlsWithAnchorTags('Visit https://example.com.');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://example.com">https://example.com</a>.'
      );
    });

    it('should handle URLs with trailing commas', () => {
      const result = replaceUrlsWithAnchorTags('Visit https://example.com, then continue.');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://example.com">https://example.com</a>, then continue.'
      );
    });

    it('should not consider URL path separators as trailing punctuation', () => {
      const result = replaceUrlsWithAnchorTags('Visit https://example.com/path/');
      expect(result).toBe(
        'Visit <a target="_blank" rel="noreferrer" href="https://example.com/path/">https://example.com/path/</a>'
      );
    });
  });
});

describe('Additional Character and Entity Processing Functions', () => {
  describe('encodeMarkdownContent', () => {
    it('should trim leading and trailing whitespace', () => {
      expect(encodeMarkdownContent('  Hello World  ')).toBe('Hello World');
    });

    it('should decode URL encoded characters', () => {
      expect(encodeMarkdownContent('Hello%20World')).toBe('Hello%20World');
    });

    it('should replace multiple newlines with a single newline', () => {
      expect(encodeMarkdownContent('Line 1\n\nLine 2')).toBe('Line 1\nLine 2');
    });

    it('should process markdown links with variables', () => {
      const result = encodeMarkdownContent('[Link]({{variable}})');
      expect(result).toBe('[Link]({{variable}})');
    });
  });

  describe('replacePhoneLinks', () => {
    it('should replace simple phone links', () => {
      const result = replacePhoneLinks('[Call Me](tel:123456)');
      expect(result).toBe(
        "<phone href=\"tel:123456\" target='_blank' rel='noreferrer'>Call Me</phone>"
      );
    });

    it('should handle multiple phone links', () => {
      const result = replacePhoneLinks('[Support](tel:123) and [Sales](tel:456)');
      expect(result).toBe(
        "<phone href=\"tel:123\" target='_blank' rel='noreferrer'>Support</phone> and <phone href=\"tel:456\" target='_blank' rel='noreferrer'>Sales</phone>"
      );
    });

    it('should not modify text without phone links', () => {
      const text = 'No phone links here';
      expect(replacePhoneLinks(text)).toBe(text);
    });
  });

  describe('replaceNbspBreaksWithBreaks', () => {
    it('should replace &nbsp; with <br />', () => {
      expect(replaceNbspBreaksWithBreaks('Line 1\n&nbsp;Line 2')).toBe('Line 1<br />Line 2');
    });

    it('should replace &amp;nbsp; with <br />', () => {
      expect(replaceNbspBreaksWithBreaks('Line 1\n&amp;nbsp;Line 2')).toBe('Line 1<br />Line 2');
    });

    it('should handle &nbsp; between newlines', () => {
      expect(replaceNbspBreaksWithBreaks('Line 1\n&nbsp;\nLine 2')).toBe('Line 1<br />\nLine 2');
    });

    it('should handle &amp;nbsp; between newlines', () => {
      expect(replaceNbspBreaksWithBreaks('Line 1\n&amp;nbsp;\nLine 2')).toBe(
        'Line 1<br />\nLine 2'
      );
    });
  });
});

describe('Additional Advanced Markdown Functions', () => {
  describe('normalizeMarkdownWhenPastingToEditor additional cases', () => {
    it('should process markdown links with variables', () => {
      const result = normalizeMarkdownWhenPastingToEditor('[Link]({{variable.with.dots}})');
      expect(result).toContain('[Link]({{variable.with.dots}})');
    });

    it('should handle complex markdown with multiple features', () => {
      const complexMarkdown = '# Heading\n\n**Bold** and *italic*\n\n- List item\n- Another item';
      const result = normalizeMarkdownWhenPastingToEditor(complexMarkdown);
      expect(result).toContain('# Heading');
      expect(result).toContain('**Bold** and *italic*');
      expect(result).toContain('- List item');
      expect(result).toContain('- Another item');
    });
  });

  describe('normalizeMarkdownWhenCopyingFromEditor additional cases', () => {
    it('should handle complex HTML structures', () => {
      const html = '<div><h1>Title</h1><p>Paragraph <strong>with bold</strong></p></div>';
      const result = normalizeMarkdownWhenCopyingFromEditor(html);
      expect(result).toBe('TitleParagraph with bold');
    });

    it('should handle multiple consecutive newlines', () => {
      const result = normalizeMarkdownWhenCopyingFromEditor('Line 1\n\n\n\nLine 2');
      expect(result).toBe('Line 1\n\n\nLine 2');
    });
  });
});
