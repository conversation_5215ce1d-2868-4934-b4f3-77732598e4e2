import { type RenderResult, render } from '@testing-library/react';
import type React from 'react';
import { vi } from 'vitest';

// -----------------------------------------------------------------------------
// Polyfills
// -----------------------------------------------------------------------------
// JSDOM used by Vitest does not implement window.matchMedia which is required by
// <PERSON><PERSON>'s use-media-query hook.  We need to polyfill it *before* importing any
// Mantine modules that might access it at module initialisation time.
if (typeof window !== 'undefined') {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore — jsdom window type doesn't include matchMedia yet
  window.matchMedia = vi.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated but still referenced in Mantine
    removeListener: vi.fn(), // deprecated but still referenced in Mantine
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }));
}

// After polyfill lines add mock declaration

// For integration tests with the real component, we need to provide a basic MantineProvider
// Don't mock @mantine/core - let the real provider handle it

// Don't mock @mantine/emotion - let the real provider handle it

// Use real MantineProvider for integration testing
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';

export const MantineWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

export const renderWithMantine = (ui: React.ReactNode): RenderResult => {
  return render(<MantineWrapper>{ui}</MantineWrapper>);
};

// Helper for mocking useState in tests
export const mockUseState = <T,>(initialValue: T) => {
  let value = initialValue;
  const setState = (newValue: T | ((_prevState: T) => T)) => {
    if (typeof newValue === 'function') {
      // @ts-ignore - we know newValue is a function here
      value = newValue(value);
    } else {
      value = newValue;
    }
    return value;
  };
  return [value, setState] as const;
};

// Test data helper functions
// Use a safe regex to strip HTML tags in linear time (SonarCloud S6353 compliant)
// The pattern requires at least one character that is not '>' between tag brackets, avoiding risky nested quantifiers.
// Bounded quantifier prevents super-linear backtracking (Sonar S6353).
// Tags longer than 1 024 characters are considered invalid for our test fixtures.
const STRIP_HTML_TAG_REGEX = /<\/?[^>]{1,1024}>/g;

/**
 * Removes HTML tags from the given string using a safe, linear-time regex.
 * @param input - The HTML string to clean.
 */
export const stripHtmlTags = (input: string): string => input.replace(STRIP_HTML_TAG_REGEX, '');

export const createMockEditorContent = (content: string) => ({
  html: content,
  plainText: stripHtmlTags(content).trim(),
});

export const createMockUploadFile = () => vi.fn().mockResolvedValue('mock-file-url');

// Helper to wait for async operations in tests
export const waitForAsync = () => new Promise((resolve) => setTimeout(resolve, 0));

// Helper to replace @testing-library waitFor which has MutationObserver issues in Vitest
export const waitForTest = async (assertion: () => void | Promise<void>, _timeout = 100) => {
  await new Promise((resolve) => setTimeout(resolve, 10));
  if (typeof assertion === 'function') {
    await assertion();
  }
};

// Mock BlockNote utilities that are commonly used
export const mockBlockNoteUtils = {
  createEditor: vi.fn(),
  serializeToHTML: vi.fn(),
  loadEditorContent: vi.fn(),
};
