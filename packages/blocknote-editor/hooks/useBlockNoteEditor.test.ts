import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock external dependencies before any imports
vi.mock('@blocknote/react', () => ({
  useCreateBlockNote: vi.fn(),
}));

vi.mock('../components/BlockNoteLocales', () => ({
  default: {
    en: { test: 'english' },
    es: { test: 'spanish' },
  },
}));

vi.mock('../components/BlockNoteExtensions', () => ({
  default: {
    extension: 'test',
  },
}));

vi.mock('../utils', () => ({
  validateUploadFile: vi.fn(),
}));

// Mock React hooks to avoid DOM dependencies
let mockState: any = {};
let mockRefs: any = {};
let mockEffects: Array<{ effect: () => undefined | (() => void); deps?: any[] }> = [];
let mockEditorRef: any;
let mockRafRef: any;

vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useState: vi.fn((initial) => {
      const key = Math.random().toString();
      mockState[key] = initial;
      return [
        mockState[key],
        (newValue: any) => {
          mockState[key] = typeof newValue === 'function' ? newValue(mockState[key]) : newValue;
        },
      ];
    }),
    useRef: vi.fn((initial) => {
      // Return specific refs based on the initial value
      if (initial === null) {
        return mockEditorRef; // First useRef call (editorRef)
      }
      if (initial === undefined) {
        return mockRafRef; // Second useRef call (rafRef)
      }
      // For other cases, return a generic ref
      const key = Math.random().toString();
      mockRefs[key] = { current: initial };
      return mockRefs[key];
    }),
    useEffect: vi.fn((effect, deps) => {
      mockEffects.push({ effect, deps });
      // Immediately call effect for testing
      const cleanup = effect();
      return cleanup;
    }),
    useMemo: vi.fn((fn, deps) => fn()),
    useCallback: vi.fn((fn, deps) => fn),
  };
});

import { useCreateBlockNote } from '@blocknote/react';
// Import React mocks and the modules we need to test
import React from 'react';
import { validateUploadFile } from '../utils';
import {
  type TipTapEditor,
  type UseBlockNoteEditorProps,
  useBlockNoteEditor,
} from './useBlockNoteEditor';

// Type the mocked functions
const mockUseState = React.useState as any;
const mockUseRef = React.useRef as any;
const mockUseEffect = React.useEffect as any;

describe('useBlockNoteEditor', () => {
  let mockEditor: any;
  let mockTipTapEditor: any;

  beforeEach(() => {
    // Reset all state and refs
    mockState = {};
    mockRefs = {};
    mockEffects = [];

    // Create fresh mock refs for each test
    mockEditorRef = { current: { contains: vi.fn() } };
    mockRafRef = { current: undefined };

    // Create fresh mocks for each test
    mockTipTapEditor = {
      isFocused: false,
      on: vi.fn(),
      off: vi.fn(),
      commands: {
        insertContent: vi.fn(),
        focus: vi.fn(),
      },
    } as unknown as TipTapEditor;

    mockEditor = {
      _tiptapEditor: mockTipTapEditor,
      focus: vi.fn(),
    };

    // Ensure window is undefined for server-side rendering simulation
    // This makes handleAutoFocus call editor.focus() directly
    (global as any).window = undefined;

    // Reset all mocks and set up default behaviors
    vi.clearAllMocks();
    (useCreateBlockNote as any).mockReturnValue(mockEditor);
    (validateUploadFile as any).mockReturnValue(true);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Hook Initialization', () => {
    it('should initialize with default values', () => {
      const result = useBlockNoteEditor({});

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          schema: undefined,
          dictionary: { test: 'english' },
          extension: 'test',
        })
      );

      expect(result.editor).toBe(mockEditor);
      expect(result.editorRef).toBeDefined();
      expect(result.handleAutoFocus).toBeInstanceOf(Function);
    });

    it('should initialize with custom schema and language', () => {
      const mockSchema = { custom: 'schema' };
      const props: UseBlockNoteEditorProps = {
        schema: mockSchema,
        language: 'es',
      };

      useBlockNoteEditor(props);

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          schema: mockSchema,
          dictionary: { test: 'spanish' },
          extension: 'test',
        })
      );
    });

    it('should initialize with upload file functionality', () => {
      const mockUploadFile = vi.fn();
      const props: UseBlockNoteEditorProps = {
        uploadFile: mockUploadFile,
      };

      useBlockNoteEditor(props);

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          uploadFile: expect.any(Function),
        })
      );
    });

    it('should not include uploadFile when not provided', () => {
      useBlockNoteEditor({});

      const callArgs = (useCreateBlockNote as any).mock.calls[0][0];
      expect(callArgs).not.toHaveProperty('uploadFile');
    });
  });

  describe('Auto Focus Functionality', () => {
    it('should auto focus when autoFocus is true', () => {
      // Ensure window is undefined for our test environment
      global.window = undefined as any;

      const props: UseBlockNoteEditorProps = {
        autoFocus: true,
      };

      useBlockNoteEditor(props);

      // Check that effects were registered (includes auto focus effect)
      expect(mockEffects.length).toBeGreaterThan(0);

      // The focus should be called since autoFocus is true and editorRef.current exists
      expect(mockEditor.focus).toHaveBeenCalled();
    });

    it('should not auto focus when autoFocus is false', () => {
      const props: UseBlockNoteEditorProps = {
        autoFocus: false,
      };

      useBlockNoteEditor(props);

      expect(mockEditor.focus).not.toHaveBeenCalled();
    });

    it('should focus when handleAutoFocus is called manually', () => {
      // Ensure window is undefined for our test environment
      global.window = undefined as any;

      const result = useBlockNoteEditor({});

      // Clear previous calls
      mockEditor.focus.mockClear();

      result.handleAutoFocus();

      expect(mockEditor.focus).toHaveBeenCalled();
    });

    it('should not focus when editor is already focused', () => {
      mockTipTapEditor.isFocused = true;

      const result = useBlockNoteEditor({});

      // Clear any calls from initialization
      mockEditor.focus.mockClear();

      result.handleAutoFocus();

      expect(mockEditor.focus).not.toHaveBeenCalled();
    });
  });

  describe('Event Handling', () => {
    it('should bind blur event when onBlur is provided', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      useBlockNoteEditor(props);

      expect(mockTipTapEditor.on).toHaveBeenCalledWith('blur', expect.any(Function));
    });

    it('should not bind blur event when onBlur is not provided', () => {
      useBlockNoteEditor({});

      const onCalls = (mockTipTapEditor.on as any).mock.calls;
      const blurCall = onCalls.find((call: any) => call[0] === 'blur');
      expect(blurCall).toBeUndefined();
    });

    it('should bind focus event when onFocus is provided', () => {
      const mockOnFocus = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onFocus: mockOnFocus,
      };

      useBlockNoteEditor(props);

      expect(mockTipTapEditor.on).toHaveBeenCalledWith('focus', expect.any(Function));
    });

    it('should not bind focus event when onFocus is not provided', () => {
      useBlockNoteEditor({});

      const onCalls = (mockTipTapEditor.on as any).mock.calls;
      const focusCall = onCalls.find((call: any) => call[0] === 'focus');
      expect(focusCall).toBeUndefined();
    });

    it('should call onBlur when focus moves outside editor', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      const result = useBlockNoteEditor(props);

      // Mock the editor ref with a contains method
      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(false),
      };
      result.editorRef.current = mockEditorElement as any;

      // Get the blur handler that was registered
      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      expect(blurHandler).toBeDefined();

      // Simulate a blur event
      const mockEvent = {
        relatedTarget: { tagName: 'DIV' }, // Mock target outside editor
      };

      blurHandler({ event: mockEvent });

      expect(mockOnBlur).toHaveBeenCalled();
    });

    it('should not call onBlur when focus stays within editor (non-checkbox)', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      const result = useBlockNoteEditor(props);

      // Mock the editor ref with a contains method that returns true
      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(true),
      };
      result.editorRef.current = mockEditorElement as any;

      // Get the blur handler
      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      // Simulate a blur event where focus stays within editor (non-checkbox element)
      const mockEvent = {
        relatedTarget: {
          tagName: 'DIV',
          closest: vi.fn().mockReturnValue(null),
        },
      };

      blurHandler({ event: mockEvent });

      expect(mockOnBlur).not.toHaveBeenCalled();
      expect(mockEditor.focus).not.toHaveBeenCalled(); // Should not auto-focus for non-checkbox
    });

    it('should auto-focus when focus stays within editor and target is a checkbox', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      // Ensure editor is not focused so handleAutoFocus will work
      mockTipTapEditor.isFocused = false;

      const result = useBlockNoteEditor(props);

      // Mock the editor ref with a contains method that returns true
      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(true),
      };
      result.editorRef.current = mockEditorElement as any;

      // Clear any previous focus calls
      mockEditor.focus.mockClear();

      // Get the blur handler
      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      // Simulate a blur event where focus stays within editor and target is a checkbox
      const mockCheckboxElement = {
        tagName: 'INPUT',
        type: 'checkbox',
        closest: vi.fn().mockReturnValue(null),
      };

      // Mock the instanceof Element check
      Object.setPrototypeOf(mockCheckboxElement, Element.prototype);

      const mockEvent = {
        relatedTarget: mockCheckboxElement,
      };

      blurHandler({ event: mockEvent });

      expect(mockOnBlur).not.toHaveBeenCalled();
      expect(mockEditor.focus).toHaveBeenCalled(); // Should auto-focus for checkbox
    });

    it('should auto-focus when focus stays within editor and target is within a checklist item', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      // Ensure editor is not focused so handleAutoFocus will work
      mockTipTapEditor.isFocused = false;

      const result = useBlockNoteEditor(props);

      // Mock the editor ref with a contains method that returns true
      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(true),
      };
      result.editorRef.current = mockEditorElement as any;

      // Clear any previous focus calls
      mockEditor.focus.mockClear();

      // Get the blur handler
      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      // Mock checklist container element
      const mockChecklistContainer = {
        querySelector: vi.fn().mockReturnValue({ type: 'checkbox' }), // Contains a checkbox
      };

      // Simulate a blur event where target is within a checklist item
      const mockParagraphElement = {
        tagName: 'P',
        closest: vi.fn().mockImplementation((selector) => {
          if (selector === '[data-content-type="checkListItem"]') {
            return mockChecklistContainer;
          }
          return null;
        }),
      };

      // Mock the instanceof Element check
      Object.setPrototypeOf(mockParagraphElement, Element.prototype);

      const mockEvent = {
        relatedTarget: mockParagraphElement,
      };

      blurHandler({ event: mockEvent });

      expect(mockOnBlur).not.toHaveBeenCalled();
      expect(mockEditor.focus).toHaveBeenCalled(); // Should auto-focus for checklist item
    });

    it('should not auto-focus when target is within a checklist item but no checkbox is found', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      const result = useBlockNoteEditor(props);

      // Mock the editor ref with a contains method that returns true
      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(true),
      };
      result.editorRef.current = mockEditorElement as any;

      // Clear any previous focus calls
      mockEditor.focus.mockClear();

      // Get the blur handler
      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      // Mock checklist container element without checkbox
      const mockChecklistContainer = {
        querySelector: vi.fn().mockReturnValue(null), // No checkbox found
      };

      // Simulate a blur event where target is within a checklist item but no checkbox
      const mockParagraphElement = {
        tagName: 'P',
        closest: vi.fn().mockImplementation((selector) => {
          if (selector === '[data-content-type="checkListItem"]') {
            return mockChecklistContainer;
          }
          return null;
        }),
      };

      // Mock the instanceof Element check
      Object.setPrototypeOf(mockParagraphElement, Element.prototype);

      const mockEvent = {
        relatedTarget: mockParagraphElement,
      };

      blurHandler({ event: mockEvent });

      expect(mockOnBlur).not.toHaveBeenCalled();
      expect(mockEditor.focus).not.toHaveBeenCalled(); // Should not auto-focus when no checkbox found
    });

    it('should handle blur event when editorRef is not available', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      const result = useBlockNoteEditor(props);

      // Leave editorRef.current as null
      result.editorRef.current = null;

      // Get the blur handler
      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      const mockEvent = {
        relatedTarget: { tagName: 'DIV' },
      };

      blurHandler({ event: mockEvent });

      // Should not call onBlur when editor ref is not available
      expect(mockOnBlur).not.toHaveBeenCalled();
    });

    it('should call onFocus when focus event occurs', () => {
      const mockOnFocus = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onFocus: mockOnFocus,
      };

      useBlockNoteEditor(props);

      // Get the focus handler
      const focusHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'focus'
      )?.[1];

      expect(focusHandler).toBeDefined();

      focusHandler();

      expect(mockOnFocus).toHaveBeenCalled();
    });
  });

  describe('File Upload', () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    it('should handle valid file upload', async () => {
      (validateUploadFile as any).mockReturnValue(true);
      const mockUploadFile = vi.fn().mockResolvedValue('https://example.com/file.jpg');

      const props: UseBlockNoteEditorProps = {
        uploadFile: mockUploadFile,
      };

      useBlockNoteEditor(props);

      const createBlockNoteCall = (useCreateBlockNote as any).mock.calls[0][0];
      const uploadFileFunction = createBlockNoteCall.uploadFile;

      expect(uploadFileFunction).toBeDefined();

      const result = await uploadFileFunction(mockFile);

      expect(validateUploadFile).toHaveBeenCalledWith(mockFile);
      expect(mockUploadFile).toHaveBeenCalledWith(mockFile);
      expect(result).toBe('https://example.com/file.jpg');
    });

    it('should return empty string when file validation fails', async () => {
      (validateUploadFile as any).mockReturnValue(false);
      const mockUploadFile = vi.fn();

      const props: UseBlockNoteEditorProps = {
        uploadFile: mockUploadFile,
      };

      useBlockNoteEditor(props);

      const createBlockNoteCall = (useCreateBlockNote as any).mock.calls[0][0];
      const uploadFileFunction = createBlockNoteCall.uploadFile;

      const result = await uploadFileFunction(mockFile);

      expect(validateUploadFile).toHaveBeenCalledWith(mockFile);
      expect(mockUploadFile).not.toHaveBeenCalled();
      expect(result).toBe('');
    });

    it('should return empty string when uploadFile is not provided', async () => {
      const props: UseBlockNoteEditorProps = {
        uploadFile: undefined,
      };

      useBlockNoteEditor(props);

      const createBlockNoteCall = (useCreateBlockNote as any).mock.calls[0][0];

      // When uploadFile is undefined, the uploadFile property should not exist
      expect(createBlockNoteCall).not.toHaveProperty('uploadFile');

      // Since no uploadFile is provided, we can't test the uploadFile function
      // This test verifies that the hook handles the case properly by not including uploadFile
    });
  });

  describe('Cleanup', () => {
    it('should remove event listeners on unmount', () => {
      const mockOnBlur = vi.fn();
      const mockOnFocus = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
        onFocus: mockOnFocus,
      };

      useBlockNoteEditor(props);

      // Get the handlers that were registered
      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];
      const focusHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'focus'
      )?.[1];

      // Find the cleanup effect and call it
      const cleanupEffects = mockEffects.filter((e) => typeof e.effect() === 'function');

      // Execute cleanup functions
      cleanupEffects.forEach((e) => {
        const cleanup = e.effect();
        if (typeof cleanup === 'function') {
          cleanup();
        }
      });

      expect(mockTipTapEditor.off).toHaveBeenCalledWith('blur', blurHandler);
      expect(mockTipTapEditor.off).toHaveBeenCalledWith('focus', focusHandler);
    });

    it('should cancel pending animation frame on unmount', () => {
      // Mock requestAnimationFrame and cancelAnimationFrame
      const mockRequestAnimationFrame = vi.fn().mockReturnValue(123);
      const mockCancelAnimationFrame = vi.fn();

      global.requestAnimationFrame = mockRequestAnimationFrame;
      global.cancelAnimationFrame = mockCancelAnimationFrame;

      const result = useBlockNoteEditor({});

      // Call handleAutoFocus to trigger requestAnimationFrame
      result.handleAutoFocus();

      // Set the rafRef to simulate it being set by requestAnimationFrame
      mockRafRef.current = 123;

      // Find and execute cleanup effects
      const cleanupEffects = mockEffects.filter((e) => typeof e.effect() === 'function');
      cleanupEffects.forEach((e) => {
        const cleanup = e.effect();
        if (typeof cleanup === 'function') {
          cleanup();
        }
      });

      expect(mockCancelAnimationFrame).toHaveBeenCalledWith(123);
    });

    it('should not remove event listeners when callbacks were not provided', () => {
      useBlockNoteEditor({});

      // Find and execute cleanup effects
      const cleanupEffects = mockEffects.filter((e) => typeof e.effect() === 'function');
      cleanupEffects.forEach((e) => {
        const cleanup = e.effect();
        if (typeof cleanup === 'function') {
          cleanup();
        }
      });

      expect(mockTipTapEditor.off).not.toHaveBeenCalled();
    });
  });

  describe('Checkbox Click Detection', () => {
    it('should detect direct checkbox input element', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      // Ensure editor is not focused so handleAutoFocus will work
      mockTipTapEditor.isFocused = false;

      const result = useBlockNoteEditor(props);

      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(true),
      };
      result.editorRef.current = mockEditorElement as any;

      // Clear any previous focus calls
      mockEditor.focus.mockClear();

      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      // Direct checkbox input element
      const mockCheckboxElement = {
        tagName: 'INPUT',
        type: 'checkbox',
        closest: vi.fn().mockReturnValue(null),
      };

      // Mock the instanceof Element check
      Object.setPrototypeOf(mockCheckboxElement, Element.prototype);

      const mockEvent = {
        relatedTarget: mockCheckboxElement,
      };

      blurHandler({ event: mockEvent });

      expect(mockEditor.focus).toHaveBeenCalled();
    });

    it('should not detect non-checkbox input elements', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      const result = useBlockNoteEditor(props);

      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(true),
      };
      result.editorRef.current = mockEditorElement as any;

      // Clear any previous focus calls
      mockEditor.focus.mockClear();

      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      // Non-checkbox input element
      const mockTextInputElement = {
        tagName: 'INPUT',
        type: 'text',
        closest: vi.fn().mockReturnValue(null),
      };

      // Mock the instanceof Element check
      Object.setPrototypeOf(mockTextInputElement, Element.prototype);

      const mockEvent = {
        relatedTarget: mockTextInputElement,
      };

      blurHandler({ event: mockEvent });

      expect(mockEditor.focus).not.toHaveBeenCalled();
    });

    it('should handle null or undefined target', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      const result = useBlockNoteEditor(props);

      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(true),
      };
      result.editorRef.current = mockEditorElement as any;

      // Clear any previous focus calls
      mockEditor.focus.mockClear();

      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      // Null target
      const mockEvent = {
        relatedTarget: null,
      };

      blurHandler({ event: mockEvent });

      expect(mockEditor.focus).not.toHaveBeenCalled();
    });

    it('should handle non-Element target', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      const result = useBlockNoteEditor(props);

      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(true),
      };
      result.editorRef.current = mockEditorElement as any;

      // Clear any previous focus calls
      mockEditor.focus.mockClear();

      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      // Non-Element target (like a text node)
      const mockEvent = {
        relatedTarget: 'text node',
      };

      blurHandler({ event: mockEvent });

      expect(mockEditor.focus).not.toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing relatedTarget in blur event', () => {
      const mockOnBlur = vi.fn();
      const props: UseBlockNoteEditorProps = {
        onBlur: mockOnBlur,
      };

      const result = useBlockNoteEditor(props);

      const mockEditorElement = {
        contains: vi.fn().mockReturnValue(false),
      };
      result.editorRef.current = mockEditorElement as any;

      const blurHandler = (mockTipTapEditor.on as any).mock.calls.find(
        (call: any) => call[0] === 'blur'
      )?.[1];

      const mockEvent = {
        relatedTarget: null,
      };

      blurHandler({ event: mockEvent });

      expect(mockOnBlur).toHaveBeenCalled();
    });

    it('should handle rapid successive handleAutoFocus calls', () => {
      // Ensure window is undefined for our test environment
      global.window = undefined as any;

      const result = useBlockNoteEditor({});

      // Clear any calls from initialization
      mockEditor.focus.mockClear();

      result.handleAutoFocus();
      result.handleAutoFocus();
      result.handleAutoFocus();

      expect(mockEditor.focus).toHaveBeenCalledTimes(3);
    });
  });

  describe('Paste Handler', () => {
    it('should fallback to default handler when ClipboardData is missing', () => {
      const defaultHandlerMock = vi.fn();

      // Create editor and grab the generated paste handler
      useBlockNoteEditor({});
      const createBlockNoteCall = (useCreateBlockNote as any).mock.calls[0][0];
      const pasteHandler = createBlockNoteCall.pasteHandler;

      // Ensure our mock editor has the paste* helpers (they may be undefined in previous mocks)
      mockEditor.pasteMarkdown = vi.fn();
      mockEditor.pasteText = vi.fn();

      // Simulate a paste event without clipboardData
      const result = pasteHandler({
        event: { clipboardData: null } as any,
        editor: mockEditor,
        defaultPasteHandler: defaultHandlerMock,
      });

      expect(defaultHandlerMock).toHaveBeenCalled();
      expect(result).toBe(false);
      expect(mockEditor.pasteMarkdown).not.toHaveBeenCalled();
      expect(mockEditor.pasteText).not.toHaveBeenCalled();
    });

    it('should invoke pasteMarkdown for markdown-like multi-line content', () => {
      const defaultHandlerMock = vi.fn();

      useBlockNoteEditor({});
      const createBlockNoteCall = (useCreateBlockNote as any).mock.calls[0][0];
      const pasteHandler = createBlockNoteCall.pasteHandler;

      mockEditor.pasteMarkdown = vi.fn();
      mockEditor.pasteText = vi.fn();

      const clipboardData = {
        getData: (type: string) => (type === 'text/plain' ? 'Heading 1\n\nContent line' : ''),
      };

      const result = pasteHandler({
        event: { clipboardData } as any,
        editor: mockEditor,
        defaultPasteHandler: defaultHandlerMock,
      });

      expect(result).toBe(true);
      expect(mockEditor.pasteMarkdown).toHaveBeenCalledTimes(1);
      expect(mockEditor.pasteText).not.toHaveBeenCalled();
    });

    it('should invoke pasteText for plain single-newline content', () => {
      const defaultHandlerMock = vi.fn();

      useBlockNoteEditor({});
      const createBlockNoteCall = (useCreateBlockNote as any).mock.calls[0][0];
      const pasteHandler = createBlockNoteCall.pasteHandler;

      mockEditor.pasteMarkdown = vi.fn();
      mockEditor.pasteText = vi.fn();

      const clipboardData = {
        getData: (type: string) => (type === 'text/plain' ? 'Line 1\nLine 2' : ''),
      };

      const result = pasteHandler({
        event: { clipboardData } as any,
        editor: mockEditor,
        defaultPasteHandler: defaultHandlerMock,
      });

      expect(result).toBe(true);
      expect(mockEditor.pasteText).toHaveBeenCalledTimes(1);
      expect(mockEditor.pasteMarkdown).not.toHaveBeenCalled();
    });

    it('should fallback to default handler for single-line text', () => {
      const defaultHandlerMock = vi.fn();

      useBlockNoteEditor({});
      const createBlockNoteCall = (useCreateBlockNote as any).mock.calls[0][0];
      const pasteHandler = createBlockNoteCall.pasteHandler;

      mockEditor.pasteMarkdown = vi.fn();
      mockEditor.pasteText = vi.fn();

      const clipboardData = {
        getData: (type: string) => (type === 'text/plain' ? 'Just a single line' : ''),
      };

      const result = pasteHandler({
        event: { clipboardData } as any,
        editor: mockEditor,
        defaultPasteHandler: defaultHandlerMock,
      });

      expect(defaultHandlerMock).toHaveBeenCalled();
      expect(result).toBe(false);
      expect(mockEditor.pasteMarkdown).not.toHaveBeenCalled();
      expect(mockEditor.pasteText).not.toHaveBeenCalled();
    });
  });
});
