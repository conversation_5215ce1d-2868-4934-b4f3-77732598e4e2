import includes from 'lodash/includes';
import isEmpty from 'lodash/isEmpty';
import { useCallback } from 'react';
import type { ClipboardEvent } from 'react';
import { BLOCKNOTE_LIST_TYPES } from '../constants/blocknote';
import {
  convertBreakLineToHTML,
  decodeTextInParagraphBlock,
  hasMultipleNewLines,
  isComplexMarkdown,
  isMarkdownContent,
  normalizeMarkdownWhenPastingToEditor,
  replaceWhiteSpaceToHTMLNbsp,
} from '../utils';

interface PasteHandlerParams {
  event: ClipboardEvent<HTMLDivElement>;
  editor: any; // Replace 'any' with proper BlockNoteEditor type
  targetClassName: string;
}

export const useEditorPasteClipboard = (editor: any) => {
  /**
   * Handles pasting HTML content from BlockNoteEditor
   */
  const handleHTMLPaste = useCallback(
    async ({ event, editor, targetClassName }: PasteHandlerParams): Promise<boolean> => {
      const currentHTML = event.clipboardData.getData('blocknote/html');
      if (!currentHTML) return false;

      if (!includes(targetClassName, 'ProseMirror-trailingBreak')) {
        return true;
      }

      const blocks = await editor.tryParseHTMLToBlocks(
        replaceWhiteSpaceToHTMLNbsp(convertBreakLineToHTML(currentHTML))
      );
      if (isEmpty(blocks)) return false;

      const selection = editor.getSelection();
      const focusingBlock = editor.getTextCursorPosition()?.block;

      if (!focusingBlock) return false;

      // Handle list item paste case
      if (!selection && includes(BLOCKNOTE_LIST_TYPES, focusingBlock.type)) {
        event.preventDefault();
        event.stopPropagation();

        const newBlocksContent = blocks.flatMap((block) => block.content);
        const newBlock = {
          ...focusingBlock,
          content: [...focusingBlock.content, ...newBlocksContent],
        };
        editor.replaceBlocks([focusingBlock.id], [newBlock]);
        return true;
      }

      return false;
    },
    []
  );

  /**
   * Handles pasting Markdown content
   */
  const handleMarkdownPaste = useCallback(
    async ({ event, editor, targetClassName }: PasteHandlerParams): Promise<boolean> => {
      const currentContent = event.clipboardData.getData('text');

      const isMarkdown = hasMultipleNewLines(currentContent) || isMarkdownContent(currentContent);
      const isValidTarget =
        includes(targetClassName, 'ProseMirror-trailingBreak') ||
        includes(targetClassName, 'bn-inline-content');

      if (!currentContent || !isMarkdown || !isValidTarget) return false;

      const blocks = await editor.tryParseMarkdownToBlocks(
        normalizeMarkdownWhenPastingToEditor(currentContent)
      );
      if (isEmpty(blocks)) return false;

      const selection = editor.getSelection();
      const focusingBlock = editor.getTextCursorPosition();

      if (!focusingBlock || isEmpty(focusingBlock.block)) return false;

      event.preventDefault();
      event.stopPropagation();

      // Case 1: Replace selected blocks
      if (selection && !isEmpty(selection.blocks)) {
        const selectedBlockIds = selection.blocks.map((block) => block.id);
        editor.replaceBlocks(selectedBlockIds, decodeTextInParagraphBlock(blocks));
        return true;
      }

      // Case 2: Replace empty focusing block
      if (isEmpty(focusingBlock.block?.content)) {
        editor.replaceBlocks([focusingBlock.block.id], decodeTextInParagraphBlock(blocks));
        return true;
      }

      // Case 3: Insert after focusing block for complex markdown
      if (isComplexMarkdown(currentContent) || blocks.length > 1) {
        editor.insertBlocks(decodeTextInParagraphBlock(blocks), focusingBlock.block.id, 'after');
        return true;
      }

      // Case 4: Merge with focusing block for simple markdown
      if (!isComplexMarkdown(currentContent) && blocks.length === 1) {
        const [block] = blocks;
        const mergedContent = [...focusingBlock.block.content, ...block.content];
        editor.updateBlock(focusingBlock.block.id, { content: mergedContent });
        return true;
      }

      return false;
    },
    []
  );

  /**
   * Main paste event handler
   */
  const handlePasteToEditorEvent = useCallback(
    async (event: ClipboardEvent<HTMLDivElement>): Promise<boolean> => {
      if (!event.clipboardData || !event.target) return false;

      const targetClassName = (event.target as HTMLDivElement)?.className;
      const params = { event, editor, targetClassName };

      // Try HTML paste first
      const htmlResult = await handleHTMLPaste(params);
      if (htmlResult) return true;

      // Try Markdown paste second
      const markdownResult = await handleMarkdownPaste(params);
      if (markdownResult) return true;

      // Default paste behavior
      return false;
    },
    [editor, handleHTMLPaste, handleMarkdownPaste]
  );

  return { handlePasteToEditorEvent };
};
