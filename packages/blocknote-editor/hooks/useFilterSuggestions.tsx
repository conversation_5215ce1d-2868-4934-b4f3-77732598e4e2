import { filterSuggestionItems } from '@blocknote/core';
import type { DefaultReactSuggestionItem } from '@blocknote/react';
import { useCallback } from 'react';

/**
 * Custom hook for handling suggestion filtering logic
 */
export const useFilterSuggestions = (props: { variableSuggestions?: string[] }) => {
  const { variableSuggestions = [] } = props;

  /**
   * Generates menu items for variables
   * @param editor The BlockNote editor instance
   * @returns Array of suggestion items for variables
   */
  const getVariableMenuItems = useCallback(
    (editor: any): DefaultReactSuggestionItem[] => {
      // Map variable suggestions to menu items
      const items = variableSuggestions.map((variable) => ({
        title: variable,
        onItemClick: () => {
          try {
            // Insert the selected variable inline content
            editor.insertInlineContent([
              {
                type: 'variable',
                props: {
                  variable,
                },
              },
              ' ', // Add space after variable for better editing experience
            ]);
          } catch (error) {
            console.error('Error inserting variable:', error);
          }
        },
      }));

      return items;
    },
    [variableSuggestions]
  );

  /**
   * Filters suggestion items based on query
   * @param editor The BlockNote editor instance
   * @param query The search query
   * @returns Filtered suggestion items
   */
  const filterVariableSuggestions = useCallback(
    async (editor: any, query: string) => {
      try {
        // Get all menu items
        const allItems = getVariableMenuItems(editor);

        // If query is empty or just has the trigger character, return all items
        if (!query || query === '{') {
          return allItems;
        }

        // Filter items based on the query
        const filteredItems = filterSuggestionItems(allItems, query.replace(/^{/, ''));
        return filteredItems;
      } catch (error) {
        console.error('Error filtering variable suggestions:', error);
        return [];
      }
    },
    [getVariableMenuItems]
  );

  return {
    filterVariableSuggestions,
  };
};
