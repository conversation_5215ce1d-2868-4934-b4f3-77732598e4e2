import { resolve } from 'node:path';
import react from '@vitejs/plugin-react-swc';
/// <reference types="vitest/config" />
import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';

// Export the configuration as a function that returns the config object
export default defineConfig({
  plugins: [
    react(),
    dts({
      include: [
        './components/**/*',
        './constants/**/*',
        './utils/**/*',
        './hooks/**/*',
        './index.tsx',
      ],
      exclude: ['./components/**/*.stories.tsx', './components/**/*.test.tsx'],
      rollupTypes: true,
      staticImport: true,
      insertTypesEntry: true,
      outDir: './dist',
    }),
  ],
  resolve: {
    alias: {
      '@resola-ai/blocknote-editor': resolve(__dirname, '.'),
    },
  },
  build: {
    lib: {
      entry: resolve(__dirname, 'index.tsx'),
      name: 'BlocknoteEditor',
      fileName: (format) => `blocknote-editor.${format}.js`,
      formats: ['es', 'cjs'],
    },
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        '@mantine/core',
        '@mantine/hooks',
        '@mantine/emotion',
        '@tabler/icons-react',
        '@blocknote/react',
        '@blocknote/core',
        '@blocknote/shadcn',
        'lodash',
        '@emotion/react',
        'react-phone-input-2',
        'react-shadow',
        /^@resola-ai\/.*/,
      ],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
        },
        minifyInternalExports: true,
        paths: (id) => {
          if (id.startsWith('@resola-ai/')) {
            return id;
          }
          return undefined;
        },
        // Ensure CSS is extracted to separate file
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            return 'styles.css';
          }
          return assetInfo.name || 'assets/[name][extname]';
        },
      },
    },
    sourcemap: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
      },
    },
    // Ensure CSS is not split in library mode
    cssCodeSplit: false,
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './setupTest.js',
    coverage: {
      enabled: true,
      provider: 'v8',
      reporter: ['text', 'lcov', 'json'],
      reportsDirectory: './coverage',
    },
    exclude: [
      'coverage/**',
      'dist/**',
      '**/*.d.ts',
      '**/__tests__/**',
      '**/*.spec.ts',
      '**/*.spec.tsx',
      '**/vite.config.*',
      '**/vitest.config.*',
      '**/jest.config.*',
      '**/setupTest.*',
      'node_modules/**',
    ],
  },
});
