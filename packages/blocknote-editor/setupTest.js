// Setup file for Vitest
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Configure @testing-library to have shorter timeout for faster tests
import { configure } from '@testing-library/react';
configure({ testIdAttribute: 'data-testid' });

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
  root: null,
  rootMargin: '',
  thresholds: [],
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock MutationObserver properly for @testing-library/dom compatibility
class MockMutationObserver {
  constructor(callback) {
    this.callback = callback;
  }
  observe() {
    // Do nothing in tests
  }
  disconnect() {
    // Do nothing in tests
  }
  takeRecords() {
    return [];
  }
}

global.MutationObserver = MockMutationObserver;
window.MutationObserver = MockMutationObserver;

// Mock document.execCommand
document.execCommand = vi.fn();

// Mock getSelection
Object.defineProperty(window, 'getSelection', {
  value: vi.fn(() => ({
    removeAllRanges: vi.fn(),
    addRange: vi.fn(),
    toString: vi.fn(() => ''),
    rangeCount: 0,
    anchorNode: null,
    anchorOffset: 0,
    focusNode: null,
    focusOffset: 0,
    isCollapsed: true,
    getRangeAt: vi.fn(),
    collapse: vi.fn(),
    extend: vi.fn(),
    collapseToStart: vi.fn(),
    collapseToEnd: vi.fn(),
    selectAllChildren: vi.fn(),
    deleteFromDocument: vi.fn(),
    containsNode: vi.fn(),
  })),
  writable: true,
});

// Mock Range
global.Range = vi.fn(() => ({
  setStart: vi.fn(),
  setEnd: vi.fn(),
  selectNode: vi.fn(),
  selectNodeContents: vi.fn(),
  collapse: vi.fn(),
  deleteContents: vi.fn(),
  extractContents: vi.fn(),
  cloneContents: vi.fn(),
  insertNode: vi.fn(),
  surroundContents: vi.fn(),
  cloneRange: vi.fn(),
  detach: vi.fn(),
  getBoundingClientRect: vi.fn(() => ({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    toJSON: vi.fn(),
  })),
  getClientRects: vi.fn(() => []),
  commonAncestorContainer: document.body,
  startContainer: document.body,
  endContainer: document.body,
  startOffset: 0,
  endOffset: 0,
  collapsed: true,
}));

// Mock createRange
document.createRange = vi.fn(() => new Range());

// Mock focus and blur methods
Element.prototype.focus = vi.fn();
Element.prototype.blur = vi.fn();

// Mock scrollIntoView
Element.prototype.scrollIntoView = vi.fn();

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 0));
global.cancelAnimationFrame = vi.fn();

// Mock clipboard API
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: vi.fn(),
    readText: vi.fn(),
  },
  writable: true,
});

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mocked-url');
global.URL.revokeObjectURL = vi.fn();

// Mock File and FileReader
global.File = vi.fn();
global.FileReader = vi.fn(() => ({
  readAsText: vi.fn(),
  readAsDataURL: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  result: null,
  error: null,
  readyState: 0,
}));

// Mock DOMParser
global.DOMParser = vi.fn(() => ({
  parseFromString: vi.fn((str) => {
    const doc = document.implementation.createHTMLDocument();
    doc.body.innerHTML = str;
    return doc;
  }),
}));

// Mock performance
global.performance = {
  ...global.performance,
  now: vi.fn(() => Date.now()),
};

// Ensure console methods don't spam during tests
console.warn = vi.fn();
console.error = vi.fn();
console.info = vi.fn();

// Mock window.matchMedia which is used by @mantine/core
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Stub Mantine useMediaQuery to avoid reliance on matchMedia in tests
vi.mock('@mantine/hooks', () => ({
  useMediaQuery: () => false,
}));

// Add any other test setup here as needed
