import { Box, Flex, rem, SimpleGrid, Text, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconArrowRight, IconArticle, IconFolder } from '@tabler/icons-react';
import {
  ArticleType,
  Category,
  CategoryArticle,
  CategoryListType,
  ResponsiveProp,
} from '../../types/pageBuilder';
import { generateResponsivePadding, generateResponsiveStyles } from '../../utils';
import { useTranslate } from '@tolgee/react';
import { withTolgee } from '../hoc';

const useStyles = createStyles(theme => ({
  listItem: {
    '&:not(:last-of-type)': {
      borderBottom: `1px solid ${theme.colors.silverFox[3]}`,
    },
    cursor: 'pointer',
    color: `${theme.colors.decaGrey[6]}`,
    '&:hover': {
      color: `${theme.colors.decaBlue[6]} !important`,
    },
    '.tabler-icon-article, .tabler-icon-arrow-right, .tabler-icon-folder': {
      flexShrink: 0,
    },
  },
  boxItem: {
    border: `1px solid ${theme.colors.decaGrey[0]}`,
    borderRadius: rem(8),
    cursor: 'pointer',
    color: `${theme.colors.decaGrey[6]}`,
    '&:hover': {
      color: `${theme.colors.decaBlue[6]} !important`,
    },
    '.tabler-icon-article, .tabler-icon-arrow-right, .tabler-icon-folder': {
      flexShrink: 0,
    },
    boxShadow: '0px 1px 2px 0px #0000000D',
  },
  categoryListWrapper: {
    border: `1px solid ${theme.colors.decaGrey[0]}`,
    borderRadius: rem(8),
    boxShadow: '0px 1px 2px 0px #0000000D',
  },
}));

type CategoryListElementProps = {
  type: string;
  padding: ResponsiveProp<Record<'left' | 'right' | 'top' | 'bottom', number>>;
  backgroundColor: string;
  showDescription: boolean;
  showPicture: boolean;
  selectedCategory: Category;
  onClickArticle: (item: Category | CategoryArticle) => void;
  textColor?: string;
  iconColor?: string;
  containerBorderColor?: string;
};

const CategoryListElement = ({
  selectedCategory,
  type,
  padding,
  backgroundColor,
  showDescription,
  textColor,
  iconColor,
  onClickArticle,
  containerBorderColor,
}: CategoryListElementProps) => {
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const items = selectedCategory?.data || [];
  const { t } = useTranslate('page-builder');

  const renderList = () => {
    return (
      <>
        {items?.map((item: Category | CategoryArticle) => (
          <Flex
            key={item.id || (item as CategoryArticle).value}
            onClick={() => onClickArticle(item)}
            align={'center'}
            w='100%'
            p={rem(15)}
            className={classes.listItem}>
            {item.type === ArticleType.Article && !item.subType ? (
              <IconArticle size={20} color={iconColor} />
            ) : (
              <IconFolder size={20} color={iconColor} />
            )}
            <Text ml={rem(10)} mr='auto' c={textColor} lineClamp={1}>
              {item.type === ArticleType.Category ? item.name : (item as CategoryArticle).label}
            </Text>
            <IconArrowRight size={20} color={iconColor} />
          </Flex>
        ))}
      </>
    );
  };

  const renderBox = () => {
    return (
      <SimpleGrid cols={2}>
        {items?.map((item: Category | CategoryArticle) => (
          <Flex
            key={item.id || (item as CategoryArticle).value}
            onClick={() => onClickArticle(item)}
            align={'center'}
            w='100%'
            p={rem(15)}
            className={classes.boxItem}>
            {item.type === ArticleType.Article && !item.subType ? (
              <IconArticle size={20} color={iconColor} />
            ) : (
              <IconFolder size={20} color={iconColor} />
            )}
            <Text ml={rem(10)} mr='auto' c={textColor} lineClamp={2}>
              {item.type === ArticleType.Category ? item.name : (item as CategoryArticle).label}
            </Text>
          </Flex>
        ))}
      </SimpleGrid>
    );
  };

  const renderLine = () => {
    return (
      <>
        {items?.map((item: Category | CategoryArticle) => (
          <Flex
            key={item.id || (item as CategoryArticle).value}
            onClick={() => onClickArticle(item)}
            align={'center'}
            w='100%'
            p={rem(15)}
            className={classes.listItem}>
            {item.type === ArticleType.Article && !item.subType ? (
              <IconArticle size={20} color={iconColor} />
            ) : (
              <IconFolder size={20} color={iconColor} />
            )}
            <Text ml={rem(10)} mr='auto' c={textColor} lineClamp={1}>
              {item.type === ArticleType.Category ? item.name : (item as CategoryArticle).label}
            </Text>
            <IconArrowRight size={20} color={iconColor} />
          </Flex>
        ))}
      </>
    );
  };

  return (
    <Flex
      w='100%'
      styles={generateResponsiveStyles({
        padding: generateResponsivePadding(padding),
        backgroundColor: backgroundColor || theme.colors.decaMono[1],
      })}>
      <Flex
        direction='column'
        w='100%'
        h='100%'
        className={type === CategoryListType.List ? classes.categoryListWrapper : ''}
        p={rem(12)}
        styles={generateResponsiveStyles({
          backgroundColor:
            type === CategoryListType.List
              ? theme.colors.decaMono[1]
              : backgroundColor || theme.colors.decaMono[1],
          borderColor: containerBorderColor ?? 'transparent',
        })}>
        <Flex mb={rem(20)}>
          {/* // Temporary hide the picture in cateogry list
          {showPicture && selectedCategory?.subType === ArticleType.Category && (
            <Image
              w={60}
              h={60}
              radius='md'
              alt=''
              src={selectedCategory?.image || 'https://placehold.co/600x400'}
            />
          )}
          */}
          <Box ml={rem(20)}>
            <Text fw={700} fz={18} c={textColor}>
              {selectedCategory?.name}
            </Text>
            <Text fz={14} c={iconColor}>
              {`${items?.length} ${selectedCategory?.subType === ArticleType.Category ? t('categories') : t('articles')}`}
            </Text>
          </Box>
        </Flex>
        {!!(showDescription && selectedCategory?.description) && (
          <Flex my={rem(20)}>{selectedCategory?.description}</Flex>
        )}
        {type === CategoryListType.List && renderList()}
        {type === CategoryListType.Box && renderBox()}
        {type === CategoryListType.Line && renderLine()}
      </Flex>
    </Flex>
  );
};

export default withTolgee(CategoryListElement);
