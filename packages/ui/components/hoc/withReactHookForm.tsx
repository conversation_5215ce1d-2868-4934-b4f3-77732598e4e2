import React from 'react';
import { Controller, Control, FieldValues, Path } from 'react-hook-form';

interface WithReactHookFormProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  defaultValue?: any;
  rules?: Record<string, any>;
  onChange?: (value: any) => void;
}

export const withReactHookForm = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  return function WithReactHookFormComponent<T extends FieldValues>({
    control,
    name,
    rules,
    defaultValue,
    onChange: onChangeProp,
    ...props
  }: WithReactHookFormProps<T> & Omit<P, 'value' | 'error' | 'onChange'>) {
    return (
      <Controller
        control={control}
        name={name}
        rules={rules}
        defaultValue={defaultValue}
        render={({ field: { onChange, value }, fieldState: { error } }) => {
          const handleChange = (value: any) => {
            onChangeProp?.(value);
            onChange(value);
          };
          return (
            <WrappedComponent
              {...(props as P)}
              onChange={handleChange}
              value={value}
              error={error?.message}
            />
          );
        }}
      />
    );
  };
};
