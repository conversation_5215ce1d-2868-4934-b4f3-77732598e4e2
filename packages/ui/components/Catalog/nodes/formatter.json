{"name": "formatter", "displayName": "<PERSON><PERSON><PERSON>", "category": ["builtin-tools", "modify"], "description": "Format data based on specified conditions", "settings": {}, "schemas": {"dt_add_data": {"name": "dt_add_data", "displayName": "DateTime Add Data", "description": "Data for the DateTime Add operation", "type": "object", "properties": {"output_date": {"name": "output_date", "displayName": "Output Date", "description": "Resulting date string", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "dt_compare_data": {"name": "dt_compare_data", "displayName": "DateTime Compare Data", "description": "Data for the DateTime Compare operation", "type": "object", "properties": {"dates_swapped": {"name": "dates_swapped", "displayName": "Dates Swapped", "description": "True if start_date is after end_date and the dates were swapped.", "type": "boolean", "required": true, "default": false, "order": 1}, "days_difference": {"name": "days_difference", "displayName": "Days Difference", "description": "Difference in days.", "type": "number", "required": true, "default": 0, "order": 2}, "hours_difference": {"name": "hours_difference", "displayName": "Hours Difference", "description": "Difference in hours.", "type": "number", "required": true, "default": 0, "order": 3}, "minutes_difference": {"name": "minutes_difference", "displayName": "Minutes Difference", "description": "Difference in minutes.", "type": "number", "required": true, "default": 0, "order": 4}, "seconds_difference": {"name": "seconds_difference", "displayName": "Seconds Difference", "description": "Difference in seconds.", "type": "number", "required": true, "default": 0, "order": 5}, "same_date": {"name": "same_date", "displayName": "Same Date", "description": "True if start_date and end_date are the same.", "type": "boolean", "required": true, "default": false, "order": 6}}, "required": true}, "number_math_data": {"name": "number_math_data", "displayName": "Number Math Data", "description": "Data for the Number Math operation", "type": "object", "properties": {"result": {"name": "result", "displayName": "Result", "description": "Resulting number", "type": "number", "required": true, "default": 0, "order": 1}}, "required": true}, "text_capitalize_data": {"name": "text_capitalize_data", "displayName": "Text Capitalize Data", "description": "Data for the Text Capitalize operation", "type": "object", "properties": {"output_text": {"name": "output_text", "displayName": "Output Text", "description": "Capitalized text", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "utilities_pickFromList_data": {"name": "utilities_pickFromList_data", "displayName": "Pick From List Data", "description": "Data for the Pick From List operation", "type": "object", "properties": {"picked": {"name": "picked", "displayName": "Picked", "description": "The picked item from the list.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "dt_format_data": {"name": "dt_format_data", "displayName": "DateTime Format Data", "description": "Data for the DateTime Format operation", "type": "object", "properties": {"formatted_date": {"name": "formatted_date", "displayName": "Formatted Date", "description": "The formatted date.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "text_split_data": {"name": "text_split_data", "displayName": "Text Split Data", "description": "Data for the Text Split operation", "type": "object", "properties": {"parts": {"name": "parts", "displayName": "Parts", "description": "The resulting parts after splitting.", "type": "array", "items": {"type": "string"}, "required": true, "default": [], "order": 1}}, "required": true}, "text_htmlToMarkdown_data": {"name": "text_htmlToMarkdown_data", "displayName": "Text HTML to Markdown Data", "description": "Data for the Text HTML to Markdown operation", "type": "object", "properties": {"result": {"name": "result", "displayName": "Result", "description": "The resulting Markdown.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "text_markdownToHtml_data": {"name": "text_markdownToHtml_data", "displayName": "Text Markdown to HTML Data", "description": "Data for the Text Markdown to HTML operation", "type": "object", "properties": {"result": {"name": "result", "displayName": "Result", "description": "The resulting HTML.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "text_join_data": {"name": "text_join_data", "displayName": "Text Join Data", "description": "Data for the Text Join operation", "type": "object", "properties": {"result": {"name": "result", "displayName": "Result", "description": "The joined string.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "text_replace_data": {"name": "text_replace_data", "displayName": "Text Replace Data", "description": "Data for the Text Replace operation", "properties": {"result": {"name": "result", "displayName": "Result", "description": "The resulting string after replacement.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "text_toUpper_data": {"name": "text_toUpper_data", "displayName": "Text To Upper Data", "description": "Data for the Text To Upper operation", "type": "object", "properties": {"output_text": {"name": "output_text", "displayName": "Output Text", "description": "Upper case text.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "text_toLower_data": {"name": "text_toLower_data", "displayName": "Text To Lower Data", "description": "Data for the Text To Lower operation", "type": "object", "properties": {"output_text": {"name": "output_text", "displayName": "Output Text", "description": "Lower case text.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "text_trim_data": {"name": "text_trim_data", "displayName": "Text Trim Data", "description": "Data for the Text Trim operation", "type": "object", "properties": {"output_text": {"name": "output_text", "displayName": "Output Text", "description": "Trimmed text.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "text_substring_data": {"name": "text_substring_data", "displayName": "Text Substring Data", "description": "Data for the Text Substring operation", "type": "object", "properties": {"output_text": {"name": "output_text", "displayName": "Output Text", "description": "Extracted substring.", "type": "string", "required": true, "default": "", "order": 1}}, "required": true}, "text_removeHtml_data": {"name": "text_removeHtml_data", "displayName": "Text Remove HTML Data", "description": "Data for the Text Remove HTML operation", "type": "object", "properties": {"output_text": {"name": "output_text", "displayName": "Output Text", "type": "string", "description": "Text with HTML removed.", "required": true, "default": "", "order": 1}}, "required": true}, "text_extractEmails_data": {"name": "text_extractEmails_data", "displayName": "Text Extract Emails Data", "description": "Data for the Text Extract Emails operation", "type": "object", "properties": {"matches": {"name": "matches", "displayName": "Matches", "type": "array", "items": {"type": "string"}, "description": "Extracted emails.", "required": true, "default": [], "order": 1}}, "required": true}, "text_extractUrls_data": {"name": "text_extractUrls_data", "displayName": "Text Extract URLs Data", "description": "Data for the Text Extract URLs operation", "type": "object", "properties": {"matches": {"name": "matches", "displayName": "Matches", "type": "array", "items": {"type": "string"}, "description": "Extracted URLs.", "required": true, "default": [], "order": 1}}, "required": true}, "text_extractPhones_data": {"name": "text_extractPhones_data", "displayName": "Text Extract Phones Data", "description": "Data for the Text Extract Phones operation", "type": "object", "properties": {"matches": {"name": "matches", "displayName": "Matches", "type": "array", "items": {"type": "string"}, "description": "Extracted phone numbers.", "required": true, "default": [], "order": 1}}, "required": true}, "dt_timezoneConvert_data": {"name": "dt_timezoneConvert_data", "displayName": "DateTime Timezone Convert Data", "description": "Data for the DateTime Timezone Convert operation", "type": "object", "properties": {"output_date": {"name": "output_date", "displayName": "Output Date", "type": "string", "description": "The converted date/time string.", "required": true, "default": "", "order": 1}}, "required": true}, "dt_extract_data": {"name": "dt_extract_data", "displayName": "DateTime Extract Data", "description": "Data for the DateTime Extract operation", "type": "object", "properties": {"value": {"name": "value", "displayName": "Value", "type": "number", "description": "The extracted value.", "required": true, "default": 0, "order": 1}}, "required": true}, "number_round_data": {"name": "number_round_data", "displayName": "Number Round Data", "description": "Data for the Number Round operation", "type": "object", "properties": {"output_number": {"name": "output_number", "displayName": "Output Number", "type": "number", "description": "The rounded number.", "required": true, "default": 0, "order": 1}}, "required": true}, "number_parse_data": {"name": "number_parse_data", "displayName": "Number Parse Data", "description": "Data for the Number Parse operation", "type": "object", "properties": {"output_number": {"name": "output_number", "displayName": "Output Number", "type": "number", "description": "The parsed number.", "required": true, "default": 0, "order": 1}}, "required": true}, "utilities_generateRandom_data": {"name": "utilities_generateRandom_data", "displayName": "Generate Random Data", "description": "Data for the Generate Random operation", "type": "object", "properties": {"value": {"name": "value", "displayName": "Value", "type": "string", "description": "The generated random value.", "required": true, "default": "", "order": 1}}, "required": true}, "number_currencyFormat_data": {"name": "number_currencyFormat_data", "displayName": "Number Currency Format Data", "description": "Data for the Number Currency Format operation", "type": "object", "properties": {"output": {"name": "output", "displayName": "Output", "type": "string", "description": "The formatted currency string.", "required": true, "default": "", "order": 1}}, "required": true}, "utilities_lookupTable_data": {"name": "utilities_lookupTable_data", "displayName": "Lookup Table Data", "description": "Data for the Lookup Table operation", "type": "object", "properties": {"value": {"name": "value", "displayName": "Value", "type": "string", "description": "The value found for the key, or the default if not found.", "required": true, "default": "", "order": 1}}, "required": true}}, "credentials": {}, "triggers": {}, "actions": {"formatter_dt_add": {"name": "formatter_dt_add", "displayName": "DateTime Add", "description": "Add to a date/time value", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "datetime", "required": true, "default": "datetime", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "dt_add", "required": true, "default": "dt_add", "order": 2, "hidden": true}, "input_date": {"name": "input_date", "displayName": "Input Date", "description": "Input date string", "type": "string", "required": true, "default": "", "order": 3}, "expression": {"name": "expression", "displayName": "Expression", "description": "Date math expression", "type": "string", "required": true, "default": "", "order": 4}, "to_format": {"name": "to_format", "displayName": "To Format", "description": "Output date format", "type": "string", "required": true, "default": "", "order": 5}, "from_format": {"name": "from_format", "displayName": "From Format", "description": "Input date format", "type": "string", "required": false, "default": "", "order": 6}}, "data": {"$ref": "#/schemas/dt_add_data"}}, "formatter_dt_compare": {"name": "formatter_dt_compare", "displayName": "DateTime Compare", "description": "Compare two date/time values", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "datetime", "required": true, "default": "datetime", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "dt_compare", "required": true, "default": "dt_compare", "order": 2, "hidden": true}, "start_date": {"name": "start_date", "displayName": "Start Date", "description": "The date to compare.", "type": "string", "required": true, "default": "", "order": 3}, "end_date": {"name": "end_date", "displayName": "End Date", "description": "The date to compare.", "type": "string", "required": true, "default": "", "order": 4}, "start_date_format": {"name": "start_date_format", "displayName": "Start Date Format", "description": "The format of the start date.", "type": "string", "required": false, "default": "", "order": 5}, "end_date_format": {"name": "end_date_format", "displayName": "End Date Format", "description": "The format of the end date.", "type": "string", "required": false, "default": "", "order": 6}}, "data": {"$ref": "#/schemas/dt_compare_data"}}, "formatter_number_math": {"name": "formatter_number_math", "displayName": "Number Math", "description": "Perform a math operation on one or more numbers", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "number", "required": true, "default": "number", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "number_math", "required": true, "default": "number_math", "order": 2, "hidden": true}, "input_numbers": {"name": "input_numbers", "displayName": "Input Numbers", "description": "The numbers to perform the math operation on.", "type": "array", "items": {"type": "number"}, "required": true, "default": [], "order": 3}, "operation_type": {"name": "operation_type", "displayName": "Operation Type", "description": "The math operation to perform (add, subtract, multiply, divide, negative, positive).", "type": "string", "enum": ["add", "subtract", "multiply", "divide", "negative", "positive"], "required": true, "default": "add", "order": 4}}, "data": {"$ref": "#/schemas/number_math_data"}}, "formatter_text_capitalize": {"name": "formatter_text_capitalize", "displayName": "Text Capitalize", "description": "Capitalize the input text", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_capitalize", "required": true, "default": "text_capitalize", "order": 2, "hidden": true}, "input_text": {"name": "input_text", "displayName": "Input Text", "description": "Input text", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/text_capitalize_data"}}, "formatter_utilities_pickFromList": {"name": "formatter_utilities_pickFromList", "displayName": "Pick From List", "description": "Pick an item from a list using an operation (first, last, random).", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "utilities", "required": true, "default": "utilities", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "utilities_pickFromList", "required": true, "default": "utilities_pickFromList", "order": 2, "hidden": true}, "input": {"name": "input", "displayName": "Input", "description": "List of items to pick from.", "type": "array", "items": {}, "required": true, "default": [], "order": 3}, "operation_type": {"name": "operation_type", "displayName": "Operation Type", "description": "The operation to perform", "type": "string", "enum": ["first", "last", "random"], "required": true, "default": "first", "order": 4}, "default": {"name": "default", "displayName": "<PERSON><PERSON><PERSON>", "description": "Default value to return if input is empty.", "type": "string", "required": false, "default": "", "order": 5}}, "data": {"$ref": "#/schemas/utilities_pickFromList_data"}}, "formatter_dt_format": {"name": "formatter_dt_format", "displayName": "DateTime Format", "description": "Format a date/time value to a specified format and timezone", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "datetime", "required": true, "default": "datetime", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "dt_format", "required": true, "default": "dt_format", "order": 2, "hidden": true}, "input_date": {"name": "input_date", "displayName": "Input Date", "description": "The date to format.", "type": "string", "required": true, "default": "", "order": 3}, "to_format": {"name": "to_format", "displayName": "To Format", "description": "The format to format the date to.", "type": "string", "required": true, "default": "", "order": 4}, "from_format": {"name": "from_format", "displayName": "From Format", "description": "The format of the date to format.", "type": "string", "required": false, "default": "", "order": 5}, "to_timezone": {"name": "to_timezone", "displayName": "To Timezone", "description": "The timezone to format the date to.", "type": "string", "required": false, "default": "", "order": 6}, "from_timezone": {"name": "from_timezone", "displayName": "From Timezone", "description": "The timezone of the date to format.", "type": "string", "required": false, "default": "", "order": 7}}, "data": {"$ref": "#/schemas/dt_format_data"}}, "formatter_text_split": {"name": "formatter_text_split", "displayName": "Text Split", "description": "Split text by a delimiter", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_split", "required": true, "default": "text_split", "order": 2, "hidden": true}, "value": {"name": "value", "displayName": "Value", "description": "The text to split.", "type": "string", "required": true, "default": "", "order": 3}, "delimiter": {"name": "delimiter", "displayName": "Delimiter", "description": "The delimiter to split by.", "type": "string", "required": true, "default": "", "order": 4}}, "data": {"$ref": "#/schemas/text_split_data"}}, "formatter_text_htmlToMarkdown": {"name": "formatter_text_htmlToMarkdown", "displayName": "HTML to Markdown", "description": "Convert HTML to Markdown", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_htmlToMarkdown", "required": true, "default": "text_htmlToMarkdown", "order": 2, "hidden": true}, "value": {"name": "value", "displayName": "Value", "description": "The HTML to convert to Markdown.", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/text_htmlToMarkdown_data"}}, "formatter_text_markdownToHtml": {"name": "formatter_text_markdownToHtml", "displayName": "Markdown to HTML", "description": "Convert Markdown to HTML", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_markdownToHtml", "required": true, "default": "text_markdownToHtml", "order": 2, "hidden": true}, "value": {"name": "value", "displayName": "Value", "description": "The Markdown to convert to HTML.", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/text_markdownToHtml_data"}}, "formatter_text_join": {"name": "formatter_text_join", "displayName": "Text Join", "description": "Join an array of strings with a delimiter", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_join", "required": true, "default": "text_join", "order": 2, "hidden": true}, "parts": {"name": "parts", "displayName": "Parts", "description": "Array of strings to join.", "type": "array", "items": {"type": "string"}, "required": true, "default": [], "order": 3}, "delimiter": {"name": "delimiter", "displayName": "Delimiter", "description": "Delimiter to use for joining.", "type": "string", "required": true, "default": "", "order": 4}}, "data": {"$ref": "#/schemas/text_join_data"}}, "formatter_text_replace": {"name": "formatter_text_replace", "displayName": "Text Replace", "description": "Replace occurrences of a substring with another substring in the input string.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_replace", "required": true, "default": "text_replace", "order": 2, "hidden": true}, "value": {"name": "value", "displayName": "Value", "description": "The input string.", "type": "string", "required": true, "default": "", "order": 3}, "old": {"name": "old", "displayName": "Old", "description": "The substring to replace.", "type": "string", "required": true, "default": "", "order": 4}, "new": {"name": "new", "displayName": "New", "description": "The replacement string.", "type": "string", "required": true, "default": "", "order": 5}, "count": {"name": "count", "displayName": "Count", "description": "Number of replacements to make (optional, 0 or negative means replace all).", "type": "integer", "required": false, "default": 0, "order": 6}}, "data": {"$ref": "#/schemas/text_replace_data"}}, "formatter_text_toUpper": {"name": "formatter_text_toUpper", "displayName": "Text To Upper Case", "description": "Convert text to upper case.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_toUpper", "required": true, "default": "text_toUpper", "order": 2, "hidden": true}, "input_text": {"name": "input_text", "displayName": "Input Text", "description": "Input text to convert to upper case.", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/text_toUpper_data"}}, "formatter_text_toLower": {"name": "formatter_text_toLower", "displayName": "Text To Lower Case", "description": "Convert text to lower case.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_toLower", "required": true, "default": "text_toLower", "order": 2, "hidden": true}, "input_text": {"name": "input_text", "displayName": "Input Text", "description": "Input text to convert to lower case.", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/text_toLower_data"}}, "formatter_text_trim": {"name": "formatter_text_trim", "displayName": "Text Trim", "description": "Trim whitespace or specified characters from the input text.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_trim", "required": true, "default": "text_trim", "order": 2, "hidden": true}, "input_text": {"name": "input_text", "displayName": "Input Text", "description": "Input text to trim.", "type": "string", "required": true, "default": "", "order": 3}, "chars": {"name": "chars", "displayName": "Chars", "description": "Characters to trim (optional, default is whitespace).", "type": "string", "required": false, "default": "", "order": 4}}, "data": {"$ref": "#/schemas/text_trim_data"}}, "formatter_text_substring": {"name": "formatter_text_substring", "displayName": "Text Substring", "description": "Extract a substring from the input text.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_substring", "required": true, "default": "text_substring", "order": 2, "hidden": true}, "input_text": {"name": "input_text", "displayName": "Input Text", "description": "Input text to extract substring from.", "type": "string", "required": true, "default": "", "order": 3}, "start": {"name": "start", "displayName": "Start", "description": "Starting index (0-based).", "type": "integer", "required": true, "default": 0, "order": 4}, "length": {"name": "length", "displayName": "Length", "description": "Number of characters to extract (optional, if omitted or negative, go to end).", "type": "integer", "required": false, "default": 0, "order": 5}}, "data": {"$ref": "#/schemas/text_substring_data"}}, "formatter_text_removeHtml": {"name": "formatter_text_removeHtml", "displayName": "Text Remove HTML", "description": "Remove all HTML tags from the input text.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_removeHtml", "required": true, "default": "text_removeHtml", "order": 2, "hidden": true}, "input_text": {"name": "input_text", "displayName": "Input Text", "description": "Input text to remove HTML from.", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/text_removeHtml_data"}}, "formatter_text_extractEmails": {"name": "formatter_text_extractEmails", "displayName": "Text Extract Emails", "description": "Extract all email addresses from the input text.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_extractEmails", "required": true, "default": "text_extractEmails", "order": 2, "hidden": true}, "input_text": {"name": "input_text", "displayName": "Input Text", "description": "Input text to extract emails from.", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/text_extractEmails_data"}}, "formatter_text_extractUrls": {"name": "formatter_text_extractUrls", "displayName": "Text Extract URLs", "description": "Extract all URLs from the input text.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_extractUrls", "required": true, "default": "text_extractUrls", "order": 2, "hidden": true}, "input_text": {"name": "input_text", "displayName": "Input Text", "description": "Input text to extract URLs from.", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/text_extractUrls_data"}}, "formatter_text_extractPhones": {"name": "formatter_text_extractPhones", "displayName": "Text Extract Phones", "description": "Extract all phone numbers from the input text.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "text", "required": true, "default": "text", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "text_extractPhones", "required": true, "default": "text_extractPhones", "order": 2, "hidden": true}, "input_text": {"name": "input_text", "displayName": "Input Text", "description": "Input text to extract phone numbers from.", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/text_extractPhones_data"}}, "formatter_dt_timezoneConvert": {"name": "formatter_dt_timezoneConvert", "displayName": "DateTime Timezone Convert", "description": "Convert a date/time value from one timezone to another.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "datetime", "required": true, "default": "datetime", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "dt_timezoneConvert", "required": true, "default": "dt_timezoneConvert", "order": 2, "hidden": true}, "input_date": {"name": "input_date", "displayName": "Input Date", "description": "The date/time string to convert.", "type": "string", "required": true, "default": "", "order": 3}, "from_timezone": {"name": "from_timezone", "displayName": "From Timezone", "description": "The timezone of the input date (IANA name).", "type": "string", "required": true, "default": "", "order": 4}, "to_timezone": {"name": "to_timezone", "displayName": "To Timezone", "description": "The target timezone (IANA name).", "type": "string", "required": true, "default": "", "order": 5}, "format": {"name": "format", "displayName": "Format", "description": "The output format (optional, default RFC3339).", "type": "string", "required": false, "default": "", "order": 6}, "input_format": {"name": "input_format", "displayName": "Input Format", "description": "The input format for parsing input_date (optional, default RFC3339).", "type": "string", "required": false, "default": "", "order": 7}}, "data": {"$ref": "#/schemas/dt_timezoneConvert_data"}}, "formatter_dt_extract": {"name": "formatter_dt_extract", "displayName": "DateTime Extract", "description": "Extract a part of a date/time value (year, month, day, hour, minute, second).", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "datetime", "required": true, "default": "datetime", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "dt_extract", "required": true, "default": "dt_extract", "order": 2, "hidden": true}, "input_date": {"name": "input_date", "displayName": "Input Date", "description": "The date to extract from.", "type": "string", "required": true, "default": "", "order": 3}, "part": {"name": "part", "displayName": "Part", "description": "The part of the date to extract (e.g., 'year', 'month', 'day', 'hour', 'minute', 'second').", "type": "string", "enum": ["year", "month", "day", "hour", "minute", "second"], "required": true, "default": "", "order": 4}, "from_format": {"name": "from_format", "displayName": "From Format", "description": "The format of the date to extract from (optional, default RFC3339).", "type": "string", "required": false, "default": "", "order": 5}}, "data": {"$ref": "#/schemas/dt_extract_data"}}, "formatter_number_round": {"name": "formatter_number_round", "displayName": "Number Round", "description": "Round a number to the nearest integer or to a specified number of decimal places.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "number", "required": true, "default": "number", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "number_round", "required": true, "default": "number_round", "order": 2, "hidden": true}, "input_number": {"name": "input_number", "displayName": "Input Number", "description": "The number to round.", "type": "number", "required": true, "default": 0, "order": 3}, "precision": {"name": "precision", "displayName": "Precision", "description": "Number of decimal places to round to (optional, default 0).", "type": "integer", "required": false, "default": 0, "order": 4}}, "data": {"$ref": "#/schemas/number_round_data"}}, "formatter_number_parse": {"name": "formatter_number_parse", "displayName": "Number Parse", "description": "Parse a value (string, int, float, bool) to a number.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "number", "required": true, "default": "number", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "number_parse", "required": true, "default": "number_parse", "order": 2, "hidden": true}, "value": {"name": "value", "displayName": "Value", "description": "The value to parse to a float number (string, int, float, bool).", "type": "string", "required": true, "default": "", "order": 3}}, "data": {"$ref": "#/schemas/number_parse_data"}}, "formatter_number_currencyFormat": {"name": "formatter_number_currencyFormat", "displayName": "Number Currency Format", "description": "Format a number as a currency string.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "number", "required": true, "default": "number", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "number_currencyFormat", "required": true, "default": "number_currencyFormat", "order": 2, "hidden": true}, "input_number": {"name": "input_number", "displayName": "Input Number", "description": "The number to format as currency.", "type": "number", "required": true, "default": 0, "order": 3}, "currency": {"name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "description": "Currency code (e.g., USD, EUR, JPY).", "type": "string", "required": true, "default": "", "order": 4}, "locale": {"name": "locale", "displayName": "Locale", "description": "Locale for formatting (optional, default en-US).", "type": "string", "required": false, "default": "", "order": 5}, "minimum_fraction_digits": {"name": "minimum_fraction_digits", "displayName": "Minimum Fraction Digits", "description": "Minimum decimal places (optional).", "type": "integer", "required": false, "default": 0, "order": 6}, "maximum_fraction_digits": {"name": "maximum_fraction_digits", "displayName": "Maximum Fraction Digits", "description": "Maximum decimal places (optional).", "type": "integer", "required": false, "default": 0, "order": 7}}, "data": {"$ref": "#/schemas/number_currencyFormat_data"}}, "formatter_utilities_lookupTable": {"name": "formatter_utilities_lookupTable", "displayName": "Lookup Table", "description": "Given a key and table, find the matching value.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "utilities", "required": true, "default": "utilities", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "utilities_lookupTable", "required": true, "default": "utilities_lookupTable", "order": 2, "hidden": true}, "key": {"name": "key", "displayName": "Key", "description": "The key to look up in the table.", "type": "string", "required": true, "default": "", "order": 3}, "table": {"name": "table", "displayName": "Table", "description": "The lookup table as an object (map).", "type": "object", "required": true, "default": {}, "order": 4}, "default": {"name": "default", "displayName": "<PERSON><PERSON><PERSON>", "description": "Default value to return if key is not found.", "type": "string", "required": false, "default": "", "order": 5}}, "data": {"$ref": "#/schemas/utilities_lookupTable_data"}}, "formatter_utilities_generateRandom": {"name": "formatter_utilities_generateRandom", "displayName": "Generate Random", "description": "Generate a random integer or float in a given range.", "properties": {"category": {"name": "category", "displayName": "Category", "description": "The category of the operation", "type": "string", "const": "utilities", "required": true, "default": "utilities", "order": 1, "hidden": true}, "operation": {"name": "operation", "displayName": "Operation", "description": "The operation to perform", "type": "string", "const": "utilities_generateRandom", "required": true, "default": "utilities_generateRandom", "order": 2, "hidden": true}, "type": {"name": "type", "displayName": "Type", "description": "Type of random value to generate: int, float, or string.", "type": "string", "enum": ["int", "float", "string"], "required": true, "default": "", "order": 3}, "min": {"name": "min", "displayName": "Min", "description": "Minimum value (inclusive for int, inclusive for float). Optional. Default: 0. Ignored for string.", "type": "number", "required": false, "default": 0, "order": 4}, "max": {"name": "max", "displayName": "Max", "description": "Maximum value (exclusive for int, exclusive for float). Optional. Default: 100 for int, 1 for float. Ignored for string.", "type": "number", "required": false, "default": 0, "order": 5}, "length": {"name": "length", "displayName": "Length", "description": "Length of the generated string (required for type=string).", "type": "integer", "required": false, "default": 0, "order": 6}, "charset": {"name": "charset", "displayName": "Charset", "description": "Characters to use for string generation. Optional. Default: alphanumeric.", "type": "string", "required": false, "default": "", "order": 7}}, "data": {"$ref": "#/schemas/utilities_generateRandom_data"}}}}