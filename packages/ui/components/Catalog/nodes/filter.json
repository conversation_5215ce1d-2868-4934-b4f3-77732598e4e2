{"name": "filter", "displayName": "Filter", "category": ["builtin-tools", "modify"], "description": "Filter, map, and reduce operations on data collections", "settings": {"input": {"name": "input", "displayName": "Input Data", "description": "The array of items to filter", "type": "array", "required": true, "order": 1}, "conditions": {"name": "conditions", "displayName": "Filter Conditions", "description": "The conditions to filter by (OR groups with AND conditions)", "addText": "Add (OR)", "removeText": "<PERSON><PERSON><PERSON> (OR)", "type": "arrayObject", "required": true, "default": [], "items": {"$ref": "#/schemas/condition_group"}, "order": 2}, "continueOnEmpty": {"name": "continueOnEmpty", "displayName": "Continue on Empty Results", "description": "Whether to continue the workflow if no items match the filter", "type": "boolean", "required": false, "default": false, "order": 3}}, "schemas": {"condition_item": {"properties": {"field": {"name": "field", "displayName": "Field", "description": "The field to check", "type": "comboboxDataPoint", "required": true, "allowInput": false, "order": 1}, "operator": {"name": "operator", "displayName": "Operator", "description": "The comparison operator", "type": "options", "options": [{"value": "exists", "label": "Exists"}, {"value": "not_exists", "label": "Does Not Exist"}, {"value": "is_empty", "label": "Is Empty"}, {"value": "is_not_empty", "label": "Is Not Empty"}, {"value": "string_equals", "label": "String: Equals"}, {"value": "string_not_equals", "label": "String: Not Equals"}, {"value": "string_contains", "label": "String: Contains"}, {"value": "string_not_contains", "label": "String: Does Not Contain"}, {"value": "string_starts_with", "label": "String: Starts With"}, {"value": "string_not_starts_with", "label": "String: Does Not Start With"}, {"value": "string_ends_with", "label": "String: Ends With"}, {"value": "string_not_ends_with", "label": "String: Does Not End With"}, {"value": "string_matches_regex", "label": "String: Matches Regex"}, {"value": "number_equals", "label": "Number: Equals"}, {"value": "number_not_equals", "label": "Number: Not Equals"}, {"value": "number_greater_than", "label": "Number: Greater Than"}, {"value": "number_less_than", "label": "Number: Less Than"}, {"value": "date_equals", "label": "Date: Equals"}, {"value": "date_not_equals", "label": "Date: Not Equals"}, {"value": "date_before", "label": "Date: Before"}, {"value": "date_after", "label": "Date: After"}, {"value": "date_is_today", "label": "Date: Is Today"}, {"value": "date_is_in_past", "label": "Date: Is In Past"}, {"value": "date_is_in_future", "label": "Date: Is In Future"}, {"value": "boolean_is_true", "label": "Boolean: <PERSON>"}, {"value": "boolean_is_false", "label": "Boolean: <PERSON>"}], "required": true, "order": 2}, "value": {"name": "value", "displayName": "Value", "description": "The value to compare against (not required for empty/exist checks)", "type": "comboboxDataPoint", "required": false, "allowInput": true, "order": 3}}}, "condition_group": {"properties": {"and": {"name": "and", "displayName": "AND", "description": "The conditions to evaluate for and mode", "addText": "Add (AND)", "removeText": "Remove (AND)", "type": "arrayObject", "required": false, "order": 1, "items": {"$ref": "#/schemas/condition_item"}}}}}, "credentials": {}, "triggers": {}, "actions": {"filter": {"name": "filter", "displayName": "Filter", "description": "Filter data based on specified conditions", "order": 1, "properties": {"input": {"name": "input", "displayName": "Input Data", "description": "The array of items to filter", "type": "array", "required": true, "order": 1}, "conditions": {"name": "conditions", "displayName": "Filter Conditions", "description": "The conditions to filter by (OR groups with AND conditions)", "addText": "Add (OR)", "removeText": "<PERSON><PERSON><PERSON> (OR)", "type": "arrayObject", "required": true, "default": [], "items": {"$ref": "#/schemas/condition_group"}, "order": 2}, "continueOnEmpty": {"name": "continueOnEmpty", "displayName": "Continue on Empty Results", "description": "Whether to continue the workflow if no items match the filter", "type": "boolean", "required": false, "default": false, "order": 3}}, "output": {"type": "object", "properties": {"filtered": {"type": "array", "description": "The filtered array of items that match the conditions"}}}}}}