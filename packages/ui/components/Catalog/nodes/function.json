{"name": "function", "displayName": "Function", "category": ["builtin-tools", "modify"], "description": "Execute a function from the function server", "version": "1.0.0", "settings": {}, "schemas": {"functionOutput": {"properties": {"output": {"name": "output", "displayName": "Output", "type": "any", "description": "Output data from the function execution", "order": 1}, "error": {"name": "error", "displayName": "Error", "type": ["string", "object"], "description": "The error message if the code execution fails", "order": 2}, "logs": {"name": "logs", "displayName": "Logs", "type": ["array", "string"], "description": "The logs of the code execution", "order": 3}}}}, "actions": {"executeFunction": {"name": "executeFunction", "displayName": "Execute Function", "description": "Execute a function from the function server", "order": 1, "properties": {"functionId": {"name": "functionId", "displayName": "Function ID", "description": "The ID of the function to execute", "type": "string", "required": true, "order": 1}, "input": {"name": "input", "displayName": "Input", "description": "Input data for the function", "type": "keyvalue", "required": false, "order": 2}}, "data": {"$ref": "#/schemas/functionOutput"}}}}