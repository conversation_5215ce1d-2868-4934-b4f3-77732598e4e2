{"name": "path", "displayName": "Path", "icon": "🔀", "group": "core", "category": ["logic", "flow-control", "builtin-tools"], "description": "Routes workflow execution based on conditions, controlling the flow path", "settings": {"paths": {"name": "paths", "displayName": "Execution Paths", "description": "Define multiple execution paths with conditions", "type": "arrayObject", "items": {"$ref": "#/schemas/path"}}, "required": true, "order": 1}, "schemas": {"condition_item": {"properties": {"field": {"name": "field", "displayName": "Field", "description": "The field from input data to evaluate", "type": "comboboxDataPoint", "required": true, "allowInput": true, "order": 1}, "operator": {"name": "operator", "displayName": "Operator", "description": "The comparison operator", "type": "options", "options": [{"value": "exists", "label": "Exists"}, {"value": "not_exists", "label": "Does Not Exist"}, {"value": "is_empty", "label": "Is Empty"}, {"value": "is_not_empty", "label": "Is Not Empty"}, {"value": "string_equals", "label": "String: Equals"}, {"value": "string_not_equals", "label": "String: Not Equals"}, {"value": "string_contains", "label": "String: Contains"}, {"value": "string_not_contains", "label": "String: Does Not Contain"}, {"value": "string_in", "label": "String: In"}, {"value": "string_not_in", "label": "String: Not In"}, {"value": "string_starts_with", "label": "String: Starts With"}, {"value": "string_not_starts_with", "label": "String: Does Not Start With"}, {"value": "string_ends_with", "label": "String: Ends With"}, {"value": "string_not_ends_with", "label": "String: Does Not End With"}, {"value": "string_matches_regex", "label": "String: Matches Regex"}, {"value": "number_equals", "label": "Number: Equals"}, {"value": "number_not_equals", "label": "Number: Not Equals"}, {"value": "number_greater_than", "label": "Number: Greater Than"}, {"value": "number_less_than", "label": "Number: Less Than"}, {"value": "date_equals", "label": "Date: Equals"}, {"value": "date_not_equals", "label": "Date: Not Equals"}, {"value": "date_before", "label": "Date: Before"}, {"value": "date_after", "label": "Date: After"}, {"value": "date_is_today", "label": "Date: Is Today"}, {"value": "date_is_in_past", "label": "Date: Is In Past"}, {"value": "date_is_in_future", "label": "Date: Is In Future"}, {"value": "boolean_is_true", "label": "Boolean: <PERSON>"}, {"value": "boolean_is_false", "label": "Boolean: <PERSON>"}], "required": true, "order": 2}, "value": {"name": "value", "displayName": "Value", "description": "The value to compare against (not required for empty/exist checks)", "type": "comboboxDataPoint", "required": false, "allowInput": true, "order": 3}}}, "condition": {"properties": {"and": {"name": "and", "displayName": "AND", "description": "The conditions to evaluate for and mode", "addText": "Add (AND)", "removeText": "Remove (AND)", "type": "arrayObject", "required": false, "order": 1, "items": {"$ref": "#/schemas/condition_item"}}}}, "path": {"properties": {"mode": {"name": "mode", "displayName": "Path Mode", "description": "How this path should be evaluated", "type": "options", "options": [{"value": "condition", "label": "Based on Condition"}, {"value": "always", "label": "Always Run (bypass condition)"}, {"value": "fallback", "label": "Fallback (when no conditions match)"}], "default": "condition", "required": true, "order": 1}, "conditions": {"name": "conditions", "displayName": "Conditions", "description": "The conditions to evaluate for condition mode", "addText": "Add (OR)", "removeText": "<PERSON><PERSON><PERSON> (OR)", "type": "arrayObject", "required": false, "order": 2, "visibleIf": {"mode": ["condition"]}, "items": {"$ref": "#/schemas/condition"}}}}}, "data": {}}