import { Flex, Stack, TextInput } from '@mantine/core';
import { useEffect, useMemo, useState } from 'react';
import { Control } from 'react-hook-form';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import { FieldHeader, renderFieldComponent } from './FieldUtils';

export interface SchemaArrayInputBaseProps {
  value?: string;
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  withContainer?: boolean;
  error?: string;
  control?: Control<any>;
}

const ObjectInputBase = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaArrayInputBaseProps) => {
  const isObjectWithProperties = useMemo(() => {
    return schema?.type === 'object' && schema?.properties;
  }, [schema]);

  if (isObjectWithProperties) {
    return <ObjectPropertiesRenderer schema={schema} onChange={onChange} error={error} value={valueProp} />;
  }

  return <SimpleInputRenderer schema={schema} onChange={onChange} error={error} value={valueProp} />;
};

const ObjectPropertiesRenderer = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaArrayInputBaseProps) => {
  const [values, setValues] = useState<Record<string, any>>({});

  useEffect(() => {
    const data = valueProp || schema?.default || '{}';
    const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
    setValues(parsedData || {});
  }, [schema?.default, valueProp]);

  const handlePropertyChange = (propertyKey: string, value: any) => {
    const newValues = { ...values, [propertyKey]: value };
    setValues(newValues);
    onChange?.(newValues);
  };

  const properties = schema.properties || {};

  return (
    <Flex direction='column'>
      <FieldHeader schema={schema} />
      <Stack gap='md' mt='sm'>
        {Object.entries(properties).map(([propertyKey, propertySchema]: [string, any]) =>
          renderFieldComponent(
            propertyKey,
            propertySchema,
            values[propertyKey],
            (val: any) => handlePropertyChange(propertyKey, val),
            error
          )
        )}
      </Stack>
    </Flex>
  );
};

const SimpleInputRenderer = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaArrayInputBaseProps) => {
  const [value, setValue] = useState(valueProp || schema?.default || '');

  useEffect(() => {
    let defaultValue = valueProp || schema?.default || '';
    // If default value is an empty object, set to empty string
    if (defaultValue && typeof defaultValue === 'object' && Object.keys(defaultValue).length === 0) {
      defaultValue = '';
    }
    setValue(defaultValue);
  }, [valueProp, schema?.default]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    onChange?.(newValue);
  };

  return (
    <Flex direction='column'>
      <FieldHeader schema={schema} />
      <TextInput
        size='sm'
        value={value}
        error={error}
        onChange={handleChange}
        placeholder={schema.placeholder}
        mt='sm'
      />
    </Flex>
  );
};

export const SchemaObjectInput = ObjectInputBase;
export const SchemaObjectInputWithContainer = withContainer(SchemaObjectInput);
export const FormObjectInput = withReactHookForm(SchemaObjectInput);
export const FormObjectInputWithContainer = withReactHookForm(SchemaObjectInputWithContainer);
