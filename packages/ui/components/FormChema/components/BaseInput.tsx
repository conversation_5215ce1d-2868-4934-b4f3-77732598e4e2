import { NumberInput, PasswordInput, Textarea, TextInput } from '@mantine/core';
import React, { useEffect, useMemo, useState } from 'react';
import { withContainer } from '../../hoc/withContainer';
import { createStyles } from '@mantine/emotion';

const useStyles = createStyles(_ => ({
  root: {
    '& input[readonly]': {
      backgroundColor: 'var(--input-disabled-bg)',
      color: 'var(--input-disabled-color)',
    },
  },
  hidden: {
    display: 'none',
  },
}));

export interface SchemaBaseInputProps {
  value?: string;
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  withContainer?: boolean;
  error?: string;
}

const NUMBER_TYPES = ['number', 'integer'];
const NUMBER_TYPES_WITH_DECIMAL = ['number'];

export const SchemaBaseInput: React.FC<SchemaBaseInputProps> = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaBaseInputProps) => {
  const { classes, cx } = useStyles();
  const [value, setValue] = useState(valueProp || (schema.default ?? ''));
  const [isInitialized, setIsInitialized] = useState(false);

  const onUpdateValue = (newValue: string) => {
    setValue(newValue);
    onChange?.(newValue);
  }
  
  useEffect(() => {
    if (!isInitialized) {
      if (
        schema.default &&
        typeof schema.default === 'object' &&
        Object.keys(schema.default).length === 0
      ) {
        onUpdateValue('');
      } else {
        onUpdateValue(valueProp || (schema.default ?? ''));
      }
      setIsInitialized(true);
    } else {
      if (valueProp !== undefined) {
        onUpdateValue(valueProp);
      } else if (schema.required && schema.default) {
        onUpdateValue(schema.default);
      }
    }
  }, [valueProp, schema.default, isInitialized, schema.required]);

  const isNotNumber = isNaN(Number(value));
  const haveLinebreak = isNotNumber && typeof value === 'string' && value.includes('\n');

  const InputComponent = useMemo(() => {
    if (haveLinebreak) {
      return Textarea;
    }
    if (schema.type === 'text-masked') {
      return PasswordInput;
    }
    if (NUMBER_TYPES.includes(schema.type)) {
      return NumberInput;
    }
    return TextInput;
  }, [haveLinebreak, schema.type]);

  const handleChange = (e) => {
    let newValue = e?.target?.value || '';
    if (NUMBER_TYPES.includes(schema.type)) {
      newValue = e;
    }
    // If the field is required and user clears the input, reset to schema default
    if (schema.required && (newValue === '' || newValue === null || newValue === undefined)) {
      const defaultValue = schema.default ?? '';
      onUpdateValue(defaultValue);
    } else {
      onUpdateValue(newValue);
    }
  }

  return (
    <InputComponent
      name={schema.name}
      value={value}
      onChange={handleChange}
      onBlur={() => {
        onChange?.(value.trim());
      }}
      label={schema.displayName}
      placeholder={schema.placeholder}
      labelProps={{
        required: schema.required,
      }}
      min={0}
      max={100000000}
      description={schema.description}
      error={error}
      allowDecimal={NUMBER_TYPES_WITH_DECIMAL.includes(schema.type)}
      allowNegative={false}
      disabled={schema?.disabled}
      readOnly={schema?.readOnly}
      hideControls
      classNames={{
        root: cx(classes.root, {
          [classes.hidden]: schema.hidden,
        }),
      }}
    />
  );
};

export const SchemaBaseInputWithContainer = withContainer(SchemaBaseInput);
