import { TextInput } from '@mantine/core';
import React, { useEffect, useState } from 'react';
import { IconLink } from '@tabler/icons-react';
import { SchemaBaseInputProps } from './BaseInput';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import { validateUrl } from '../../../utils/schema';

export const SchemaURLInput: React.FC<SchemaBaseInputProps> = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaBaseInputProps) => {
  const [value, setValue] = useState(valueProp || schema.default || '');
  const [validationError, setValidationError] = useState<string | undefined>();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!isInitialized) {
      setValue(valueProp || schema.default || '');
      setIsInitialized(true);
    } else if (valueProp !== undefined) {
      setValue(valueProp);
    }
  }, [valueProp, schema.default, isInitialized]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);

    if (newValue && !validateUrl(newValue)) {
      setValidationError('Please enter a valid URL (e.g., https://example.com)');
    } else {
      setValidationError(undefined);
    }

    onChange?.(newValue);
  };

  return (
    <TextInput
      type="url"
      name={schema.name}
      value={value}
      onChange={handleChange}
      label={schema.displayName}
      placeholder={schema.placeholder}
      labelProps={{
        required: schema.required,
      }}
      description={schema.description}
      error={error || validationError}
      leftSection={<IconLink size={16} />}
    />
  );
};

export const SchemaURLInputWithContainer = withContainer(SchemaURLInput);
export const FormURLInput = withReactHookForm(SchemaURLInput);
export const FormURLInputWithContainer = withReactHookForm(SchemaURLInputWithContainer); 